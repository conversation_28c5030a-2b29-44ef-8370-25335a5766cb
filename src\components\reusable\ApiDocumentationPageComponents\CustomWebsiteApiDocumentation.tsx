import { useState } from 'react';

const requestParameters = [
  {
    field: 'apiKey',
    type: 'string',
    required: 'Yes',
    description: 'API key used for authentication.',
  },
  {
    field: 'customOrderId',
    type: 'string',
    required: 'Yes (Unique)',
    description: 'Unique identifier for the order from your system.',
  },
  {
    field: 'customCustomerId',
    type: 'string',
    required: 'No',
    description: 'Optional identifier for the customer in your system.',
  },
  {
    field: 'firstName',
    type: 'string',
    required: 'Yes',
    description: 'Customer’s first name.',
  },
  {
    field: 'lastName',
    type: 'string',
    required: 'No',
    description: 'Customer’s last name (optional).',
  },
  {
    field: 'mobileNumber',
    type: 'string',
    required: 'Yes',
    description: 'Customer’s mobile number.',
  },
  {
    field: 'email',
    type: 'string',
    required: 'No',
    description: 'Customer email address (optional).',
  },
  {
    field: 'note',
    type: 'string',
    required: 'No',
    description: 'Additional note or message.',
  },
  {
    field: 'orderDate',
    type: 'string',
    required: 'Yes',
    description: 'The date of the order (ISO format recommended).',
  },
  {
    field: 'address.street',
    type: 'string',
    required: 'Yes',
    description: 'Street name and number.',
  },
  {
    field: 'address.district',
    type: 'string',
    required: 'No',
    description: 'District (optional).',
  },
  {
    field: 'address.zipCode',
    type: 'string',
    required: 'No',
    description: 'Postal code (optional).',
  },
  {
    field: 'address.division',
    type: 'string',
    required: 'No',
    description: 'Division or state (optional).',
  },
  {
    field: 'address.country',
    type: 'string',
    required: 'No',
    description: 'Country (optional).',
  },
  {
    field: 'flatDiscount',
    type: 'number',
    required: 'No',
    description: 'Flat discount applied to the order.',
  },
  {
    field: 'deliveryCharge',
    type: 'number',
    required: 'Yes',
    description: 'Delivery charge added to the order.',
  },
  {
    field: 'lineItems',
    type: 'array',
    required: 'Yes',
    description: 'List of products in the order.',
  },
  {
    field: 'paymentMethods',
    type: 'array',
    required: 'No',
    description: 'Array of payment methods used.',
  },
];

function CustomWebsiteApiDocumentation({ apiKey }: { apiKey: string }) {
  const [copied, setCopied] = useState(false);

  const schema = `{
    "apiKey": "string",
    "customOrderId": "string",
    "customCustomerId": "string",
    "firstName": "string",
    "lastName": "string",
    "mobileNumber": "string",
    "email": "string",
    "note": "string",
    "orderDate": "string",
    "address": {
      "street": "string",
      "district": "string",
      "zipCode": "string",
      "division": "string",
      "country": "string"
    },
    "flatDiscount": 0,
    "deliveryCharge": 0,
    "lineItems": [
      {
        "customLineItemId": "string",
        "productName": "string",
        "price": 0,
        "discount": 0,
        "quantity": 0
      }
    ],
    "paymentMethods": [
      {
        "method": "CASH",
        "amount": 0,
        "serialNo": "string"
      }
    ]
  }`;

  const handleCopy = () => {
    navigator.clipboard.writeText(schema);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div className="h-custom2 overflow-y-auto rounded-lg bg-white p-2">
      <h1 className="mb-6 text-3xl font-bold text-gray-800">
        API Documentation For Custom Code Website
      </h1>
      <p className="mb-8 text-gray-600">
        This documentation will guide you on how to integrate your website API
        with our POS system. Follow the steps below to get started.
      </p>

      <div className="mb-8">
        <h2 className="mb-4 text-xl font-semibold text-gray-700">
          Basic Setup
        </h2>
        <table className="w-full border-collapse border border-gray-300 text-sm">
          <thead className="bg-gray-100">
            <tr>
              <th className="border px-4 py-2 text-left">Name</th>
              <th className="border px-4 py-2 text-left">Value</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="border px-4 py-2">Base Url</td>
              <td className="border px-4 py-2">
                https://pos-api.softs.ai/api/v1
              </td>
            </tr>
            <tr>
              <td className="border px-4 py-2">API Key</td>
              <td className="border px-4 py-2">{apiKey}</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div className="mb-4">
        <h2 className="text-xl font-semibold text-gray-700">
          1. Submit Website Order to POS
        </h2>
      </div>

      {/* Step 2: Endpoints */}
      <div className="mb-2">
        <table className="w-full border-collapse border border-gray-300 text-sm">
          <thead className="bg-gray-100">
            <tr>
              <th className="border px-4 py-2 text-left">Endpoint</th>
              <th className="border px-4 py-2 text-left">Method</th>
              <th className="border px-4 py-2 text-left">Description</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="border px-4 py-2">/webhook/listen/ecommerce</td>
              <td className="border px-4 py-2">POST</td>
              <td className="border px-4 py-2">Submit website order to POS</td>
            </tr>
          </tbody>
        </table>
      </div>

      {/* Step 4: Schema with Copy Button */}
      <div className="mb-8">
        <h2 className="mb-2 text-lg font-semibold text-gray-700">
          Order Submission Schema
        </h2>
        <p className="mb-2 text-gray-600">
          Use the following schema to send an order to the POS system:
        </p>
        <div className="relative">
          <button
            type="button"
            onClick={handleCopy}
            className="absolute right-4 top-4 rounded-md bg-blue-500 px-3 py-1 text-sm text-white hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-400"
          >
            {copied ? 'Copied!' : 'Copy'}
          </button>
          <pre className="rounded-lg bg-black p-4 text-sm text-white">
            {schema}
          </pre>
        </div>
      </div>
      {/* Step 5: Field Descriptions */}
      <div className="mb-8">
        <h2 className="mb-4 text-xl font-semibold text-gray-700">
          Request Parameters
        </h2>
        <p className="mb-2 text-gray-600">
          The table below outlines the meaning, data type, and requirement
          status of each field in the order submission schema:
        </p>

        <div className="overflow-auto">
          <table className="w-full border-collapse border border-gray-300 text-sm">
            <thead className="bg-gray-100">
              <tr>
                <th className="border px-4 py-2 text-left">Field</th>
                <th className="border px-4 py-2 text-left">Type</th>
                <th className="border px-4 py-2 text-left">Required</th>
                <th className="border px-4 py-2 text-left">Description</th>
              </tr>
            </thead>
            <tbody>
              {requestParameters.map((param) => (
                <tr key={param.field}>
                  <td className="border px-4 py-2">{param.field}</td>
                  <td className="border px-4 py-2">{param.type}</td>
                  <td className="border px-4 py-2">{param.required}</td>
                  <td className="border px-4 py-2">{param.description}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      <div className="mb-8">
        <h2 className="mb-4 text-xl font-semibold text-gray-700">
          Example Response
        </h2>
        <pre className="overflow-auto rounded-lg bg-gray-100 p-4 text-sm text-gray-800">
          orders will be created successfully
        </pre>
      </div>

      {/* Step 6: Support */}
      <div>
        <h2 className="mb-4 text-xl font-semibold text-gray-700">Support</h2>
        <p className="text-gray-600">
          Need help? Contact our support team at{' '}
          <a
            href="mailto:<EMAIL>"
            className="text-blue-500 underline"
          >
            <EMAIL>
          </a>
          .
        </p>
      </div>
    </div>
  );
}

export default CustomWebsiteApiDocumentation;
