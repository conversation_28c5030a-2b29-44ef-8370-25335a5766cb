import { Document, pdf } from '@react-pdf/renderer';
import { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import StockEntryInvoicePdf from './StockEntryInvoicePdf';
import StockEntryListFilterOptions from './StockEntryListFilterOptions';
import StockEntryListPageFilterModal from './StockEntryListPageFilterModal';

import {
  BarcodePDFButton,
  EyeButton,
  PrintA4Button,
} from '@/components/reusable/Buttons/CommonButtons';
import FilterButton from '@/components/reusable/Buttons/FilterButton';
import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import ImageViewer from '@/components/reusable/ImageViewer/ImageViewer';
import Modal from '@/components/reusable/Modal/Modal';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import Pagination from '@/components/reusable/Pagination/Pagination';
import PdfGeneratingLoader from '@/components/reusable/PdfGeneratingLoader/PdfGeneratingLoader';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import {
  useGetSingleEntryDetailsQuery,
  useGetSupplierBillsQuery,
} from '@/redux/api/warehouseApis/purchaseApis';
import { useAppSelector } from '@/redux/hooks';
import { ROUTES } from '@/Routes';
import { SingleSupplierBillDetails } from '@/types/warehouseTypes/purchaseTypes';
import { formatNumberWithComma } from '@/utils/formatNumberWithComma';
import { generateFilterParams } from '@/utils/generateFilterParams';

interface Props {
  warehouseId: string;
}

function StockEntryListPageOverview({ warehouseId }: Props) {
  const { warehouseDetails } = useAppSelector((state) => state);
  const navigate = useNavigate();
  const router = new URLSearchParams(useLocation().search);
  const page = router.get('page');
  const limit = router.get('limit');
  const serialNo = router.get('serialNo');
  const supplierName = router.get('supplierName');
  const supplierInvoiceNo = router.get('supplierInvoiceNo');
  const startDate = router.get('startDate');
  const endDate = router.get('endDate');
  const { data, isLoading, isFetching } = useGetSupplierBillsQuery({
    warehouseId,
    page: page ?? '1',
    limit: limit ?? '10',
    serialNo: serialNo ?? undefined,
    supplierInvoiceNo: supplierInvoiceNo ?? undefined,
    supplierName: supplierName ?? undefined,
    type: startDate && endDate ? 'custom' : undefined,
    startDate: startDate ?? undefined,
    endDate: endDate ?? undefined,
  });
  const [isFilterModalOpen, setIsFilterModalOpen] = useState<boolean>(false);
  const [isPdfGenerating, setIsPdfGenerating] = useState<boolean>(false);

  const handleFilter = (fieldName: string, value: string) => {
    const query = generateFilterParams(fieldName, value);
    navigate(ROUTES.STOCK.ENTRY_LIST(warehouseId, query));
  };

  const [entryDetailsForInvoicePdf, setEntryDetailsForInvoicePdf] = useState({
    id: '',
    isAvailable: false,
  });

  const {
    data: stockEntryDetailsData,
    isLoading: isEntryDetailsLoading,
    refetch,
  } = useGetSingleEntryDetailsQuery(
    {
      id: entryDetailsForInvoicePdf.id,
      warehouseId,
    },
    {
      skip: !entryDetailsForInvoicePdf.isAvailable,
    },
  );

  useEffect(() => {
    const handleGenerateSingleEntryPdf = async () => {
      const blob = await pdf(
        <Document>
          <StockEntryInvoicePdf
            supplierBillData={stockEntryDetailsData?.data}
            warehouseDetails={warehouseDetails}
          />
        </Document>,
      ).toBlob();

      const url = URL.createObjectURL(blob);
      window.open(url);
    };
    if (stockEntryDetailsData?.data) {
      handleGenerateSingleEntryPdf();
      setIsPdfGenerating(false);
      setEntryDetailsForInvoicePdf({
        id: '',
        isAvailable: false,
      });
    }
  }, [stockEntryDetailsData, isEntryDetailsLoading]);

  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-2">
        <div className="flex items-center">
          <div className="search-title-and-btn flex items-center">
            <div className="relative">
              <div className="block xl:hidden">
                <FilterButton
                  handleClick={() => setIsFilterModalOpen(!isFilterModalOpen)}
                />
              </div>
              <div
                className={`${isFilterModalOpen ? 'block' : 'hidden'} xl:hidden`}
              >
                <StockEntryListPageFilterModal
                  handleClearAndClose={() => setIsFilterModalOpen(false)}
                  handleFilter={handleFilter}
                />
              </div>
            </div>
          </div>
          <div className="hidden xl:block">
            <div className="flex items-center gap-x-2">
              <StockEntryListFilterOptions handleFilter={handleFilter} />
            </div>
          </div>
        </div>
      </div>
      {!isLoading && !isFetching ? (
        <div>
          <div className="tableTop w-full">
            <p>Purchase Invoice</p>
            <p>Total : {data?.pagination?.total}</p>
          </div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {data?.data?.length ? (
              <div className="full-table-box h-custom">
                <table className="full-table">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="tableHead">No</th>
                      <th className="tableHead">Invoice No</th>
                      <th className="tableHead">Image</th>
                      <th className="tableHead">User</th>
                      <th className="tableHead">Supplier</th>
                      <th className="tableHead">Supplier Invoice</th>
                      <th className="tableHead">Items</th>
                      <th className="tableHead">Products</th>
                      <th className="tableHead">TP</th>
                      <th className="tableHead">Paid</th>
                      <th className="tableHead">Due</th>
                      <th className="tableHead">Created At</th>
                      <th className="tableHead">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y bg-slate-200">
                    {data?.data?.map(
                      (
                        singleBill: SingleSupplierBillDetails,
                        index: number,
                      ) => (
                        <tr key={singleBill?.id}>
                          <td className="tableData">{index + 1}</td>
                          <td className="tableData">
                            {singleBill?.serialNo ?? 0}
                          </td>
                          <td className="tableData">
                            <ImageViewer imageUrl={singleBill?.proofUrl} />
                          </td>
                          <td className="tableData">User</td>
                          <td className="tableData">
                            {singleBill?.Supplier?.User?.name ?? ''}
                          </td>
                          <td className="tableData">
                            {singleBill?.supplierInvoiceNo ?? ''}
                          </td>
                          <td className="tableData">
                            {singleBill?.totalProduct}
                          </td>
                          <td className="tableData">
                            {formatNumberWithComma(singleBill?.totalStock)}
                          </td>
                          <td className="tableData">
                            {formatNumberWithComma(singleBill?.totalPrice)}
                          </td>
                          <td className="tableData">
                            {formatNumberWithComma(singleBill?.totalPaid)}
                          </td>
                          <td className="tableData">
                            {formatNumberWithComma(singleBill?.totalDue)}
                          </td>
                          <td className="tableData">
                            <DateAndTimeViewer date={singleBill?.createdAt} />
                          </td>
                          <td className="tableData">
                            <div className="flex items-center justify-center gap-2">
                              <EyeButton
                                handleClick={() =>
                                  navigate(
                                    ROUTES.STOCK.ENTRY_DETAILS(
                                      warehouseId,
                                      singleBill?.id,
                                    ),
                                  )
                                }
                              />
                              {/* <BarcodeButtonv2
                                handleClick={() => {
                                  const url = ROUTES.STOCK.SINGLE_ENTRY_BARCODE(
                                    singleBill?.id,
                                    warehouseId,
                                  );
                                  window.open(url, '_blank');
                                }}
                              /> */}
                              <BarcodePDFButton
                                handleClick={() => {
                                  const url =
                                    ROUTES.STOCK.SINGLE_ENTRY_BARCODE_PDF(
                                      singleBill?.id,
                                      warehouseId,
                                    );
                                  window.open(url, '_blank');
                                }}
                              />
                              <PrintA4Button
                                handleClick={() => {
                                  setIsPdfGenerating(true);
                                  setEntryDetailsForInvoicePdf({
                                    id: singleBill.id,
                                    isAvailable: true,
                                  });
                                  refetch();
                                }}
                              />
                            </div>
                          </td>
                        </tr>
                      ),
                    )}
                  </tbody>
                </table>
              </div>
            ) : (
              <NoResultFound pageType="stock entry" />
            )}
          </div>
          <div className="pagination-box flex justify-end rounded bg-white p-3">
            <Pagination
              currentPage={page ?? '1'}
              limit={Number(limit ?? 10)}
              handleFilter={(fieldName: string, value: any) =>
                handleFilter(fieldName, value)
              }
              totalCount={data?.pagination?.total}
              totalPages={Math.ceil(
                Number(data?.pagination?.total) /
                  Number(data?.pagination?.limit),
              )}
            />
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={12} tableRow={6} />
      )}
      <Modal showModal={isPdfGenerating} setShowModal={setIsPdfGenerating}>
        <PdfGeneratingLoader />
      </Modal>
    </div>
  );
}

export default StockEntryListPageOverview;
