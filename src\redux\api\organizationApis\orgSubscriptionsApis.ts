import BaseApi from '../baseApi';

import { Pagination } from '@/redux/commonTypes';
import { TagTypes } from '@/redux/tag-types';

interface GetPaymentsReq {
  organizationId?: string;
  page?: string;
  limit?: string;
}
const OrgSubscriptionsApis = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getOrgSubscriptionsList: builder.query<
      GetOrgSubscriptionsListResponse,
      GetPaymentsReq
    >({
      query: (params) => ({
        url: `/subscription`,
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.ORG_SUBSCRIPTIONS],
    }),
    getSingleOrgSubscriptionDetails: builder.query<
      GetOrgSingleSubscriptionsDetailsResponse,
      string
    >({
      query: (id) => ({
        url: `/subscription/${id}`,
        method: 'GET',
      }),
      providesTags: [TagTypes.ORG_SUBSCRIPTIONS],
    }),
    initiateSubscriptionPayment: builder.mutation<
      SubscriptionPaymentInitiatedResponse,
      any
    >({
      query: (data) => ({
        url: '/subscription/pay',
        method: 'POST',
        data,
      }),
      invalidatesTags: [TagTypes.ORG_SUBSCRIPTIONS],
    }),
  }),
});

export const {
  useGetOrgSubscriptionsListQuery,
  useGetSingleOrgSubscriptionDetailsQuery,
  useInitiateSubscriptionPaymentMutation,
} = OrgSubscriptionsApis;

export interface GetOrgSubscriptionsListResponse {
  success: boolean;
  message: string;
  statusCode: number;
  pagination: Pagination;
  data: SingleSubscription[];
}

export interface GetOrgSingleSubscriptionsDetailsResponse {
  success: boolean;
  message: string;
  statusCode: number;
  data: SingleSubscription;
}

export interface SingleSubscription {
  id: string;
  createdAt: string;
  updatedAt: string;
  organizationId: string;
  invoiceNo: string;
  planId: string;
  isActive: boolean;
  startDate: string;
  endDate: string;
  billingCycle: string;
  paymentStatus: string;
  nextBillingDate: string;
  status: string;
  amount: number;
  isCancelled: boolean;
  isPaid: boolean;
  Organization: Organization;
  Plan: SinglePlanOfSubscription;
}

export interface Organization {
  id: string;
  name: string;
}

export interface SinglePlanOfSubscription {
  id: string;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
  isDeleted: boolean;
  name: string;
  planType: string;
  priceMonthly: number;
  priceYearly: number;
  warehouseLimit: number;
  shopLimit: number;
  productLimit: number;
  orderLimit: number;
  employeeLimit: number;
  stockLimit: number;
  hasSmsFeature: boolean;
  hasAdvancedReport: boolean;
  discount: number;
  discountType: string;
  isCustom: boolean;
  isTrial: boolean;
}

export interface SubscriptionPaymentInitiatedResponse {
  success: boolean;
  message: string;
  statusCode: number;
  data: { redirectUrl: string };
}
