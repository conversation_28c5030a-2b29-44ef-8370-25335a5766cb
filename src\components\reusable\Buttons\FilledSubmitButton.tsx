interface Props {
  text: string;
  isLoading: boolean;
  isDisabled?: boolean;
}
function FilledSubmitButton({ text, isLoading, isDisabled }: Props) {
  return (
    <button
      disabled={isLoading || isDisabled}
      type="submit"
      className="w-full cursor-pointer rounded-lg bg-[#28243D] px-8 py-2 font-semibold text-white hover:bg-[#28243dd4] disabled:bg-slate-400"
    >
      {isLoading ? (
        <div className="flex h-6 items-center justify-center">
          <div className="h-6 w-6 animate-spin rounded-full border-t-4 border-blue-500" />
        </div>
      ) : (
        <span>{text}</span>
      )}
    </button>
  );
}

export default FilledSubmitButton;
