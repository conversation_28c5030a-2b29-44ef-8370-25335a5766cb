import BaseApi from '../baseApi';

import { TagTypes } from '@/redux/tag-types';
import {
  GetSingleStockEntryRequestDetailsResponse,
  GetStockEntryRequestListResponse,
  GetStockListResponse,
  GetStockLocationResponse,
} from '@/types/warehouseTypes/stockTypes';

interface GetStockListParams {
  organizationId: string;
  warehouseId?: string;
  shopId?: string;
  optimal?: boolean;
  page?: string;
  productName?: string;
  barcode?: string;
  limit?: string;
  productSerialNo?: string;
  productId?: string;
  type?: string;
  startDate?: string;
  endDate?: string;
  status?: string;
}

interface GetStockLocationParams {
  productName?: string | null;
  productId?: string | null;
}

const ShopStocksApi = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getShopStockList: builder.query<GetStockListResponse, GetStockListParams>({
      query: (params) => ({
        url: '/stock',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP_STOCK],
    }),
    getShopStockHoldList: builder.query<
      GetStockEntryRequestListResponse,
      GetStockListParams
    >({
      query: (params) => ({
        url: '/stock/hold',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP_STOCK],
    }),
    getShopSingleStockEntryDetails: builder.query<
      GetSingleStockEntryRequestDetailsResponse,
      any
    >({
      query: (data) => ({
        url: `/stock/hold/${data.id}?shopId=${data.shopId}`,
        method: 'GET',
      }),
      providesTags: [TagTypes.SHOP_STOCK],
    }),
    acceptStockEntry: builder.mutation({
      query: ({ id, data }) => ({
        url: `/stock/hold/${id}`,
        method: 'PATCH',
        data,
      }),
      invalidatesTags: [TagTypes.SHOP_STOCK],
    }),
    acceptStockRestock: builder.mutation({
      query: ({ id, data }) => ({
        url: `/stock/approve-reject/${id}`,
        method: 'PATCH',
        data,
      }),
      invalidatesTags: [TagTypes.SHOP_STOCK],
    }),
    getStockLocation: builder.query<
      GetStockLocationResponse,
      GetStockLocationParams
    >({
      query: (params) => ({
        url: '/products/check',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP_STOCK],
    }),
  }),
});

export const {
  useGetShopStockListQuery,
  useGetShopStockHoldListQuery,
  useGetShopSingleStockEntryDetailsQuery,
  useAcceptStockEntryMutation,
  useGetStockLocationQuery,
  useAcceptStockRestockMutation,
} = ShopStocksApi;
