import JsBarcode from 'jsbarcode';
import { useEffect } from 'react';

interface Props {
  barcode: string;
  name: string;
  price: string;
}
function SingleStockBarcode({ barcode, name, price }: Props) {
  useEffect(() => {
    if (barcode) {
      const barcodeValue = `${barcode}`;
      const canvas = document.getElementById(barcode);
      JsBarcode(canvas, barcodeValue, {
        format: 'CODE128',
        displayValue: false,
      });
    }
  }, [barcode]);
  return (
    <div
      // className="ml-[4px] mt-[4px] flex h-[85px] w-[144px] flex-col items-center justify-center"
      style={{
        margin: '0px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'column',
        height: '1in',
        maxHeight: '1in',
        width: '144px',
        overflow: 'hidden',
      }}
    >
      <canvas id={barcode} style={{ width: '120px', height: '30px' }} />
      <p
        // className="w-full text-center text-[10px] font-bold"
        style={{
          width: '100%',
          textAlign: 'center',
          fontSize: '10px',
          fontWeight: 600,
        }}
      >
        [{barcode}], BDT-{price}
      </p>
      <p
        // className="w-full text-center text-[10px] font-semibold"
        style={{
          lineHeight: '12px',
          width: '100%',
          textAlign: 'center',
          fontSize: '10px',
          fontWeight: 500,
        }}
      >
        {name?.toLowerCase()}
      </p>
    </div>
  );
}

export default SingleStockBarcode;
