interface Option {
  label: string;
  value: string;
}
interface Props {
  options: Option[];
  handleSelect: (value: string) => void;
  selectedValue: string;
  placeHolder?: string;
  hideAllOption?: boolean;
}
function CustomSelectForFilter({
  options,
  handleSelect,
  selectedValue,
  placeHolder,
  hideAllOption = false,
}: Props) {
  return (
    <form className="w-full">
      <div className="relative">
        <select
          id="countries"
          className="block w-full cursor-pointer appearance-none rounded-lg border border-gray-400 py-[7.5px] pl-3 pr-10 text-sm text-gray-600"
          style={{ outline: 'none', backgroundPositionX: '5px' }}
          value={selectedValue}
          onChange={(e) => handleSelect(e.target.value)}
        >
          <option value="" disabled>
            {placeHolder || 'Choose Option'}
          </option>
          {!hideAllOption ? <option value="">All</option> : ''}
          {options?.map((singleOption: Option) => (
            <option value={singleOption?.value} key={singleOption?.value}>
              {singleOption.label}
            </option>
          ))}
        </select>
        <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
          <svg
            className="h-4 w-4 text-gray-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M19 9l-7 7-7-7"
            />
          </svg>
        </span>
      </div>
    </form>
  );
}

export default CustomSelectForFilter;
