interface Props {
  label: string;
  name: string;
  placeholder: string;
  formik?: any;
  isDisabled?: boolean;
}

function CustomDateInput({
  label,
  name,
  placeholder,
  formik,
  isDisabled,
}: Props) {
  return (
    <div className="group relative z-0 mt-[-20px] w-full">
      <label
        htmlFor={name}
        className="relative left-3 top-2.5 w-auto bg-white px-1 font-mono text-[12px] font-bold text-gray-900 group-focus-within:text-red-600 dark:text-gray-300"
      >
        {label}
      </label>
      <input
        disabled={isDisabled}
        type="date"
        name={name}
        id={name}
        className={`text-10 py-55-rem block h-10 w-full rounded-lg border disabled:cursor-not-allowed disabled:bg-gray-100 ${
          formik.touched[name] && formik.errors[name]
            ? 'border-red-500'
            : 'border-gray-300'
        } bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500`}
        placeholder={placeholder}
        onChange={formik.handleChange}
        onBlur={formik.handleBlur}
        value={formik.values[name] || new Date().toISOString().substring(0, 10)}
      />
      {formik.touched[name] && formik.errors[name] ? (
        <p className="m-0 text-xs text-red-400">{formik.errors[name]}</p>
      ) : null}
    </div>
  );
}

export default CustomDateInput;
