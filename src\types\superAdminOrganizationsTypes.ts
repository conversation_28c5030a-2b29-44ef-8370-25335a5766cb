import { Pagination } from '@/redux/commonTypes';

export interface GetSuperAdminOrganizationsResponse {
  success: boolean;
  statusCode: number;
  message: string;
  data: SuperAdminSingleOrganization[];
  pagination: Pagination;
}

export interface SuperAdminSingleOrganization {
  id: string;
  createdAt: string;
  updatedAt: string;
  name: string;
  address: any;
  imgUrl: any;
  userId: string;
  lastPurchaseNo: number;
  lastSupplierBillNo: number;
  lastSupplierNo: number;
  lastStockNo: number;
  lastProductNo: number;
  lastCustomerNo: number;
  lastEmployeeNo: number;
  CourierProvider: any[];
  warehouseLimit: number;
  shopLimit: number;
  Shop: Shop[];
  Warehouse: Warehouse[];
  User: User;
}

export interface Shop {
  id: string;
}

export interface Warehouse {
  id: string;
}

export interface User {
  id: string;
  imgUrl: any;
  name: string;
  email: string;
  mobileNumber: string;
}
