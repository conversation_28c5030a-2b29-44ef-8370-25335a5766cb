import { Pagination } from '@/redux/commonTypes';

export interface GetSuperAdminOrganizationsResponse {
  success: boolean;
  statusCode: number;
  message: string;
  data: SuperAdminSingleOrganization[];
  pagination: Pagination;
}

export interface SuperAdminSingleOrganization {
  id: string;
  name: string;
  email: string;
  username: string;
  mobileNumber: string;
  imgUrl?: string;
  organizationLimit: number;
  warehouseLimit: number;
  shopLimit: number;
  type: string;
  organizationCount: number;
  warehouseCount: number;
  shopCount: number;
  totalShops: number;
  totalWarehouses: number;
  createdAt: string;
}
