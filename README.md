# POS Client Application

A modern Point of Sale (POS) client application built with React, TypeScript, and Vite. This application provides a robust and user-friendly interface for managing sales, inventory, and business operations.

## 🚀 Features

- **Modern Tech Stack**: Built with React 18, TypeScript, and Vite
- **State Management**: Redux Toolkit for efficient state management
- **UI Components**: 
  - Radix UI for accessible components
  - Headless UI for unstyled, accessible components
  - Tailwind CSS for styling
- **Data Visualization**: ApexCharts for interactive charts and graphs
- **Form Handling**: Formik with Yup validation
- **PDF Generation**: React-PDF for document generation
- **Real-time Updates**: Socket.IO for real-time communication
- **Error Tracking**: Sentry integration for error monitoring
- **Code Quality**: ESLint, <PERSON>ttier, and <PERSON><PERSON> for code quality and consistency

## 📦 Installation

1. Clone the repository:
```bash
git clone [repository-url]
cd pos-client
```

2. Install dependencies:
```bash
yarn install
```

3. Start the development server:
```bash
yarn dev
```

## 🛠️ Available Scripts

- `yarn dev` - Start development server
- `yarn build` - Build for production
- `yarn preview` - Preview production build
- `yarn lint` - Run ESLint
- `yarn lint:fix` - Fix ESLint issues
- `yarn type:check` - Run TypeScript type checking
- `yarn deploy` - Build and deploy to AWS

## 🏗️ Project Structure

```
src/
├── components/         # Reusable UI components
├── redux/             # Redux store and slices
├── routes/            # Application routes
├── utils/             # Utility functions
└── types/             # TypeScript type definitions
```

## 🔧 Configuration

The project uses several configuration files:

- `vite.config.ts` - Vite configuration
- `tailwind.config.js` - Tailwind CSS configuration
- `tsconfig.json` - TypeScript configuration
- `.eslintrc.cjs` - ESLint configuration
- `.prettierrc.cjs` - Prettier configuration

## 🚀 Deployment

The application can be deployed to AWS using the provided deployment script:

```bash
yarn deploy
```

This will:
1. Build the application
2. Sync the build to S3
3. Create a CloudFront invalidation

## 🛡️ Code Quality

The project enforces code quality through:

- ESLint for code linting
- Prettier for code formatting
- Husky for git hooks
- Commitlint for commit message validation
- TypeScript for type safety
