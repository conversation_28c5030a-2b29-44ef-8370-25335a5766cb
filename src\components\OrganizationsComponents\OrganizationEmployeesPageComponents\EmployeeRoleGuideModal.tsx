import { X } from 'lucide-react';

function EmployeeRoleGuideModal({ handleClose }: { handleClose: () => void }) {
  const roles = [
    {
      title: 'MANAGER',
      description: 'Full system access and control',
      permissions: [
        'Manage all employees and roles',
        'Access all reports and analytics',
        'Manage inventory and stock',
        'Handle financial transactions',
        'Configure system settings',
        'View and manage all orders',
      ],
    },
    {
      title: 'SHOP BILLER',
      description: 'Sales and billing operations',
      permissions: [
        'Process sales transactions',
        'Handle customer billing',
        'Generate sales reports',
        'Manage customer accounts',
        'Process returns and refunds',
        'View inventory levels',
      ],
    },
    {
      title: 'STOCK MANAGER',
      description: 'Inventory management specialist',
      permissions: [
        'Update stock levels',
        'Process stock entries',
        'Generate stock reports',
        'Manage product locations',
        'Handle stock transfers',
        'Monitor low stock items',
      ],
    },
    {
      title: 'MODERATOR',
      description: 'Supervisory and oversight capabilities',
      permissions: [
        'Monitor Moderator Sales',
        'Access basic reports',
        'Supervise daily operations',
      ],
    },
  ];

  return (
    <div className="max-h-[90vh] w-[90vw] max-w-6xl overflow-y-auto rounded-lg bg-white">
      {/* Header */}
      <div className="sticky top-0 flex items-center justify-between border-b bg-white p-4">
        <h2 className="text-2xl font-semibold text-gray-800">
          Employee Roles & Permissions Guide
        </h2>
        <button
          type="button"
          onClick={handleClose}
          className="rounded-full p-2 transition-colors hover:bg-gray-100"
        >
          <X className="h-5 w-5 text-gray-500" />
        </button>
      </div>

      {/* Content */}
      <div className="grid grid-cols-1 gap-6 p-6 md:grid-cols-2">
        {roles.map((role) => (
          <div
            key={role.title}
            className="rounded-xl border border-gray-200 bg-white shadow-sm transition-shadow hover:shadow-md"
          >
            <div className="p-6">
              <div className="mb-4 flex items-center justify-between">
                <h3 className="text-xl font-semibold text-gray-800">
                  {role.title}
                </h3>
                <span className="rounded-full bg-blue-50 px-3 py-1 text-sm font-medium text-blue-600">
                  {role.permissions.length} Permissions
                </span>
              </div>
              <p className="mb-4 text-gray-600">{role.description}</p>
              <div className="space-y-3">
                {role.permissions.map((permission) => (
                  <div
                    key={`${role.title}-${permission}`}
                    className="ml-2 flex items-center"
                  >
                    <div className="mt-1 flex-shrink-0">
                      <div className="h-2 w-2 rounded-full bg-green-500" />
                    </div>
                    <p className="ml-3 text-gray-700">{permission}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

export default EmployeeRoleGuideModal;
