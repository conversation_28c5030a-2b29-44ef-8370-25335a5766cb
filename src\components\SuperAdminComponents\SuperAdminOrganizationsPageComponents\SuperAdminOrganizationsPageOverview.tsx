import { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import CreateOrEditOrganizationModal from './CreateOrEditOrganizationModal';
import SuperAdminOrganizationsPageFilterModal from './SuperAdminOrganizationsPageFilterModal';

import {
  DeleteButton,
  EditButton,
  EyeButton,
} from '@/components/reusable/Buttons/CommonButtons';
import FilledButton from '@/components/reusable/Buttons/FilledButton';
import FilterButton from '@/components/reusable/Buttons/FilterButton';
import SearchInput from '@/components/reusable/Inputs/SearchInput';
import Modal from '@/components/reusable/Modal/Modal';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import Pagination from '@/components/reusable/Pagination/Pagination';
import SpinnerLoader from '@/components/reusable/SpinnerLoader/SpinnerLoader';
import { useGetOrganizationsQuery } from '@/redux/api/superAdminOrganizationsApi';
import { ROUTES } from '@/Routes';
import { SuperAdminSingleOrganization } from '@/types/superAdminOrganizationsTypes';

function SuperAdminOrganizationsPageOverview() {
  const navigate = useNavigate();
  const location = useLocation();
  const router = new URLSearchParams(location.search);
  const name = router.get('name');
  const page = router.get('page');
  const limit = router.get('limit');
  const [isCreateOrganizationModalOpen, setIsCreateOrganizationModalOpen] =
    useState<boolean>(false);
  const { data, isLoading, isFetching, refetch } = useGetOrganizationsQuery({
    name: name ?? null,
    page: page ?? '1',
    limit: limit ?? '10',
  });
  const [
    isOrganizationShopLimitModalOpen,
    setIsOrganizationShopLimitModalOpen,
  ] = useState<boolean>(false);
  const [selectedOrganizationDetails, setSelectedOrganizationDetails] =
    useState<SuperAdminSingleOrganization>();

  const handleFilter = (value: string) => {
    router.set('name', value);
    navigate(`${location.pathname}?${router.toString()}`);
  };

  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-1">
        <div className="flex items-center gap-x-2">
          <div className="search-title-and-btn flex items-center gap-x-3">
            <div className="relative">
              <div className="block xl:hidden">
                <FilterButton handleClick={() => console.log('higbig')} />
              </div>
              <div className="block xl:hidden">
                <SuperAdminOrganizationsPageFilterModal />
              </div>
            </div>
          </div>
          <div className="hidden xl:block">
            <div className="flex items-center gap-x-2">
              <SearchInput
                placeholder="Search by Name"
                handleSubmit={(value: string) => handleFilter(value)}
                value={name ?? ''}
              />
            </div>
          </div>
        </div>
        <div>
          <FilledButton
            isLoading={false}
            text="Add New Organization"
            handleClick={() => setIsCreateOrganizationModalOpen(true)}
            isDisabled={false}
          />
        </div>
      </div>
      {!isLoading && !isFetching ? (
        <div>
          <div className="tableTop w-full">
            <p>Organizations List</p>
            <p>Total : {data?.data?.length}</p>
          </div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {data?.data?.length ? (
              <div className="full-table-box h-custom">
                <table className="full-table">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="tableHead">No</th>

                      <th className="tableHead">Image</th>
                      <th className="tableHead">Name</th>
                      <th className="tableHead">Email</th>
                      <th className="tableHead">Phone Number</th>
                      <th className="tableHead">Org</th>
                      <th className="tableHead">Shop</th>
                      <th className="tableHead">Warehouse</th>
                      <th className="tableHead">Created At</th>
                      <th className="tableHead">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y bg-slate-200">
                    {data?.data?.map(
                      (
                        organization: SuperAdminSingleOrganization,
                        index: number,
                      ) => (
                        <tr key={organization?.id}>
                          <td className="tableData">{index + 1}</td>
                          <td className="tableData">
                            <img
                              src={
                                organization?.imgUrl
                                  ? `https://retail-pluse-upload.s3.ap-southeast-1.amazonaws.com/${organization.imgUrl}`
                                  : 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQMXYd5y7rsgpHBZgJYq-mpg_E-eNPJYvxbpw&s'
                              }
                              alt=""
                              className="h-[50px] w-[50px]"
                            />
                          </td>
                          <td className="tableData">{organization?.name}</td>
                          <td className="tableData">{organization?.email}</td>
                          <td className="tableData">
                            {organization?.mobileNumber}
                          </td>
                          <td className="tableData">1/1</td>
                          <td className="tableData">
                            {organization?.totalShops}/{organization?.shopLimit}
                          </td>
                          <td className="tableData">
                            {organization?.totalWarehouses}/
                            {organization?.warehouseLimit}
                          </td>

                          <td className="tableData">
                            {new Date(
                              organization.createdAt,
                            ).toLocaleDateString()}
                          </td>
                          <td className="tableData">
                            <div className="flex items-center justify-center gap-2">
                              <EditButton
                                handleClick={() => {
                                  setSelectedOrganizationDetails(organization);
                                  setIsOrganizationShopLimitModalOpen(true);
                                }}
                              />
                              <DeleteButton
                                handleClick={() => console.log('ll')}
                              />
                              <EyeButton
                                handleClick={() =>
                                  navigate(
                                    ROUTES.SUPER_ADMIN.ORGANIZATION_DETAILS(
                                      organization.id,
                                    ),
                                  )
                                }
                              />
                            </div>
                          </td>
                        </tr>
                      ),
                    )}
                  </tbody>
                </table>
              </div>
            ) : (
              <NoResultFound pageType="organizations" />
            )}
          </div>
          <div className="pagination-box flex justify-end rounded bg-white p-3">
            <Pagination
              currentPage={page ?? '1'}
              limit={Number(10)}
              totalCount={data?.pagination?.total}
              totalPages={Math.ceil(
                Number(data?.pagination?.total) /
                  Number(data?.pagination?.limit),
              )}
            />
          </div>
        </div>
      ) : (
        <SpinnerLoader />
      )}
      <Modal
        setShowModal={setIsCreateOrganizationModalOpen}
        showModal={isCreateOrganizationModalOpen}
      >
        <CreateOrEditOrganizationModal
          type="new"
          handleClose={() => setIsCreateOrganizationModalOpen(false)}
          updateRefreshCounter={() => refetch()}
        />
      </Modal>
      <Modal
        setShowModal={setIsOrganizationShopLimitModalOpen}
        showModal={isOrganizationShopLimitModalOpen}
      >
        <CreateOrEditOrganizationModal
          type="edit"
          handleClose={() => setIsOrganizationShopLimitModalOpen(false)}
          updateRefreshCounter={() => refetch()}
          organizationDetails={selectedOrganizationDetails}
        />
      </Modal>
    </div>
  );
}

export default SuperAdminOrganizationsPageOverview;
