import { useLocation } from 'react-router-dom';

import SearchInput from '@/components/reusable/Inputs/SearchInput';

interface Props {
  handleFilter: (fieldName: string, value: string) => void;
}

function SuppliersFilterOptions({ handleFilter }: Props) {
  const router = new URLSearchParams(useLocation().search);
  const name = router.get('name');
  const mobileNumber = router.get('mobileNumber');

  return (
    <div className="flex flex-col gap-3 md:flex-row md:gap-4">
      <SearchInput
        placeholder="Search by Name"
        handleSubmit={(value: string) => handleFilter('name', value)}
        value={name ?? ''}
      />
      <SearchInput
        placeholder="Search Mobile Number"
        handleSubmit={(value: string) => handleFilter('mobileNumber', value)}
        value={mobileNumber ?? ''}
      />
    </div>
  );
}

export default SuppliersFilterOptions;
