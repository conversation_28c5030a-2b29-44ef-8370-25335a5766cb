import { Font, Image, Page, Text, View } from '@react-pdf/renderer';
import JsBarcode from 'jsbarcode';
import { useEffect, useState } from 'react';

import { posInvoiceStyles } from './SingleOrderPosInvoice.style';

import {
  OrderSinglePaymentDetails,
  ShopOrderDetails,
  ShopSingleOrderItem,
} from '@/types/shopTypes/shopOrderTypes';
import { numberToWords } from '@/utils/NumberToText';

Font.register({
  family: 'Open Sans',
  fonts: [
    {
      src: 'https://cdn.jsdelivr.net/npm/open-sans-all@0.1.3/fonts/open-sans-regular.ttf',
    },
    {
      src: 'https://cdn.jsdelivr.net/npm/open-sans-all@0.1.3/fonts/open-sans-600.ttf',
      fontWeight: 800,
    },
  ],
});

interface Props {
  orderDetails?: ShopOrderDetails;
  returnPolicyText?: string;
}

function SingleOrderPosInvoice({ orderDetails, returnPolicyText }: Props) {
  const [barcode, setBarcode] = useState<string>('');

  useEffect(() => {
    if (orderDetails?.serialNo) {
      const canvas = document.createElement('canvas');
      JsBarcode(canvas, orderDetails.serialNo.toString(), {
        format: 'CODE128',
        displayValue: false,
      });
      setBarcode(canvas.toDataURL('image/png'));
    }
  }, [orderDetails]);
  return (
    <Page size={[227.76]} style={posInvoiceStyles.page}>
      <View>
        {/* <View style={posInvoiceStyles.title}>
          <Text>INVOICE</Text>
        </View> */}
        <View style={posInvoiceStyles.header}>
          {orderDetails?.Shop?.imgUrl ? (
            <View
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Image
                src={{
                  uri: `https://retail-pluse-upload.s3.ap-southeast-1.amazonaws.com/${orderDetails?.Shop?.imgUrl}`,
                  method: 'GET',
                  headers: { 'Cache-Control': 'no-cache' },
                  body: '',
                }}
                style={posInvoiceStyles.companyLogo}
                // cache={false}
              />
            </View>
          ) : (
            <Text style={posInvoiceStyles.userName}>
              {orderDetails?.Shop?.name}
            </Text>
          )}
          <Text>{orderDetails?.Shop?.address}</Text>
          <Text>{orderDetails?.Shop?.mobileNumber}</Text>
        </View>
        <View style={posInvoiceStyles.borderLine} />
        <View style={posInvoiceStyles.borderLine} />
        <View>
          <View style={posInvoiceStyles.DateTime}>
            <Text>Invoice No: {orderDetails?.serialNo}</Text>
            <Text>
              Date:{' '}
              {new Intl.DateTimeFormat('en-US', {
                day: '2-digit',
                month: 'short',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
              }).format(new Date(orderDetails?.createdAt ?? ''))}
            </Text>
          </View>
        </View>
        <View style={posInvoiceStyles.borderLine} />
        <View style={posInvoiceStyles.userDetails}>
          <View>
            <Text style={posInvoiceStyles.to}>Invoice To,</Text>
            <Text style={posInvoiceStyles.userName}>
              {orderDetails?.Customer?.name}
            </Text>
            <Text>Address: {orderDetails?.address}</Text>
            <Text>Phone: {orderDetails?.Customer?.mobileNumber}</Text>
          </View>
        </View>
        <View style={posInvoiceStyles.dashedBorderLine} />
        <View style={posInvoiceStyles.dashedBorderLine} />
        <View style={posInvoiceStyles.table}>
          <View style={posInvoiceStyles.tableRow}>
            <Text
              style={[posInvoiceStyles.tableCol, posInvoiceStyles.headerCol]}
            >
              Barcode
            </Text>
            <Text
              style={[
                posInvoiceStyles.tableCol,
                posInvoiceStyles.headerCol,
                posInvoiceStyles.tabeName,
              ]}
            >
              Product Name
            </Text>
            <Text
              style={[
                posInvoiceStyles.tableCol,
                posInvoiceStyles.headerCol,
                posInvoiceStyles.textRight,
              ]}
            >
              Price
            </Text>
          </View>

          {orderDetails?.OrderItem?.map((product: ShopSingleOrderItem) => (
            <View style={posInvoiceStyles.tableRow} key={product?.id}>
              <Text style={posInvoiceStyles.tableCol}>
                {product?.Stock?.barcode}
              </Text>
              <Text
                style={[posInvoiceStyles.tableCol, posInvoiceStyles.tabeName]}
              >
                {product?.Stock?.Product?.name}
                {product?.Stock?.Variant?.name
                  ? `-${product?.Stock?.Variant?.name}`
                  : ''}
              </Text>
              <Text
                style={[posInvoiceStyles.tableCol, posInvoiceStyles.textRight]}
              >
                {product?.Stock?.retailPrice}
              </Text>
            </View>
          ))}
        </View>
        <View style={posInvoiceStyles.borderLine} />
        <View>
          <View style={posInvoiceStyles.amount}>
            <Text style={posInvoiceStyles.leftText}>SubTotal:</Text>
            <Text style={posInvoiceStyles.rightText}>
              {Number(orderDetails?.grandTotal) +
                Number(orderDetails?.productDiscount) +
                Number(orderDetails?.adminDiscount)}
            </Text>
          </View>
          <View style={posInvoiceStyles.amount}>
            <Text style={posInvoiceStyles.leftText}>Discount:</Text>
            <Text style={posInvoiceStyles.rightText}>
              {Number(orderDetails?.productDiscount) +
                Number(orderDetails?.adminDiscount)}
            </Text>
          </View>
          <View style={posInvoiceStyles.amount}>
            <Text style={posInvoiceStyles.leftText}>Vat:</Text>
            <Text style={posInvoiceStyles.rightText}>{orderDetails?.vat}</Text>
          </View>
          <View style={posInvoiceStyles.amount}>
            <Text style={posInvoiceStyles.leftText}>Total:</Text>
            <Text style={posInvoiceStyles.rightText}>
              {Number(orderDetails?.grandTotal) +
                Number(orderDetails?.deliveryCharge)}
            </Text>
          </View>
          <View style={posInvoiceStyles.amount}>
            <Text style={posInvoiceStyles.leftText}>Paid Amount:</Text>
            <Text style={posInvoiceStyles.rightText}>
              {orderDetails?.totalPaid}
            </Text>
          </View>
          <View style={posInvoiceStyles.amount}>
            <Text style={posInvoiceStyles.leftText}>Due Amount:</Text>
            <Text style={posInvoiceStyles.rightText}>
              {orderDetails?.totalDue}
            </Text>
          </View>
        </View>
        <View style={posInvoiceStyles.borderLine} />
        <View style={posInvoiceStyles.borderLine} />
        {/* <View style={posInvoiceStyles.moneyInWords}>
          <Text>
            {numberToWords(
              Number(orderDetails?.grandTotal),
            ).toLocaleUpperCase()}
          </Text>
        </View> */}
        <view>
          <View style={posInvoiceStyles.paidDetailsWrapper}>
            <Text style={posInvoiceStyles.paidDetails}>Paid Details</Text>
          </View>
          {/*  <View
            style={{
              display: 'flex',
              flexDirection: 'row',
              gap: '10px',
              alignItems: 'flex-start',
              width: '200px',
            }}
          > */}
          {/* <Text style={posInvoiceStyles.payable}>Paid Amount:</Text> */}
          <Text
            style={{
              marginTop: 5,
              textAlign: 'center',
            }}
          >
            {numberToWords(Number(orderDetails?.totalPaid)).toLocaleUpperCase()}
          </Text>
          {/* </View> */}
          {orderDetails?.Payment?.length ? (
            <View
              style={{
                display: 'flex',
                flexDirection: 'row',
                gap: '10px',
                alignItems: 'flex-start',
              }}
            >
              <Text style={posInvoiceStyles.payable}>Payment Methods:</Text>
              <View
                style={{
                  marginTop: 5,
                }}
              >
                {orderDetails?.Payment?.map(
                  (sin: OrderSinglePaymentDetails) => (
                    <Text>
                      {sin.paymentMethod} - {sin.amount}
                    </Text>
                  ),
                )}
              </View>
            </View>
          ) : (
            ''
          )}
        </view>
        {returnPolicyText?.length ? (
          <View>
            <Text
              style={{
                textAlign: 'center',
                marginTop: '6px',
                marginBottom: '6px',
              }}
            >
              {returnPolicyText}
            </Text>
          </View>
        ) : (
          ''
        )}
        <view style={posInvoiceStyles.barcodeDesign}>
          <Image src={barcode} style={posInvoiceStyles.barcode} />
        </view>
        <Text style={posInvoiceStyles.tnxMessage}>
          Have a nice day. Thanks for your kind visit.
        </Text>
        <Text style={posInvoiceStyles.tnxMessage}>
          Software Made By SOFTS.AI.
        </Text>
        <Text style={posInvoiceStyles.tnxMessage}>Call-: 01723-714141</Text>
      </View>
    </Page>
  );
}

export default SingleOrderPosInvoice;
