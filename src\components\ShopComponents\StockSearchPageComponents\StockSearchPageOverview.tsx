import { useState } from 'react';
import { useLocation } from 'react-router-dom';

import StockSearchPageFilterModal from './StockSearchPageFilterModal';

import FilterButton from '@/components/reusable/Buttons/FilterButton';
import SearchInput from '@/components/reusable/Inputs/SearchInput';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import Pagination from '@/components/reusable/Pagination/Pagination';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import { useGetStockLocationQuery } from '@/redux/api/shopApis/shopStockApis';
import {
  Location,
  StockLocationDetails,
} from '@/types/warehouseTypes/stockTypes';

/* interface Props {
  shopId: string;
} */

function StockSearchPageOverview() {
  const [isFilterModalOpen, setIsFilterModalOpen] = useState<boolean>(false);
  const router = new URLSearchParams(useLocation().search);
  const productId = router.get('productId');
  const [productName, setProductName] = useState('');

  const { data, isLoading, isFetching } = useGetStockLocationQuery(
    {
      productName: productName ?? null,
      productId: productId ?? null,
    },
    {
      skip: Boolean(!(productName || productId)),
    },
  );

  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-1">
        <div className="flex items-center">
          <div className="search-title-and-btn flex items-center">
            <div className="relative">
              <div className="block xl:hidden">
                <FilterButton
                  handleClick={() => setIsFilterModalOpen(!isFilterModalOpen)}
                />
              </div>
              <div
                className={`${isFilterModalOpen ? 'block' : 'hidden'} xl:hidden`}
              >
                <StockSearchPageFilterModal />
              </div>
            </div>
          </div>
          <div className="hidden xl:block">
            <div className="flex items-center gap-x-2">
              <SearchInput
                placeholder="Search by Name"
                handleSubmit={(value: string) => setProductName(value)}
              />
              <SearchInput
                placeholder="Search by Brand"
                handleSubmit={(value: string) => console.log(value)}
              />
            </div>
          </div>
        </div>
      </div>
      <div>
        {!isLoading && !isFetching ? (
          <div>
            <div className="tableTop w-full">
              <p>Stock Location</p>
              <p>Total : {data?.pagination?.total ?? 0}</p>
            </div>
            <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
              {data?.data?.length ? (
                <div className="full-table-box h-custom">
                  <table className="full-table">
                    <thead className="bg-gray-100">
                      <tr>
                        <th className="tableHead">No</th>
                        <th className="tableHead table-col-width">
                          Product Name
                        </th>
                        <th className="tableHeadLeftAlign">Shop Name</th>
                        <th className="tableHead">Available Quantity</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y bg-slate-200">
                      {data?.data?.map(
                        (stock: StockLocationDetails, index: number) => (
                          <>
                            {stock?.location?.map(
                              (singleLocation: Location, locIndex: number) => (
                                <tr key={singleLocation?.shopName}>
                                  {locIndex === 0 && (
                                    <>
                                      <td
                                        className="tableData"
                                        rowSpan={stock.location.length}
                                      >
                                        {index + 1}
                                      </td>
                                      <td
                                        className="tableData table-col-width"
                                        rowSpan={stock.location.length}
                                      >
                                        {stock.name}
                                      </td>
                                    </>
                                  )}
                                  <td className="tableDataLeftAlign">
                                    {singleLocation?.shopName}(
                                    {singleLocation.nickname})
                                  </td>
                                  <td className="tableData">
                                    {singleLocation?.quantity}
                                  </td>
                                </tr>
                              ),
                            )}
                          </>
                        ),
                      )}
                    </tbody>
                  </table>
                </div>
              ) : (
                <NoResultFound pageType="stock" />
              )}
            </div>
            <div className="pagination-box flex justify-end rounded bg-white p-3">
              <Pagination
                currentPage="1"
                limit={Number(10)}
                handleFilter={(fieldName: string, value: any) =>
                  // handleFilter(fieldName, value)
                  console.log(fieldName, value)
                }
                totalCount={data?.pagination?.total}
                totalPages={Math.ceil(
                  Number(data?.pagination?.total) /
                    Number(data?.pagination?.limit),
                )}
              />
            </div>
          </div>
        ) : (
          <TableSkeletonLoader tableColumn={4} tableRow={5} />
        )}
      </div>
    </div>
  );
}

export default StockSearchPageOverview;
