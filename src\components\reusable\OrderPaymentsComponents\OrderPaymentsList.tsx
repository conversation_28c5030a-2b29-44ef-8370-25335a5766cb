import { PencilLine } from 'lucide-react';
import { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import FilterButton from '../Buttons/FilterButton';
import DateAndTimeViewer from '../DateAndTimeViewer/DateAndTimeViewer';
import CustomSelectForFilter from '../Inputs/CustomSelectForFilter';
import SearchInput from '../Inputs/SearchInput';
import Modal from '../Modal/Modal';
import NoResultFound from '../NoResultFound/NoResultFound';
import Pagination from '../Pagination/Pagination';
import TableSkeletonLoader from '../SkeletonLoader/TableSkeletonLoader';

import EditPaymentMethodModal from './EditPaymentMethodModal';

import {
  SinglePaymentDetails,
  useGetShopPaymentsQuery,
} from '@/redux/api/shopApis/shopPaymentsApis';
import { useAppSelector } from '@/redux/hooks';
import { ROUTES } from '@/Routes';
import { generateFilterParams } from '@/utils/generateFilterParams';

interface Props {
  shopId?: string;
  warehouseId?: string;
  pageType?: string;
}

function OrderPaymentsList({ shopId, pageType, warehouseId }: Props) {
  const navigate = useNavigate();
  const router = new URLSearchParams(useLocation().search);
  const shopSettings = useAppSelector((state) => state.shopSettings);
  const serialNo = router.get('serialNo');
  const page = router.get('page');
  const from = router.get('from');
  const limit = router.get('limit');
  const [isFilterModalOpen, setIsFilterModalOpen] = useState<boolean>(false);
  const [
    isUpdateOrderPaymentMethodModalOpe,
    setIsUpdateOrderPaymentMethodModalOpe,
  ] = useState(false);
  const [selectedPayment, setSelectedPayment] =
    useState<SinglePaymentDetails>();

  const { data, isLoading, isFetching } = useGetShopPaymentsQuery(
    {
      shopId: shopId ?? null,
      warehouseId: pageType === 'warehouse' ? warehouseId : null,
      page: page ?? '1',
      limit: limit ?? '10',
      from: from ?? undefined,
      serialNo: serialNo ?? undefined,
    },
    { skip: !shopId || !warehouseId },
  );

  const handleFilter = (fieldName: string, value: string) => {
    const query = generateFilterParams(fieldName, value);

    navigate(ROUTES.SHOP.ORDER_PAYMENTS(shopId, query));
  };

  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 lg:py-1">
        <div className="flex items-center">
          <div className="search-title-and-btn flex items-center">
            <div className="relative">
              <div className="block lg:hidden">
                <FilterButton
                  handleClick={() => setIsFilterModalOpen(!isFilterModalOpen)}
                />
              </div>
              {/* <div
                className={`${isFilterModalOpen ? 'block' : 'hidden'} xl:hidden`}
              >
                <BrandPageFilterModal />
              </div> */}
            </div>
          </div>
          <div className="hidden lg:block">
            <div className="flex items-center gap-x-2">
              <SearchInput
                placeholder="Search by Order id"
                handleSubmit={(value: string) =>
                  handleFilter('serialNo', value)
                }
                value={serialNo ?? ''}
              />
              <CustomSelectForFilter
                options={[
                  { value: 'SelfCollect', label: 'Customer' },
                  { value: 'Pathao', label: 'Pathao' },
                ]}
                selectedValue={from ?? ''}
                handleSelect={(e) => handleFilter('from', e)}
                placeHolder="Payment From"
              />
            </div>
          </div>
        </div>
      </div>
      {!isLoading && !isFetching ? (
        <div>
          <div className="tableTop w-full">
            <p>Payments List</p>
            <p>Total : {data?.pagination?.total}</p>
          </div>
          <div className="full-table-container w-full md:w-custom">
            {data?.data.length ? (
              <div className="full-table-box h-custom">
                <table className="full-table">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="tableHead">No</th>
                      <th className="tableHead">Order Id</th>
                      <th className="tableHead">Paid Amount</th>
                      <th className="tableHead">Payment Method</th>
                      <th className="tableHead">Collected From</th>
                      <th className="tableHead">Created At</th>
                      {/* <th className="tableHead">Updated At</th> */}
                      <th className="tableHead">Action</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y bg-slate-200">
                    {data?.data?.map(
                      (brand: SinglePaymentDetails, index: number) => (
                        <tr key={brand?.id}>
                          <td className="tableData">{index + 1}</td>
                          <td className="tableData">
                            {brand?.Order?.serialNo}
                          </td>
                          <td className="tableData">{brand?.amount}</td>
                          <td className="tableData">{brand?.paymentMethod}</td>
                          <td className="tableData">
                            {brand?.from === 'SelfCollect'
                              ? 'Customer'
                              : brand?.from ?? ''}
                          </td>
                          <td className="tableData">
                            <DateAndTimeViewer date={brand.createdAt} />
                          </td>
                          {/* <td className="tableData">
                            <DateAndTimeViewer date={brand.updatedAt} />
                          </td> */}
                          <td className="tableData">
                            <button
                              type="button"
                              onClick={() => {
                                setIsUpdateOrderPaymentMethodModalOpe(true);
                                setSelectedPayment(brand);
                              }}
                              className="commonActionButton watchButton disabled:cursor-not-allowed disabled:opacity-50"
                              disabled={
                                !shopSettings?.maximumPaymentMethodEditPolicy || // Ensure policy exists
                                !brand?.createdAt || // Ensure createdAt exists
                                new Date().getTime() -
                                  new Date(brand.createdAt).getTime() >
                                  shopSettings.maximumPaymentMethodEditPolicy *
                                    24 *
                                    60 *
                                    60 *
                                    1000 // Check time difference
                              }
                            >
                              <PencilLine
                                size={16}
                                className="watchButtonIcon"
                              />
                            </button>
                          </td>
                        </tr>
                      ),
                    )}
                  </tbody>
                </table>
              </div>
            ) : (
              <NoResultFound pageType="payments" />
            )}
          </div>
          <div className="pagination-box flex justify-end rounded bg-white p-3">
            <Pagination
              currentPage={page ?? '1'}
              limit={Number(limit ?? 10)}
              handleFilter={(fieldName: string, value: any) =>
                handleFilter(fieldName, value)
              }
              totalCount={data?.pagination?.total}
              totalPages={Math.ceil(
                Number(data?.pagination?.total) /
                  Number(data?.pagination?.limit),
              )}
            />
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={4} tableRow={6} />
      )}
      <Modal
        showModal={isUpdateOrderPaymentMethodModalOpe}
        setShowModal={setIsUpdateOrderPaymentMethodModalOpe}
      >
        <EditPaymentMethodModal
          paymentDetails={selectedPayment}
          handleClose={() => setIsUpdateOrderPaymentMethodModalOpe(false)}
        />
      </Modal>
    </div>
  );
}

export default OrderPaymentsList;
