import BaseApi from '../baseApi';

import { TagTypes } from '@/redux/tag-types';
import { GetShopExpensesResponse } from '@/types/shopTypes/shopExpensesTypes';

interface GetShopExpensesParams {
  shopId?: string;
  warehouseId?: string;
}

const ShopExpensesApi = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getShopExpenses: builder.query<
      GetShopExpensesResponse,
      GetShopExpensesParams
    >({
      query: (params) => ({
        url: '/expense',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP_EXPENSES],
    }),
    addShopExpense: builder.mutation({
      query: (data) => ({
        url: '/expense/new',
        method: 'POST',
        data,
      }),
      invalidatesTags: [TagTypes.SHOP_EXPENSES],
    }),
  }),
});

export const { useGetShopExpensesQuery, useAddShopExpenseMutation } =
  ShopExpensesApi;
