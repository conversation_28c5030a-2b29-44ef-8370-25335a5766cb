interface TotalCalculationParams {
  productPrice: number;
  deliveryCharge: number;
  productDiscount: number;
  adminDiscount: number;
}
export const OrderTotalPriceCalculator = ({
  productPrice,
  deliveryCharge,
  productDiscount,
  adminDiscount,
}: TotalCalculationParams) => {
  let total = 0;
  total +=
    Number(productPrice) +
    Number(deliveryCharge) -
    Number(productDiscount) -
    Number(adminDiscount);
  return total;
};

interface DueCalculationParams {
  productPrice: number;
  deliveryCharge: number;
  productDiscount: number;
  adminDiscount: number;
  paidAmount: number;
}
export const OrderTotalDueCalculator = ({
  productPrice,
  deliveryCharge,
  productDiscount,
  adminDiscount,
  paidAmount,
}: DueCalculationParams) => {
  let total = 0;
  total +=
    Number(productPrice) +
    Number(deliveryCharge) -
    Number(productDiscount) -
    Number(adminDiscount) -
    Number(paidAmount);
  return total;
};
