interface Props {
  type: string;
  name: string;
  handleClose: () => void;
  handleDelete: () => void;
}
function DeleteModal({ type, name, handleClose, handleDelete }: Props) {
  return (
    <div className="w-[350px] rounded-xl bg-white p-4 md:w-[500px]">
      <div className="flex flex-col gap-4">
        <span className="text-center text-2xl font-bold">Confirm Delete</span>
        <span>
          Are you sure you want to delete {type} named with {name}
        </span>
        <div className="flex items-center justify-center gap-4">
          <button
            type="button"
            onClick={handleDelete}
            className="w-full rounded-lg bg-[#28243D] px-8 py-2 font-semibold text-white"
          >
            Yes, Delete
          </button>
          <button
            type="button"
            onClick={handleClose}
            className="w-full rounded-lg border-2 border-[#28243D] px-8 py-2 font-semibold text-[#28243D]"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
}

export default DeleteModal;
