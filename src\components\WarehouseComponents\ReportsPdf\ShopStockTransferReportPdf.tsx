import { Page, Text, View } from '@react-pdf/renderer';

import PrintedTime from './PrintedTime';
import { reportPdfStyles } from './ReportPdfStyles';
import SoftwareMarketingOnPdf from './SoftwareMarketingOnPdf';

import { SingleShopDetails } from '@/types/shopTypes';
import {
  ShopStockTransferDetails,
  TransferredProduct,
} from '@/types/shopTypes/shopStockTransferReportTypes';

const products = [
  {
    no: 1,
    created: 'Sep 25, 2024, 12:25 AM',
    name: 'Cosrx Advanced Snail 96 Mucin Power Essence 100ML',
    qty: 12,
    buyingPrice: 12232,
    retailPrice: 12212,
  },
];

interface Props {
  stockTransferList?: ShopStockTransferDetails[];
  shop?: SingleShopDetails;
}

function ShopStockTransferReportPdf({ stockTransferList, shop }: Props) {
  return (
    <Page size="A4" style={reportPdfStyles.page}>
      <View>
        <View style={reportPdfStyles.header}>
          <Text style={reportPdfStyles.userName}>{shop?.name}</Text>
          <Text style={reportPdfStyles.address}>{shop?.address}</Text>
          <Text style={reportPdfStyles.phone}>{shop?.mobileNumber}</Text>
        </View>
        <PrintedTime />

        <View style={reportPdfStyles.table}>
          <View style={reportPdfStyles.tableRow}>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.shopStockTransferReportCol,
              ]}
            >
              No
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.shopStockTransferReportCol,
                reportPdfStyles.shopStockTransferReportDateCol,
              ]}
            >
              Date
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.shopStockTransferReportCol,
                reportPdfStyles.shopStockTransferReportNameCol,
              ]}
            >
              Product Name
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.shopStockTransferReportCol,
              ]}
            >
              Quantity
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.shopStockTransferReportCol,
              ]}
            >
              Buying Price
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.tableColLast,
                reportPdfStyles.shopStockTransferReportCol,
              ]}
            >
              Retail Price
            </Text>
          </View>
          {stockTransferList?.map((product) => (
            <>
              {product?.products?.map(
                (singleItem: TransferredProduct, index) => (
                  <View
                    key={singleItem.id}
                    style={
                      index === products.length - 1
                        ? [reportPdfStyles.tableRowLast]
                        : [reportPdfStyles.tableRow]
                    }
                  >
                    <Text
                      style={[
                        reportPdfStyles.tableCol,
                        reportPdfStyles.shopStockTransferReportCol,
                      ]}
                    >
                      {0}
                    </Text>
                    <Text
                      style={[
                        reportPdfStyles.tableCol,
                        reportPdfStyles.shopStockTransferReportCol,
                        reportPdfStyles.shopStockTransferReportDateCol,
                      ]}
                    >
                      {new Intl.DateTimeFormat('en-US', {
                        day: '2-digit',
                        month: 'short',
                        year: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit',
                      }).format(new Date(product?.date))}
                    </Text>
                    <Text
                      style={[
                        reportPdfStyles.tableCol,
                        reportPdfStyles.shopStockTransferReportCol,
                        reportPdfStyles.shopStockTransferReportNameCol,
                      ]}
                    >
                      {singleItem.name}
                    </Text>
                    <Text
                      style={[
                        reportPdfStyles.tableCol,
                        reportPdfStyles.shopStockTransferReportCol,
                      ]}
                    >
                      {singleItem.total}
                    </Text>
                    <Text
                      style={[
                        reportPdfStyles.tableCol,
                        reportPdfStyles.shopStockTransferReportCol,
                      ]}
                    >
                      {singleItem.totalPurchasePrice}
                    </Text>
                    <Text
                      style={[
                        reportPdfStyles.tableCol,
                        reportPdfStyles.shopStockTransferReportCol,
                        reportPdfStyles.tableColLast,
                      ]}
                    >
                      {singleItem.totalRetailPrice}
                    </Text>
                  </View>
                ),
              )}
            </>
          ))}
        </View>
        <SoftwareMarketingOnPdf />
      </View>
    </Page>
  );
}

export default ShopStockTransferReportPdf;
