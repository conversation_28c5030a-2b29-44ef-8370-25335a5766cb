import { useFormik } from 'formik';
import { toast } from 'react-toastify';
import * as Yup from 'yup';

import FilledSubmitButton from '@/components/reusable/Buttons/FilledSubmitButton';
import CustomDropdown from '@/components/reusable/CustomInputField/CustomDropdown';
import ModalTitle from '@/components/reusable/Modal/ModalTitle';
import { useTransferStockMutation } from '@/redux/api/warehouseApis/stockApis';
import { SingleShopDetails } from '@/types/shopTypes';

interface Props {
  shopList: SingleShopDetails[];
  selectedStocks?: string[];
  handleClose: () => void;
  updateRefreshCounter: () => void;
  warehouseId: string;
}

const formikInitialValues = {
  shopName: '',
};

const validation = Yup.object({
  shopName: Yup.string().required('Shop name is required'),
});

function StockTransferModal({
  shopList,
  selectedStocks,
  handleClose,
  updateRefreshCounter,
  warehouseId,
}: Props) {
  const [transferStock, { isLoading }] = useTransferStockMutation();
  const formik = useFormik({
    initialValues: formikInitialValues,
    validationSchema: validation,

    onSubmit: async (values) => {
      toast.promise(
        transferStock({
          stockIds: selectedStocks,
          shopId: values.shopName,
          warehouseId,
        }).unwrap(),
        {
          pending: 'Stock Transferring...',
          success: {
            render({ data: res }) {
              if (res?.statusCode === 200 || res?.statusCode === 201) {
                updateRefreshCounter();
                handleClose();
              }
              return 'Stock Transferred Successfully';
            },
          },
          error: {
            render({ data: error }) {
              console.log(error);
              return 'Error on transfer stock';
            },
          },
        },
      );
    },
  });
  return (
    <div className="flex w-[400px] flex-col gap-4 rounded-xl bg-white p-4">
      <ModalTitle text="Transfer Stock Modal" handleClose={handleClose} />
      <form onSubmit={formik.handleSubmit} className="w-full">
        <CustomDropdown
          placeholder="Select Shop"
          name="shopName"
          label="Shop Name"
          formik={formik}
          options={
            shopList
              ? shopList.map((sin: SingleShopDetails) => {
                  return {
                    value: sin.id,
                    label: `${sin.name} - ${sin?.nickName}`,
                  };
                })
              : []
          }
        />
        <div className="mt-[10px] flex w-full items-center justify-center">
          <FilledSubmitButton text="Transfer Stock" isLoading={isLoading} />
        </div>
      </form>
    </div>
  );
}

export default StockTransferModal;
