import Cookies from 'js-cookie';
import { Check, Truck, X } from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import Swal from 'sweetalert2';

import ShopCourierBookingModal from '../ShopCourierBooking/ShopCourierBookingModal';

import { CourierButton } from '@/components/reusable/Buttons/CommonButtons';
import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import Modal from '@/components/reusable/Modal/Modal';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import OrderStatusViewer from '@/components/reusable/OrdersPagesReusableComponents/OrderStatusViewer';
import { useBookBulkOrderOnPathaoMutation } from '@/redux/api/shopApis/shopOrdersApis';
import {
  ShopOrderDetails,
  ShopSingleOrderItem,
} from '@/types/shopTypes/shopOrderTypes';
import { SingleCourierDetails } from '@/types/warehouseTypes/settingsTypes';

interface Props {
  courierDetails?: SingleCourierDetails;
  orders?: ShopOrderDetails[];
  total?: number;
  shopId: string;
}

function OrderListForPathaoBulkEntry({
  orders,
  total,
  courierDetails,
  shopId,
}: Props) {
  const [selectedOrderList, setSelectedOrderList] =
    useState<ShopOrderDetails[]>();
  const [bookAbleOrders, setBookAbleOrders] = useState<ShopOrderDetails[]>();
  const [isCourierBookingModalOpen, setIsCourierBookingModalOpen] =
    useState<boolean>(false);
  const [courierBookingOrderDetails, setCourierBookingOrderDetails] =
    useState<ShopOrderDetails>();
  const [bookBulkOrderOnPathao, { isLoading: isBulkOrderBooking }] =
    useBookBulkOrderOnPathaoMutation();
  console.log(isBulkOrderBooking);

  const handleSelectOrder = (order: ShopOrderDetails) => {
    const isExist = selectedOrderList?.some(
      (sin: ShopOrderDetails) => sin.id === order.id,
    );
    if (!isExist) {
      setSelectedOrderList([...(selectedOrderList ?? []), order]);
    } else {
      setSelectedOrderList(
        selectedOrderList?.filter(
          (sin: ShopOrderDetails) => sin.id !== order.id,
        ),
      );
    }
  };

  const handleSelectOrUnselectAll = () => {
    if (selectedOrderList?.length === bookAbleOrders?.length) {
      setSelectedOrderList([]);
    } else {
      setSelectedOrderList(bookAbleOrders);
    }
  };

  useEffect(() => {
    if (orders?.length) {
      const bookAble = orders?.filter(
        (order) => order.recipientZone && order.recipientCity,
      );
      setBookAbleOrders(bookAble);
    }
  }, [orders]);

  const handleSubmitBooking = async () => {
    if (!selectedOrderList || selectedOrderList.length === 0) {
      toast.error('No orders selected.');
      return;
    }

    const bookData = selectedOrderList.map((order: ShopOrderDetails) => {
      let description = '';
      order.OrderItem?.map((single: ShopSingleOrderItem, index: number) => {
        description += `${index + 1} ) ${single.Stock?.barcode}-${single.Stock?.Product?.name} - ${single.Stock?.retailPrice} \n`;
        return 0;
      });
      return {
        store_id: courierDetails?.storeId ?? '',
        merchant_order_id: order.serialNo,
        recipient_name: order.customerName ?? order.Customer?.name ?? '',
        recipient_phone: order.customerMobileNumber ?? '',
        recipient_address: order.address || '',
        recipient_city: Number(order?.recipientCity),
        recipient_zone: Number(order?.recipientZone),
        recipient_area: Number(order.recipientArea) || null,
        delivery_type: 48,
        item_type: 2,
        special_instruction: order.orderNotes[0]?.notes ?? order.note ?? '',
        item_quantity: order.OrderItem.length,
        item_weight: '0.5',
        amount_to_collect: order.totalDue,
        item_description: description ?? '',
      };
    });
    Swal.fire({
      title: `Are you sure? you want to entry ${bookData?.length} orders to Pathao`,
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: `Yes, Book ${bookData?.length} order`,
    }).then((result) => {
      if (result.isConfirmed) {
        toast.promise(
          bookBulkOrderOnPathao({
            shopId,
            courierId: courierDetails?.id,
            pathaoToken: Cookies.get('pathaoAccessToken'),
            orders: bookData,
          }).unwrap(),
          {
            pending: 'Bulk Order Booking on Courier...',
            success: {
              render({ data: res }) {
                if (res?.statusCode === 200 || res?.statusCode === 201) {
                  console.log('success');
                }
                return 'Order booked Successfully';
              },
            },
            error: {
              render({ data: error }) {
                console.log(error);
                return 'Error on booking order';
              },
            },
          },
        );
      }
    });
  };

  return (
    <div>
      <div className="tableTop w-full">
        <p>Pending Order List</p>
        <div className="flex items-center gap-4">
          {selectedOrderList?.length ? (
            <button
              type="button"
              onClick={() => handleSubmitBooking()}
              // className="flex items-center gap-1 rounded border-2 border-slate-200 px-2 py-1 text-xs font-bold text-white hidden"
              className="hidden items-center gap-1 rounded border-2 border-slate-200 px-2 py-1 text-xs font-bold text-white"
              // disabled={isBulkOrderBooking}
              disabled
            >
              <Truck size={16} />
              <span>Entry</span>
              <span>{selectedOrderList?.length}</span>
            </button>
          ) : (
            ''
          )}
          <p className="blink-animation">Asbe asbe 🙋‍♂️</p>
          <p>Total : {total}</p>
        </div>
      </div>
      <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
        {orders?.length ? (
          <div className="full-table-box h-orderTable">
            <table className="full-table">
              <thead className="bg-gray-100">
                <tr>
                  <th className="tableHead">
                    <input
                      type="checkbox"
                      onClick={handleSelectOrUnselectAll}
                      onChange={() => {}}
                      checked={
                        selectedOrderList?.length === bookAbleOrders?.length
                      }
                    />
                  </th>
                  <th className="tableHead">#</th>
                  <th className="tableHead">Order No</th>

                  <th className="tableHead table-col-width">Name</th>
                  <th className="tableHead">Phone</th>
                  <th className="tableHead">Total</th>
                  <th className="tableHead">Paid</th>
                  <th className="tableHead">Due</th>
                  <th className="tableHead">Biller</th>
                  <th className="tableHead">Status</th>
                  <th className="tableHead">City</th>
                  <th className="tableHead">Zone</th>
                  <th className="tableHead">Area</th>
                  <th className="tableHead">Created At</th>
                  <th className="tableHead">Is Ready</th>
                  <th className="tableHead">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y bg-slate-200">
                {orders?.map((order: ShopOrderDetails, index: number) => {
                  const isReadyForBooking =
                    order.recipientZone &&
                    order.recipientCity &&
                    order.bulkBookingStatus === 'NOT_SENT';
                  return (
                    <tr
                      key={order?.id}
                      className={
                        isReadyForBooking ? 'tableRowGreen' : 'tableRowRed'
                      }
                    >
                      <td className="tableData">
                        <input
                          type="checkbox"
                          onClick={() => handleSelectOrder(order)}
                          onChange={() => {}}
                          checked={selectedOrderList?.some(
                            (ord: ShopOrderDetails) => ord.id === order.id,
                          )}
                          disabled={!isReadyForBooking}
                        />
                      </td>
                      <td className="tableData">{index + 1}</td>
                      <td className="tableData">{order?.serialNo}</td>
                      <td className="tableData table-col-width">
                        {order?.customerName ?? order?.Customer.name}
                      </td>
                      <td className="tableData">
                        {order?.customerMobileNumber ??
                          order?.Customer.mobileNumber}
                      </td>
                      <td className="tableData">
                        {Number(order?.grandTotal) +
                          Number(order?.deliveryCharge)}
                      </td>
                      <td className="tableData">{order?.totalPaid}</td>
                      <td className="tableData">{order?.totalDue}</td>
                      <td className="tableData">
                        {order?.CreatedBy?.name ?? '--'}
                      </td>
                      <td className="tableData">
                        <div>
                          <OrderStatusViewer status={order.orderStatus} />
                        </div>
                      </td>
                      <td className="tableData">{order.recipientCity}</td>
                      <td className="tableData">{order.recipientZone}</td>
                      <td className="tableData">{order.recipientArea}</td>
                      <td className="tableData">
                        <DateAndTimeViewer date={order?.createdAt} />
                      </td>
                      <td className="tableData">
                        <div className="flex items-center justify-center">
                          {isReadyForBooking ? (
                            <Check size={20} />
                          ) : (
                            <X size={20} />
                          )}
                        </div>
                      </td>
                      <td className="tableData">
                        <div className="flex items-center justify-center">
                          <CourierButton
                            handleClick={() => {
                              setCourierBookingOrderDetails(order);
                              setIsCourierBookingModalOpen(true);
                            }}
                          />
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        ) : (
          <NoResultFound pageType="order" />
        )}
      </div>
      <Modal
        showModal={isCourierBookingModalOpen}
        setShowModal={setIsCourierBookingModalOpen}
      >
        <ShopCourierBookingModal
          orderDetails={courierBookingOrderDetails}
          handleClose={() => setIsCourierBookingModalOpen(false)}
        />
      </Modal>
    </div>
  );
}

export default OrderListForPathaoBulkEntry;
