import BaseApi from '../baseApi';

import { TagTypes } from '@/redux/tag-types';
import { GetCategoriesResponse } from '@/types/warehouseTypes/categoriesTypes';

interface GetShopCategoriesParams {
  organizationId: string;
  shopId?: string;
  warehouseId?: string;
  name?: string;
  page?: string;
  limit?: string;
  purpose: string;
}

const ShopCategoriesApi = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getShopCategories: builder.query<
      GetCategoriesResponse,
      GetShopCategoriesParams
    >({
      query: (params) => ({
        url: '/category',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP_CATEGORIES],
    }),
  }),
});

export const { useGetShopCategoriesQuery } = ShopCategoriesApi;
