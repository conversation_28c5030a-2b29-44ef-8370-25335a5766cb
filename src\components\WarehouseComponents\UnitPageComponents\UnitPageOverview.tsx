import { useState } from 'react';
import { toast } from 'react-toastify';

import { DeleteButton, EditButton } from '../../reusable/Buttons/CommonButtons';
import FilledButton from '../../reusable/Buttons/FilledButton';
import DeleteModal from '../../reusable/DeleteModal/DeleteModal';
import Modal from '../../reusable/Modal/Modal';

import AddOrEditUnitModal from './AddOrEditUnitModal';
import UnitPageFilterModal from './UnitPageFilterModal';

import FilterButton from '@/components/reusable/Buttons/FilterButton';
import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import SearchInput from '@/components/reusable/Inputs/SearchInput';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import Pagination from '@/components/reusable/Pagination/Pagination';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import {
  useDeleteUnitMutation,
  useGetUnitsQuery,
} from '@/redux/api/warehouseApis/unitsApi';
import { SingleUnitDetails } from '@/types/warehouseTypes/unitTypes';

interface Props {
  warehouseId: string;
}

function UnitPageOverview({ warehouseId }: Props) {
  const { data, isLoading, refetch } = useGetUnitsQuery({ warehouseId });

  const [deleteUnit] = useDeleteUnitMutation();
  const [isCreateUnitModalOpen, setIsCreateUnitModalOpen] =
    useState<boolean>(false);
  const [isEditUnitModalOpen, setIsEditUnitModalOpen] = useState(false);
  const [selectedUnit, setSelectedUnit] = useState<SingleUnitDetails>();
  const [isDeleteUnitModalOpen, setIsDeleteUnitModalOpen] = useState(false);
  const [deleteUnitData, setDeleteUnitData] = useState<SingleUnitDetails>();
  const [isFilterModalOpen, setIsFilterModalOpen] = useState<boolean>(false);

  const handleDeleteUnit = async () => {
    toast.promise(deleteUnit(deleteUnitData?.id).unwrap(), {
      pending: 'Deleting Brand...',
      success: {
        render({ data: res }) {
          if (res?.statusCode === 200 || res?.statusCode === 201) {
            refetch();
            setIsDeleteUnitModalOpen(false);
          }
          return 'Brand Deleted Successfully';
        },
      },
      error: {
        render({ data: error }) {
          console.log(error);
          return 'Error on delete Brand';
        },
      },
    });
  };
  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 lg:py-1">
        <div className="flex items-center">
          <div className="search-title-and-btn flex items-center">
            <div className="relative">
              <div className="block lg:hidden">
                <FilterButton
                  handleClick={() => setIsFilterModalOpen(!isFilterModalOpen)}
                />
              </div>
              <div
                className={`${isFilterModalOpen ? 'block' : 'hidden'} xl:hidden`}
              >
                <UnitPageFilterModal />
              </div>
            </div>
          </div>
          <div className="hidden lg:block">
            <div className="flex items-center gap-x-2">
              <SearchInput
                placeholder="Search by Name"
                handleSubmit={(value: string) => console.log(value)}
              />
            </div>
          </div>
        </div>
        <div>
          <FilledButton
            isLoading={false}
            text="Add New Unit"
            handleClick={() => setIsCreateUnitModalOpen(true)}
            isDisabled={false}
          />
        </div>
      </div>
      {!isLoading ? (
        <div>
          <div className="tableTop w-full">
            <p>Unit List</p>
            <p>Total : 982</p>
          </div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {data?.data?.length ? (
              <div className="full-table-box h-custom">
                <table className="full-table">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="tableHead">No</th>
                      <th className="tableHead">Name</th>
                      <th className="tableHead">Created At</th>
                      <th className="tableHead">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y bg-slate-200">
                    {data?.data?.map(
                      (unit: SingleUnitDetails, index: number) => (
                        <tr key={unit?.id}>
                          <td className="tableData">{index + 1}</td>
                          <td className="tableData">{unit?.name}</td>
                          <td className="tableData">
                            <DateAndTimeViewer date={unit?.createdAt} />
                          </td>
                          <td className="tableData">
                            <div className="flex items-center justify-center gap-2">
                              <EditButton
                                handleClick={() => {
                                  setSelectedUnit(unit);
                                  setIsEditUnitModalOpen(true);
                                }}
                              />
                              <DeleteButton
                                handleClick={() => {
                                  setIsDeleteUnitModalOpen(true);
                                  setDeleteUnitData(unit);
                                }}
                              />
                            </div>
                          </td>
                        </tr>
                      ),
                    )}
                  </tbody>
                </table>
              </div>
            ) : (
              <NoResultFound pageType="unit" />
            )}
          </div>
          <div className="pagination-box flex justify-end rounded bg-white p-3">
            <Pagination />
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={5} tableRow={6} />
      )}
      <Modal
        setShowModal={setIsCreateUnitModalOpen}
        showModal={isCreateUnitModalOpen}
      >
        <AddOrEditUnitModal
          type="new"
          warehouseId={warehouseId}
          handleClose={() => setIsCreateUnitModalOpen(false)}
          updateRefreshCounter={refetch}
        />
      </Modal>
      <Modal
        setShowModal={setIsEditUnitModalOpen}
        showModal={isEditUnitModalOpen}
      >
        <AddOrEditUnitModal
          type="edit"
          warehouseId={warehouseId}
          handleClose={() => setIsEditUnitModalOpen(false)}
          unitData={selectedUnit}
          updateRefreshCounter={refetch}
        />
      </Modal>
      <Modal
        setShowModal={setIsDeleteUnitModalOpen}
        showModal={isDeleteUnitModalOpen}
      >
        <DeleteModal
          type="Unit"
          name={deleteUnitData?.name ?? ''}
          handleClose={() => setIsDeleteUnitModalOpen(false)}
          handleDelete={handleDeleteUnit}
        />
      </Modal>
    </div>
  );
}

export default UnitPageOverview;
