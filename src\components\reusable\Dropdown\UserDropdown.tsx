import { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useGetUserProfileQuery } from '@/redux/api/userApi';
import { useAppSelector } from '@/redux/hooks';
import { HandleLogOut } from '@/utils/logOut';

function UserDropdown() {
  const [name, setName] = useState<string>('');
  const userDetailsFromState = useAppSelector((state) => state.userDetails);
  const { data: userDetails } = useGetUserProfileQuery(
    {},
    { skip: !!userDetailsFromState?.id },
  );
  useEffect(() => {
    if (userDetailsFromState.name) {
      setName(userDetailsFromState?.name);
    } else {
      setName(userDetails?.data?.name ?? '');
    }
  }, [userDetailsFromState, userDetails]);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger style={{ outline: 'none' }}>
        <div>
          <div className="flex h-[40px] items-center gap-2 rounded-full pl-[1px] pr-2">
            <span className="flex h-[35px] w-[35px] items-center justify-center rounded-full bg-slate-300 text-lg font-bold">
              {name?.split(' ')[0] ? name?.split(' ')[0][0].toUpperCase() : ''}
              {name?.split(' ')[1]
                ? name?.split(' ')[1][0].toUpperCase()
                : name?.split(' ')[0]
                  ? name?.split(' ')[0][1].toUpperCase()
                  : ''}
            </span>
            {/* <span className="text-md font-semibold text-white">{name}</span> */}
          </div>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent side="bottom" align="end">
        <DropdownMenuLabel>My Account</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem>
          <Link to="/profile">Profile</Link>
        </DropdownMenuItem>
        <DropdownMenuItem>Settings</DropdownMenuItem>
        <DropdownMenuItem>
          <Link to="/update-password">Change Password</Link>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={HandleLogOut}>Logout</DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export default UserDropdown;
