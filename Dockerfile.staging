# Stage 1: Build
FROM node:20 as builder

# Accept the VITE_BASEURL build argument
ARG VITE_BASEURL
# Set the environment variable for Vite
ENV VITE_BASEURL=$VITE_BASEURL

# Set working directory
WORKDIR /app

# Copy package.json and yarn.lock
COPY package.json yarn.lock ./

# Install dependencies
RUN yarn install --frozen-lockfile

# Copy the rest of the application code
COPY . .

# Build the app, using the environment variable
RUN yarn build

# Stage 2: Serve with Nginx
FROM nginx:alpine

# Copy build output to Nginx's default static directory
COPY --from=builder /app/dist /usr/share/nginx/html

# Add default Nginx configuration for SPAs
COPY retail-pilot-staging.conf /etc/nginx/conf.d/default.conf

# Expose port 80
EXPOSE 80

# Start Nginx
CMD ["nginx", "-g", "daemon off;"]