import { useFormik } from 'formik';
import { useEffect } from 'react';
import { toast } from 'react-toastify';
import * as Yup from 'yup';

import FilledSubmitButton from '../../reusable/Buttons/FilledSubmitButton';
import CustomInputField from '../../reusable/CustomInputField/CustomInputField';
import ModalTitle from '../../reusable/Modal/ModalTitle';

import CustomDropdown from '@/components/reusable/CustomInputField/CustomDropdown';
import { useCreateOrderPaymentMutation } from '@/redux/api/warehouseApis/warehouseOrdersApis';
import { ShopOrderDetails } from '@/types/shopTypes/shopOrderTypes';

interface Props {
  handleClose: () => void;
  // updateRefreshCounter: () => void;
  orderDetails?: ShopOrderDetails;
}

const formikInitialValues = {
  amount: 0,
  customerPaid: 0,
  deliveryCost: 0,
  paymentMethod: 'BANK',
  orderId: null,
};

/* const validation = Yup.object({
  amount: Yup.number().required('Amount is required'),
  customerPaid: Yup.number().required('Customer Paid is required'),
  paymentMethod: Yup.string().required('Payment Method is required'),
}); */

function OrderDueCollectionModal({
  handleClose,
  // updateRefreshCounter,
  orderDetails,
}: Props) {
  const [createOrderPayment, { isLoading }] = useCreateOrderPaymentMutation();
  const formik = useFormik({
    initialValues: formikInitialValues,
    validationSchema: Yup.object({
      amount: Yup.number()
        .required('Amount is required')
        .max(Number(orderDetails?.totalDue)),
      customerPaid: Yup.number().required('Customer Paid is required'),
      paymentMethod: Yup.string().required('Payment Method is required'),
      /* deliveryCost: Yup.number()
        .required('Delivery Cost is required')
        .min(0)
        .positive('Delivery Cost Must Be greater than or equal to 0'), */
    }),

    onSubmit: async (values) => {
      toast.promise(createOrderPayment(values).unwrap(), {
        pending: 'Receiving Payment...',
        success: {
          render({ data: res }) {
            if (res?.statusCode === 200 || res?.statusCode === 201) {
              formik.resetForm();
              handleClose();
            }
            return 'Payment Received Successfully';
          },
        },
        error: {
          render({ data: error }) {
            console.log(error);
            return 'Error on receiving payment';
          },
        },
      });
    },
  });

  useEffect(() => {
    if (orderDetails) {
      formik.setFieldValue('customerPaid', orderDetails?.totalDue);
      formik.setFieldValue('orderId', orderDetails?.id);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [orderDetails]);

  /* useEffect(() => {
    const dc =
      Number(formik.values.customerPaid) - Number(formik.values.amount);
    formik.setFieldValue('deliveryCost', dc);
  }, [formik.values.amount]); */

  return (
    <div className="flex w-[400px] flex-col gap-4 rounded-xl bg-white p-4">
      <ModalTitle text="Collect Due" handleClose={handleClose} />
      <form
        onSubmit={formik.handleSubmit}
        className="flex w-full flex-col items-center justify-center gap-4"
      >
        <CustomInputField
          type="number"
          placeholder="Customer Paid Amount"
          name="customerPaid"
          label="Customer Paid"
          formik={formik}
        />
        {/* <CustomInputField
          type="number"
          placeholder="Enter Received Amount"
          name="amount"
          label="Received Amount"
          formik={formik}
        /> */}
        <div className="group relative z-0 mt-[-20px] w-full">
          <span className="relative left-3 top-2.5 w-auto bg-white px-1 font-mono text-[12px] font-bold text-gray-900 group-focus-within:text-red-600 dark:text-gray-300">
            Received Amount
          </span>
          <input
            type="number"
            name="amount"
            id="amount"
            className={`text-10 py-55-rem block h-10 w-full rounded-lg border disabled:cursor-not-allowed disabled:bg-gray-100 ${
              formik.touched.amount && formik.errors.amount
                ? 'border-red-500'
                : 'border-gray-300'
            } bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500`}
            placeholder="Enter Received Amount"
            onChange={(e) => {
              formik.setFieldValue('amount', Number(e.target.value));
              formik.setFieldValue(
                'deliveryCost',
                Number(formik.values.customerPaid) - Number(e.target.value),
              );
            }}
            onBlur={formik.handleBlur}
            value={formik.values.amount}
          />
          {formik.touched.amount && formik.errors.amount ? (
            <p className="m-0 text-xs text-red-400">{formik.errors.amount}</p>
          ) : null}
        </div>
        <div className="group relative z-0 mt-[-20px] w-full">
          <span className="relative left-3 top-2.5 w-auto bg-white px-1 font-mono text-[12px] font-bold text-gray-900 group-focus-within:text-red-600 dark:text-gray-300">
            Delivery Cost
          </span>
          <input
            type="number"
            name="deliveryCost"
            id="deliveryCost"
            className={`text-10 py-55-rem block h-10 w-full rounded-lg border disabled:cursor-not-allowed disabled:bg-gray-100 ${
              formik.touched.deliveryCost && formik.errors.deliveryCost
                ? 'border-red-500'
                : 'border-gray-300'
            } bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500`}
            placeholder="Enter Received Amount"
            onChange={(e) => {
              formik.setFieldValue('deliveryCost', Number(e.target.value));
              formik.setFieldValue(
                'amount',
                Number(formik.values.customerPaid) - Number(e.target.value),
              );
            }}
            onBlur={formik.handleBlur}
            value={formik.values.deliveryCost}
          />
          {formik.touched.deliveryCost && formik.errors.deliveryCost ? (
            <p className="m-0 text-xs text-red-400">
              {formik.errors.deliveryCost}
            </p>
          ) : null}
        </div>
        {/* <CustomInputField
          type="number"
          placeholder="Enter Delivery Cost"
          name="deliveryCost"
          label="Delivery Cost"
          formik={formik}
        /> */}
        <CustomDropdown
          placeholder="Select Payment Method"
          name="paymentMethod"
          label="Payment Method"
          formik={formik}
          options={[
            { label: 'CASH', value: 'CASH' },
            { label: 'NAGAD', value: 'NAGAD' },
            { label: 'ROCKET', value: 'ROCKET' },
            { label: 'BKASH', value: 'BKASH' },
            { label: 'BANK', value: 'BANK' },
            { label: 'OTHER', value: 'OTHER' },
          ]}
        />
        <div className="mt-[10px] flex w-full items-center justify-center">
          <FilledSubmitButton text="Submit" isLoading={isLoading} />
        </div>
      </form>
    </div>
  );
}

export default OrderDueCollectionModal;
