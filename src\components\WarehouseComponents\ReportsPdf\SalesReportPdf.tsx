import { Page, Text, View } from '@react-pdf/renderer';

import PrintedTime from './PrintedTime';
import { reportPdfStyles } from './ReportPdfStyles';
import SoftwareMarketingOnPdf from './SoftwareMarketingOnPdf';
import WarehousePdfHeader from './WarehousePdfHeader';

import { SingleShopDetails } from '@/types/shopTypes';
import { Warehouse } from '@/types/shopTypes/shopExpensesTypes';
import { ShopOrderDetails } from '@/types/shopTypes/shopOrderTypes';

const products = [
  {
    id: 1,
    orderNo: 3,
    name: '<PERSON><PERSON>',
    phone: '01686534171',
    total: 9996,
    paid: 10076,
    due: 0,
    Seller: 'Hasib Vai',
    paymentMethod: 'cashondelivary',
    createdAt: 'Sep 17, 2024, 09:16 PM',
  },
  {
    id: 2,
    orderNo: 3,
    name: '<PERSON><PERSON>',
    phone: '01686534171',
    total: 9996,
    paid: 10076,
    due: 0,
    Seller: 'Hasib Vai',
    paymentMethod: 'cashondelivary',
    createdAt: 'Sep 17, 2024, 09:16 PM',
  },
];

interface Props {
  shopSalesReportData: {
    cashReceive: number;
    totalDue: number;
    totalOrderAmount: number;
    orderList: ShopOrderDetails[];
    warehouse?: Warehouse;
    shop?: SingleShopDetails;
  };
}

function SalesReportPdf({ shopSalesReportData }: Props) {
  return (
    <Page size="A4" style={reportPdfStyles.page}>
      <View>
        {shopSalesReportData?.warehouse?.name ? (
          <WarehousePdfHeader
            warehouseDetails={shopSalesReportData.warehouse}
          />
        ) : (
          <View style={reportPdfStyles.header}>
            <Text style={reportPdfStyles.userName}>
              {shopSalesReportData?.shop?.name}
            </Text>
            {shopSalesReportData?.shop?.address ? (
              <Text style={reportPdfStyles.address}>
                {shopSalesReportData?.shop?.address}
              </Text>
            ) : (
              ''
            )}
            {shopSalesReportData?.shop?.mobileNumber ? (
              <Text style={reportPdfStyles.phone}>
                {shopSalesReportData?.shop?.mobileNumber}
              </Text>
            ) : (
              ''
            )}
          </View>
        )}
        <PrintedTime />

        <View style={reportPdfStyles.table}>
          <View style={reportPdfStyles.tableRow}>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.salesReportCol,
                reportPdfStyles.salesReportNoTableCol,
              ]}
            >
              No
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.salesReportCol,
                reportPdfStyles.salesReportOrderNumberCol,
              ]}
            >
              Order No
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.salesReportCol,
                reportPdfStyles.salesReportnameCol,
              ]}
            >
              Name
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.salesReportCol,
                reportPdfStyles.salesReportPhoneTableCol,
              ]}
            >
              Phone
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.salesReportCol,
                reportPdfStyles.salesReportTotalCol,
              ]}
            >
              Total
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.salesReportCol,
                reportPdfStyles.salesReportTotalPaidCol,
              ]}
            >
              Paid
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.salesReportCol,
                reportPdfStyles.salesReportTotalDueCol,
              ]}
            >
              Due
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.tableColLast,
                reportPdfStyles.salesReportCol,
                reportPdfStyles.salesReportCreatedAtTableCol,
              ]}
            >
              Created At
            </Text>
          </View>
          {shopSalesReportData?.orderList?.map(
            (product: ShopOrderDetails, index) => (
              <View
                key={product.id}
                style={
                  index === products.length - 1
                    ? [reportPdfStyles.tableRowLast]
                    : [reportPdfStyles.tableRow]
                }
              >
                <Text
                  style={[
                    reportPdfStyles.tableCol,
                    reportPdfStyles.salesReportCol,
                    reportPdfStyles.salesReportNoTableCol,
                  ]}
                >
                  {index + 1}
                </Text>
                <Text
                  style={[
                    reportPdfStyles.tableCol,
                    reportPdfStyles.salesReportCol,
                    reportPdfStyles.salesReportOrderNumberCol,
                  ]}
                >
                  {product.serialNo}
                </Text>
                <Text
                  style={[
                    reportPdfStyles.tableCol,
                    reportPdfStyles.salesReportCol,
                    reportPdfStyles.salesReportnameCol,
                  ]}
                >
                  {product.Customer?.name}
                </Text>
                <Text
                  style={[
                    reportPdfStyles.tableCol,
                    reportPdfStyles.salesReportCol,
                    reportPdfStyles.salesReportPhoneTableCol,
                  ]}
                >
                  {product.Customer?.mobileNumber}
                </Text>
                <Text
                  style={[
                    reportPdfStyles.tableCol,
                    reportPdfStyles.salesReportCol,
                    reportPdfStyles.salesReportTotalCol,
                  ]}
                >
                  {product.grandTotal}
                </Text>
                <Text
                  style={[
                    reportPdfStyles.tableCol,
                    reportPdfStyles.salesReportCol,
                    reportPdfStyles.salesReportTotalPaidCol,
                  ]}
                >
                  {product.totalPaid}
                </Text>
                <Text
                  style={[
                    reportPdfStyles.tableCol,
                    reportPdfStyles.salesReportCol,
                    reportPdfStyles.salesReportTotalDueCol,
                  ]}
                >
                  {product.totalDue}
                </Text>
                <Text
                  style={[
                    reportPdfStyles.tableCol,
                    reportPdfStyles.salesReportCol,
                    reportPdfStyles.tableColLast,
                    reportPdfStyles.salesReportCreatedAtTableCol,
                  ]}
                >
                  {new Intl.DateTimeFormat('en-US', {
                    day: '2-digit',
                    month: 'short',
                    year: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                  }).format(new Date(product.createdAt))}
                </Text>
              </View>
            ),
          )}
        </View>

        <View style={reportPdfStyles.tableCalculationContainer}>
          <View style={reportPdfStyles.tableCalculation}>
            <View style={reportPdfStyles.tableSingleCalculation}>
              <Text>Total Order Amount:</Text>
              <Text>{shopSalesReportData?.totalOrderAmount}</Text>
            </View>
            <View style={reportPdfStyles.tableSingleCalculation}>
              <Text>Cash Received:</Text>
              <Text>{shopSalesReportData?.cashReceive}</Text>
            </View>
            <View style={reportPdfStyles.tableSingleCalculation}>
              <Text>Due Amount:</Text>
              <Text>{shopSalesReportData?.totalDue}</Text>
            </View>
          </View>
        </View>

        <SoftwareMarketingOnPdf />
      </View>
    </Page>
  );
}

export default SalesReportPdf;
