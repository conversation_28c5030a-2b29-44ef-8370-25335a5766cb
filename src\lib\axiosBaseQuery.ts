import { toast } from 'react-toastify';

import AxiosInstance from './axiosInstance';

const AxiosBaseQuery =
  ({ baseUrl } = { baseUrl: '' }) =>
  async ({ url, method, data, params, contentType, headers = {} }: any) => {
    try {
      const result = await AxiosInstance({
        url: baseUrl + url,
        method,
        data,
        params,
        headers: {
          contentType: contentType || 'application/json',
          ...headers,
        },
      });
      return { ...result };
    } catch (axiosError: any) {
      const err = axiosError;
      toast.error(err?.response?.data?.message);
      return {
        error: {
          status: err.response?.status,
          data: err.response?.data || err.message,
        },
      };
    }
  };

export default AxiosBaseQuery;
