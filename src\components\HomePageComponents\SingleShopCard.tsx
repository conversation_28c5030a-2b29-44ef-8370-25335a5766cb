import { useState } from 'react';
import { Link } from 'react-router-dom';

import DeleteModal from '../reusable/DeleteModal/DeleteModal';
import Modal from '../reusable/Modal/Modal';

import { useDeleteShopMutation } from '@/redux/api/shopApi';
import { ROUTES } from '@/Routes';
import { WarehouseOrShopPermission } from '@/types/userTypes';

interface Props {
  shop: WarehouseOrShopPermission;
}

function SingleShopCard({ shop }: Props) {
  const [deleteShop] = useDeleteShopMutation();
  const [isDeleteShopModalOpen, setIsDeleteShopModalOpen] =
    useState<boolean>(false);

  const handleDeleteShop = async () => {
    try {
      await deleteShop(shop.shopId);
      setIsDeleteShopModalOpen(false);
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <Link
      to={
        shop?.permissionLevel === 'warehouse'
          ? ROUTES.WAREHOUSE.DASHBOARD(shop?.warehouseId as string)
          : ROUTES.SHOP.DASHBOARD(shop?.shopId as string)
      }
      className="relative rounded-xl border border-gray-400 bg-white p-4 shadow-lg"
    >
      <div className="grid grid-cols-12 items-center gap-4 xl:gap-7">
        <img
          src={
            shop?.imgUrl
              ? `https://retail-pluse-upload.s3.ap-southeast-1.amazonaws.com/${
                  shop?.imgUrl
                }`
              : shop?.permissionLevel === 'shop'
                ? 'https://cdn.pixabay.com/photo/2013/07/12/15/49/shop-150362_640.png'
                : 'https://c8.alamy.com/comp/2J2WW0P/warehouse-text-written-on-black-wooden-frame-school-blackboard-2J2WW0P.jpg'
          }
          alt=""
          className="col col-span-3"
        />
        <div className="col col-span-9">
          <div className="w-[85%]">
            <span className="font-bold">
              {shop?.name} (
              {shop.permissionLevel === 'shop' ? shop?.nickName : 'Warehouse'})
            </span>
          </div>
          <div className="mt-2 flex flex-col xl:mt-3">
            <p className="text-sm">
              <span className="font-bold capitalize">Type:</span>{' '}
              {shop?.permissionLevel}
            </p>
          </div>
        </div>
      </div>
      <Modal
        setShowModal={setIsDeleteShopModalOpen}
        showModal={isDeleteShopModalOpen}
      >
        <DeleteModal
          type="Shop"
          name={shop?.name}
          handleClose={() => setIsDeleteShopModalOpen(false)}
          handleDelete={() => handleDeleteShop()}
        />
      </Modal>
    </Link>
  );
}

export default SingleShopCard;
