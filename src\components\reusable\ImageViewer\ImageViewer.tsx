import { useState } from 'react';
import Modal from '../Modal/Modal';
import { X } from 'lucide-react';

interface Props {
  imageUrl: string;
}

function ImageViewer({ imageUrl }: Props) {
  const [isViewImageModalOpen, setIsViewImageModalOpen] =
    useState<boolean>(false);
  const baseUrl = 'https://retail-pluse-upload.s3.ap-southeast-1.amazonaws.com';
  return (
    <div>
      <div
        className="flex items-center justify-center"
        onClick={() => setIsViewImageModalOpen(imageUrl ? true : false)}
      >
        <img
          src={
            imageUrl
              ? `${baseUrl}/${imageUrl}`
              : 'https://cdn.vectorstock.com/i/500p/50/20/no-photography-sign-image-vector-23665020.jpg'
          }
          alt=""
          className="h-[30px] w-[30px] cursor-pointer"
        />
      </div>
      <Modal
        setShowModal={setIsViewImageModalOpen}
        showModal={isViewImageModalOpen}
      >
        <div className="max-h-[80vh] w-[70vh] rounded bg-white p-4">
          <div className="mb-4 flex items-center justify-between">
            <span className="text-xl font-bold">Image Viewer</span>
            <button
              type="button"
              onClick={() => setIsViewImageModalOpen(false)}
              className="hover:text-red-600"
            >
              <X />
            </button>
          </div>
          <div className="flex items-center justify-center">
            <img
              src={
                imageUrl
                  ? `${baseUrl}/${imageUrl}`
                  : 'https://cdn.vectorstock.com/i/500p/50/20/no-photography-sign-image-vector-23665020.jpg'
              }
              alt=""
              className="h-full max-h-[70vh] w-full object-cover"
            />
          </div>
        </div>
      </Modal>
    </div>
  );
}

export default ImageViewer;
