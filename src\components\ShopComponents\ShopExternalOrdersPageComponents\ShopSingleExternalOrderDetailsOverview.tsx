import { Upload } from 'lucide-react';
import { useState } from 'react';
import { Link } from 'react-router-dom';

import ShopAddExternalOrderToPosOrderModal from './ShopAddExternalOrderToPosOrderModal';

import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import Modal from '@/components/reusable/Modal/Modal';
import OrderStatusViewer from '@/components/reusable/OrdersPagesReusableComponents/OrderStatusViewer';
import {
  SingleProductOfExternalOrder,
  useGetShopSingleExternalOrderDetailsQuery,
} from '@/redux/api/shopApis/shopExternalOrdersApis';
import { ROUTES } from '@/Routes';

interface Props {
  orderId?: string;
}

function ShopSingleExternalOrderDetailsOverview({ orderId }: Props) {
  const [isAddOrderToPosModalOpen, setIsAddOrderToPosModalOpen] =
    useState(false);
  const { data } = useGetShopSingleExternalOrderDetailsQuery(orderId);

  if (!data) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <p className="text-gray-600">Loading order details...</p>
      </div>
    );
  }

  const { shippingAddress, lineItems, ...orderDetails } = data.data;

  return (
    <div className="w-full bg-gray-100">
      <div className="rounded-lg bg-white p-8 shadow-lg">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-800">Order Details</h1>
          <div className="flex items-center gap-4">
            <div className="w-[150px] text-center">
              <OrderStatusViewer status={data.data.status} />
            </div>
            {data.data.status === 'PENDING' ? (
              <button
                type="button"
                onClick={() => setIsAddOrderToPosModalOpen(true)}
                className="flex items-center gap-2 rounded-lg bg-primary px-4 py-2 text-white hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-400"
              >
                <Upload size={20} />
                <span>Entry To POS</span>
              </button>
            ) : (
              ''
            )}
          </div>
        </div>

        {/* Order Summary */}
        <div className="mb-8 mt-4">
          <div className="grid grid-cols-2 gap-2 text-gray-600">
            <p>
              <span className="font-medium">Order ID:</span>{' '}
              {orderDetails.orderNumber}
            </p>
            <p className="flex items-center gap-2">
              <span className="font-medium">Order Date:</span>{' '}
              <DateAndTimeViewer date={orderDetails.orderDate} />
            </p>
            <p>
              <span className="font-medium">Customer Name:</span>{' '}
              {orderDetails.firstName} {orderDetails.lastName}
            </p>
            <p>
              <span className="font-medium">Email:</span> {orderDetails.email}
            </p>
            <p>
              <span className="font-medium">Phone:</span> {orderDetails.phone}
            </p>
            <p>
              <span className="font-medium">Address:</span>{' '}
              {[
                shippingAddress.address1,
                shippingAddress.address2,
                shippingAddress.city,
                shippingAddress.province,
                shippingAddress.zip,
                shippingAddress.country,
              ]
                .filter(Boolean)
                .join(', ')}
            </p>
            <p>
              <span className="font-medium">Total Price:</span> ৳
              {orderDetails.totalPrice.toFixed(2)}
            </p>
            <p>
              <span className="font-medium">Subtotal:</span> ৳
              {orderDetails.subtotalPrice.toFixed(2)}
            </p>
            <p>
              <span className="font-medium">Total Discount:</span> ৳
              {orderDetails.totalDiscount.toFixed(2)}
            </p>
            <p>
              <span className="font-medium">Shipping Cost:</span> ৳
              {orderDetails.totalShippingCost.toFixed(2)}
            </p>
            <p>
              <span className="font-medium">Outstanding:</span> ৳
              {orderDetails.totalOutstanding.toFixed(2)}
            </p>
            <p>
              <span className="font-medium">POS Order Id:</span>
              {orderDetails?.Order?.id ? (
                <Link
                  to={ROUTES.SHOP.ORDER_DETAILS(
                    orderDetails?.shopId ?? '',
                    orderDetails?.Order?.id ?? '',
                  )}
                >
                  {orderDetails?.Order?.serialNo || '-'}
                </Link>
              ) : (
                ''
              )}
            </p>
          </div>
        </div>

        {/* Line Items */}
        <div>
          <h2 className="mb-4 text-lg font-semibold text-gray-700">
            Order Items
          </h2>
          <div>
            <table className="relative w-full border border-black">
              <thead className="bg-yellow-400">
                <tr>
                  {/* <th className="tableHead">No</th> */}
                  <th className="tableHead bg-yellow-400">#</th>
                  <th className="tableHead table-col-width">Name</th>
                  <th className="tableHead">Quantity</th>
                  <th className="tableHead">Price</th>
                  <th className="tableHead">Discount</th>
                  <th className="tableHead">Total</th>
                </tr>
              </thead>
              <tbody className="table-tbody divide-y bg-slate-200">
                {data?.data.lineItems?.map(
                  (product: SingleProductOfExternalOrder, index) => {
                    return (
                      <tr key={product?.id}>
                        {/* <td className="tableData">{index + 1}</td> */}
                        <td className="tableData">{index + 1}</td>
                        <td className="tableData table-col-width">
                          {product.name}
                        </td>
                        <td className="tableData"> {product.quantity}</td>
                        <td className="tableData">
                          {' '}
                          {product.price.toFixed(2)}
                        </td>
                        <td className="tableData">
                          {product.discount.toFixed(2)}%
                        </td>
                        <td className="tableData">
                          {(
                            product.price * product.quantity -
                            product.price *
                              product.quantity *
                              (product.discount / 100)
                          ).toFixed(2)}
                        </td>
                      </tr>
                    );
                  },
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <Modal
        setShowModal={setIsAddOrderToPosModalOpen}
        showModal={isAddOrderToPosModalOpen}
      >
        <ShopAddExternalOrderToPosOrderModal
          handleClose={() => setIsAddOrderToPosModalOpen(false)}
          selectedOrderId={orderId}
          shopId={orderDetails?.shopId ?? ''}
        />
      </Modal>
    </div>
  );
}

export default ShopSingleExternalOrderDetailsOverview;
