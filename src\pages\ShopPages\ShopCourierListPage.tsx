import Cookies from 'js-cookie';
import { useParams } from 'react-router-dom';

import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import { useGetCouriersQuery } from '@/redux/api/warehouseApis/couriersApi';
import { SingleCourierDetails } from '@/types/warehouseTypes/settingsTypes';
import { ProtectedRoute } from '@/utils/ProtectedRoutes';

function ShopCourierListPage() {
  const { shopId } = useParams();

  const { data, isLoading } = useGetCouriersQuery({
    shopId,
    organizationId: Cookies.get('organizationId'),
  });

  return (
    <ProtectedRoute>
      <div>
        {!isLoading ? (
          <div>
            <div className="tableTop w-full">
              <p>Courier List</p>
              <p>Total : {data?.pagination?.total}</p>
            </div>
            <div className="full-table-container w-full">
              {data?.data?.length ? (
                <div className="full-table-box h-custom">
                  <table className="full-table">
                    <thead className="bg-gray-100">
                      <tr>
                        <th className="tableHead">No</th>
                        <th className="tableHead">Courier</th>
                        <th className="tableHead">Nickname</th>
                        <th className="tableHead">Assigned Shop</th>
                        <th className="tableHead">Created At</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y bg-slate-200">
                      {data?.data?.map(
                        (brand: SingleCourierDetails, index: number) => (
                          <tr key={brand?.id}>
                            <td className="tableData">{index + 1}</td>
                            <td className="tableData">{brand?.type}</td>
                            <td className="tableData">{brand?.nickName}</td>
                            <td className="tableData">
                              {brand?.AssignedShop?.id
                                ? `${brand?.AssignedShop?.name} (
                            ${brand?.AssignedShop?.nickName})`
                                : 'Not Assigned Yet'}
                            </td>
                            <td className="tableData">
                              <DateAndTimeViewer date={brand?.createdAt} />
                            </td>
                          </tr>
                        ),
                      )}
                    </tbody>
                  </table>
                </div>
              ) : (
                <NoResultFound pageType="courier" />
              )}
            </div>
          </div>
        ) : (
          <TableSkeletonLoader tableColumn={6} tableRow={6} />
        )}
      </div>
    </ProtectedRoute>
  );
}

export default ShopCourierListPage;
