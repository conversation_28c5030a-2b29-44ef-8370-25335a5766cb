import { useFormik } from 'formik';
import Cookies from 'js-cookie';
import { toast } from 'react-toastify';
import * as Yup from 'yup';

import FilledSubmitButton from '@/components/reusable/Buttons/FilledSubmitButton';
import CustomDropdown from '@/components/reusable/CustomInputField/CustomDropdown';
import ModalTitle from '@/components/reusable/Modal/ModalTitle';
import { useGetOrgShopsQuery } from '@/redux/api/organizationApis/orgShopAndWarehouseApis';
import { useAddNewAuditMutation } from '@/redux/api/shopApis/shopStockAuditApis';
import { useGetSellersQuery } from '@/redux/api/warehouseApis/sellersApis';
import { SingleShopDetails } from '@/types/shopTypes';
import { SingleSellerDetails } from '@/types/warehouseTypes/sellersTypes';

interface Props {
  handleClose: () => void;
  updateRefreshCounter: () => void;
}
const formikInitialValues = {
  shopId: '',
  submittedById: '',
};

const steadFastValidation = Yup.object({
  shopId: Yup.string().required('Shop is required'),
  submittedById: Yup.string().required('Audit officer is required'),
});
function OrganizationAddNewAuditModal({
  handleClose,
  updateRefreshCounter,
}: Props) {
  const { data, isLoading } = useGetOrgShopsQuery({
    organizationId: Cookies.get('organizationId'),
  });
  const [addNewAudit, { isLoading: isCreatingNewAudit }] =
    useAddNewAuditMutation();

  const formik = useFormik({
    initialValues: formikInitialValues,
    validationSchema: steadFastValidation,

    onSubmit: async (values) => {
      toast.promise(
        addNewAudit({
          ...values,
          organizationId: Cookies.get('organizationId'),
        }).unwrap(),
        {
          pending: 'Creating New Audit...',
          success: {
            render({ data: res }) {
              console.log(res);

              if (res?.statusCode === 200 || res?.statusCode === 201) {
                updateRefreshCounter();
                handleClose();
              }
              return 'Audit created Successfully';
            },
          },
          error: {
            render() {
              return 'Error on creating Audit';
            },
          },
        },
      );
      console.log(values);
    },
  });

  const { data: employeeData } = useGetSellersQuery(
    {
      organizationId: Cookies.get('organizationId'),
      role: 'AUDITOR',
      shopId: formik.values.shopId,
    },
    {
      skip: !formik.values.shopId.length,
    },
  );

  return (
    <div className="flex w-[400px] flex-col gap-4 rounded-xl bg-white p-4">
      <ModalTitle text="Start New Audit" handleClose={handleClose} />
      {!isLoading ? (
        <form
          onSubmit={formik.handleSubmit}
          className="flex w-full flex-col gap-4"
        >
          <CustomDropdown
            placeholder="Select Shop"
            name="shopId"
            label="Select Shop"
            formik={formik}
            options={
              data?.data?.length
                ? data?.data?.map((single: SingleShopDetails) => {
                    return {
                      value: single.id,
                      label: `${single.name} (${single.nickName})`,
                    };
                  })
                : []
            }
          />
          <CustomDropdown
            placeholder="Select Audit Officer"
            name="submittedById"
            label="Select Audit Officer"
            formik={formik}
            options={
              employeeData?.data?.length
                ? employeeData?.data?.map((single: SingleSellerDetails) => {
                    return {
                      value: single.User?.id,
                      label: single.User?.name,
                    };
                  })
                : []
            }
          />
          <div className="mt-[10px] flex w-full items-center justify-center">
            <FilledSubmitButton
              text="Start Audit"
              isLoading={isCreatingNewAudit}
            />
          </div>
        </form>
      ) : (
        'Loading...'
      )}
    </div>
  );
}

export default OrganizationAddNewAuditModal;
