import { useFormik } from 'formik';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import Swal from 'sweetalert2';
import * as Yup from 'yup';

import emptyCartImage from '../../../images/emptyCartImage.png';

import StockEntryModal from './StockEntryModal';

import {
  DeleteButton,
  EditButton,
  EntryButton,
} from '@/components/reusable/Buttons/CommonButtons';
import FilledSubmitButton from '@/components/reusable/Buttons/FilledSubmitButton';
import CustomDateInput from '@/components/reusable/CustomInputField/CustomDateInput';
import CustomDropdown from '@/components/reusable/CustomInputField/CustomDropdown';
import CustomInputField from '@/components/reusable/CustomInputField/CustomInputField';
import ImageSelector from '@/components/reusable/ImageSelector/ImageSelector';
import CustomSelectForFilter from '@/components/reusable/Inputs/CustomSelectForFilter';
import SearchInput from '@/components/reusable/Inputs/SearchInput';
import Modal from '@/components/reusable/Modal/Modal';
import NoResultFoundForOthers from '@/components/reusable/NoResultFound/NoResultFoundForOthers';
import Pagination from '@/components/reusable/Pagination/Pagination';
import TableSkeletonLoaderHalf from '@/components/reusable/SkeletonLoader/TableSkeletionLoaderHalf';
import { useGetBrandsQuery } from '@/redux/api/warehouseApis/brandsApi';
import { useGetCategoriesQuery } from '@/redux/api/warehouseApis/categoriesApi';
import { useGetProductsQuery } from '@/redux/api/warehouseApis/productsApi';
import { useEntryStockMutation } from '@/redux/api/warehouseApis/stockApis';
import { useGetSuppliersQuery } from '@/redux/api/warehouseApis/suppliersApi';
import { SingleBrandDetails } from '@/types/warehouseTypes/brandsTypes';
import { SingleCategoryDetails } from '@/types/warehouseTypes/categoriesTypes';
import {
  ProductVariant,
  SingleProductDetails,
} from '@/types/warehouseTypes/productTypes';
import { SingleSupplier } from '@/types/warehouseTypes/suppliersTypes';
import { UploadImageOnAws } from '@/utils/ImageUploadModule';

interface Props {
  warehouseId: string;
}

interface SelectedProductType {
  productId: 'string';
  productName: 'string';
  manufactureDate: 'string';
  expireDate: 'string';
  purchasePrice: number;
  retailPrice: number;
  quantity: number;
  discountType: 'string';
  discount: number;
  vat: number;
  wholesalePrice: number;
  variantId: string;
}

const formikInitialValues = {
  supplierId: '',
  supplierInvoiceNo: '',
  purchaseDate: new Date().toISOString().substring(0, 10),
  note: null,
  totalPrice: 0,
  totalPaid: 0,
  advancePayment: 0,
};

const validation = Yup.object({
  supplierId: Yup.string().required('Supplier name is required'),
  // purchasePrice: Yup.string().required('Purchase Price is required'),
  // retailPrice: Yup.string().required('Retails Price is required'),
  // quantity: Yup.string().required('Quantity Price is required'),
});

function StockEntryPageOverview({ warehouseId }: Props) {
  const [productName, setProductName] = useState<string>('');
  const [currentFile, setCurrentFile] = useState<any>();
  const [brandName, setBrandName] = useState<string>('');
  const [categoryName, setCategoryName] = useState<string>('');
  const [isStockEntryModalOpen, setIsStockEntryModalOpen] =
    useState<boolean>(false);
  const [isStockUpdateModalOpen, setIsStockUpdateModalOpen] =
    useState<boolean>(false);
  const [selectedProduct, setSelectedProduct] =
    useState<SingleProductDetails>();
  const [selectedStock, setSelectedStock] = useState<SelectedProductType>();
  const [selectedProducts, setSelectedProducts] =
    useState<SelectedProductType[]>();

  const { data: brands } = useGetBrandsQuery({ warehouseId });
  const { data: categories } = useGetCategoriesQuery({
    warehouseId,
  });
  const { data, isLoading, refetch } = useGetProductsQuery({
    warehouseId,
    name: productName ?? undefined,
    brandName: brandName ?? undefined,
    categoryName: categoryName ?? undefined,
  });
  const { data: suppliers } = useGetSuppliersQuery({ warehouseId });

  const [entryStock, { isLoading: isStockEntryLoading }] =
    useEntryStockMutation();

  const formik = useFormik({
    initialValues: formikInitialValues,
    validationSchema: validation,

    onSubmit: async (values) => {
      Swal.fire({
        title: `Are you sure? you want to entry stock`,
        text: "You won't be able to revert this!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: `Yes, Entry`,
      }).then(async (result) => {
        if (result.isConfirmed) {
          const proofUrl = currentFile
            ? await UploadImageOnAws(currentFile)
            : null;
          toast.promise(
            entryStock({
              ...values,
              warehouseId,
              proofUrl,
              products: selectedProducts,
            }).unwrap(),
            {
              pending: 'New Stock Entry...',
              success: {
                render({ data: res }) {
                  if (res?.statusCode === 200 || res?.statusCode === 201) {
                    formik.resetForm();
                    setSelectedProducts([]);
                    refetch();
                    Swal.fire({
                      title: 'Success',
                      text: 'Your stock entry request submitted successfully. Stock will be created within few minutes. please entry again after sometimes.',
                      icon: 'success',
                      confirmButtonText: 'Cool',
                    });
                    // const url = ROUTES.STOCK.SINGLE_ENTRY_BARCODE_PDF(
                    //   res.data,
                    //   warehouseId,
                    // );
                    // window.open(url, '_blank');
                  }
                  return 'Stock entry done Successfully';
                },
              },
              error: {
                render({ data: error }) {
                  console.log(error);
                  return 'Error on entry stock';
                },
              },
            },
          );
        }
      });
    },
  });

  const handleSelectProductForEntry = (newProd: SelectedProductType) => {
    setSelectedProducts((prevProducts = []) => {
      const existingIndex = prevProducts.findIndex(
        (prod) =>
          prod.productId === newProd.productId &&
          prod.variantId === newProd.variantId,
      );

      if (existingIndex !== -1) {
        // Update quantity if the product already exists
        const updatedProducts = [...prevProducts];
        updatedProducts[existingIndex] = {
          ...updatedProducts[existingIndex],
          quantity: updatedProducts[existingIndex].quantity + newProd.quantity,
        };
        return updatedProducts;
      }
      // Add new product to the list
      return [...prevProducts, newProd];
    });
  };

  const handleUpdateSelectedProduct = (updatedProduct: SelectedProductType) => {
    setSelectedProducts((prevSelectedProducts: any) =>
      prevSelectedProducts.map((product: SelectedProductType) =>
        product.productId === updatedProduct.productId &&
        product?.variantId === updatedProduct?.variantId
          ? updatedProduct
          : product,
      ),
    );
  };

  const handleUnselectItem = (newProd: SelectedProductType) => {
    Swal.fire({
      title: 'Are you sure? you want to remove item from list?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, delete it!',
    }).then((result) => {
      if (result.isConfirmed) {
        const others = selectedProducts?.filter(
          (single: SelectedProductType) =>
            single.variantId !== newProd.variantId,
        );
        setSelectedProducts(others);
        toast.success('Item Removed');
      }
    });
  };

  useEffect(() => {
    let total = 0;
    if (selectedProducts) {
      selectedProducts.map((single: SelectedProductType) => {
        total += Number(single.purchasePrice) * Number(single.quantity);
        return 0;
      });
    }
    formik.setFieldValue('totalPrice', total);
  }, [selectedProducts]);

  return (
    <div>
      <div className="grid grid-cols-12 gap-2">
        <div className="col col-span-12 w-full md:col-span-6">
          {!isLoading ? (
            <div>
              <div className="tableTop-2 w-full">
                <div className="flex w-full items-center justify-between">
                  <SearchInput
                    placeholder="Search by Name"
                    handleSubmit={(value: string) => setProductName(value)}
                  />
                  <CustomSelectForFilter
                    options={
                      brands?.data
                        ? brands?.data?.map((brand: SingleBrandDetails) => {
                            return { label: brand?.name, value: brand?.name };
                          })
                        : []
                    }
                    selectedValue={brandName ?? ''}
                    handleSelect={(e) => setBrandName(e)}
                    placeHolder="Select Brand"
                  />
                  <CustomSelectForFilter
                    options={
                      categories?.data
                        ? categories?.data?.map(
                            (category: SingleCategoryDetails) => {
                              return {
                                label: category?.name,
                                value: category?.name,
                              };
                            },
                          )
                        : []
                    }
                    selectedValue={categoryName ?? ''}
                    handleSelect={(e) => setCategoryName(e)}
                    placeHolder="Select Category"
                  />
                </div>
              </div>
              <div className="w-full">
                {data?.data?.length ? (
                  <div className="h-custom2">
                    <table className="w-full">
                      <thead className="bg-gray-100">
                        <tr>
                          <th className="tableHead">No</th>

                          <th className="tableHead table-col-width">Name</th>
                          <th className="tableHead">TP</th>
                          <th className="tableHead">Regular</th>
                          <th className="tableHead">Available</th>
                          <th className="tableHead">Actions</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y bg-slate-200">
                        {data?.data?.map(
                          (product: SingleProductDetails, index: number) => {
                            /* const isSelected = selectedProducts?.some(
                              (single: SelectedProductType) =>
                                single.productId === product.id,
                            ); */
                            return (
                              <tr
                                key={product?.id}
                                // className={isSelected ? 'bg-orange-500' : ''}
                              >
                                <td className="tableData">{index + 1}</td>
                                <td className="tableData table-col-width">
                                  {product?.name}
                                </td>
                                <td className="tableData">
                                  {product?.currentPurchasePrice}
                                </td>
                                <td className="tableData">
                                  {product?.currentSellingPrice}
                                </td>
                                <td className="tableData">
                                  {product?.totalAvailable}
                                </td>
                                <td className="tableData">
                                  <div className="flex items-center justify-center gap-2">
                                    <EntryButton
                                      handleClick={() => {
                                        setSelectedProduct(product);
                                        setIsStockEntryModalOpen(true);
                                      }}
                                      disabled={false}
                                    />
                                  </div>
                                </td>
                              </tr>
                            );
                          },
                        )}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <NoResultFoundForOthers pageType="Stock" />
                )}
              </div>
              <div className="pagination-box flex justify-end rounded bg-white p-3">
                <Pagination
                  currentPage="1"
                  limit={Number(10)}
                  handleFilter={(fieldName: string, value: any) =>
                    // handleFilter(fieldName, value)
                    console.log(fieldName, value)
                  }
                  totalCount={data?.pagination?.total}
                  totalPages={Math.ceil(
                    Number(data?.pagination?.total) /
                      Number(data?.pagination?.limit),
                  )}
                />
              </div>
            </div>
          ) : (
            <TableSkeletonLoaderHalf tableColumn={6} tableRow={6} />
          )}
        </div>
        <div className="col col-span-12 w-full md:col-span-6">
          <div className="tableTop w-full">
            <p>Selected Products</p>
          </div>
          <div className="w-full">
            <div className="h-customtable2 overflow-y-auto">
              <table className="w-full">
                <thead className="bg-gray-100">
                  <tr>
                    <th className="tableHead">No</th>

                    <th className="tableHead table-col-width">Name</th>
                    <th className="tableHead">Price</th>
                    <th className="tableHead">Quantity</th>
                    <th className="tableHead">Total</th>
                    <th className="tableHead">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y bg-slate-200">
                  {selectedProducts?.length ? (
                    selectedProducts?.map(
                      (product: SelectedProductType, index: number) => (
                        <tr key={product?.productId}>
                          <td className="tableData">{index + 1}</td>
                          <td className="tableData table-col-width">
                            {product?.productName}-(
                            {
                              data?.data
                                ?.find(
                                  (prod: SingleProductDetails) =>
                                    prod.id === product?.productId,
                                )
                                ?.Variants?.find(
                                  (variant: ProductVariant) =>
                                    variant.id === product?.variantId,
                                )?.name
                            }
                            )
                          </td>
                          <td className="tableData">
                            {product?.purchasePrice}
                          </td>
                          <td className="tableData">{product?.quantity}</td>
                          <td className="tableData">
                            {Number(product?.purchasePrice) *
                              Number(product.quantity)}
                          </td>
                          <td className="tableData">
                            <div className="flex items-center justify-center gap-1">
                              <EditButton
                                handleClick={() => {
                                  setSelectedStock(product);
                                  setIsStockUpdateModalOpen(true);
                                }}
                              />
                              <DeleteButton
                                handleClick={() => handleUnselectItem(product)}
                              />
                            </div>
                          </td>
                        </tr>
                      ),
                    )
                  ) : (
                    <tr>
                      <td className="tableData" colSpan={6}>
                        <div className="flex flex-col items-center justify-center gap-4 rounded-lg bg-gray-100 p-6 text-center shadow-md">
                          <img src={emptyCartImage} alt="" className="h-40" />
                          <h2 className="text-xl font-semibold text-gray-700">
                            No Selected Product
                          </h2>
                          <p className="mt-2 text-gray-500">
                            Select Product to Entry stock
                          </p>
                        </div>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
          <div>
            <form
              onSubmit={formik.handleSubmit}
              className="mt-2 flex w-full flex-col gap-4 bg-white p-2"
            >
              <div className="flex gap-4">
                <div className="w-[120px]">
                  <ImageSelector
                    previousImage=""
                    setNewImage={(e) => setCurrentFile(e)}
                  />
                </div>
                <div className="flex w-full flex-col gap-2">
                  <div className="flex items-center gap-2">
                    <CustomDropdown
                      placeholder="Select Supplier"
                      name="supplierId"
                      label="Supplier"
                      formik={formik}
                      options={suppliers?.data?.map(
                        (single: SingleSupplier) => {
                          return {
                            value: single.id,
                            label: single.User?.name,
                          };
                        },
                      )}
                    />
                    <CustomInputField
                      type="text"
                      placeholder="Enter Supplier Invoice No"
                      name="supplierInvoiceNo"
                      label="Invoice No"
                      formik={formik}
                    />
                  </div>
                  <div className="flex items-center gap-2">
                    <CustomInputField
                      type="number"
                      placeholder="Total Amount"
                      name="totalPrice"
                      label="Total Price"
                      formik={formik}
                      isDisabled
                    />
                    <CustomInputField
                      type="number"
                      placeholder="Total Paid"
                      name="totalPaid"
                      label="Total Paid"
                      formik={formik}
                    />
                  </div>
                  <div className="flex items-center gap-2">
                    <CustomDateInput
                      placeholder="Purchase Date"
                      name="purchaseDate"
                      label="Purchase Date"
                      formik={formik}
                    />
                    <CustomInputField
                      type="text"
                      placeholder="Note"
                      name="note"
                      label="Note"
                      formik={formik}
                    />
                  </div>
                </div>
              </div>

              <div className="mt-[10px] flex w-full items-center justify-center">
                <FilledSubmitButton
                  text="Entry Stock"
                  isLoading={isStockEntryLoading}
                  isDisabled={!selectedProducts?.length}
                />
              </div>
            </form>
          </div>
        </div>
      </div>
      <Modal
        setShowModal={setIsStockEntryModalOpen}
        showModal={isStockEntryModalOpen}
      >
        <StockEntryModal
          handleClose={() => setIsStockEntryModalOpen(false)}
          productData={selectedProduct}
          handleSave={(newEntryData: SelectedProductType) =>
            handleSelectProductForEntry(newEntryData)
          }
          type="new"
        />
      </Modal>
      <Modal
        setShowModal={setIsStockUpdateModalOpen}
        showModal={isStockUpdateModalOpen}
      >
        <StockEntryModal
          handleClose={() => setIsStockUpdateModalOpen(false)}
          handleSave={(newEntryData: SelectedProductType) =>
            handleUpdateSelectedProduct(newEntryData)
          }
          type="edit"
          editData={selectedStock}
          productData={data?.data?.find(
            (product: SingleProductDetails) =>
              product.id === selectedStock?.productId,
          )}
        />
      </Modal>
    </div>
  );
}

export default StockEntryPageOverview;
