import { Page, Text, View } from '@react-pdf/renderer';

import PrintedTime from './PrintedTime';
import { reportPdfStyles } from './ReportPdfStyles';
import SoftwareMarketingOnPdf from './SoftwareMarketingOnPdf';

import {
  GetShopAccountsSummeryResponse,
  GetShopProductWiseSummeryResponse,
  SingleProductSale,
} from '@/types/shopTypes/shopStockTransferReportTypes';
import { formatNumberWithComma } from '@/utils/formatNumberWithComma';
import { truncateText } from '@/utils/stringTruncate';

interface Props {
  productWiseSaleReportData?: GetShopProductWiseSummeryResponse;
  accountSummery?: GetShopAccountsSummeryResponse;
}

function ProductWiseSalesSummeryPdf({
  productWiseSaleReportData,
  accountSummery,
}: Props) {
  return (
    <Page size="A4" style={reportPdfStyles.page}>
      <View>
        <View style={reportPdfStyles.header}>
          <Text style={reportPdfStyles.userName}>
            {productWiseSaleReportData?.data?.warehouse?.name ??
              productWiseSaleReportData?.data?.shop?.name}
          </Text>
          <Text style={reportPdfStyles.address}>
            {productWiseSaleReportData?.data?.warehouse?.address ??
              productWiseSaleReportData?.data?.shop?.address}
          </Text>
          {/* <Text style={reportPdfStyles.phone}>
            {categoryWiseSaleReportData?.data?.warehouse?.name}
          </Text> */}
        </View>
        <PrintedTime />

        <View style={reportPdfStyles.table}>
          <View style={reportPdfStyles.tableRow}>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                { width: '50%' },
              ]}
            >
              Product Name
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.categorySalesCol,
              ]}
            >
              Quantity
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.tableColLast,
                reportPdfStyles.categorySalesCol,
                { textAlign: 'right' },
              ]}
            >
              Total
            </Text>
          </View>
          {productWiseSaleReportData?.data?.result.map(
            (product: SingleProductSale) => (
              <View key={product.productName} style={reportPdfStyles.tableRow}>
                <Text
                  style={[
                    reportPdfStyles.tableCol,
                    reportPdfStyles.categorySalesCol,
                    { width: '50%' },
                  ]}
                >
                  {truncateText(product?.productName, 50)}
                </Text>
                <Text
                  style={[
                    reportPdfStyles.tableCol,
                    reportPdfStyles.categorySalesCol,
                  ]}
                >
                  {product.quantity}
                </Text>
                <Text
                  style={[
                    reportPdfStyles.tableCol,
                    reportPdfStyles.categorySalesCol,
                    reportPdfStyles.tableColLast,
                    { textAlign: 'right' },
                  ]}
                >
                  {formatNumberWithComma(product.totalRetailPrice)}
                </Text>
              </View>
            ),
          )}
          <View style={reportPdfStyles.tableRowLast}>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.categorySalesCol,
                { width: '50%' },
              ]}
            >
              Total
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.categorySalesCol,
              ]}
            >
              {formatNumberWithComma(accountSummery?.data.totalStockSold)}
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.categorySalesCol,
                reportPdfStyles.tableColLast,
                { textAlign: 'right' },
              ]}
            >
              {formatNumberWithComma(accountSummery?.data.totalPayable)}
            </Text>
          </View>
        </View>

        <View style={reportPdfStyles.tableCalculationContainer}>
          <View style={reportPdfStyles.tableCalculation}>
            <View style={reportPdfStyles.tableSingleCalculation}>
              <Text>MRP:</Text>
              <Text>
                {formatNumberWithComma(accountSummery?.data?.totalSubtotal)}
              </Text>
            </View>
            <View style={reportPdfStyles.tableSingleCalculation}>
              <Text>Delivery Charge:</Text>
              <Text>
                {formatNumberWithComma(
                  accountSummery?.data?.totalDeliveryCharge,
                )}
              </Text>
            </View>
            <View style={reportPdfStyles.tableSingleCalculation}>
              <Text>Discount:</Text>
              <Text>
                {formatNumberWithComma(accountSummery?.data?.totalDiscount)}
              </Text>
            </View>
            <View style={reportPdfStyles.tableSingleCalculation}>
              <Text>Vat:</Text>
              <Text>
                {formatNumberWithComma(accountSummery?.data?.totalVat)}
              </Text>
            </View>
            <View style={reportPdfStyles.tableSingleCalculation}>
              <Text>Payable:</Text>
              <Text>
                {formatNumberWithComma(
                  Number(accountSummery?.data?.totalPayable) +
                    Number(accountSummery?.data?.totalDeliveryCharge),
                )}
              </Text>
            </View>
            <View style={reportPdfStyles.tableSingleCalculation}>
              <Text>Received:</Text>
              <Text>
                {formatNumberWithComma(accountSummery?.data?.cashReceive)}
              </Text>
            </View>
            <View style={reportPdfStyles.tableSingleCalculation}>
              <Text>Due:</Text>
              <Text>
                {formatNumberWithComma(accountSummery?.data?.totalDue)}
              </Text>
            </View>
          </View>
        </View>

        <SoftwareMarketingOnPdf />
      </View>
    </Page>
  );
}

export default ProductWiseSalesSummeryPdf;
