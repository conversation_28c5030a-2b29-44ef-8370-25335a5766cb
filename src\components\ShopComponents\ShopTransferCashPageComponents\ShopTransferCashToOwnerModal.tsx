import { useFormik } from 'formik';
import { toast } from 'react-toastify';
import * as Yup from 'yup';

import FilledSubmitButton from '../../reusable/Buttons/FilledSubmitButton';
import CustomInputField from '../../reusable/CustomInputField/CustomInputField';
import ModalTitle from '../../reusable/Modal/ModalTitle';

import CustomDropdown from '@/components/reusable/CustomInputField/CustomDropdown';
import { useCreateNewShopCashTransferMutation } from '@/redux/api/shopApis/shopCashTransferApis';
import { useAppSelector } from '@/redux/hooks';
import { GetLoggedInUserDetailsFromCookie } from '@/utils/GetLoggedInUserDetailsFromCookie';
import { PaymentMethods } from '@/utils/staticData';

interface Props {
  shopId: string | undefined;
  handleClose: () => void;
  updateRefreshCounter: () => void;
}

const formikInitialValues = {
  transferredTo: '',
  amount: 0,
  paymentMethod: '',
};

const validation = Yup.object({
  transferredTo: Yup.string().required('Receiver Name is required'),
  paymentMethod: Yup.string().required('Payment Method is required'),
  amount: Yup.string().required('Amount is required'),
});

function ShopTransferCashToOwnerModal({
  shopId,
  handleClose,
  updateRefreshCounter,
}: Props) {
  const { warehouseId } = useAppSelector((state) => state.shopDetails);
  const [createNewShopCashTransfer, { isLoading }] =
    useCreateNewShopCashTransferMutation();

  const formik = useFormik({
    initialValues: formikInitialValues,
    validationSchema: validation,

    onSubmit: async (values) => {
      toast.promise(
        createNewShopCashTransfer({
          ...values,
          shopId,
          warehouseId,
          senderId: GetLoggedInUserDetailsFromCookie()?.id ?? null,
        }).unwrap(),
        {
          pending: 'Transferring Amount...',
          success: {
            render({ data: res }) {
              if (res?.statusCode === 200 || res?.statusCode === 201) {
                updateRefreshCounter();
                handleClose();
              }
              return 'Amount Transferred Successfully';
            },
          },
          error: {
            render({ data: error }) {
              console.log(error);
              return 'Error on Transfer Amount';
            },
          },
        },
      );
    },
  });

  return (
    <div className="flex w-[400px] flex-col gap-4 rounded-xl bg-white p-4">
      <ModalTitle text="Transfer Amount" handleClose={handleClose} />
      <form
        onSubmit={formik.handleSubmit}
        className="flex w-full flex-col gap-2"
      >
        <CustomInputField
          type="text"
          placeholder="Received By"
          name="transferredTo"
          label="Receiver Name"
          formik={formik}
        />
        <CustomDropdown
          placeholder="Select Payment Method"
          name="paymentMethod"
          label="Payment Method"
          formik={formik}
          options={PaymentMethods}
        />
        <CustomInputField
          type="number"
          placeholder="Enter Transfer Amount"
          name="amount"
          label="Amount"
          formik={formik}
        />
        <div className="mt-[10px] flex w-full items-center justify-center">
          <FilledSubmitButton
            text="Transfer"
            isLoading={isLoading}
            isDisabled={isLoading || formik.values.amount <= 0}
          />
        </div>
      </form>
    </div>
  );
}

export default ShopTransferCashToOwnerModal;
