import Cookies from 'js-cookie';
import { useState } from 'react';

import OrganizationAddNewAuditModal from './OrganizationAddNewAuditModal';

import FilledButton from '@/components/reusable/Buttons/FilledButton';
import Modal from '@/components/reusable/Modal/Modal';
import Pagination from '@/components/reusable/Pagination/Pagination';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import StockAuditListViewer from '@/components/ShopComponents/ShopStockAuditPageComponents/StockAuditListViewer';
import { useGetShopStockAuditListQuery } from '@/redux/api/shopApis/shopStockAuditApis';

function OrganizationStockAuditPageOverview() {
  const [isCreateNewAuditModalOpen, setIsCreateNewAuditModalOpen] =
    useState<boolean>(false);
  const { data, isLoading, isFetching, refetch } =
    useGetShopStockAuditListQuery({
      organizationId: Cookies.get('organizationId'),
      page: '1',
      limit: '10',
    });

  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-1">
        <div className="flex items-center">
          <div className="search-title-and-btn flex items-center">
            {/* <p className="whitespace-nowrap">Search Filters</p> */}
            <div className="relative">
              <div className="block xl:hidden">kk</div>
            </div>
          </div>
          <div className="hidden xl:block">
            <div className="flex items-center gap-x-2">Audit Page</div>
          </div>
        </div>
        <div>
          <FilledButton
            isLoading={false}
            text="Create Audit"
            handleClick={() => setIsCreateNewAuditModalOpen(true)}
            isDisabled={false}
          />
        </div>
      </div>
      {!isLoading && !isFetching ? (
        <div>
          <div className="tableTop w-full">
            <p>Audit List</p>
            <div className="flex items-center">
              {/* <p>Total : {data?.pagination?.total}</p> */}
            </div>
          </div>
          <div className="full-table-container w-full">
            <StockAuditListViewer data={data} />
          </div>
          <div className="pagination-box flex justify-end rounded bg-white p-3">
            <Pagination
              totalCount={data?.pagination?.total}
              totalPages={Math.ceil(
                Number(data?.pagination?.total) /
                  Number(data?.pagination?.limit),
              )}
            />
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={14} tableRow={6} />
      )}
      <Modal
        setShowModal={setIsCreateNewAuditModalOpen}
        showModal={isCreateNewAuditModalOpen}
      >
        <OrganizationAddNewAuditModal
          handleClose={() => setIsCreateNewAuditModalOpen(false)}
          updateRefreshCounter={refetch}
        />
      </Modal>
    </div>
  );
}

export default OrganizationStockAuditPageOverview;
