import { useParams } from 'react-router-dom';

import UnitPageOverview from '@/components/WarehouseComponents/UnitPageComponents/UnitPageOverview';
import { ProtectedRoute } from '@/utils/ProtectedRoutes';

function UnitPage() {
  const { warehouseId } = useParams();
  return (
    <ProtectedRoute>
      <UnitPageOverview warehouseId={warehouseId ?? ''} />
    </ProtectedRoute>
  );
}

export default UnitPage;
