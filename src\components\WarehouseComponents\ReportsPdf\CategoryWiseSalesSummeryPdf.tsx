import { Page, Text, View } from '@react-pdf/renderer';

import { reportPdfStyles } from './ReportPdfStyles';

import {
  GetShopAccountsSummeryResponse,
  GetShopCategoryWiseSummeryResponse,
  SingleCategorySale,
} from '@/types/shopTypes/shopStockTransferReportTypes';
import { formatNumberWithComma } from '@/utils/formatNumberWithComma';
import { truncateText } from '@/utils/stringTruncate';

interface Props {
  categoryWiseSaleReportData?: GetShopCategoryWiseSummeryResponse;
  accountSummery?: GetShopAccountsSummeryResponse;
}

function CategoryWiseSalesSummeryPdf({
  categoryWiseSaleReportData,
  accountSummery,
}: Props) {
  return (
    <Page size="A4" style={reportPdfStyles.page}>
      <View>
        <View style={reportPdfStyles.header}>
          <Text style={reportPdfStyles.userName}>
            {categoryWiseSaleReportData?.data?.warehouse?.name ??
              categoryWiseSaleReportData?.data?.shop?.name}
          </Text>
          <Text style={reportPdfStyles.address}>
            {categoryWiseSaleReportData?.data?.warehouse?.address ??
              categoryWiseSaleReportData?.data?.shop?.address}
          </Text>
          {/* <Text style={reportPdfStyles.phone}>
            {categoryWiseSaleReportData?.data?.warehouse?.name}
          </Text> */}
        </View>

        <View style={reportPdfStyles.table}>
          <View style={reportPdfStyles.tableRow}>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.categorySalesCol,
              ]}
            >
              Category Name
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.categorySalesCol,
              ]}
            >
              Sub Category Name
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.categorySalesCol,
              ]}
            >
              Quantity
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.tableColLast,
                reportPdfStyles.categorySalesCol,
                { textAlign: 'right' },
              ]}
            >
              Total
            </Text>
          </View>
          {categoryWiseSaleReportData?.data?.result.map(
            (product: SingleCategorySale, index) => (
              <View
                key={product.categoryName}
                style={
                  index ===
                  Number(categoryWiseSaleReportData?.data?.result?.length) - 1
                    ? [reportPdfStyles.tableRowLast]
                    : [reportPdfStyles.tableRow]
                }
              >
                <Text
                  style={[
                    reportPdfStyles.tableCol,
                    reportPdfStyles.categorySalesCol,
                  ]}
                >
                  {truncateText(product?.categoryName, 25)}
                </Text>
                <Text
                  style={[
                    reportPdfStyles.tableCol,
                    reportPdfStyles.categorySalesCol,
                  ]}
                >
                  {truncateText(product?.subCategoryName, 25)}
                </Text>
                <Text
                  style={[
                    reportPdfStyles.tableCol,
                    reportPdfStyles.categorySalesCol,
                  ]}
                >
                  {product.quantity}
                </Text>
                <Text
                  style={[
                    reportPdfStyles.tableCol,
                    reportPdfStyles.categorySalesCol,
                    reportPdfStyles.tableColLast,
                    { textAlign: 'right' },
                  ]}
                >
                  {formatNumberWithComma(product.totalRetailPrice)}
                </Text>
              </View>
            ),
          )}
        </View>

        <View style={reportPdfStyles.tableCalculationContainer}>
          <View style={reportPdfStyles.tableCalculation}>
            <View style={reportPdfStyles.tableSingleCalculation}>
              <Text>MRP:</Text>
              <Text>
                {formatNumberWithComma(accountSummery?.data?.totalSubtotal)}
              </Text>
            </View>
            <View style={reportPdfStyles.tableSingleCalculation}>
              <Text>Delivery Charge:</Text>
              <Text>
                {formatNumberWithComma(
                  accountSummery?.data?.totalDeliveryCharge,
                )}
              </Text>
            </View>
            <View style={reportPdfStyles.tableSingleCalculation}>
              <Text>Discount:</Text>
              <Text>
                {formatNumberWithComma(accountSummery?.data?.totalDiscount)}
              </Text>
            </View>
            <View style={reportPdfStyles.tableSingleCalculation}>
              <Text>Vat:</Text>
              <Text>
                {formatNumberWithComma(accountSummery?.data?.totalVat)}
              </Text>
            </View>
            <View style={reportPdfStyles.tableSingleCalculation}>
              <Text>Payable:</Text>
              <Text>
                {formatNumberWithComma(
                  Number(accountSummery?.data?.totalPayable) +
                    Number(accountSummery?.data?.totalDeliveryCharge),
                )}
              </Text>
            </View>
            <View style={reportPdfStyles.tableSingleCalculation}>
              <Text>Received:</Text>
              <Text>
                {formatNumberWithComma(accountSummery?.data?.cashReceive)}
              </Text>
            </View>
            <View style={reportPdfStyles.tableSingleCalculation}>
              <Text>Due:</Text>
              <Text>
                {formatNumberWithComma(accountSummery?.data?.totalDue)}
              </Text>
            </View>
          </View>
        </View>

        <View style={reportPdfStyles.org}>
          <Text style={reportPdfStyles.tnxMessage}>
            Software Made By SOFTS.AI.
          </Text>
          <Text style={reportPdfStyles.tnxMessage}>Call-: 01723-714141</Text>
        </View>
      </View>
    </Page>
  );
}

export default CategoryWiseSalesSummeryPdf;
