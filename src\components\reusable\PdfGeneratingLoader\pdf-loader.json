{"nm": "Flow 6", "ddd": 0, "h": 461, "w": 367, "meta": {"g": "LottieFiles Figma v57"}, "layers": [{"ty": 0, "nm": "Setting", "sr": 1, "st": 0, "op": 241.24, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": true, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [60, 60], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [60, 60], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [60, 60], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [60, 60], "t": 180}, {"s": [60, 60], "t": 240}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 180}, {"s": [100, 100], "t": 240}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [153, 159], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [153, 159], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [153, 159], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [153, 159], "t": 180}, {"s": [153, 159], "t": 240}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [90], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [180], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [270], "t": 180}, {"s": [360], "t": 240}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 180}, {"s": [100], "t": 240}]}}, "masksProperties": [{"nm": "", "inv": false, "mode": "a", "x": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "pt": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[120, 0], [120, 120], [0, 120], [0, 0]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[120, 0], [120, 120], [0, 120], [0, 0]]}], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[120, 0], [120, 120], [0, 120], [0, 0]]}], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[120, 0], [120, 120], [0, 120], [0, 0]]}], "t": 180}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[120, 0], [120, 120], [0, 120], [0, 0]]}], "t": 240}]}}], "w": 367, "h": 461, "refId": "1", "ind": 1}, {"ty": 0, "nm": "Frame 1000005398", "sr": 1, "st": 0, "op": 241.24, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [121.5, 157.5], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [132, 171.11], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [121.5, 157.5], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [132, 171.11], "t": 180}, {"s": [121.5, 157.5], "t": 240}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 180}, {"s": [100, 100], "t": 240}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [185.5, 227.5], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [185.5, 227.5], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [185.5, 227.5], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [185.5, 227.5], "t": 180}, {"s": [185.5, 227.5], "t": 240}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 180}, {"s": [0], "t": 240}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 180}, {"s": [100], "t": 240}]}}, "w": 367, "h": 461, "refId": "2", "ind": 2}], "v": "5.7.0", "fr": 60, "op": 240.24, "ip": 0, "assets": [{"nm": "[Asset] Setting", "id": "1", "layers": [{"ty": 4, "nm": "Vector", "sr": 1, "st": 0, "op": 241.24, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [44, 44], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [44, 44], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [44, 44], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [44, 44], "t": 180}, {"s": [44, 44], "t": 240}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 180}, {"s": [100, 100], "t": 240}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [60, 60], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [60, 60], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [60, 60], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [60, 60], "t": 180}, {"s": [60, 60], "t": 240}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 180}, {"s": [0], "t": 240}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 180}, {"s": [100], "t": 240}]}}, "ef": [], "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0.15, 0.9], [0.64, 0.65], [0, 0], [0.91, 0.15], [0.82, -0.41], [0, 0], [0, 0], [0.74, 0.54], [0.91, 0.01], [0, 0], [0.75, -0.54], [0.3, -0.87], [0, 0], [0, 0], [0.9, -0.15], [0.65, -0.64], [0, 0], [0.15, -0.91], [-0.41, -0.82], [0, 0], [0, 0], [0.54, -0.74], [0.01, -0.91], [0, 0], [-0.54, -0.75], [-0.87, -0.3], [0, 0], [0, 0], [-0.15, -0.9], [-0.64, -0.65], [0, 0], [-0.91, -0.15], [-0.82, 0.41], [0, 0], [0, 0], [-0.75, -0.54], [-0.92, 0], [0, 0], [-0.75, 0.54], [-0.3, 0.87], [0, 0], [0, 0], [-0.89, 0.15], [-0.64, 0.63], [0, 0], [-0.15, 0.91], [0.41, 0.82], [0, 0], [0, 0], [-0.54, 0.74], [-0.01, 0.91], [0, 0], [0.54, 0.75], [0.87, 0.3]], "o": [[0, 0], [0, 0], [0.4, -0.82], [-0.15, -0.9], [0, 0], [-0.65, -0.65], [-0.91, -0.15], [0, 0], [0, 0], [-0.29, -0.87], [-0.74, -0.54], [0, 0], [-0.92, 0], [-0.75, 0.54], [0, 0], [0, 0], [-0.82, -0.4], [-0.9, 0.15], [0, 0], [-0.65, 0.65], [-0.15, 0.91], [0, 0], [0, 0], [-0.87, 0.29], [-0.54, 0.74], [0, 0], [0, 0.92], [0.54, 0.75], [0, 0], [0, 0], [-0.4, 0.82], [0.15, 0.9], [0, 0], [0.65, 0.65], [0.91, 0.15], [0, 0], [0, 0], [0.3, 0.87], [0.75, 0.54], [0, 0], [0.92, 0], [0.75, -0.54], [0, 0], [0, 0], [0.81, 0.39], [0.89, -0.15], [0, 0], [0.65, -0.65], [0.15, -0.91], [0, 0], [0, 0], [0.87, -0.29], [0.54, -0.74], [0, 0], [0, -0.92], [-0.54, -0.75], [0, 0]], "v": [[85.01, 33.22], [76.69, 30.45], [80.61, 22.62], [80.99, 19.98], [79.77, 17.6], [70.4, 8.23], [68, 7], [65.34, 7.39], [57.51, 11.31], [54.74, 2.99], [53.15, 0.83], [50.6, 0], [37.4, 0], [34.83, 0.82], [33.22, 2.99], [30.45, 11.31], [22.62, 7.39], [19.98, 7.01], [17.6, 8.23], [8.23, 17.6], [7, 20], [7.39, 22.66], [11.31, 30.49], [2.99, 33.26], [0.83, 34.85], [0, 37.4], [0, 50.6], [0.82, 53.17], [2.99, 54.78], [11.31, 57.55], [7.39, 65.38], [7.01, 68.03], [8.23, 70.4], [17.6, 79.77], [20, 81], [22.66, 80.61], [30.49, 76.69], [33.26, 85.01], [34.87, 87.18], [37.44, 88], [50.64, 88], [53.22, 87.18], [54.82, 85.01], [57.6, 76.69], [65.43, 80.61], [68.05, 80.97], [70.4, 79.77], [79.77, 70.4], [81, 68], [80.61, 65.34], [76.69, 57.51], [85.01, 54.74], [87.17, 53.15], [88, 50.6], [88, 37.4], [87.18, 34.83], [85.01, 33.22]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0.15, 0.9], [0.64, 0.65], [0, 0], [0.91, 0.15], [0.82, -0.41], [0, 0], [0, 0], [0.74, 0.54], [0.91, 0.01], [0, 0], [0.75, -0.54], [0.3, -0.87], [0, 0], [0, 0], [0.9, -0.15], [0.65, -0.64], [0, 0], [0.15, -0.91], [-0.41, -0.82], [0, 0], [0, 0], [0.54, -0.74], [0.01, -0.91], [0, 0], [-0.54, -0.75], [-0.87, -0.3], [0, 0], [0, 0], [-0.15, -0.9], [-0.64, -0.65], [0, 0], [-0.91, -0.15], [-0.82, 0.41], [0, 0], [0, 0], [-0.75, -0.54], [-0.92, 0], [0, 0], [-0.75, 0.54], [-0.3, 0.87], [0, 0], [0, 0], [-0.89, 0.15], [-0.64, 0.63], [0, 0], [-0.15, 0.91], [0.41, 0.82], [0, 0], [0, 0], [-0.54, 0.74], [-0.01, 0.91], [0, 0], [0.54, 0.75], [0.87, 0.3]], "o": [[0, 0], [0, 0], [0.4, -0.82], [-0.15, -0.9], [0, 0], [-0.65, -0.65], [-0.91, -0.15], [0, 0], [0, 0], [-0.29, -0.87], [-0.74, -0.54], [0, 0], [-0.92, 0], [-0.75, 0.54], [0, 0], [0, 0], [-0.82, -0.4], [-0.9, 0.15], [0, 0], [-0.65, 0.65], [-0.15, 0.91], [0, 0], [0, 0], [-0.87, 0.29], [-0.54, 0.74], [0, 0], [0, 0.92], [0.54, 0.75], [0, 0], [0, 0], [-0.4, 0.82], [0.15, 0.9], [0, 0], [0.65, 0.65], [0.91, 0.15], [0, 0], [0, 0], [0.3, 0.87], [0.75, 0.54], [0, 0], [0.92, 0], [0.75, -0.54], [0, 0], [0, 0], [0.81, 0.39], [0.89, -0.15], [0, 0], [0.65, -0.65], [0.15, -0.91], [0, 0], [0, 0], [0.87, -0.29], [0.54, -0.74], [0, 0], [0, -0.92], [-0.54, -0.75], [0, 0]], "v": [[85.01, 33.22], [76.69, 30.45], [80.61, 22.62], [80.99, 19.98], [79.77, 17.6], [70.4, 8.23], [68, 7], [65.34, 7.39], [57.51, 11.31], [54.74, 2.99], [53.15, 0.83], [50.6, 0], [37.4, 0], [34.83, 0.82], [33.22, 2.99], [30.45, 11.31], [22.62, 7.39], [19.98, 7.01], [17.6, 8.23], [8.23, 17.6], [7, 20], [7.39, 22.66], [11.31, 30.49], [2.99, 33.26], [0.83, 34.85], [0, 37.4], [0, 50.6], [0.82, 53.17], [2.99, 54.78], [11.31, 57.55], [7.39, 65.38], [7.01, 68.03], [8.23, 70.4], [17.6, 79.77], [20, 81], [22.66, 80.61], [30.49, 76.69], [33.26, 85.01], [34.87, 87.18], [37.44, 88], [50.64, 88], [53.22, 87.18], [54.82, 85.01], [57.6, 76.69], [65.43, 80.61], [68.05, 80.97], [70.4, 79.77], [79.77, 70.4], [81, 68], [80.61, 65.34], [76.69, 57.51], [85.01, 54.74], [87.17, 53.15], [88, 50.6], [88, 37.4], [87.18, 34.83], [85.01, 33.22]]}], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0.15, 0.9], [0.64, 0.65], [0, 0], [0.91, 0.15], [0.82, -0.41], [0, 0], [0, 0], [0.74, 0.54], [0.91, 0.01], [0, 0], [0.75, -0.54], [0.3, -0.87], [0, 0], [0, 0], [0.9, -0.15], [0.65, -0.64], [0, 0], [0.15, -0.91], [-0.41, -0.82], [0, 0], [0, 0], [0.54, -0.74], [0.01, -0.91], [0, 0], [-0.54, -0.75], [-0.87, -0.3], [0, 0], [0, 0], [-0.15, -0.9], [-0.64, -0.65], [0, 0], [-0.91, -0.15], [-0.82, 0.41], [0, 0], [0, 0], [-0.75, -0.54], [-0.92, 0], [0, 0], [-0.75, 0.54], [-0.3, 0.87], [0, 0], [0, 0], [-0.89, 0.15], [-0.64, 0.63], [0, 0], [-0.15, 0.91], [0.41, 0.82], [0, 0], [0, 0], [-0.54, 0.74], [-0.01, 0.91], [0, 0], [0.54, 0.75], [0.87, 0.3]], "o": [[0, 0], [0, 0], [0.4, -0.82], [-0.15, -0.9], [0, 0], [-0.65, -0.65], [-0.91, -0.15], [0, 0], [0, 0], [-0.29, -0.87], [-0.74, -0.54], [0, 0], [-0.92, 0], [-0.75, 0.54], [0, 0], [0, 0], [-0.82, -0.4], [-0.9, 0.15], [0, 0], [-0.65, 0.65], [-0.15, 0.91], [0, 0], [0, 0], [-0.87, 0.29], [-0.54, 0.74], [0, 0], [0, 0.92], [0.54, 0.75], [0, 0], [0, 0], [-0.4, 0.82], [0.15, 0.9], [0, 0], [0.65, 0.65], [0.91, 0.15], [0, 0], [0, 0], [0.3, 0.87], [0.75, 0.54], [0, 0], [0.92, 0], [0.75, -0.54], [0, 0], [0, 0], [0.81, 0.39], [0.89, -0.15], [0, 0], [0.65, -0.65], [0.15, -0.91], [0, 0], [0, 0], [0.87, -0.29], [0.54, -0.74], [0, 0], [0, -0.92], [-0.54, -0.75], [0, 0]], "v": [[85.01, 33.22], [76.69, 30.45], [80.61, 22.62], [80.99, 19.98], [79.77, 17.6], [70.4, 8.23], [68, 7], [65.34, 7.39], [57.51, 11.31], [54.74, 2.99], [53.15, 0.83], [50.6, 0], [37.4, 0], [34.83, 0.82], [33.22, 2.99], [30.45, 11.31], [22.62, 7.39], [19.98, 7.01], [17.6, 8.23], [8.23, 17.6], [7, 20], [7.39, 22.66], [11.31, 30.49], [2.99, 33.26], [0.83, 34.85], [0, 37.4], [0, 50.6], [0.82, 53.17], [2.99, 54.78], [11.31, 57.55], [7.39, 65.38], [7.01, 68.03], [8.23, 70.4], [17.6, 79.77], [20, 81], [22.66, 80.61], [30.49, 76.69], [33.26, 85.01], [34.87, 87.18], [37.44, 88], [50.64, 88], [53.22, 87.18], [54.82, 85.01], [57.6, 76.69], [65.43, 80.61], [68.05, 80.97], [70.4, 79.77], [79.77, 70.4], [81, 68], [80.61, 65.34], [76.69, 57.51], [85.01, 54.74], [87.17, 53.15], [88, 50.6], [88, 37.4], [87.18, 34.83], [85.01, 33.22]]}], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0.15, 0.9], [0.64, 0.65], [0, 0], [0.91, 0.15], [0.82, -0.41], [0, 0], [0, 0], [0.74, 0.54], [0.91, 0.01], [0, 0], [0.75, -0.54], [0.3, -0.87], [0, 0], [0, 0], [0.9, -0.15], [0.65, -0.64], [0, 0], [0.15, -0.91], [-0.41, -0.82], [0, 0], [0, 0], [0.54, -0.74], [0.01, -0.91], [0, 0], [-0.54, -0.75], [-0.87, -0.3], [0, 0], [0, 0], [-0.15, -0.9], [-0.64, -0.65], [0, 0], [-0.91, -0.15], [-0.82, 0.41], [0, 0], [0, 0], [-0.75, -0.54], [-0.92, 0], [0, 0], [-0.75, 0.54], [-0.3, 0.87], [0, 0], [0, 0], [-0.89, 0.15], [-0.64, 0.63], [0, 0], [-0.15, 0.91], [0.41, 0.82], [0, 0], [0, 0], [-0.54, 0.74], [-0.01, 0.91], [0, 0], [0.54, 0.75], [0.87, 0.3]], "o": [[0, 0], [0, 0], [0.4, -0.82], [-0.15, -0.9], [0, 0], [-0.65, -0.65], [-0.91, -0.15], [0, 0], [0, 0], [-0.29, -0.87], [-0.74, -0.54], [0, 0], [-0.92, 0], [-0.75, 0.54], [0, 0], [0, 0], [-0.82, -0.4], [-0.9, 0.15], [0, 0], [-0.65, 0.65], [-0.15, 0.91], [0, 0], [0, 0], [-0.87, 0.29], [-0.54, 0.74], [0, 0], [0, 0.92], [0.54, 0.75], [0, 0], [0, 0], [-0.4, 0.82], [0.15, 0.9], [0, 0], [0.65, 0.65], [0.91, 0.15], [0, 0], [0, 0], [0.3, 0.87], [0.75, 0.54], [0, 0], [0.92, 0], [0.75, -0.54], [0, 0], [0, 0], [0.81, 0.39], [0.89, -0.15], [0, 0], [0.65, -0.65], [0.15, -0.91], [0, 0], [0, 0], [0.87, -0.29], [0.54, -0.74], [0, 0], [0, -0.92], [-0.54, -0.75], [0, 0]], "v": [[85.01, 33.22], [76.69, 30.45], [80.61, 22.62], [80.99, 19.98], [79.77, 17.6], [70.4, 8.23], [68, 7], [65.34, 7.39], [57.51, 11.31], [54.74, 2.99], [53.15, 0.83], [50.6, 0], [37.4, 0], [34.83, 0.82], [33.22, 2.99], [30.45, 11.31], [22.62, 7.39], [19.98, 7.01], [17.6, 8.23], [8.23, 17.6], [7, 20], [7.39, 22.66], [11.31, 30.49], [2.99, 33.26], [0.83, 34.85], [0, 37.4], [0, 50.6], [0.82, 53.17], [2.99, 54.78], [11.31, 57.55], [7.39, 65.38], [7.01, 68.03], [8.23, 70.4], [17.6, 79.77], [20, 81], [22.66, 80.61], [30.49, 76.69], [33.26, 85.01], [34.87, 87.18], [37.44, 88], [50.64, 88], [53.22, 87.18], [54.82, 85.01], [57.6, 76.69], [65.43, 80.61], [68.05, 80.97], [70.4, 79.77], [79.77, 70.4], [81, 68], [80.61, 65.34], [76.69, 57.51], [85.01, 54.74], [87.17, 53.15], [88, 50.6], [88, 37.4], [87.18, 34.83], [85.01, 33.22]]}], "t": 180}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0.15, 0.9], [0.64, 0.65], [0, 0], [0.91, 0.15], [0.82, -0.41], [0, 0], [0, 0], [0.74, 0.54], [0.91, 0.01], [0, 0], [0.75, -0.54], [0.3, -0.87], [0, 0], [0, 0], [0.9, -0.15], [0.65, -0.64], [0, 0], [0.15, -0.91], [-0.41, -0.82], [0, 0], [0, 0], [0.54, -0.74], [0.01, -0.91], [0, 0], [-0.54, -0.75], [-0.87, -0.3], [0, 0], [0, 0], [-0.15, -0.9], [-0.64, -0.65], [0, 0], [-0.91, -0.15], [-0.82, 0.41], [0, 0], [0, 0], [-0.75, -0.54], [-0.92, 0], [0, 0], [-0.75, 0.54], [-0.3, 0.87], [0, 0], [0, 0], [-0.89, 0.15], [-0.64, 0.63], [0, 0], [-0.15, 0.91], [0.41, 0.82], [0, 0], [0, 0], [-0.54, 0.74], [-0.01, 0.91], [0, 0], [0.54, 0.75], [0.87, 0.3]], "o": [[0, 0], [0, 0], [0.4, -0.82], [-0.15, -0.9], [0, 0], [-0.65, -0.65], [-0.91, -0.15], [0, 0], [0, 0], [-0.29, -0.87], [-0.74, -0.54], [0, 0], [-0.92, 0], [-0.75, 0.54], [0, 0], [0, 0], [-0.82, -0.4], [-0.9, 0.15], [0, 0], [-0.65, 0.65], [-0.15, 0.91], [0, 0], [0, 0], [-0.87, 0.29], [-0.54, 0.74], [0, 0], [0, 0.92], [0.54, 0.75], [0, 0], [0, 0], [-0.4, 0.82], [0.15, 0.9], [0, 0], [0.65, 0.65], [0.91, 0.15], [0, 0], [0, 0], [0.3, 0.87], [0.75, 0.54], [0, 0], [0.92, 0], [0.75, -0.54], [0, 0], [0, 0], [0.81, 0.39], [0.89, -0.15], [0, 0], [0.65, -0.65], [0.15, -0.91], [0, 0], [0, 0], [0.87, -0.29], [0.54, -0.74], [0, 0], [0, -0.92], [-0.54, -0.75], [0, 0]], "v": [[85.01, 33.22], [76.69, 30.45], [80.61, 22.62], [80.99, 19.98], [79.77, 17.6], [70.4, 8.23], [68, 7], [65.34, 7.39], [57.51, 11.31], [54.74, 2.99], [53.15, 0.83], [50.6, 0], [37.4, 0], [34.83, 0.82], [33.22, 2.99], [30.45, 11.31], [22.62, 7.39], [19.98, 7.01], [17.6, 8.23], [8.23, 17.6], [7, 20], [7.39, 22.66], [11.31, 30.49], [2.99, 33.26], [0.83, 34.85], [0, 37.4], [0, 50.6], [0.82, 53.17], [2.99, 54.78], [11.31, 57.55], [7.39, 65.38], [7.01, 68.03], [8.23, 70.4], [17.6, 79.77], [20, 81], [22.66, 80.61], [30.49, 76.69], [33.26, 85.01], [34.87, 87.18], [37.44, 88], [50.64, 88], [53.22, 87.18], [54.82, 85.01], [57.6, 76.69], [65.43, 80.61], [68.05, 80.97], [70.4, 79.77], [79.77, 70.4], [81, 68], [80.61, 65.34], [76.69, 57.51], [85.01, 54.74], [87.17, 53.15], [88, 50.6], [88, 37.4], [87.18, 34.83], [85.01, 33.22]]}], "t": 240}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.94, -0.87], [0.49, -1.18], [-0.06, -1.28], [-0.59, -1.13], [0, 0], [0, 0], [0, 0], [1.26, 0.04], [1.16, -0.48], [0.86, -0.92], [0.4, -1.2], [0, 0], [0, 0], [0, 0], [0.87, 0.94], [1.18, 0.49], [1.28, -0.06], [1.13, -0.59], [0, 0], [0, 0], [0, 0], [-0.06, 1.28], [0.49, 1.18], [0.94, 0.87], [1.21, 0.39], [0, 0], [0, 0], [0, 0], [-0.94, 0.87], [-0.49, 1.18], [0.06, 1.28], [0.59, 1.13], [0, 0], [0, 0], [0, 0], [-1.28, -0.06], [-1.18, 0.49], [-0.87, 0.94], [-0.39, 1.21], [0, 0], [0, 0], [0, 0], [-0.87, -0.94], [-1.18, -0.49], [-1.28, 0.06], [-1.13, 0.59], [0, 0], [0, 0], [0, 0], [0.04, -1.26], [-0.48, -1.16], [-0.92, -0.86], [-1.2, -0.4], [0, 0], [0, 0]], "o": [[0, 0], [-1.21, 0.39], [-0.94, 0.87], [-0.49, 1.18], [0.06, 1.28], [0, 0], [0, 0], [0, 0], [-1.13, -0.56], [-1.26, -0.04], [-1.16, 0.48], [-0.86, 0.92], [0, 0], [0, 0], [0, 0], [-0.39, -1.21], [-0.87, -0.94], [-1.18, -0.49], [-1.28, 0.06], [0, 0], [0, 0], [0, 0], [0.59, -1.13], [0.06, -1.28], [-0.49, -1.18], [-0.94, -0.87], [0, 0], [0, 0], [0, 0], [1.21, -0.39], [0.94, -0.87], [0.49, -1.18], [-0.06, -1.28], [0, 0], [0, 0], [0, 0], [1.13, 0.59], [1.28, 0.06], [1.18, -0.49], [0.87, -0.94], [0, 0], [0, 0], [0, 0], [0.39, 1.21], [0.87, 0.94], [1.18, 0.49], [1.28, -0.06], [0, 0], [0, 0], [0, 0], [-0.56, 1.13], [-0.04, 1.26], [0.48, 1.16], [0.92, 0.86], [0, 0], [0, 0], [0, 0]], "v": [[79.2, 47.43], [73.92, 49.19], [70.66, 51.11], [68.5, 54.21], [67.84, 57.94], [68.82, 61.6], [71.32, 66.62], [66.48, 71.46], [61.6, 68.82], [57.98, 67.9], [54.3, 68.57], [51.23, 70.7], [49.32, 73.92], [47.56, 79.2], [40.57, 79.2], [38.81, 73.92], [36.89, 70.66], [33.79, 68.5], [30.06, 67.84], [26.4, 68.82], [21.38, 71.32], [16.54, 66.48], [19.18, 61.6], [20.16, 57.94], [19.5, 54.21], [17.34, 51.11], [14.08, 49.19], [8.8, 47.43], [8.8, 40.57], [14.08, 38.81], [17.34, 36.89], [19.5, 33.79], [20.16, 30.06], [19.18, 26.4], [16.68, 21.52], [21.52, 16.68], [26.4, 19.18], [30.06, 20.16], [33.79, 19.5], [36.89, 17.34], [38.81, 14.08], [40.57, 8.8], [47.43, 8.8], [49.19, 14.08], [51.11, 17.34], [54.21, 19.5], [57.94, 20.16], [61.6, 19.18], [66.62, 16.68], [71.46, 21.52], [68.82, 26.4], [67.9, 30.02], [68.57, 33.7], [70.7, 36.77], [73.92, 38.68], [79.2, 40.44], [79.2, 47.43]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.94, -0.87], [0.49, -1.18], [-0.06, -1.28], [-0.59, -1.13], [0, 0], [0, 0], [0, 0], [1.26, 0.04], [1.16, -0.48], [0.86, -0.92], [0.4, -1.2], [0, 0], [0, 0], [0, 0], [0.87, 0.94], [1.18, 0.49], [1.28, -0.06], [1.13, -0.59], [0, 0], [0, 0], [0, 0], [-0.06, 1.28], [0.49, 1.18], [0.94, 0.87], [1.21, 0.39], [0, 0], [0, 0], [0, 0], [-0.94, 0.87], [-0.49, 1.18], [0.06, 1.28], [0.59, 1.13], [0, 0], [0, 0], [0, 0], [-1.28, -0.06], [-1.18, 0.49], [-0.87, 0.94], [-0.39, 1.21], [0, 0], [0, 0], [0, 0], [-0.87, -0.94], [-1.18, -0.49], [-1.28, 0.06], [-1.13, 0.59], [0, 0], [0, 0], [0, 0], [0.04, -1.26], [-0.48, -1.16], [-0.92, -0.86], [-1.2, -0.4], [0, 0], [0, 0]], "o": [[0, 0], [-1.21, 0.39], [-0.94, 0.87], [-0.49, 1.18], [0.06, 1.28], [0, 0], [0, 0], [0, 0], [-1.13, -0.56], [-1.26, -0.04], [-1.16, 0.48], [-0.86, 0.92], [0, 0], [0, 0], [0, 0], [-0.39, -1.21], [-0.87, -0.94], [-1.18, -0.49], [-1.28, 0.06], [0, 0], [0, 0], [0, 0], [0.59, -1.13], [0.06, -1.28], [-0.49, -1.18], [-0.94, -0.87], [0, 0], [0, 0], [0, 0], [1.21, -0.39], [0.94, -0.87], [0.49, -1.18], [-0.06, -1.28], [0, 0], [0, 0], [0, 0], [1.13, 0.59], [1.28, 0.06], [1.18, -0.49], [0.87, -0.94], [0, 0], [0, 0], [0, 0], [0.39, 1.21], [0.87, 0.94], [1.18, 0.49], [1.28, -0.06], [0, 0], [0, 0], [0, 0], [-0.56, 1.13], [-0.04, 1.26], [0.48, 1.16], [0.92, 0.86], [0, 0], [0, 0], [0, 0]], "v": [[79.2, 47.43], [73.92, 49.19], [70.66, 51.11], [68.5, 54.21], [67.84, 57.94], [68.82, 61.6], [71.32, 66.62], [66.48, 71.46], [61.6, 68.82], [57.98, 67.9], [54.3, 68.57], [51.23, 70.7], [49.32, 73.92], [47.56, 79.2], [40.57, 79.2], [38.81, 73.92], [36.89, 70.66], [33.79, 68.5], [30.06, 67.84], [26.4, 68.82], [21.38, 71.32], [16.54, 66.48], [19.18, 61.6], [20.16, 57.94], [19.5, 54.21], [17.34, 51.11], [14.08, 49.19], [8.8, 47.43], [8.8, 40.57], [14.08, 38.81], [17.34, 36.89], [19.5, 33.79], [20.16, 30.06], [19.18, 26.4], [16.68, 21.52], [21.52, 16.68], [26.4, 19.18], [30.06, 20.16], [33.79, 19.5], [36.89, 17.34], [38.81, 14.08], [40.57, 8.8], [47.43, 8.8], [49.19, 14.08], [51.11, 17.34], [54.21, 19.5], [57.94, 20.16], [61.6, 19.18], [66.62, 16.68], [71.46, 21.52], [68.82, 26.4], [67.9, 30.02], [68.57, 33.7], [70.7, 36.77], [73.92, 38.68], [79.2, 40.44], [79.2, 47.43]]}], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.94, -0.87], [0.49, -1.18], [-0.06, -1.28], [-0.59, -1.13], [0, 0], [0, 0], [0, 0], [1.26, 0.04], [1.16, -0.48], [0.86, -0.92], [0.4, -1.2], [0, 0], [0, 0], [0, 0], [0.87, 0.94], [1.18, 0.49], [1.28, -0.06], [1.13, -0.59], [0, 0], [0, 0], [0, 0], [-0.06, 1.28], [0.49, 1.18], [0.94, 0.87], [1.21, 0.39], [0, 0], [0, 0], [0, 0], [-0.94, 0.87], [-0.49, 1.18], [0.06, 1.28], [0.59, 1.13], [0, 0], [0, 0], [0, 0], [-1.28, -0.06], [-1.18, 0.49], [-0.87, 0.94], [-0.39, 1.21], [0, 0], [0, 0], [0, 0], [-0.87, -0.94], [-1.18, -0.49], [-1.28, 0.06], [-1.13, 0.59], [0, 0], [0, 0], [0, 0], [0.04, -1.26], [-0.48, -1.16], [-0.92, -0.86], [-1.2, -0.4], [0, 0], [0, 0]], "o": [[0, 0], [-1.21, 0.39], [-0.94, 0.87], [-0.49, 1.18], [0.06, 1.28], [0, 0], [0, 0], [0, 0], [-1.13, -0.56], [-1.26, -0.04], [-1.16, 0.48], [-0.86, 0.92], [0, 0], [0, 0], [0, 0], [-0.39, -1.21], [-0.87, -0.94], [-1.18, -0.49], [-1.28, 0.06], [0, 0], [0, 0], [0, 0], [0.59, -1.13], [0.06, -1.28], [-0.49, -1.18], [-0.94, -0.87], [0, 0], [0, 0], [0, 0], [1.21, -0.39], [0.94, -0.87], [0.49, -1.18], [-0.06, -1.28], [0, 0], [0, 0], [0, 0], [1.13, 0.59], [1.28, 0.06], [1.18, -0.49], [0.87, -0.94], [0, 0], [0, 0], [0, 0], [0.39, 1.21], [0.87, 0.94], [1.18, 0.49], [1.28, -0.06], [0, 0], [0, 0], [0, 0], [-0.56, 1.13], [-0.04, 1.26], [0.48, 1.16], [0.92, 0.86], [0, 0], [0, 0], [0, 0]], "v": [[79.2, 47.43], [73.92, 49.19], [70.66, 51.11], [68.5, 54.21], [67.84, 57.94], [68.82, 61.6], [71.32, 66.62], [66.48, 71.46], [61.6, 68.82], [57.98, 67.9], [54.3, 68.57], [51.23, 70.7], [49.32, 73.92], [47.56, 79.2], [40.57, 79.2], [38.81, 73.92], [36.89, 70.66], [33.79, 68.5], [30.06, 67.84], [26.4, 68.82], [21.38, 71.32], [16.54, 66.48], [19.18, 61.6], [20.16, 57.94], [19.5, 54.21], [17.34, 51.11], [14.08, 49.19], [8.8, 47.43], [8.8, 40.57], [14.08, 38.81], [17.34, 36.89], [19.5, 33.79], [20.16, 30.06], [19.18, 26.4], [16.68, 21.52], [21.52, 16.68], [26.4, 19.18], [30.06, 20.16], [33.79, 19.5], [36.89, 17.34], [38.81, 14.08], [40.57, 8.8], [47.43, 8.8], [49.19, 14.08], [51.11, 17.34], [54.21, 19.5], [57.94, 20.16], [61.6, 19.18], [66.62, 16.68], [71.46, 21.52], [68.82, 26.4], [67.9, 30.02], [68.57, 33.7], [70.7, 36.77], [73.92, 38.68], [79.2, 40.44], [79.2, 47.43]]}], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0.94, -0.87], [0.49, -1.18], [-0.06, -1.28], [-0.59, -1.13], [0, 0], [0, 0], [0, 0], [1.26, 0.04], [1.16, -0.48], [0.86, -0.92], [0.4, -1.2], [0, 0], [0, 0], [0, 0], [0.87, 0.94], [1.18, 0.49], [1.28, -0.06], [1.13, -0.59], [0, 0], [0, 0], [0, 0], [-0.06, 1.28], [0.49, 1.18], [0.94, 0.87], [1.21, 0.39], [0, 0], [0, 0], [0, 0], [-0.94, 0.87], [-0.49, 1.18], [0.06, 1.28], [0.59, 1.13], [0, 0], [0, 0], [0, 0], [-1.28, -0.06], [-1.18, 0.49], [-0.87, 0.94], [-0.39, 1.21], [0, 0], [0, 0], [0, 0], [-0.87, -0.94], [-1.18, -0.49], [-1.28, 0.06], [-1.13, 0.59], [0, 0], [0, 0], [0, 0], [0.04, -1.26], [-0.48, -1.16], [-0.92, -0.86], [-1.2, -0.4], [0, 0], [0, 0]], "o": [[0, 0], [-1.21, 0.39], [-0.94, 0.87], [-0.49, 1.18], [0.06, 1.28], [0, 0], [0, 0], [0, 0], [-1.13, -0.56], [-1.26, -0.04], [-1.16, 0.48], [-0.86, 0.92], [0, 0], [0, 0], [0, 0], [-0.39, -1.21], [-0.87, -0.94], [-1.18, -0.49], [-1.28, 0.06], [0, 0], [0, 0], [0, 0], [0.59, -1.13], [0.06, -1.28], [-0.49, -1.18], [-0.94, -0.87], [0, 0], [0, 0], [0, 0], [1.21, -0.39], [0.94, -0.87], [0.49, -1.18], [-0.06, -1.28], [0, 0], [0, 0], [0, 0], [1.13, 0.59], [1.28, 0.06], [1.18, -0.49], [0.87, -0.94], [0, 0], [0, 0], [0, 0], [0.39, 1.21], [0.87, 0.94], [1.18, 0.49], [1.28, -0.06], [0, 0], [0, 0], [0, 0], [-0.56, 1.13], [-0.04, 1.26], [0.48, 1.16], [0.92, 0.86], [0, 0], [0, 0], [0, 0]], "v": [[79.2, 47.43], [73.92, 49.19], [70.66, 51.11], [68.5, 54.21], [67.84, 57.94], [68.82, 61.6], [71.32, 66.62], [66.48, 71.46], [61.6, 68.82], [57.98, 67.9], [54.3, 68.57], [51.23, 70.7], [49.32, 73.92], [47.56, 79.2], [40.57, 79.2], [38.81, 73.92], [36.89, 70.66], [33.79, 68.5], [30.06, 67.84], [26.4, 68.82], [21.38, 71.32], [16.54, 66.48], [19.18, 61.6], [20.16, 57.94], [19.5, 54.21], [17.34, 51.11], [14.08, 49.19], [8.8, 47.43], [8.8, 40.57], [14.08, 38.81], [17.34, 36.89], [19.5, 33.79], [20.16, 30.06], [19.18, 26.4], [16.68, 21.52], [21.52, 16.68], [26.4, 19.18], [30.06, 20.16], [33.79, 19.5], [36.89, 17.34], [38.81, 14.08], [40.57, 8.8], [47.43, 8.8], [49.19, 14.08], [51.11, 17.34], [54.21, 19.5], [57.94, 20.16], [61.6, 19.18], [66.62, 16.68], [71.46, 21.52], [68.82, 26.4], [67.9, 30.02], [68.57, 33.7], [70.7, 36.77], [73.92, 38.68], [79.2, 40.44], [79.2, 47.43]]}], "t": 180}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0.94, -0.87], [0.49, -1.18], [-0.06, -1.28], [-0.59, -1.13], [0, 0], [0, 0], [0, 0], [1.26, 0.04], [1.16, -0.48], [0.86, -0.92], [0.4, -1.2], [0, 0], [0, 0], [0, 0], [0.87, 0.94], [1.18, 0.49], [1.28, -0.06], [1.13, -0.59], [0, 0], [0, 0], [0, 0], [-0.06, 1.28], [0.49, 1.18], [0.94, 0.87], [1.21, 0.39], [0, 0], [0, 0], [0, 0], [-0.94, 0.87], [-0.49, 1.18], [0.06, 1.28], [0.59, 1.13], [0, 0], [0, 0], [0, 0], [-1.28, -0.06], [-1.18, 0.49], [-0.87, 0.94], [-0.39, 1.21], [0, 0], [0, 0], [0, 0], [-0.87, -0.94], [-1.18, -0.49], [-1.28, 0.06], [-1.13, 0.59], [0, 0], [0, 0], [0, 0], [0.04, -1.26], [-0.48, -1.16], [-0.92, -0.86], [-1.2, -0.4], [0, 0], [0, 0]], "o": [[0, 0], [-1.21, 0.39], [-0.94, 0.87], [-0.49, 1.18], [0.06, 1.28], [0, 0], [0, 0], [0, 0], [-1.13, -0.56], [-1.26, -0.04], [-1.16, 0.48], [-0.86, 0.92], [0, 0], [0, 0], [0, 0], [-0.39, -1.21], [-0.87, -0.94], [-1.18, -0.49], [-1.28, 0.06], [0, 0], [0, 0], [0, 0], [0.59, -1.13], [0.06, -1.28], [-0.49, -1.18], [-0.94, -0.87], [0, 0], [0, 0], [0, 0], [1.21, -0.39], [0.94, -0.87], [0.49, -1.18], [-0.06, -1.28], [0, 0], [0, 0], [0, 0], [1.13, 0.59], [1.28, 0.06], [1.18, -0.49], [0.87, -0.94], [0, 0], [0, 0], [0, 0], [0.39, 1.21], [0.87, 0.94], [1.18, 0.49], [1.28, -0.06], [0, 0], [0, 0], [0, 0], [-0.56, 1.13], [-0.04, 1.26], [0.48, 1.16], [0.92, 0.86], [0, 0], [0, 0], [0, 0]], "v": [[79.2, 47.43], [73.92, 49.19], [70.66, 51.11], [68.5, 54.21], [67.84, 57.94], [68.82, 61.6], [71.32, 66.62], [66.48, 71.46], [61.6, 68.82], [57.98, 67.9], [54.3, 68.57], [51.23, 70.7], [49.32, 73.92], [47.56, 79.2], [40.57, 79.2], [38.81, 73.92], [36.89, 70.66], [33.79, 68.5], [30.06, 67.84], [26.4, 68.82], [21.38, 71.32], [16.54, 66.48], [19.18, 61.6], [20.16, 57.94], [19.5, 54.21], [17.34, 51.11], [14.08, 49.19], [8.8, 47.43], [8.8, 40.57], [14.08, 38.81], [17.34, 36.89], [19.5, 33.79], [20.16, 30.06], [19.18, 26.4], [16.68, 21.52], [21.52, 16.68], [26.4, 19.18], [30.06, 20.16], [33.79, 19.5], [36.89, 17.34], [38.81, 14.08], [40.57, 8.8], [47.43, 8.8], [49.19, 14.08], [51.11, 17.34], [54.21, 19.5], [57.94, 20.16], [61.6, 19.18], [66.62, 16.68], [71.46, 21.52], [68.82, 26.4], [67.9, 30.02], [68.57, 33.7], [70.7, 36.77], [73.92, 38.68], [79.2, 40.44], [79.2, 47.43]]}], "t": 240}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [2.89, -1.93], [1.33, -3.22], [-0.68, -3.41], [-2.46, -2.46], [-3.41, -0.68], [-3.22, 1.33], [-1.93, 2.89], [0, 3.48], [3.3, 3.3], [4.67, 0]], "o": [[-3.48, 0], [-2.89, 1.93], [-1.33, 3.22], [0.68, 3.41], [2.46, 2.46], [3.41, 0.68], [3.22, -1.33], [1.93, -2.89], [0, -4.67], [-3.3, -3.3], [0, 0]], "v": [[44, 26.4], [34.22, 29.37], [27.74, 37.26], [26.74, 47.43], [31.55, 56.45], [40.57, 61.26], [50.74, 60.26], [58.63, 53.78], [61.6, 44], [56.45, 31.55], [44, 26.4]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [2.89, -1.93], [1.33, -3.22], [-0.68, -3.41], [-2.46, -2.46], [-3.41, -0.68], [-3.22, 1.33], [-1.93, 2.89], [0, 3.48], [3.3, 3.3], [4.67, 0]], "o": [[-3.48, 0], [-2.89, 1.93], [-1.33, 3.22], [0.68, 3.41], [2.46, 2.46], [3.41, 0.68], [3.22, -1.33], [1.93, -2.89], [0, -4.67], [-3.3, -3.3], [0, 0]], "v": [[44, 26.4], [34.22, 29.37], [27.74, 37.26], [26.74, 47.43], [31.55, 56.45], [40.57, 61.26], [50.74, 60.26], [58.63, 53.78], [61.6, 44], [56.45, 31.55], [44, 26.4]]}], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [2.89, -1.93], [1.33, -3.22], [-0.68, -3.41], [-2.46, -2.46], [-3.41, -0.68], [-3.22, 1.33], [-1.93, 2.89], [0, 3.48], [3.3, 3.3], [4.67, 0]], "o": [[-3.48, 0], [-2.89, 1.93], [-1.33, 3.22], [0.68, 3.41], [2.46, 2.46], [3.41, 0.68], [3.22, -1.33], [1.93, -2.89], [0, -4.67], [-3.3, -3.3], [0, 0]], "v": [[44, 26.4], [34.22, 29.37], [27.74, 37.26], [26.74, 47.43], [31.55, 56.45], [40.57, 61.26], [50.74, 60.26], [58.63, 53.78], [61.6, 44], [56.45, 31.55], [44, 26.4]]}], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [2.89, -1.93], [1.33, -3.22], [-0.68, -3.41], [-2.46, -2.46], [-3.41, -0.68], [-3.22, 1.33], [-1.93, 2.89], [0, 3.48], [3.3, 3.3], [4.67, 0]], "o": [[-3.48, 0], [-2.89, 1.93], [-1.33, 3.22], [0.68, 3.41], [2.46, 2.46], [3.41, 0.68], [3.22, -1.33], [1.93, -2.89], [0, -4.67], [-3.3, -3.3], [0, 0]], "v": [[44, 26.4], [34.22, 29.37], [27.74, 37.26], [26.74, 47.43], [31.55, 56.45], [40.57, 61.26], [50.74, 60.26], [58.63, 53.78], [61.6, 44], [56.45, 31.55], [44, 26.4]]}], "t": 180}, {"s": [{"c": true, "i": [[0, 0], [2.89, -1.93], [1.33, -3.22], [-0.68, -3.41], [-2.46, -2.46], [-3.41, -0.68], [-3.22, 1.33], [-1.93, 2.89], [0, 3.48], [3.3, 3.3], [4.67, 0]], "o": [[-3.48, 0], [-2.89, 1.93], [-1.33, 3.22], [0.68, 3.41], [2.46, 2.46], [3.41, 0.68], [3.22, -1.33], [1.93, -2.89], [0, -4.67], [-3.3, -3.3], [0, 0]], "v": [[44, 26.4], [34.22, 29.37], [27.74, 37.26], [26.74, 47.43], [31.55, 56.45], [40.57, 61.26], [50.74, 60.26], [58.63, 53.78], [61.6, 44], [56.45, 31.55], [44, 26.4]]}], "t": 240}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [1.45, 0.97], [0.67, 1.61], [-0.34, 1.71], [-1.23, 1.23], [-1.71, 0.34], [-1.61, -0.67], [-0.97, -1.45], [0, -1.74], [1.65, -1.65], [2.33, 0]], "o": [[-1.74, 0], [-1.45, -0.97], [-0.67, -1.61], [0.34, -1.71], [1.23, -1.23], [1.71, -0.34], [1.61, 0.67], [0.97, 1.45], [0, 2.33], [-1.65, 1.65], [0, 0]], "v": [[44, 52.8], [39.11, 51.32], [35.87, 47.37], [35.37, 42.28], [37.78, 37.78], [42.28, 35.37], [47.37, 35.87], [51.32, 39.11], [52.8, 44], [50.22, 50.22], [44, 52.8]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [1.45, 0.97], [0.67, 1.61], [-0.34, 1.71], [-1.23, 1.23], [-1.71, 0.34], [-1.61, -0.67], [-0.97, -1.45], [0, -1.74], [1.65, -1.65], [2.33, 0]], "o": [[-1.74, 0], [-1.45, -0.97], [-0.67, -1.61], [0.34, -1.71], [1.23, -1.23], [1.71, -0.34], [1.61, 0.67], [0.97, 1.45], [0, 2.33], [-1.65, 1.65], [0, 0]], "v": [[44, 52.8], [39.11, 51.32], [35.87, 47.37], [35.37, 42.28], [37.78, 37.78], [42.28, 35.37], [47.37, 35.87], [51.32, 39.11], [52.8, 44], [50.22, 50.22], [44, 52.8]]}], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [1.45, 0.97], [0.67, 1.61], [-0.34, 1.71], [-1.23, 1.23], [-1.71, 0.34], [-1.61, -0.67], [-0.97, -1.45], [0, -1.74], [1.65, -1.65], [2.33, 0]], "o": [[-1.74, 0], [-1.45, -0.97], [-0.67, -1.61], [0.34, -1.71], [1.23, -1.23], [1.71, -0.34], [1.61, 0.67], [0.97, 1.45], [0, 2.33], [-1.65, 1.65], [0, 0]], "v": [[44, 52.8], [39.11, 51.32], [35.87, 47.37], [35.37, 42.28], [37.78, 37.78], [42.28, 35.37], [47.37, 35.87], [51.32, 39.11], [52.8, 44], [50.22, 50.22], [44, 52.8]]}], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [1.45, 0.97], [0.67, 1.61], [-0.34, 1.71], [-1.23, 1.23], [-1.71, 0.34], [-1.61, -0.67], [-0.97, -1.45], [0, -1.74], [1.65, -1.65], [2.33, 0]], "o": [[-1.74, 0], [-1.45, -0.97], [-0.67, -1.61], [0.34, -1.71], [1.23, -1.23], [1.71, -0.34], [1.61, 0.67], [0.97, 1.45], [0, 2.33], [-1.65, 1.65], [0, 0]], "v": [[44, 52.8], [39.11, 51.32], [35.87, 47.37], [35.37, 42.28], [37.78, 37.78], [42.28, 35.37], [47.37, 35.87], [51.32, 39.11], [52.8, 44], [50.22, 50.22], [44, 52.8]]}], "t": 180}, {"s": [{"c": true, "i": [[0, 0], [1.45, 0.97], [0.67, 1.61], [-0.34, 1.71], [-1.23, 1.23], [-1.71, 0.34], [-1.61, -0.67], [-0.97, -1.45], [0, -1.74], [1.65, -1.65], [2.33, 0]], "o": [[-1.74, 0], [-1.45, -0.97], [-0.67, -1.61], [0.34, -1.71], [1.23, -1.23], [1.71, -0.34], [1.61, 0.67], [0.97, 1.45], [0, 2.33], [-1.65, 1.65], [0, 0]], "v": [[44, 52.8], [39.11, 51.32], [35.87, 47.37], [35.37, 42.28], [37.78, 37.78], [42.28, 35.37], [47.37, 35.87], [51.32, 39.11], [52.8, 44], [50.22, 50.22], [44, 52.8]]}], "t": 240}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.1294, 0.2863, 0.7569], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.1294, 0.2863, 0.7569], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.1294, 0.2863, 0.7569], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.1294, 0.2863, 0.7569], "t": 180}, {"s": [0.1294, 0.2863, 0.7569], "t": 240}]}, "r": 1, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 180}, {"s": [100], "t": 240}]}}], "ind": 1}, {"ty": 4, "nm": "Vector 247", "sr": 1, "st": 0, "op": 241.24, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [39.25, 40.25], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [39.25, 40.25], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [39.25, 40.25], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [39.25, 40.25], "t": 180}, {"s": [39.25, 40.25], "t": 240}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 180}, {"s": [100, 100], "t": 240}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [59.75, 60.25], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [59.75, 60.25], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [59.75, 60.25], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [59.75, 60.25], "t": 180}, {"s": [59.75, 60.25], "t": 240}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 180}, {"s": [0], "t": 240}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 180}, {"s": [100], "t": 240}]}}, "ef": [], "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[27, 12], [17, 7.5], [6.5, 16.5], [10, 28], [0, 33], [0, 47.5], [11, 51], [6.5, 61], [18, 72.5], [27, 68], [32.5, 80.5], [47, 80.5], [50.5, 68], [63, 73.5], [71.5, 63.5], [69, 51], [78.5, 46], [78.5, 33], [68, 30], [70.5, 15.5], [63, 7.5], [52, 12], [45.5, 0], [33.5, 0], [27, 12]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[27, 12], [17, 7.5], [6.5, 16.5], [10, 28], [0, 33], [0, 47.5], [11, 51], [6.5, 61], [18, 72.5], [27, 68], [32.5, 80.5], [47, 80.5], [50.5, 68], [63, 73.5], [71.5, 63.5], [69, 51], [78.5, 46], [78.5, 33], [68, 30], [70.5, 15.5], [63, 7.5], [52, 12], [45.5, 0], [33.5, 0], [27, 12]]}], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[27, 12], [17, 7.5], [6.5, 16.5], [10, 28], [0, 33], [0, 47.5], [11, 51], [6.5, 61], [18, 72.5], [27, 68], [32.5, 80.5], [47, 80.5], [50.5, 68], [63, 73.5], [71.5, 63.5], [69, 51], [78.5, 46], [78.5, 33], [68, 30], [70.5, 15.5], [63, 7.5], [52, 12], [45.5, 0], [33.5, 0], [27, 12]]}], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[27, 12], [17, 7.5], [6.5, 16.5], [10, 28], [0, 33], [0, 47.5], [11, 51], [6.5, 61], [18, 72.5], [27, 68], [32.5, 80.5], [47, 80.5], [50.5, 68], [63, 73.5], [71.5, 63.5], [69, 51], [78.5, 46], [78.5, 33], [68, 30], [70.5, 15.5], [63, 7.5], [52, 12], [45.5, 0], [33.5, 0], [27, 12]]}], "t": 180}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[27, 12], [17, 7.5], [6.5, 16.5], [10, 28], [0, 33], [0, 47.5], [11, 51], [6.5, 61], [18, 72.5], [27, 68], [32.5, 80.5], [47, 80.5], [50.5, 68], [63, 73.5], [71.5, 63.5], [69, 51], [78.5, 46], [78.5, 33], [68, 30], [70.5, 15.5], [63, 7.5], [52, 12], [45.5, 0], [33.5, 0], [27, 12]]}], "t": 240}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 180}, {"s": [1, 1, 1], "t": 240}]}, "r": 1, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 180}, {"s": [100], "t": 240}]}}], "ind": 2}, {"ty": 4, "nm": "Setting Bg", "sr": 1, "st": 0, "op": 241.24, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [60, 60], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [60, 60], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [60, 60], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [60, 60], "t": 180}, {"s": [60, 60], "t": 240}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 180}, {"s": [100, 100], "t": 240}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [60, 60], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [60, 60], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [60, 60], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [60, 60], "t": 180}, {"s": [60, 60], "t": 240}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 180}, {"s": [0], "t": 240}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 180}, {"s": [100], "t": 240}]}}, "ef": [], "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[120, 0], [120, 120], [0, 120], [0, 0]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[120, 0], [120, 120], [0, 120], [0, 0]]}], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[120, 0], [120, 120], [0, 120], [0, 0]]}], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[120, 0], [120, 120], [0, 120], [0, 0]]}], "t": 180}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[120, 0], [120, 120], [0, 120], [0, 0]]}], "t": 240}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1, 1, 1], "t": 180}, {"s": [1, 1, 1], "t": 240}]}, "r": 1, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 180}, {"s": [0], "t": 240}]}}], "ind": 3}]}, {"nm": "[Asset] Frame 1000005398", "id": "2", "layers": [{"ty": 4, "nm": "PDF", "sr": 1, "st": 0, "op": 241.24, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [43.5, 17], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [47.26, 18.47], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [43.5, 17], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [47.26, 18.47], "t": 180}, {"s": [43.5, 17], "t": 240}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 180}, {"s": [100, 100], "t": 240}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [121.5, 253], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [132, 274.86], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [121.5, 253], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [132, 274.86], "t": 180}, {"s": [121.5, 253], "t": 240}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 180}, {"s": [0], "t": 240}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 180}, {"s": [100], "t": 240}]}}, "ef": [], "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [-1.82, -0.99], [-0.95, -1.74], [0, -2.26], [0.98, -1.73], [1.85, -0.96], [2.62, 0], [0, 0], [0, 0], [0, 0], [-0.9, 0.48], [-0.44, 0.85], [0, 1.12], [0.45, 0.84], [0.91, 0.46], [1.4, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [2.58, 0], [1.82, 0.97], [0.97, 1.73], [0, 2.26], [-0.98, 1.73], [-1.84, 0.96], [0, 0], [0, 0], [0, 0], [1.39, 0], [0.91, -0.49], [0.45, -0.86], [0, -1.13], [-0.44, -0.85], [-0.91, -0.48], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 34], [0, 0], [13.45, 0], [20.05, 1.48], [24.21, 5.54], [25.66, 11.52], [24.2, 17.5], [19.95, 21.53], [13.26, 22.98], [4.69, 22.98], [4.69, 17.22], [12.1, 17.22], [15.53, 16.5], [17.56, 14.49], [18.24, 11.52], [17.56, 8.57], [15.53, 6.59], [12.06, 5.88], [7.21, 5.88], [7.21, 34], [0, 34]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [-1.98, -1.07], [-1.04, -1.89], [0, -2.45], [1.06, -1.88], [2.01, -1.05], [2.84, 0], [0, 0], [0, 0], [0, 0], [-0.98, 0.52], [-0.48, 0.93], [0, 1.21], [0.49, 0.91], [0.99, 0.51], [1.52, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [2.81, 0], [1.98, 1.06], [1.05, 1.88], [0, 2.45], [-1.06, 1.88], [-2, 1.05], [0, 0], [0, 0], [0, 0], [1.51, 0], [0.99, -0.53], [0.49, -0.94], [0, -1.23], [-0.48, -0.93], [-0.99, -0.52], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 36.94], [0, 0], [14.61, 0], [21.79, 1.61], [26.31, 6.02], [27.88, 12.52], [26.29, 19.01], [21.68, 23.39], [14.41, 24.96], [5.1, 24.96], [5.1, 18.7], [13.14, 18.7], [16.87, 17.93], [19.07, 15.75], [19.81, 12.52], [19.07, 9.31], [16.87, 7.16], [13.11, 6.38], [7.83, 6.38], [7.83, 36.94], [0, 36.94]]}], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [-1.82, -0.99], [-0.95, -1.74], [0, -2.26], [0.98, -1.73], [1.85, -0.96], [2.62, 0], [0, 0], [0, 0], [0, 0], [-0.9, 0.48], [-0.44, 0.85], [0, 1.12], [0.45, 0.84], [0.91, 0.46], [1.4, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [2.58, 0], [1.82, 0.97], [0.97, 1.73], [0, 2.26], [-0.98, 1.73], [-1.84, 0.96], [0, 0], [0, 0], [0, 0], [1.39, 0], [0.91, -0.49], [0.45, -0.86], [0, -1.13], [-0.44, -0.85], [-0.91, -0.48], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 34], [0, 0], [13.45, 0], [20.05, 1.48], [24.21, 5.54], [25.66, 11.52], [24.2, 17.5], [19.95, 21.53], [13.26, 22.98], [4.69, 22.98], [4.69, 17.22], [12.1, 17.22], [15.53, 16.5], [17.56, 14.49], [18.24, 11.52], [17.56, 8.57], [15.53, 6.59], [12.06, 5.88], [7.21, 5.88], [7.21, 34], [0, 34]]}], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [-1.98, -1.07], [-1.04, -1.89], [0, -2.45], [1.06, -1.88], [2.01, -1.05], [2.84, 0], [0, 0], [0, 0], [0, 0], [-0.98, 0.52], [-0.48, 0.93], [0, 1.21], [0.49, 0.91], [0.99, 0.51], [1.52, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [2.81, 0], [1.98, 1.06], [1.05, 1.88], [0, 2.45], [-1.06, 1.88], [-2, 1.05], [0, 0], [0, 0], [0, 0], [1.51, 0], [0.99, -0.53], [0.49, -0.94], [0, -1.23], [-0.48, -0.93], [-0.99, -0.52], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 36.94], [0, 0], [14.61, 0], [21.79, 1.61], [26.31, 6.02], [27.88, 12.52], [26.29, 19.01], [21.68, 23.39], [14.41, 24.96], [5.1, 24.96], [5.1, 18.7], [13.14, 18.7], [16.87, 17.93], [19.07, 15.75], [19.81, 12.52], [19.07, 9.31], [16.87, 7.16], [13.11, 6.38], [7.83, 6.38], [7.83, 36.94], [0, 36.94]]}], "t": 180}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [-1.82, -0.99], [-0.95, -1.74], [0, -2.26], [0.98, -1.73], [1.85, -0.96], [2.62, 0], [0, 0], [0, 0], [0, 0], [-0.9, 0.48], [-0.44, 0.85], [0, 1.12], [0.45, 0.84], [0.91, 0.46], [1.4, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [2.58, 0], [1.82, 0.97], [0.97, 1.73], [0, 2.26], [-0.98, 1.73], [-1.84, 0.96], [0, 0], [0, 0], [0, 0], [1.39, 0], [0.91, -0.49], [0.45, -0.86], [0, -1.13], [-0.44, -0.85], [-0.91, -0.48], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[0, 34], [0, 0], [13.45, 0], [20.05, 1.48], [24.21, 5.54], [25.66, 11.52], [24.2, 17.5], [19.95, 21.53], [13.26, 22.98], [4.69, 22.98], [4.69, 17.22], [12.1, 17.22], [15.53, 16.5], [17.56, 14.49], [18.24, 11.52], [17.56, 8.57], [15.53, 6.59], [12.06, 5.88], [7.21, 5.88], [7.21, 34], [0, 34]]}], "t": 240}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.47, -1.36], [-1.33, -2.53], [0, -3.53], [1.34, -2.55], [2.5, -1.36], [3.46, 0]], "o": [[0, 0], [0, 0], [0, 0], [3.43, 0], [2.47, 1.35], [1.34, 2.53], [0, 3.54], [-1.33, 2.55], [-2.48, 1.36], [0, 0]], "v": [[42.42, 34], [30.34, 34], [30.34, 0], [42.52, 0], [51.38, 2.04], [57.08, 7.87], [59.1, 16.97], [57.08, 26.1], [51.34, 31.96], [42.42, 34]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.69, -1.48], [-1.45, -2.75], [0, -3.84], [1.46, -2.77], [2.71, -1.48], [3.76, 0]], "o": [[0, 0], [0, 0], [0, 0], [3.72, 0], [2.69, 1.47], [1.46, 2.75], [0, 3.85], [-1.45, 2.77], [-2.7, 1.48], [0, 0]], "v": [[46.09, 36.94], [32.96, 36.94], [32.96, 0], [46.2, 0], [55.82, 2.22], [62.02, 8.55], [64.2, 18.43], [62.02, 28.35], [55.78, 34.72], [46.09, 36.94]]}], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.47, -1.36], [-1.33, -2.53], [0, -3.53], [1.34, -2.55], [2.5, -1.36], [3.46, 0]], "o": [[0, 0], [0, 0], [0, 0], [3.43, 0], [2.47, 1.35], [1.34, 2.53], [0, 3.54], [-1.33, 2.55], [-2.48, 1.36], [0, 0]], "v": [[42.42, 34], [30.34, 34], [30.34, 0], [42.52, 0], [51.38, 2.04], [57.08, 7.87], [59.1, 16.97], [57.08, 26.1], [51.34, 31.96], [42.42, 34]]}], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.69, -1.48], [-1.45, -2.75], [0, -3.84], [1.46, -2.77], [2.71, -1.48], [3.76, 0]], "o": [[0, 0], [0, 0], [0, 0], [3.72, 0], [2.69, 1.47], [1.46, 2.75], [0, 3.85], [-1.45, 2.77], [-2.7, 1.48], [0, 0]], "v": [[46.09, 36.94], [32.96, 36.94], [32.96, 0], [46.2, 0], [55.82, 2.22], [62.02, 8.55], [64.2, 18.43], [62.02, 28.35], [55.78, 34.72], [46.09, 36.94]]}], "t": 180}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.47, -1.36], [-1.33, -2.53], [0, -3.53], [1.34, -2.55], [2.5, -1.36], [3.46, 0]], "o": [[0, 0], [0, 0], [0, 0], [3.43, 0], [2.47, 1.35], [1.34, 2.53], [0, 3.54], [-1.33, 2.55], [-2.48, 1.36], [0, 0]], "v": [[42.42, 34], [30.34, 34], [30.34, 0], [42.52, 0], [51.38, 2.04], [57.08, 7.87], [59.1, 16.97], [57.08, 26.1], [51.34, 31.96], [42.42, 34]]}], "t": 240}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [-1.45, 0.75], [-0.73, 1.59], [0, 2.53], [0.74, 1.59], [1.45, 0.75], [2.13, 0], [0, 0], [0, 0]], "o": [[0, 0], [2.13, 0], [1.46, -0.76], [0.74, -1.6], [0, -2.51], [-0.73, -1.59], [-1.45, -0.75], [0, 0], [0, 0], [0, 0]], "v": [[37.55, 27.84], [42.12, 27.84], [47.5, 26.71], [50.79, 23.18], [51.91, 16.97], [50.79, 10.81], [47.51, 7.29], [42.14, 6.16], [37.55, 6.16], [37.55, 27.84]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [-1.58, 0.82], [-0.8, 1.73], [0, 2.75], [0.81, 1.73], [1.58, 0.82], [2.31, 0], [0, 0], [0, 0]], "o": [[0, 0], [2.31, 0], [1.59, -0.83], [0.81, -1.74], [0, -2.73], [-0.8, -1.73], [-1.58, -0.82], [0, 0], [0, 0], [0, 0]], "v": [[40.79, 30.25], [45.76, 30.25], [51.6, 29.02], [55.18, 25.18], [56.39, 18.43], [55.18, 11.74], [51.62, 7.92], [45.78, 6.69], [40.79, 6.69], [40.79, 30.25]]}], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [-1.45, 0.75], [-0.73, 1.59], [0, 2.53], [0.74, 1.59], [1.45, 0.75], [2.13, 0], [0, 0], [0, 0]], "o": [[0, 0], [2.13, 0], [1.46, -0.76], [0.74, -1.6], [0, -2.51], [-0.73, -1.59], [-1.45, -0.75], [0, 0], [0, 0], [0, 0]], "v": [[37.55, 27.84], [42.12, 27.84], [47.5, 26.71], [50.79, 23.18], [51.91, 16.97], [50.79, 10.81], [47.51, 7.29], [42.14, 6.16], [37.55, 6.16], [37.55, 27.84]]}], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [-1.58, 0.82], [-0.8, 1.73], [0, 2.75], [0.81, 1.73], [1.58, 0.82], [2.31, 0], [0, 0], [0, 0]], "o": [[0, 0], [2.31, 0], [1.59, -0.83], [0.81, -1.74], [0, -2.73], [-0.8, -1.73], [-1.58, -0.82], [0, 0], [0, 0], [0, 0]], "v": [[40.79, 30.25], [45.76, 30.25], [51.6, 29.02], [55.18, 25.18], [56.39, 18.43], [55.18, 11.74], [51.62, 7.92], [45.78, 6.69], [40.79, 6.69], [40.79, 30.25]]}], "t": 180}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [-1.45, 0.75], [-0.73, 1.59], [0, 2.53], [0.74, 1.59], [1.45, 0.75], [2.13, 0], [0, 0], [0, 0]], "o": [[0, 0], [2.13, 0], [1.46, -0.76], [0.74, -1.6], [0, -2.51], [-0.73, -1.59], [-1.45, -0.75], [0, 0], [0, 0], [0, 0]], "v": [[37.55, 27.84], [42.12, 27.84], [47.5, 26.71], [50.79, 23.18], [51.91, 16.97], [50.79, 10.81], [47.51, 7.29], [42.14, 6.16], [37.55, 6.16], [37.55, 27.84]]}], "t": 240}]}}, {"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[64.43, 34], [64.43, 0], [87, 0], [87, 5.93], [71.64, 5.93], [71.64, 14.03], [85.5, 14.03], [85.5, 19.96], [71.64, 19.96], [71.64, 34], [64.43, 34]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[70, 36.94], [70, 0], [94.52, 0], [94.52, 6.44], [77.83, 6.44], [77.83, 15.24], [92.89, 15.24], [92.89, 21.68], [77.83, 21.68], [77.83, 36.94], [70, 36.94]]}], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[64.43, 34], [64.43, 0], [87, 0], [87, 5.93], [71.64, 5.93], [71.64, 14.03], [85.5, 14.03], [85.5, 19.96], [71.64, 19.96], [71.64, 34], [64.43, 34]]}], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[70, 36.94], [70, 0], [94.52, 0], [94.52, 6.44], [77.83, 6.44], [77.83, 15.24], [92.89, 15.24], [92.89, 21.68], [77.83, 21.68], [77.83, 36.94], [70, 36.94]]}], "t": 180}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[64.43, 34], [64.43, 0], [87, 0], [87, 5.93], [71.64, 5.93], [71.64, 14.03], [85.5, 14.03], [85.5, 19.96], [71.64, 19.96], [71.64, 34], [64.43, 34]]}], "t": 240}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.1294, 0.2863, 0.7569], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.1294, 0.2863, 0.7569], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.1294, 0.2863, 0.7569], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.1294, 0.2863, 0.7569], "t": 180}, {"s": [0.1294, 0.2863, 0.7569], "t": 240}]}, "r": 1, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 180}, {"s": [100], "t": 240}]}}], "ind": 1}, {"ty": 4, "nm": "Vector", "sr": 1, "st": 0, "op": 241.24, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [33.25, 34.25], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [36.12, 37.21], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [33.25, 34.25], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [36.12, 37.21], "t": 180}, {"s": [33.25, 34.25], "t": 240}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 180}, {"s": [100, 100], "t": 240}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [203.25, 42.75], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [220.81, 46.44], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [203.25, 42.75], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [220.81, 46.44], "t": 180}, {"s": [203.25, 42.75], "t": 240}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 180}, {"s": [0], "t": 240}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 180}, {"s": [100], "t": 240}]}}, "ef": [], "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": false, "i": [[0, 0], [0, 0], [-6.63, 0], [0, 0]], "o": [[0, 0], [0, 6.63], [0, 0], [0, 0]], "v": [[0, 0], [0, 56.5], [12, 68.5], [66.5, 68.5]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": false, "i": [[0, 0], [0, 0], [-7.2, 0], [0, 0]], "o": [[0, 0], [0, 7.2], [0, 0], [0, 0]], "v": [[0, 0], [0, 61.38], [13.04, 74.42], [72.25, 74.42]]}], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": false, "i": [[0, 0], [0, 0], [-6.63, 0], [0, 0]], "o": [[0, 0], [0, 6.63], [0, 0], [0, 0]], "v": [[0, 0], [0, 56.5], [12, 68.5], [66.5, 68.5]]}], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": false, "i": [[0, 0], [0, 0], [-7.2, 0], [0, 0]], "o": [[0, 0], [0, 7.2], [0, 0], [0, 0]], "v": [[0, 0], [0, 61.38], [13.04, 74.42], [72.25, 74.42]]}], "t": 180}, {"s": [{"c": false, "i": [[0, 0], [0, 0], [-6.63, 0], [0, 0]], "o": [[0, 0], [0, 6.63], [0, 0], [0, 0]], "v": [[0, 0], [0, 56.5], [12, 68.5], [66.5, 68.5]]}], "t": 240}]}}, {"ty": "st", "bm": 0, "hd": false, "nm": "", "lc": 1, "lj": 1, "ml": 4, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 180}, {"s": [100], "t": 240}]}, "w": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [8], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [8.691357612609863], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [8], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [8.691357612609863], "t": 180}, {"s": [8], "t": 240}]}, "c": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.1294, 0.2863, 0.7569], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.1294, 0.2863, 0.7569], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.1294, 0.2863, 0.7569], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.1294, 0.2863, 0.7569], "t": 180}, {"s": [0.1294, 0.2863, 0.7569], "t": 240}]}}], "ind": 2}, {"ty": 4, "nm": "Vector", "sr": 1, "st": 0, "op": 241.24, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [116.5, 152.5], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [126.57, 165.68], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [116.5, 152.5], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [126.57, 165.68], "t": 180}, {"s": [116.5, 152.5], "t": 240}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 180}, {"s": [100, 100], "t": 240}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [121.5, 157.5], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [132, 171.11], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [121.5, 157.5], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [132, 171.11], "t": 180}, {"s": [121.5, 157.5], "t": 240}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 180}, {"s": [0], "t": 240}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 180}, {"s": [100], "t": 240}]}}, "ef": [], "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [-19.97, 0], [0, 0], [-5.26, -5.15], [0, 0], [0, 0], [0, -7.1], [0, 0], [19.97, 0], [0, 0], [0, 19.95]], "o": [[0, 0], [0, -19.95], [0, 0], [7.36, 0], [0, 0], [0, 0], [4.84, 5.2], [0, 0], [0, 19.95], [0, 0], [-19.97, 0], [0, 0]], "v": [[0, 268.88], [0, 36.12], [36.16, 0], [156.99, 0], [176.67, 8.03], [213.12, 43.72], [225.47, 56.97], [233, 76.11], [233, 268.88], [196.85, 305], [36.16, 305], [0, 268.88]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [-21.69, 0], [0, 0], [-5.71, -5.59], [0, 0], [0, 0], [0, -7.72], [0, 0], [21.69, 0], [0, 0], [0, 21.67]], "o": [[0, 0], [0, -21.67], [0, 0], [8, 0], [0, 0], [0, 0], [5.26, 5.65], [0, 0], [0, 21.67], [0, 0], [-21.69, 0], [0, 0]], "v": [[0, 292.12], [0, 39.24], [39.28, 0], [170.56, 0], [191.94, 8.72], [231.54, 47.5], [244.95, 61.9], [253.14, 82.69], [253.14, 292.12], [213.86, 331.36], [39.28, 331.36], [0, 292.12]]}], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [-19.97, 0], [0, 0], [-5.26, -5.15], [0, 0], [0, 0], [0, -7.1], [0, 0], [19.97, 0], [0, 0], [0, 19.95]], "o": [[0, 0], [0, -19.95], [0, 0], [7.36, 0], [0, 0], [0, 0], [4.84, 5.2], [0, 0], [0, 19.95], [0, 0], [-19.97, 0], [0, 0]], "v": [[0, 268.88], [0, 36.12], [36.16, 0], [156.99, 0], [176.67, 8.03], [213.12, 43.72], [225.47, 56.97], [233, 76.11], [233, 268.88], [196.85, 305], [36.16, 305], [0, 268.88]]}], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [-21.69, 0], [0, 0], [-5.71, -5.59], [0, 0], [0, 0], [0, -7.72], [0, 0], [21.69, 0], [0, 0], [0, 21.67]], "o": [[0, 0], [0, -21.67], [0, 0], [8, 0], [0, 0], [0, 0], [5.26, 5.65], [0, 0], [0, 21.67], [0, 0], [-21.69, 0], [0, 0]], "v": [[0, 292.12], [0, 39.24], [39.28, 0], [170.56, 0], [191.94, 8.72], [231.54, 47.5], [244.95, 61.9], [253.14, 82.69], [253.14, 292.12], [213.86, 331.36], [39.28, 331.36], [0, 292.12]]}], "t": 180}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [-19.97, 0], [0, 0], [-5.26, -5.15], [0, 0], [0, 0], [0, -7.1], [0, 0], [19.97, 0], [0, 0], [0, 19.95]], "o": [[0, 0], [0, -19.95], [0, 0], [7.36, 0], [0, 0], [0, 0], [4.84, 5.2], [0, 0], [0, 19.95], [0, 0], [-19.97, 0], [0, 0]], "v": [[0, 268.88], [0, 36.12], [36.16, 0], [156.99, 0], [176.67, 8.03], [213.12, 43.72], [225.47, 56.97], [233, 76.11], [233, 268.88], [196.85, 305], [36.16, 305], [0, 268.88]]}], "t": 240}]}}, {"ty": "st", "bm": 0, "hd": false, "nm": "", "lc": 2, "lj": 1, "ml": 4, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 180}, {"s": [100], "t": 240}]}, "w": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [8], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [8.691357612609863], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [8], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [8.691357612609863], "t": 180}, {"s": [8], "t": 240}]}, "c": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.1294, 0.2863, 0.7569], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.1294, 0.2863, 0.7569], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.1294, 0.2863, 0.7569], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.1294, 0.2863, 0.7569], "t": 180}, {"s": [0.1294, 0.2863, 0.7569], "t": 240}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.9137, 0.9294, 0.9765], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.9137, 0.9294, 0.9765], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.9137, 0.9294, 0.9765], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.9137, 0.9294, 0.9765], "t": 180}, {"s": [0.9137, 0.9294, 0.9765], "t": 240}]}, "r": 1, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 180}, {"s": [100], "t": 240}]}}], "ind": 3}, {"ty": 4, "nm": "Frame 1000005398 Bg", "sr": 1, "st": 0, "op": 241.24, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [121.5, 157.5], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [132, 171.11], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [121.5, 157.5], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [132, 171.11], "t": 180}, {"s": [121.5, 157.5], "t": 240}]}, "s": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100, 100], "t": 180}, {"s": [100, 100], "t": 240}]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [121.5, 157.5], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [132, 171.11], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [121.5, 157.5], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [132, 171.11], "t": 180}, {"s": [121.5, 157.5], "t": 240}]}, "r": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 180}, {"s": [0], "t": 240}]}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 180}, {"s": [100], "t": 240}]}}, "ef": [], "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[243, 0], [243, 315], [0, 315], [0, 0]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[264, 0], [264, 342.22], [0, 342.22], [0, 0]]}], "t": 60}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[243, 0], [243, 315], [0, 315], [0, 0]]}], "t": 120}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[264, 0], [264, 342.22], [0, 342.22], [0, 0]]}], "t": 180}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[243, 0], [243, 315], [0, 315], [0, 0]]}], "t": 240}]}}], "ind": 4}]}]}