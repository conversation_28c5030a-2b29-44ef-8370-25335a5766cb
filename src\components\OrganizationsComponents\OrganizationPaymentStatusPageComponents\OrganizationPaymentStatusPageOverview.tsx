import { Document, pdf } from '@react-pdf/renderer';
import { CheckCircle, CircleAlert, X } from 'lucide-react';
import { useState } from 'react';
import { useLocation } from 'react-router-dom';

import SubscriptionPayNowModal from '../OrganizationSubscriptionsPageComponents/SubscriptionPayNowModal';

import OrganizationBillInvoicePdf from './OrganizationBillInvoicePdf';

import Modal from '@/components/reusable/Modal/Modal';
import { useGetSingleOrgPaymentDetailsQuery } from '@/redux/api/organizationApis/orgPaymentApis';

interface Props {
  type: 'SUCCESS' | 'FAILED' | 'CANCELLED';
}

function OrganizationPaymentStatusPageOverview({ type }: Props) {
  const location = useLocation();
  const [isPayNowModalOpen, setIsPayNowModalOpen] = useState<boolean>(false);
  const router = new URLSearchParams(location.search);
  const paymentId = router.get('paymentId');
  const { data } = useGetSingleOrgPaymentDetailsQuery(paymentId ?? '', {
    skip: !paymentId?.length,
  });

  const handleGenerateSingleInvoicePdf = async () => {
    const blob = await pdf(
      <Document>
        <OrganizationBillInvoicePdf billDetails={data?.data} />
      </Document>,
    ).toBlob();

    const url = URL.createObjectURL(blob);
    window.open(url);
  };

  const getContent = () => {
    switch (type) {
      case 'SUCCESS':
        return {
          icon: <CheckCircle className="h-16 w-16 text-green-500" />,
          title: 'Payment Successful!',
          message:
            'Thank you for your payment. Your transaction has been successfully processed.',
          buttonText: 'Download Invoice',
          buttonColor: 'bg-green-500 hover:bg-green-600',
        };
      case 'FAILED':
        return {
          icon: <X className="h-16 w-16 text-red-500" />,
          title: 'Payment Failed!',
          message:
            'We encountered an issue while processing your payment. Please try again.',
          buttonText: 'Retry Payment',
          buttonColor: 'bg-red-500 hover:bg-red-600',
        };
      case 'CANCELLED':
        return {
          icon: <CircleAlert className="h-16 w-16 text-yellow-500" />,
          title: 'Payment Cancelled!',
          message:
            'Your payment has been cancelled. If this was a mistake, please try again.',
          buttonText: 'Try Again',
          buttonColor: 'bg-yellow-500 hover:bg-yellow-600',
        };
      default:
        return null;
    }
  };

  const content = getContent();

  if (!content) return null;

  return (
    <div className="h-full bg-gray-100">
      <div className="flex h-full flex-col items-center justify-center">
        <div className="max-w-md rounded-lg bg-white p-8 text-center shadow-lg">
          <div className="mb-4 flex justify-center">{content.icon}</div>
          <h1 className="mb-2 text-2xl font-bold text-gray-800">
            {content.title}
          </h1>
          <p className="mb-6 text-gray-600">{content.message}</p>
          <button
            onClick={() => {
              if (type === 'SUCCESS') {
                handleGenerateSingleInvoicePdf();
              } else {
                setIsPayNowModalOpen(true);
              }
            }}
            className={`rounded-lg px-6 py-2 text-white transition duration-300 ${content.buttonColor}`}
            type="button"
          >
            {content.buttonText}
          </button>
        </div>
      </div>
      <Modal showModal={isPayNowModalOpen} setShowModal={setIsPayNowModalOpen}>
        <SubscriptionPayNowModal
          handleClose={() => setIsPayNowModalOpen(false)}
          paymentDetails={data?.data?.Subscription}
        />
      </Modal>
    </div>
  );
}

export default OrganizationPaymentStatusPageOverview;
