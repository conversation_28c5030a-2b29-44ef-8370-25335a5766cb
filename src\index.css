@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@keyframes blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.blink {
  animation: blink 1s infinite;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

.tableData {
  @apply border border-gray-300 bg-white px-[4px] py-[4px] text-center text-[12px] font-normal text-black 2xl:text-[14px];
}
.tableDataRed {
  @apply border border-gray-300 bg-red-600 px-[4px] py-[4px] text-center text-[12px] font-normal text-white 2xl:text-[14px];
}

.tableDataLeftAlign {
  @apply border border-gray-300 bg-white px-[4px] py-[4px] text-left text-[12px] font-normal text-black 2xl:text-[14px];
}

.tableDataRightAlign {
  @apply border border-gray-300 bg-white px-[4px] py-[4px] text-right text-[12px] font-normal text-black 2xl:text-[14px];
}

.tableDataSkeleton {
  @apply bg-gray-100 py-5;
}

.tableHead {
  @apply sticky top-[-2px] z-10 border border-gray-300 bg-[#F9FAFC] px-[4px] py-[4px] text-center text-[12px] font-semibold text-black 2xl:text-[14px];
}

.tableHeadLeftAlign {
  @apply sticky top-[-2px] z-10 border border-gray-300 bg-[#F9FAFC] px-[4px] py-[4px] text-left text-[12px] font-semibold text-black 2xl:text-[14px];
}

.tableHeadRightAlign {
  @apply sticky top-[-2px] z-10 border border-gray-300 bg-[#F9FAFC] px-[4px] py-[4px] text-right text-[12px] font-semibold text-black 2xl:text-[14px];
}

.tableHeadSkeleton {
  @apply bg-[#b3b8c4] py-4;
}

.tableTop {
  @apply sticky flex items-center justify-between rounded-tl-[6px] rounded-tr-[6px] bg-[#140364bf] p-[20px];
}

.tableTop-2 {
  @apply sticky flex items-center justify-between rounded-tl-[6px] rounded-tr-[6px] bg-[#140364bf] px-[20px] py-[6px];
}

.tableTop p {
  @apply text-[16px] font-bold text-white;
}

.table-col-width {
  @apply max-w-full text-left lg:max-w-80;
}

.full-table-box {
  @apply overflow-y-auto;
}

.full-table {
  @apply relative w-full overflow-x-scroll border border-black;
}

.dropdown-link::before {
  @apply inline-block;
}

.full-table tbody tr:nth-child(even) td {
  @apply bg-gray-200;
}

.table-tbody tr:nth-child(even) td {
  @apply bg-gray-200;
}

.full-table tbody .tableRowRed td {
  @apply bg-red-600 text-white;
}

.full-table tbody .tableRowRed:nth-child(even) td {
  @apply bg-red-600 text-white;
}

.full-table tbody .tableRowGreen td {
  @apply bg-green-600 text-white;
}

.full-table tbody .tableRowGreen:nth-child(even) td {
  @apply bg-green-600 text-white;
}
.full-table tbody .tableRowPurple td {
  @apply bg-purple-600 text-white;
}

.full-table tbody .tableRowPurple:nth-child(even) td {
  @apply bg-purple-600 text-white;
}
.full-table tbody .tableRowBlue td {
  @apply bg-blue-600 text-white;
}

.full-table tbody .tableRowBlue:nth-child(even) td {
  @apply bg-blue-600 text-white;
}

.full-table tbody .tableRowYellow td {
  @apply bg-yellow-500 text-white;
}

.full-table tbody .tableRowYellow:nth-child(even) td {
  @apply bg-yellow-500 text-white;
}

/* width */
::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

/* Track */
::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgb(54, 54, 54);
  border-radius: 10px;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #6c65d9;
  border-radius: 10px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #6c65d9;
  width: 10px;
}

.commonActionButton {
  padding: 4px;
  background: #efefef;
  border: 1px solid #e6e6e6;
  /* border: none; */
  border-radius: 3px;
  color: #444;
  font-size: 1rem;
  font-weight: 700;
  letter-spacing: 0.2rem;
  text-align: center;
  outline: none;
  cursor: pointer;
  transition: 0.2s ease-in-out;
  box-shadow:
    -6px -6px 14px rgba(255, 255, 255, 0.7),
    -6px -6px 10px rgba(255, 255, 255, 0.5),
    6px 6px 8px rgba(255, 255, 255, 0.075),
    6px 6px 10px rgba(0, 0, 0, 0.15);
}

.commonActionButton:hover {
  box-shadow:
    -2px -2px 6px rgba(255, 255, 255, 0.6),
    -2px -2px 4px rgba(255, 255, 255, 0.4),
    2px 2px 2px rgba(255, 255, 255, 0.05),
    2px 2px 4px rgba(0, 0, 0, 0.1);
}

.commonTransparentActionButton {
  padding: 4px;
  background: transparent;
  border: 1px solid #e6e6e6;
  border-radius: 3px;
  color: #fff;
  font-size: 1rem;
  font-weight: 700;
  letter-spacing: 0.2rem;
  text-align: center;
  outline: none;
  cursor: pointer;
  transition: 0.2s ease-in-out;
  box-shadow:
    -2px -2px 6px rgba(41, 41, 41, 0.6),
    /* reduced spread */ -2px -2px 4px rgba(41, 41, 41, 0.4),
    /* reduced spread */ 2px 2px 2px rgba(20, 20, 20, 0.2),
    /* reduced spread */ 2px 2px 4px rgba(20, 20, 20, 0.3);
  /* reduced spread */
}

.commonTransparentActionButton:hover {
  box-shadow:
    -1px -1px 4px rgba(41, 41, 41, 0.4),
    /* reduced spread */ -1px -1px 2px rgba(41, 41, 41, 0.3),
    /* reduced spread */ 1px 1px 1px rgba(20, 20, 20, 0.1),
    /* reduced spread */ 1px 1px 3px rgba(20, 20, 20, 0.2);
  /* reduced spread */
}

.tooltipContent {
  background-color: #000000;
  border: none;
}

.commonActionButton.disabled {
  opacity: 0.5;
  /* Adjust the opacity to indicate the disabled state */
  cursor: not-allowed;
  /* Change the cursor to indicate it's not clickable */
}

/* .tooltipArrow {
  width: 10px;
  height: 10px;
  background-color: #333;
  transform: rotate(45deg);
  position: absolute;
  bottom: -5px;
  left: 50%;
  margin-left: -5px;
} */

.commonActionButtonIcon {
  color: currentColor;
  /* Inherit the color from the button */
}

.watchButton:hover {
  color: #0055b6;
  border: 1px solid #0055b6;
}

.watchButtonIcon {
  color: currentColor;
}

.editButton:hover {
  color: #018b2f;
  border: 1px solid #018b2f;
}

.editButtonIcon {
  color: currentColor;
}

.deleteButton:hover {
  color: #7e0101;
  border: 1px solid #7e0101;
}

.deleteButtonIcon {
  color: currentColor;
}

.payButton:hover {
  color: #ffd000;
  border: 1px solid #ffd000;
}

.payButtonIcon {
  color: currentColor;
}

.printButton:hover {
  color: #8400ff;
  border: 1px solid #8400ff;
}

.printButtonIcon {
  color: currentColor;
}

.printA4Button:hover {
  color: #ff00b3;
  border: 1px solid #ff00b3;
}

.printA4ButtonIcon {
  color: currentColor;
}

.dueButton:hover {
  color: #00e1ff;
  border: 1px solid #00e1ff;
}

.dueButtonIcon {
  color: currentColor;
}

.cartButton:hover {
  color: #00ff95;
  border: 1px solid #00ff95;
}

.cartButtonIcon {
  color: currentColor;
}

.courierButton:hover {
  color: #ff7b00;
  border: 1px solid #ff7b00;
}

.courierButtonIcon {
  color: currentColor;
}

.returnButton:hover {
  color: #ff0000;
  border: 1px solid #ff0000;
}

.returnButtonIcon {
  color: currentColor;
}

.barcodeButton:hover {
  color: #ff7b00;
  border: 1px solid #ff7b00;
}

.barcodeButtonIcon {
  color: currentColor;
}

.blink-animation {
  animation: blink 1s infinite;
}

@keyframes blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
