import { useEffect, useRef, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import ShopWiseSalesSummary from '../SalesSummeryPageComponents/ShopwiseSalesSummary';

import DashboardUpdatesSection from './DashboardUpdates';
import StatusWiseSummaryForDashboard from './StatusWiseSummaryForDashboard';
import TopTenCustomersList from './TopTenCustomersList';
import TopTenProductCards from './TopTenProductCards';
import WeeklySalesSummeryChart from './WeeklySalesSummeryChart';

import CustomSearchButton from '@/components/reusable/Buttons/CustomSearchButton';
import CustomDateFilterInput from '@/components/reusable/Inputs/CustomDateFilterInput';
import CustomSelectForFilter from '@/components/reusable/Inputs/CustomSelectForFilter';
import CustomMarqueText from '@/components/reusable/MarqueComponent/CustomMarqueText';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import SpinnerLoader from '@/components/reusable/SpinnerLoader/SpinnerLoader';
import {
  useGetDashboardLastSevenDaysSummeryQuery,
  useGetDashboardUpdatesQuery,
} from '@/redux/api/warehouseApis/dashboardApis';
import { useGetShopSummeryReportQuery } from '@/redux/api/warehouseApis/reportsApis';
import { ROUTES } from '@/Routes';
import { generateDateString } from '@/utils/generateDateFormat';
import { generateFilterParams } from '@/utils/generateFilterParams';

interface Props {
  warehouseId: string;
  viewFrom?: string;
}

function DashboardPageOverview({ warehouseId, viewFrom }: Props) {
  const router = new URLSearchParams(useLocation().search);
  const navigate = useNavigate();
  const filterType = router.get('filterType');
  const startDate =
    router.get('startDate') ??
    generateDateString(
      new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toString(),
    );
  const endDate =
    router.get('endDate') ?? generateDateString(new Date().toString());

  const handleFilter = (fieldName: string, value: string) => {
    const query = generateFilterParams(fieldName, value);
    if (viewFrom === 'org') {
      navigate(ROUTES.ORGANIZATION.DASHBOARD_FILTER(query));
    } else {
      navigate(ROUTES.WAREHOUSE.DASHBOARD(warehouseId, query));
    }
  };

  // Local state for custom filter dates
  const [customStartDate, setCustomStartDate] = useState(startDate);
  const [customEndDate, setCustomEndDate] = useState(endDate);

  // Update local state when filter type changes away from custom
  useEffect(() => {
    if (filterType !== 'custom') {
      setCustomStartDate(startDate);
      setCustomEndDate(endDate);
    }
  }, [filterType, startDate, endDate]);

  const { data: dashboardUpdates } = useGetDashboardUpdatesQuery({
    warehouseId,
    type: 'daily',
  });

  const {
    data: dashboardSalesSummery,
    isLoading: isDashboardSalesSummeryLoading,
    isFetching: isDashboardSalesSummeryFetching,
  } = useGetDashboardLastSevenDaysSummeryQuery({
    warehouseId,
    type: filterType ?? 'last7days',
    shopId: null,
    startDate: filterType === 'custom' ? startDate : undefined,
    endDate: filterType === 'custom' ? endDate : undefined,
  });

  const {
    data: shopWiseData,
    isLoading: isSummaryLoading,
    isFetching: isSummaryFetching,
  } = useGetShopSummeryReportQuery({
    warehouseId,
    type: filterType ?? 'last7days',
    startDate: filterType === 'custom' ? startDate : undefined,
    endDate: filterType === 'custom' ? endDate : undefined,
  });

  // Lazy load TopTenProductCards and TopTenCustomersList
  const [showTopTen, setShowTopTen] = useState(false);
  const topTenRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const observer = new window.IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          setShowTopTen(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 },
    );
    if (topTenRef.current) {
      observer.observe(topTenRef.current);
    }
    return () => observer.disconnect();
  }, []);

  return (
    <div>
      <div className="mb-2 bg-orange-600 p-2 font-bold text-white">
        <CustomMarqueText />
      </div>
      <div className="mb-4">
        <DashboardUpdatesSection dashboardUpdates={dashboardUpdates} />
      </div>
      <div className="mb-4">
        <StatusWiseSummaryForDashboard
          type="warehouse"
          warehouseId={warehouseId}
        />
      </div>
      <div className="my-4 flex items-center justify-end gap-6 rounded-lg bg-white p-4">
        {filterType === 'custom' ? (
          <>
            <div className="w-[150px]">
              <CustomDateFilterInput
                value={customStartDate ?? ''}
                placeholder="Select Start Date"
                label="Start Date"
                handleChange={(value: string) => setCustomStartDate(value)}
              />
            </div>
            <div className="w-[150px]">
              <CustomDateFilterInput
                value={customEndDate ?? ''}
                placeholder="Select End Date"
                label="End Date"
                handleChange={(value: string) => setCustomEndDate(value)}
                minimumDate={customStartDate ?? ''}
              />
            </div>
            <CustomSearchButton
              handleSearch={() => {
                handleFilter('startDate', customStartDate ?? '');
                handleFilter('endDate', customEndDate ?? '');
              }}
              isSearchEnabled={
                Boolean(customStartDate.length) || Boolean(customEndDate.length)
              }
            />
          </>
        ) : (
          ''
        )}
        <div className="w-[200px]">
          <CustomSelectForFilter
            options={[
              // { label: 'Weekly', value: 'weekly' },
              { label: 'Last 7 Days', value: 'last7days' },
              { label: 'Current Month', value: 'monthly' },
              { label: 'Custom', value: 'custom' },
            ]}
            selectedValue={filterType ?? 'last7days'}
            handleSelect={(e) => handleFilter('filterType', e)}
            placeHolder="Summery Filter"
            hideAllOption
          />
        </div>
      </div>
      <div className="mb-6 grid grid-cols-1 gap-5 lg:grid-cols-1 xl:grid-cols-1">
        <div className="rounded-md bg-white p-5 shadow-xl">
          <div className="flex items-center justify-center">
            {!isDashboardSalesSummeryLoading &&
            !isDashboardSalesSummeryFetching ? (
              <WeeklySalesSummeryChart
                chartData={dashboardSalesSummery}
                mrpTotal={shopWiseData?.data?.totalSubtotal}
                filterType={
                  (filterType as
                    | 'weekly'
                    | 'last7days'
                    | 'monthly'
                    | 'custom') ?? 'last7days'
                }
              />
            ) : (
              <SpinnerLoader />
            )}
          </div>
        </div>
      </div>
      <div>
        {!isSummaryLoading && !isSummaryFetching ? (
          <ShopWiseSalesSummary shopWiseData={shopWiseData} />
        ) : (
          <TableSkeletonLoader tableColumn={9} tableRow={2} />
        )}
      </div>
      <div
        className="mb-6 grid grid-cols-1 gap-5 xl:grid-cols-2"
        ref={topTenRef}
      >
        {showTopTen && (
          <>
            <TopTenProductCards type="warehouse" warehouseId={warehouseId} />
            <TopTenCustomersList type="warehouse" warehouseId={warehouseId} />
          </>
        )}
      </div>
    </div>
  );
}

export default DashboardPageOverview;
