import { useParams } from 'react-router-dom';

import OrdersPageOverview from '@/components/WarehouseComponents/OrdersPageComponents/OrdersPageOverview';
import { ProtectedRoute } from '@/utils/ProtectedRoutes';

function WholeSaleOrdersPage() {
  const { warehouseId } = useParams();
  return (
    <ProtectedRoute>
      <OrdersPageOverview
        warehouseId={warehouseId ?? ''}
        orderType="Wholesale"
      />
    </ProtectedRoute>
  );
}

export default WholeSaleOrdersPage;
