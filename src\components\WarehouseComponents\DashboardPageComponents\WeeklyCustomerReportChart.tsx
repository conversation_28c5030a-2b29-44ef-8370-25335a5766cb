import ReactApexChart from 'react-apexcharts';

import { SingleDayCustomerSummery } from '@/types/warehouseTypes/dashboardTypes';

interface Props {
  weeklyData?: SingleDayCustomerSummery[];
}

function WeeklyCustomerReportChart({ weeklyData }: Props) {
  const series = [
    {
      name: 'Total',
      data: weeklyData?.length
        ? weeklyData?.map((single: SingleDayCustomerSummery) =>
            Number(single.totalOrders),
          )
        : [],
    },
    {
      name: 'Regular',
      data: weeklyData?.length
        ? weeklyData?.map((single: SingleDayCustomerSummery) =>
            Number(single.regular),
          )
        : [],
    },
    {
      name: 'General',
      data: weeklyData?.length
        ? weeklyData?.map((single: SingleDayCustomerSummery) =>
            Number(single.general),
          )
        : [],
    },
    {
      name: 'Premium',
      data: weeklyData?.length
        ? weeklyData?.map((single: SingleDayCustomerSummery) =>
            Number(single.premium),
          )
        : [],
    },
  ];
  const options = {
    options: {
      chart: {
        height: 350,
        // type: 'area',
      },
      dataLabels: {
        enabled: true,
      },
      stroke: {
        curve: 'smooth' as 'smooth', // Explicitly assert the type here
      },
      xaxis: {
        type: 'datetime' as 'datetime',
        categories: weeklyData?.length
          ? weeklyData?.map((single: SingleDayCustomerSummery) => single.date)
          : [],
      },
      tooltip: {
        x: {
          format: 'dd/MM/yy',
        },
      },
      fill: {
        type: 'solid',
        opacity: 0,
      },
      title: {
        text: 'Weekly Customers Summery',
      },
    },
  };

  return (
    <ReactApexChart
      options={options.options}
      series={series}
      type="area"
      height={350}
    />
  );
}

export default WeeklyCustomerReportChart;
