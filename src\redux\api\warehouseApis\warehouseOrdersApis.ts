import BaseApi from '../baseApi';

import { TagTypes } from '@/redux/tag-types';
import {
  GetShopOrderDetailsResponse,
  GetShopOrdersListResponse,
} from '@/types/shopTypes/shopOrderTypes';

interface GetWarehouseOrdersParams {
  warehouseId?: string;
  shopId?: string;
  isBooked?: boolean;
  serialNo?: string;
  customerName?: string;
  customerId?: string;
  page?: string;
  limit?: string;
  mobileNumber?: string;
  orderType?: string;
  startDate?: string;
  endDate?: string;
  orderStatus?: string;
  productId?: string;
  hasDue?: boolean;
}

const WarehouseOrdersApi = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getWarehouseOrders: builder.query<
      GetShopOrdersListResponse,
      GetWarehouseOrdersParams
    >({
      query: (params) => ({
        url: '/order',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP_ORDERS],
    }),
    getOrderDetails: builder.query<GetShopOrderDetailsResponse, any>({
      query: (id) => ({
        url: `/order/${id}`,
        method: 'GET',
      }),
      providesTags: [TagTypes.SHOP_ORDERS],
    }),
    createOrderPayment: builder.mutation({
      query: (data) => ({
        url: '/order/payment/new',
        method: 'POST',
        data,
      }),
      invalidatesTags: [TagTypes.SHOP_ORDERS],
    }),
    getReturnOrdersList: builder.query<GetShopOrdersListResponse, any>({
      query: (params) => ({
        url: `/return`,
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP_ORDERS],
    }),
  }),
});

export const {
  useGetWarehouseOrdersQuery,
  useGetOrderDetailsQuery,
  useCreateOrderPaymentMutation,
  useGetReturnOrdersListQuery,
} = WarehouseOrdersApi;
