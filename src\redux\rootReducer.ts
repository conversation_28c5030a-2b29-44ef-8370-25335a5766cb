import BaseApi from './api/baseApi';
import shopSettingsReducer from './slice/shopSettingsSlice';
import shopReducer from './slice/storeSlice';
import userReducer from './slice/userSlice';
import warehouseReducer from './slice/warehouseSlice';

export const RootReducer = {
  [BaseApi.reducerPath]: BaseApi.reducer,
  shopDetails: shopReducer,
  userDetails: userReducer,
  shopSettings: shopSettingsReducer,
  warehouseDetails: warehouseReducer,
  // orderWebsocket: orderWebsocketReducer,
};
