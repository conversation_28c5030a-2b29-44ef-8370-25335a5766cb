import { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

import AddOrEditSupplierModal from './AddOrEditSupplierModal';
import SupplierPaymentModal from './SupplierPaymentModal';
import SuppliersFilterOptions from './SuppliersFilterOptions';
import SuppliersPageFilterModal from './SuppliersPageFilterModal';

import {
  DeleteButton,
  EditButton,
  EyeButton,
  PayButton,
} from '@/components/reusable/Buttons/CommonButtons';
import FilledButton from '@/components/reusable/Buttons/FilledButton';
import FilterButton from '@/components/reusable/Buttons/FilterButton';
import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import DeleteModal from '@/components/reusable/DeleteModal/DeleteModal';
import Modal from '@/components/reusable/Modal/Modal';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import Pagination from '@/components/reusable/Pagination/Pagination';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import {
  useDeleteSupplierMutation,
  useGetSuppliersQuery,
} from '@/redux/api/warehouseApis/suppliersApi';
import { ROUTES } from '@/Routes';
import { SingleSupplier } from '@/types/warehouseTypes/suppliersTypes';
import { generateFilterParams } from '@/utils/generateFilterParams';

interface Props {
  warehouseId: string;
}

function SuppliersPageOverview({ warehouseId }: Props) {
  const navigate = useNavigate();
  const router = new URLSearchParams(useLocation().search);
  const name = router.get('name');
  const mobileNumber = router.get('mobileNumber');
  const page = router.get('page');
  const limit = router.get('limit');

  const { data, isLoading, refetch } = useGetSuppliersQuery({
    warehouseId,
    page: page ?? '1',
    name: name ?? undefined,
    mobileNumber: mobileNumber ?? undefined,
    limit: limit ?? '10',
  });

  const [deleteSupplier] = useDeleteSupplierMutation();
  const [isCreateSupplierModalOpen, setIsCreateSupplierModalOpen] =
    useState<boolean>(false);
  const [isEditSupplierModalOpen, setIsEditSupplierModalOpen] = useState(false);
  const [selectedSupplier, setSelectedSupplier] = useState<SingleSupplier>();
  const [isDeleteSupplierModalOpen, setIsDeleteSupplierModalOpen] =
    useState(false);

  const [isSupplierPaymentModalOpen, setIsSupplierPaymentModalOpen] =
    useState(false);

  const [isFilterModalOpen, setIsFilterModalOpen] = useState<boolean>(false);

  const handleDeleteSupplier = async () => {
    toast.promise(deleteSupplier(selectedSupplier?.id).unwrap(), {
      pending: 'Deleting Supplier...',
      success: {
        render({ data: res }) {
          if (res?.statusCode === 200 || res?.statusCode === 201) {
            refetch();
            setIsDeleteSupplierModalOpen(false);
          }
          return 'Supplier Deleted Successfully';
        },
      },
      error: {
        render({ data: error }) {
          console.log(error);
          return 'Error on delete Supplier';
        },
      },
    });
  };

  const handleFilter = (fieldName: string, value: string) => {
    const query = generateFilterParams(fieldName, value);
    navigate(ROUTES.WAREHOUSE.SUPPLIERS(warehouseId, query));
  };

  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-2">
        <div className="flex items-center">
          <div className="search-title-and-btn flex items-center">
            <div className="relative">
              <div className="block xl:hidden">
                <FilterButton
                  handleClick={() => setIsFilterModalOpen(!isFilterModalOpen)}
                />
              </div>
              <div
                className={`${isFilterModalOpen ? 'block' : 'hidden'} xl:hidden`}
              >
                <SuppliersPageFilterModal />
              </div>
            </div>
          </div>
          <div className="hidden xl:block">
            <div className="flex items-center gap-x-2">
              <SuppliersFilterOptions handleFilter={handleFilter} />
            </div>
          </div>
        </div>
        <div>
          <FilledButton
            isLoading={false}
            text="Add New Supplier"
            handleClick={() => setIsCreateSupplierModalOpen(true)}
            isDisabled={false}
          />
        </div>
      </div>
      {!isLoading ? (
        <div>
          <div className="tableTop w-full">
            <p>Suppliers</p>
            <p>Total : {data?.pagination?.total}</p>
          </div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {data?.data?.length ? (
              <div className="full-table-box h-custom">
                <table className="full-table">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="tableHead">No</th>
                      <th className="tableHead table-col-width">Name</th>
                      <th className="tableHead">Phone Number</th>
                      <th className="tableHead">Total Amount</th>
                      <th className="tableHead">Paid</th>
                      <th className="tableHead">Due</th>
                      <th className="tableHead">Created At</th>
                      <th className="tableHead">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y bg-slate-200">
                    {data?.data?.map(
                      (supplier: SingleSupplier, index: number) => (
                        <tr key={supplier?.id}>
                          <td className="tableData">{index + 1}</td>
                          <td className="tableData table-col-width">
                            {supplier?.User?.name}
                          </td>
                          <td className="tableData">
                            {supplier?.User?.mobileNumber}
                          </td>
                          <td className="tableData">{supplier?.totalAmount}</td>
                          <td className="tableData">{supplier?.totalPaid}</td>
                          <td className="tableData">{supplier?.totalDue}</td>
                          <td className="tableData">
                            <DateAndTimeViewer date={supplier?.createdAt} />
                          </td>
                          <td className="tableData">
                            <div className="flex items-center justify-center gap-2">
                              <PayButton
                                handleClick={() => {
                                  setSelectedSupplier(supplier);
                                  setIsSupplierPaymentModalOpen(true);
                                }}
                              />
                              <EditButton
                                handleClick={() => {
                                  setSelectedSupplier(supplier);
                                  setIsEditSupplierModalOpen(true);
                                }}
                              />
                              <DeleteButton
                                handleClick={() => {
                                  setIsDeleteSupplierModalOpen(true);
                                  setSelectedSupplier(supplier);
                                }}
                              />
                              <EyeButton
                                handleClick={() =>
                                  navigate(
                                    ROUTES.WAREHOUSE.SUPPLIER_DETAILS(
                                      warehouseId,
                                      supplier.id,
                                    ),
                                  )
                                }
                              />
                            </div>
                          </td>
                        </tr>
                      ),
                    )}
                  </tbody>
                </table>
              </div>
            ) : (
              <NoResultFound pageType="supplier" />
            )}
          </div>
          <div className="pagination-box flex justify-end rounded bg-white p-3">
            <Pagination
              currentPage={page ?? '1'}
              limit={Number(limit ?? 10)}
              handleFilter={(fieldName: string, value: any) =>
                handleFilter(fieldName, value)
              }
              totalCount={data?.pagination?.total}
              totalPages={Math.ceil(
                Number(data?.pagination?.total) /
                  Number(data?.pagination?.limit),
              )}
            />
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={8} tableRow={6} />
      )}
      <Modal
        setShowModal={setIsCreateSupplierModalOpen}
        showModal={isCreateSupplierModalOpen}
      >
        <AddOrEditSupplierModal
          type="new"
          warehouseId={warehouseId}
          handleClose={() => setIsCreateSupplierModalOpen(false)}
          updateRefreshCounter={refetch}
        />
      </Modal>
      <Modal
        setShowModal={setIsEditSupplierModalOpen}
        showModal={isEditSupplierModalOpen}
      >
        <AddOrEditSupplierModal
          type="edit"
          warehouseId={warehouseId}
          handleClose={() => setIsEditSupplierModalOpen(false)}
          supplierData={selectedSupplier}
          updateRefreshCounter={refetch}
        />
      </Modal>
      <Modal
        setShowModal={setIsDeleteSupplierModalOpen}
        showModal={isDeleteSupplierModalOpen}
      >
        <DeleteModal
          type="Supplier"
          name={selectedSupplier?.User?.name ?? ''}
          handleClose={() => setIsDeleteSupplierModalOpen(false)}
          handleDelete={handleDeleteSupplier}
        />
      </Modal>
      <Modal
        setShowModal={setIsSupplierPaymentModalOpen}
        showModal={isSupplierPaymentModalOpen}
      >
        <SupplierPaymentModal
          handleClose={() => setIsSupplierPaymentModalOpen(false)}
          updateRefreshCounter={refetch}
          supplierDetails={selectedSupplier}
        />
      </Modal>
    </div>
  );
}

export default SuppliersPageOverview;
