import { useFormik } from 'formik';
import Cookies from 'js-cookie';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import * as Yup from 'yup';

import FilledButton from '@/components/reusable/Buttons/FilledButton';
import FilledSubmitButton from '@/components/reusable/Buttons/FilledSubmitButton';
import CustomDropdown from '@/components/reusable/CustomInputField/CustomDropdown';
import CustomInputField from '@/components/reusable/CustomInputField/CustomInputField';
import CustomTextArea from '@/components/reusable/CustomInputField/CustomTextArea';
import ImageSelector from '@/components/reusable/ImageSelector/ImageSelector';
import ModalTitle from '@/components/reusable/Modal/ModalTitle';
import {
  useCreateNewShopCustomerMutation,
  useGetShopCustomerDetailsByPhoneQuery,
} from '@/redux/api/shopApis/shopCustomersApi';
import { useAppSelector } from '@/redux/hooks';
import { ROUTES } from '@/Routes';
import { UploadImageOnAws } from '@/utils/ImageUploadModule';
import { divisionsWithDistrict, divisionTypes } from '@/utils/staticData';

interface Props {
  type: string;
  shopId: string;
  handleClose: () => void;
  updateRefreshCounter: () => void;
  // brandData?: SingleBrandDetails;
}

const formikInitialValues = {
  name: null,
  district: null,
  division: null,
  address: null,
  zipCode: null,
  mobileNumber: '',
  email: null,
  gender: null,
};

const validation = Yup.object({
  name: Yup.string().required('Customer name is required'),
  mobileNumber: Yup.string().required('Phone Number is required'),
});

function AddOrEditCustomerModal({
  type,
  handleClose,
  updateRefreshCounter,
  shopId,
}: Props) {
  const navigate = useNavigate();
  const userDetailsFromState = useAppSelector((state) => state.userDetails);
  const [districts, setDistricts] = useState<string[]>();
  const [afterRegistrationOption, setAfterRegistrationOption] =
    useState<string>('go-details');
  const [currentFile, setCurrentFile] = useState();

  const [createNewShopCustomer, { isLoading }] =
    useCreateNewShopCustomerMutation();

  const formik = useFormik({
    initialValues: formikInitialValues,
    validationSchema: validation,

    onSubmit: async (values) => {
      if (type === 'new') {
        const imgUrl = currentFile ? await UploadImageOnAws(currentFile) : null;
        toast.promise(
          createNewShopCustomer({
            name: values.name,
            mobileNumber: values.mobileNumber,
            address: values.address,
            zipCode: values.zipCode,
            district: values.district,
            email: values.email,
            gender: values?.gender,
            imgUrl,
            organizationId: Cookies.get('organizationId'),
          }).unwrap(),
          {
            pending: 'Registering New Customer...',
            success: {
              render({ data: res }) {
                if (res?.statusCode === 200 || res?.statusCode === 201) {
                  updateRefreshCounter();
                  if (afterRegistrationOption === 'go-billing') {
                    // add customer id on second param from response
                    navigate(ROUTES.SHOP.ADD_NEW_ORDER(shopId, res?.data?.id));
                  }
                  if (afterRegistrationOption === 'go-details') {
                    navigate(
                      ROUTES.SHOP.CUSTOMER_DETAILS(shopId, res?.data?.id),
                    );
                  }
                  handleClose();
                }
                return 'Customer Registered Successfully';
              },
            },
            error: {
              render({ data: error }) {
                console.log(error);
                return 'Error on customer registration';
              },
            },
          },
        );
      }
      /* else {
        toast.promise(
          updateBrand({
            data: { name: values.name },
            id: brandData?.id,
          }),
          {
            pending: 'Updating Brand...',
            success: {
              render({ data: res }) {
                if (
                  res.data?.statusCode === 200 ||
                  res.data?.statusCode === 201
                ) {
                  updateRefreshCounter();
                  handleClose();
                }
                return 'Brand updated Successfully';
              },
            },
            error: {
              render({ data: error }) {
                console.log(error);
                return 'Error on update brand';
              },
            },
          },
        );
      } */
    },
  });

  const { data: customerData, isLoading: isCustomerDataLoading } =
    useGetShopCustomerDetailsByPhoneQuery(
      {
        phone: formik.values.mobileNumber,
        organizationId: Cookies.get('organizationId'),
      },
      {
        skip: formik.values.mobileNumber?.length !== 11,
      },
    );

  useEffect(() => {
    if (customerData?.data) {
      formik.setFieldValue('name', customerData?.data?.name);
      formik.setFieldValue('mobileNumber', customerData?.data?.mobileNumber);
      formik.setFieldValue('address', customerData?.data?.address);
      formik.setFieldValue('email', customerData?.data?.email);
      formik.setFieldValue('gender', customerData?.data?.gender);
      formik.setFieldValue('customerId', customerData?.data?.id);
    }
  }, [customerData]);

  useEffect(() => {
    if (formik.values.division) {
      const selected = divisionsWithDistrict?.find(
        (sin: divisionTypes) => sin.division === formik.values.division,
      );
      setDistricts(selected?.districts);
    }
    // eslint-disable-next-line react-hooks/rules-of-hooks
  }, [formik.values.division]);
  return (
    <div className="flex w-[350px] flex-col gap-4 rounded-xl bg-white p-4 sm:w-[550px]">
      {isCustomerDataLoading && (
        <div className="absolute inset-0 z-10 flex items-center justify-center rounded bg-white bg-opacity-75">
          <div className="text-xl font-bold">Loading...</div>
        </div>
      )}
      <ModalTitle
        text={type === 'new' ? 'Register New Customer' : 'Edit Customer'}
        handleClose={handleClose}
      />
      <form
        onSubmit={formik.handleSubmit}
        className="flex w-full flex-col gap-4"
      >
        <div className="flex w-full gap-2">
          {userDetailsFromState.shopType === 'ONLINE' ? (
            <div className="w-[150px] sm:w-[130px]">
              <ImageSelector
                previousImage=""
                setNewImage={(e) => setCurrentFile(e)}
              />
            </div>
          ) : (
            ''
          )}
          <div className="flex w-full flex-col gap-2">
            <CustomInputField
              type="text"
              placeholder="Enter Phone Number"
              name="mobileNumber"
              label="Phone"
              formik={formik}
            />
            <CustomInputField
              type="text"
              placeholder="Enter Customer Name"
              name="name"
              label="Name"
              formik={formik}
            />
          </div>
        </div>
        <CustomInputField
          type="email"
          placeholder="Enter Customer Email"
          name="email"
          label="Email (Optional)"
          formik={formik}
        />
        <CustomTextArea
          placeholder="Enter Customer Address"
          name="address"
          label="Address (Optional)"
          formik={formik}
        />
        {userDetailsFromState.shopType === 'ONLINE' ? (
          <>
            <div className="flex items-center gap-2">
              <CustomDropdown
                placeholder="Select Division"
                name="division"
                label="Division"
                formik={formik}
                options={divisionsWithDistrict?.map((single: divisionTypes) => {
                  return {
                    value: single.division,
                    label: single.division,
                  };
                })}
              />
              <CustomDropdown
                placeholder="Select District"
                name="district"
                label="District"
                formik={formik}
                options={districts?.map((district: string) => {
                  return {
                    value: district,
                    label: district,
                  };
                })}
              />
            </div>
            <div className="flex items-center gap-2">
              <CustomInputField
                type="text"
                placeholder="Post Code"
                name="zipCode"
                label="Post Code"
                formik={formik}
              />
              <CustomDropdown
                placeholder="Select Gender"
                name="gender"
                label="Gender"
                formik={formik}
                options={[
                  { value: 'MALE', label: 'Male' },
                  { value: 'FEMALE', label: 'Female' },
                  { value: 'OTHERS', label: 'Others' },
                ]}
              />
            </div>
          </>
        ) : (
          ''
        )}
        {type === 'new' ? (
          <div>
            <div className="text-center font-semibold">
              After Registration Customer
            </div>
            <div className="ml-2 mt-2 flex items-center gap-4">
              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  id="stay-here"
                  name="stay-here"
                  value="stay"
                  checked={afterRegistrationOption === 'go-details'}
                  onClick={() => setAfterRegistrationOption('go-details')}
                  className="form-radio h-4 w-4 text-blue-600 transition duration-150 ease-in-out"
                />
                <button
                  type="button"
                  // htmlFor="stay-here"
                  className="cursor-pointer select-none text-gray-700"
                  onClick={() => setAfterRegistrationOption('go-details')}
                >
                  Go To Customer Details
                </button>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  id="stay-here"
                  name="stay-here"
                  value="stay"
                  checked={afterRegistrationOption === 'go-billing'}
                  onClick={() => setAfterRegistrationOption('go-billing')}
                  className="form-radio h-4 w-4 text-blue-600 transition duration-150 ease-in-out"
                />
                <button
                  type="button"
                  // htmlFor="stay-here"
                  className="cursor-pointer select-none text-gray-700"
                  onClick={() => setAfterRegistrationOption('go-billing')}
                >
                  Go To Billing
                </button>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  id="stay-here"
                  name="stay-here"
                  value="stay"
                  checked={afterRegistrationOption === 'stay-here'}
                  onClick={() => setAfterRegistrationOption('stay-here')}
                  className="form-radio h-4 w-4 text-blue-600 transition duration-150 ease-in-out"
                />
                <button
                  type="button"
                  // htmlFor="stay-here"
                  className="cursor-pointer select-none text-gray-700"
                  onClick={() => setAfterRegistrationOption('stay-here')}
                >
                  Stay On This Page
                </button>
              </div>
            </div>
          </div>
        ) : (
          ''
        )}
        <div className="mt-[10px] flex w-full items-center justify-center">
          {customerData?.data?.id ? (
            <FilledButton
              text="Go To Billing"
              handleClick={() => {
                navigate(
                  ROUTES.SHOP.ADD_NEW_ORDER(shopId, customerData?.data?.id),
                );
                handleClose();
              }}
              isDisabled={false}
              isLoading={false}
            />
          ) : (
            <FilledSubmitButton
              text={type === 'new' ? 'Register New Customer' : 'Done Editing'}
              isLoading={isLoading}
            />
          )}
        </div>
      </form>
    </div>
  );
}

export default AddOrEditCustomerModal;
