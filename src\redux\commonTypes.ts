export interface Pagination {
  page: number;
  limit: number;
  total: number;
}

export interface CreatedBy {
  id: string;
  createdAt: string;
  updatedAt: string;
  name: string;
  email: string;
  username: string;
  mobileNumber: string;
  password: string;
  refreshToken: string;
  imgUrl: any;
  organizationLimit: number;
  warehouseLimit: number;
  shopLimit: number;
  type: string;
}

export interface AddressDetailsType {
  id: string;
  createdAt: string;
  updatedAt: string;
  street: string;
  district: string;
  zipCode: any;
  division: string;
  country: string;
  orderId: string;
}

export type shopOrWarehouse = 'WAREHOUSE' | 'SHOP';

