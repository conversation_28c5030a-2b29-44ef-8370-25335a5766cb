import { Document, PDFViewer } from '@react-pdf/renderer';
import { useLocation } from 'react-router-dom';

import SingleStockBarcodePdf from '@/components/reusable/BarcodePdf/SingleStockBarcodePdf';
import SpinnerLoader from '@/components/reusable/SpinnerLoader/SpinnerLoader';
import { useGetSingleProductBarcodeListQuery } from '@/redux/api/warehouseApis/purchaseApis';
import { SingleBarcodeDetails } from '@/types/warehouseTypes/purchaseTypes';

function SingleProductBarcodesPage() {
  const searchParams = new URLSearchParams(useLocation().search);
  const invoiceId = searchParams.get('invoiceId') ?? '';
  const warehouseId = searchParams.get('warehouseId') ?? '';
  const { data, isLoading } = useGetSingleProductBarcodeListQuery({
    id: invoiceId,
    warehouseId,
  });

  return (
    <div className="flex w-full flex-col items-center justify-center">
      <div className="h-[40px] pb-4 pt-2 text-xl font-bold">
        <h2>{data?.data?.length} Barcode Loaded</h2>
      </div>
      {!isLoading ? (
        <PDFViewer style={{ height: `calc(100vh - 40px)`, width: '100%' }}>
          <Document>
            {data?.data?.map((singleBarcode: SingleBarcodeDetails) => (
              <>
                {!singleBarcode?.isHold && !singleBarcode?.isSold ? (
                  <SingleStockBarcodePdf
                    key={singleBarcode.barcode}
                    barcode={singleBarcode?.barcode.toString()}
                    name={singleBarcode?.name ?? singleBarcode?.productName}
                    price={singleBarcode?.retailPrice.toString()}
                  />
                ) : (
                  ''
                )}
              </>
            ))}
          </Document>
        </PDFViewer>
      ) : (
        <SpinnerLoader />
      )}
    </div>
  );
}

export default SingleProductBarcodesPage;
