import { useParams } from 'react-router-dom';

import ShopStockEntryRequestPageOverview from '@/components/ShopComponents/ShopStockEntryRequestPageComponents/ShopStockEntryRequestPageOverview';
import { ProtectedRoute } from '@/utils/ProtectedRoutes';

function StockTransferHistoryPage() {
  const { warehouseId } = useParams();
  return (
    <ProtectedRoute>
      <ShopStockEntryRequestPageOverview
        warehouseId={warehouseId ?? ''}
        isWarehouse
        isShop={false}
      />
    </ProtectedRoute>
  );
}

export default StockTransferHistoryPage;
