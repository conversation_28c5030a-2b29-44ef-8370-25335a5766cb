import { useParams } from 'react-router-dom';

import StockPullPageOverview from '@/components/WarehouseComponents/StockListPageComponents/StockPullPageOverview';
import { ProtectedRoute } from '@/utils/ProtectedRoutes';

function StockPullPage() {
  const { warehouseId } = useParams();
  return (
    <ProtectedRoute>
      <StockPullPageOverview warehouseId={warehouseId ?? ''} />
    </ProtectedRoute>
  );
}

export default StockPullPage;
