import { Document, pdf } from '@react-pdf/renderer';
import { useState } from 'react';

import ShopCourierBookingModal from '../ShopCourierBooking/ShopCourierBookingModal';

import ShopAddNewParcelPageFilterModal from './ShopAddNewParcelPageFilterModal';

import {
  CourierButton,
  PrintA4Button,
} from '@/components/reusable/Buttons/CommonButtons';
import FilterButton from '@/components/reusable/Buttons/FilterButton';
import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import SearchInput from '@/components/reusable/Inputs/SearchInput';
import Modal from '@/components/reusable/Modal/Modal';
import Pagination from '@/components/reusable/Pagination/Pagination';
import SingleOrderInvoicePdf from '@/components/reusable/SingeOrderInvoicePdf/SingleOrderInvoicePdf';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import { useGetShopOrdersQuery } from '@/redux/api/shopApis/shopOrdersApis';
import { ShopOrderDetails } from '@/types/shopTypes/shopOrderTypes';

interface Props {
  shopId: string;
}
function ShopAddNewParcelPageOverview({ shopId }: Props) {
  const [isFilterModalOpen, setIsFilterModalOpen] = useState<boolean>(false);
  const [isCourierBookingModalOpen, setIsCourierBookingModalOpen] =
    useState<boolean>(false);
  const [courierBookingOrderDetails, setCourierBookingOrderDetails] =
    useState<ShopOrderDetails>();
  const { data, isLoading } = useGetShopOrdersQuery({
    shopId,
    page: '1',
    limit: '10',
    isBooked: false,
  });

  const handleGeneratePdf = async (order: ShopOrderDetails) => {
    const blob = await pdf(
      <Document>
        <SingleOrderInvoicePdf orderDetails={order} />
      </Document>,
    ).toBlob();

    // Create a URL for the blob and open it in a new window
    const url = URL.createObjectURL(blob);
    window.open(url);
  };

  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-1">
        <div className="flex items-center">
          <div className="search-title-and-btn flex items-center">
            <div className="relative">
              <div className="block xl:hidden">
                <FilterButton
                  handleClick={() => setIsFilterModalOpen(!isFilterModalOpen)}
                />
              </div>
              <div
                className={`${isFilterModalOpen ? 'block' : 'hidden'} xl:hidden`}
              >
                <ShopAddNewParcelPageFilterModal />
              </div>
            </div>
          </div>
          <div className="hidden xl:block">
            <div className="flex items-center gap-x-2">
              <SearchInput
                placeholder="Search by Name"
                handleSubmit={(value: string) => console.log(value)}
              />
              <SearchInput
                placeholder="Search by Brand"
                handleSubmit={(value: string) => console.log(value)}
              />
              <SearchInput
                placeholder="Search by Category"
                handleSubmit={(value: string) => console.log(value)}
              />
              <SearchInput
                placeholder="Search by Sub Category"
                handleSubmit={(value: string) => console.log(value)}
              />
            </div>
          </div>
        </div>
      </div>
      {!isLoading ? (
        <div>
          <div className="tableTop w-full">
            <p>Order List</p>
            <p>Total : {data?.pagination?.total}</p>
          </div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {data?.data?.length ? (
              <div className="full-table-box h-custom">
                <table className="full-table">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="tableHead">No</th>
                      <th className="tableHead">Order No</th>

                      <th className="tableHead table-col-width">Name</th>
                      <th className="tableHead">Phone</th>
                      {/* <th className="tableHead">Address</th> */}
                      <th className="tableHead">Total</th>
                      <th className="tableHead">Paid</th>
                      <th className="tableHead">Due</th>
                      <th className="tableHead">User</th>
                      <th className="tableHead">Seller</th>
                      <th className="tableHead">Payment Method</th>
                      <th className="tableHead">Status</th>
                      <th className="tableHead">Created At</th>
                      <th className="tableHead">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y bg-slate-200">
                    {data?.data?.map(
                      (order: ShopOrderDetails, index: number) => (
                        <tr key={order?.id}>
                          <td className="tableData">{index + 1}</td>
                          <td className="tableData">{order?.serialNo}</td>
                          <td className="tableData table-col-width">
                            {order?.Customer.name}
                          </td>
                          <td className="tableData">
                            {order?.Customer.mobileNumber}
                          </td>
                          {/* <td className="tableData">{order?.address}</td> */}
                          <td className="tableData">{order?.grandTotal}</td>
                          <td className="tableData">{order?.totalPaid}</td>
                          <td className="tableData">{order?.totalDue}</td>
                          <td className="tableData">
                            {order?.CreatedBy?.name ?? '--'}
                          </td>
                          <td className="tableData">
                            {order?.Employee?.User?.name ?? '--'}
                          </td>
                          <td className="tableData">{order?.paymentMethod}</td>
                          <td className="tableData">{order?.orderStatus}</td>
                          <td className="tableData">
                            <DateAndTimeViewer date={order?.createdAt} />
                          </td>
                          <td className="tableData">
                            <div className="flex items-center justify-center gap-2">
                              {!order?.trackingNumber ? (
                                <CourierButton
                                  handleClick={() => {
                                    setCourierBookingOrderDetails(order);
                                    setIsCourierBookingModalOpen(true);
                                  }}
                                />
                              ) : (
                                ''
                              )}
                              <PrintA4Button
                                handleClick={() => {
                                  handleGeneratePdf(order);
                                }}
                              />
                            </div>
                          </td>
                        </tr>
                      ),
                    )}
                  </tbody>
                </table>
              </div>
            ) : (
              <div>
                <h2>No product found</h2>
              </div>
            )}
          </div>
          <div className="pagination-box flex justify-end rounded bg-white p-3">
            <Pagination
              currentPage="1"
              limit={Number(10)}
              handleFilter={(fieldName: string, value: any) =>
                console.log(fieldName, value)
              }
              totalCount={data?.pagination?.total}
              totalPages={Math.ceil(
                Number(data?.pagination?.total) /
                  Number(data?.pagination?.limit),
              )}
            />
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={13} tableRow={6} />
      )}
      <Modal
        showModal={isCourierBookingModalOpen}
        setShowModal={setIsCourierBookingModalOpen}
      >
        <ShopCourierBookingModal
          orderDetails={courierBookingOrderDetails}
          handleClose={() => setIsCourierBookingModalOpen(false)}
        />
      </Modal>
    </div>
  );
}

export default ShopAddNewParcelPageOverview;
