import { TagTypes } from '@/redux/tag-types';
import { GetShopListResponse } from '@/types/shopTypes';
import BaseApi from '../baseApi';

const OrgShopAndWarehouseApi = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    createShop: builder.mutation({
      query: (data) => ({
        url: '/shop/new',
        method: 'POST',
        data,
      }),
      invalidatesTags: [TagTypes.SHOP],
    }),
    updateShop: builder.mutation({
      query: ({ data, id }) => ({
        url: `/shop/${id}`,
        method: 'PATCH',
        data,
      }),
      invalidatesTags: [TagTypes.SHOP],
    }),
    updateWarehouse: builder.mutation({
      query: ({ data, id }) => ({
        url: `/warehouse/${id}`,
        method: 'PATCH',
        data,
      }),
      invalidatesTags: [TagTypes.SHOP],
    }),
    deleteShop: builder.mutation({
      query: (id) => ({
        url: `/shop/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: [TagTypes.SHOP],
    }),
    getOrgWarehouses: builder.query<GetShopListResponse, any>({
      query: (params) => ({
        url: `/orgs/warehouses`,
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP],
    }),
    getOrgShops: builder.query<GetShopListResponse, any>({
      query: (params) => ({
        url: `/orgs/shops`,
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP],
    }),
  }),
});

export const {
  useCreateShopMutation,
  useUpdateShopMutation,
  useUpdateWarehouseMutation,
  useDeleteShopMutation,
  useGetOrgWarehousesQuery,
  useGetOrgShopsQuery,
} = OrgShopAndWarehouseApi;
