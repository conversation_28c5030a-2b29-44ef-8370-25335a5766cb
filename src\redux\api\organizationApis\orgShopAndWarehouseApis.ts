import BaseApi from '../baseApi';

import { TagTypes } from '@/redux/tag-types';
import { GetShopListResponse } from '@/types/shopTypes';

const OrgShopAndWarehouseApi = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    createShop: builder.mutation({
      query: (data) => ({
        url: '/shop/new',
        method: 'POST',
        data,
      }),
      invalidatesTags: [TagTypes.SHOP],
    }),
    updateShop: builder.mutation({
      query: ({ data, id }) => ({
        url: `/shop/${id}`,
        method: 'PATCH',
        data,
      }),
      invalidatesTags: [TagTypes.SHOP],
    }),
    updateWarehouse: builder.mutation({
      query: ({ data, id }) => ({
        url: `/warehouse/${id}`,
        method: 'PATCH',
        data,
      }),
      invalidatesTags: [TagTypes.SHOP],
    }),
    deleteShop: builder.mutation({
      query: (id) => ({
        url: `/shop/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: [TagTypes.SHOP],
    }),
    getOrgWarehouses: builder.query<GetShopListResponse, any>({
      query: (params) => ({
        url: `/warehouse`,
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP],
    }),
    getOrgShops: builder.query<GetShopListResponse, any>({
      query: (params) => ({
        url: `/shop`,
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP],
    }),
  }),
});

export const {
  useCreateShopMutation,
  useUpdateShopMutation,
  useUpdateWarehouseMutation,
  useDeleteShopMutation,
  useGetOrgWarehousesQuery,
  useGetOrgShopsQuery,
} = OrgShopAndWarehouseApi;
