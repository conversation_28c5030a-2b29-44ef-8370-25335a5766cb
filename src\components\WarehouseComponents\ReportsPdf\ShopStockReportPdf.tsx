import { Page, Text, View } from '@react-pdf/renderer';

import PrintedTime from './PrintedTime';
import { reportPdfStyles } from './ReportPdfStyles';
import SoftwareMarketingOnPdf from './SoftwareMarketingOnPdf';

import { ShopDetailsInRedux } from '@/redux/slice/storeSlice';
import {
  ShopStockReportResponse,
  SingleProductStockReport,
} from '@/types/shopTypes/shopStockTransferReportTypes';
import { truncateText } from '@/utils/stringTruncate';

const products = [
  {
    no: 1,
    name: 'Cosrx Advanced Snail 96 Mucin Power Essence 100ML',
    qty: 12,
    purchasePrice: 2930,
    retailPrice: 2930,
  },
];
interface Props {
  stockReportData?: ShopStockReportResponse;
  shopDetails?: ShopDetailsInRedux;
}
function ShopStockReportPdf({ stockReportData, shopDetails }: Props) {
  return (
    <Page size="A4" style={reportPdfStyles.page}>
      <View>
        <View style={reportPdfStyles.header}>
          <Text style={reportPdfStyles.userName}>{shopDetails?.name}</Text>
          <Text style={reportPdfStyles.address}>{shopDetails?.address}</Text>
          <Text style={reportPdfStyles.phone}>{shopDetails?.mobileNumber}</Text>
        </View>
        <PrintedTime />

        <View style={reportPdfStyles.table}>
          <View style={reportPdfStyles.tableRow}>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.shopStockReportCol,
              ]}
            >
              No
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.shopStockReportCol,
                reportPdfStyles.shopStockReportNameCol,
              ]}
            >
              Name
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.shopStockReportCol,
              ]}
            >
              Quantity
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.shopStockReportCol,
              ]}
            >
              Purchase Price
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.tableColLast,
                reportPdfStyles.shopStockReportCol,
              ]}
            >
              Retail Price
            </Text>
          </View>
          {stockReportData?.data.result?.map(
            (product: SingleProductStockReport, index) => (
              <View
                key={product.id}
                style={
                  index === products.length - 1
                    ? [reportPdfStyles.tableRowLast]
                    : [reportPdfStyles.tableRow]
                }
              >
                <Text
                  style={[
                    reportPdfStyles.tableCol,
                    reportPdfStyles.shopStockReportCol,
                  ]}
                >
                  {index + 1}
                </Text>
                <Text
                  style={[
                    reportPdfStyles.tableCol,
                    reportPdfStyles.shopStockReportCol,
                    reportPdfStyles.shopStockReportNameCol,
                  ]}
                >
                  {truncateText(product?.name)}
                </Text>
                <Text
                  style={[
                    reportPdfStyles.tableCol,
                    reportPdfStyles.shopStockReportCol,
                  ]}
                >
                  {product.quantity}
                </Text>
                <Text
                  style={[
                    reportPdfStyles.tableCol,
                    reportPdfStyles.shopStockReportCol,
                  ]}
                >
                  {product.purchasePrice}
                </Text>
                <Text
                  style={[
                    reportPdfStyles.tableCol,
                    reportPdfStyles.shopStockReportCol,
                    reportPdfStyles.tableColLast,
                  ]}
                >
                  {product.retailPrice}
                </Text>
              </View>
            ),
          )}
        </View>

        <View style={reportPdfStyles.tableCalculationContainer}>
          <View style={reportPdfStyles.tableCalculation}>
            <View style={reportPdfStyles.tableSingleCalculation}>
              <Text>Total Quantity:</Text>
              <Text>{stockReportData?.data.totalQuantity}</Text>
            </View>
            <View style={reportPdfStyles.tableSingleCalculation}>
              <Text>Total Purchase Price:</Text>
              <Text>{stockReportData?.data.totalPurchasePrice}</Text>
            </View>
            <View style={reportPdfStyles.tableSingleCalculation}>
              <Text>Total Retail Price:</Text>
              <Text>{stockReportData?.data.totalRetailPrice}</Text>
            </View>
          </View>
        </View>

        <SoftwareMarketingOnPdf />
      </View>
    </Page>
  );
}

export default ShopStockReportPdf;
