import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import TableSkeletonLoaderHalf from '@/components/reusable/SkeletonLoader/TableSkeletionLoaderHalf';
import { useGetShopAccountsSummeryReportQuery } from '@/redux/api/shopApis/shopReportsApis';
import { OrderSinglePaymentDetails } from '@/types/shopTypes/shopOrderTypes';
import {
  SingleCashReceiveDetailsOnReport,
  SingleCashTransferDetailsOnReport,
  SingleExpenseDetailsOnReport,
} from '@/types/shopTypes/shopStockTransferReportTypes';

interface Props {
  shopId: string;
}

function ShopAccountsSummeryPageOverview({ shopId }: Props) {
  const { data, isLoading, isFetching } = useGetShopAccountsSummeryReportQuery({
    shopId,
    type: 'monthly',
  });

  return (
    <div>
      {!isLoading && !isFetching ? (
        <div className="grid grid-cols-12 gap-2">
          <div className="col col-span-6">
            <div className="tableTop w-full">
              <p>Cash Received List</p>
            </div>
            <div className="w-full">
              {data?.data?.cashReceiveList ? (
                <div className="full-table-box h-[230px]">
                  <table className="full-table">
                    <thead className="bg-gray-100">
                      <tr>
                        <th className="tableHead">No</th>
                        <th className="tableHead">Order No</th>
                        <th className="tableHead">Amount</th>
                        <th className="tableHead">Payment Method</th>
                        <th className="tableHead">Transfer Date</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y bg-slate-200">
                      {data?.data?.cashReceiveList?.map(
                        (
                          receiveHistory: SingleCashReceiveDetailsOnReport,
                          index: number,
                        ) => (
                          <tr key={receiveHistory?.id}>
                            <td className="tableData">{index + 1}</td>
                            <td className="tableData">
                              {receiveHistory?.serialNo}
                            </td>
                            <td className="tableData">
                              {receiveHistory?.totalPaid}
                            </td>
                            <td className="tableData">
                              {receiveHistory?.OrderPayment?.map(
                                (singlePayment: OrderSinglePaymentDetails) => (
                                  <div key={singlePayment?.id}>
                                    {singlePayment?.paymentMethod} -{' '}
                                    {singlePayment?.amount}
                                  </div>
                                ),
                              )}
                            </td>
                            <td className="tableData">
                              <DateAndTimeViewer
                                date={receiveHistory.createdAt}
                              />
                            </td>
                          </tr>
                        ),
                      )}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div>
                  <h2>Not found</h2>
                </div>
              )}
            </div>
          </div>
          <div className="col col-span-6">
            <div className="tableTop w-full">
              <p>Expenses List</p>
            </div>
            <div className="w-full">
              {data?.data?.totalExpenseList ? (
                <div className="full-table-box h-[230px]">
                  <table className="full-table">
                    <thead className="bg-gray-100">
                      <tr>
                        <th className="tableHead">No</th>
                        <th className="tableHead">Name</th>
                        <th className="tableHead">Amount</th>
                        <th className="tableHead">Date</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y bg-slate-200">
                      {data?.data?.totalExpenseList?.map(
                        (
                          expenseHistory: SingleExpenseDetailsOnReport,
                          index: number,
                        ) => (
                          <tr key={expenseHistory?.id}>
                            <td className="tableData">{index + 1}</td>
                            <td className="tableData">
                              {expenseHistory?.name}
                            </td>
                            <td className="tableData">
                              {expenseHistory?.amount}
                            </td>
                            <td className="tableData">
                              <DateAndTimeViewer
                                date={expenseHistory.createdAt}
                              />
                            </td>
                          </tr>
                        ),
                      )}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div>
                  <h2>Not found</h2>
                </div>
              )}
            </div>
          </div>
          <div className="col col-span-6">
            <div className="tableTop w-full">
              <p>Cash Transfer List</p>
            </div>
            <div className="w-full">
              {data?.data?.totalCashTransferList ? (
                <div className="full-table-box h-[230px]">
                  <table className="full-table">
                    <thead className="bg-gray-100">
                      <tr>
                        <th className="tableHead">No</th>
                        <th className="tableHead">Received By</th>
                        <th className="tableHead">Transferred By</th>
                        <th className="tableHead">Amount</th>
                        <th className="tableHead">Transfer Date</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y bg-slate-200">
                      {data?.data?.totalCashTransferList?.map(
                        (
                          transferHistory: SingleCashTransferDetailsOnReport,
                          index: number,
                        ) => (
                          <tr key={transferHistory?.id}>
                            <td className="tableData">{index + 1}</td>
                            <td className="tableData">
                              {transferHistory?.transferredTo}
                            </td>
                            <td className="tableData">
                              {transferHistory?.senderId}
                            </td>
                            <td className="tableData">
                              {transferHistory?.amount}
                            </td>
                            <td className="tableData">
                              <DateAndTimeViewer
                                date={transferHistory.createdAt}
                              />
                            </td>
                          </tr>
                        ),
                      )}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div>
                  <h2>Not found</h2>
                </div>
              )}
            </div>
          </div>
          <div className="col col-span-6">
            <div className="tableTop w-full">
              <p>Total Summery</p>
            </div>
            <div className="w-full">
              {data?.data?.totalCashTransferList ? (
                <div className="full-table-box h-[230px]">
                  <table className="full-table">
                    <thead className="bg-gray-100">
                      <tr>
                        <th className="tableHead">No</th>
                        <th className="tableHead">Field</th>
                        <th className="tableHead">Amount</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y bg-slate-200">
                      <tr>
                        <td className="tableData">1</td>
                        <td className="tableData">Total Cash Received</td>
                        <td className="tableData">{data?.data?.cashReceive}</td>
                      </tr>
                      <tr>
                        <td className="tableData">2</td>
                        <td className="tableData">Total Expense</td>
                        <td className="tableData">
                          {data?.data?.totalExpense}
                        </td>
                      </tr>
                      <tr>
                        <td className="tableData">3</td>
                        <td className="tableData">Total Transfer</td>
                        <td className="tableData">
                          {data?.data?.totalCashTransfer}
                        </td>
                      </tr>
                      <tr>
                        <td className="tableData">4</td>
                        <td className="tableData">Available Balance</td>
                        <td className="tableData">
                          {Number(data?.data?.cashReceive) -
                            Number(data?.data?.totalExpense) -
                            Number(data?.data?.totalCashTransfer)}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              ) : (
                <div>
                  <h2>Not found</h2>
                </div>
              )}
            </div>
          </div>
        </div>
      ) : (
        <TableSkeletonLoaderHalf tableColumn={5} tableRow={6} />
      )}
    </div>
  );
}

export default ShopAccountsSummeryPageOverview;
