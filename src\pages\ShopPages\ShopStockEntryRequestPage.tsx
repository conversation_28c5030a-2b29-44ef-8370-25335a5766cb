import { useParams } from 'react-router-dom';

import ShopStockEntryRequestPageOverview from '@/components/ShopComponents/ShopStockEntryRequestPageComponents/ShopStockEntryRequestPageOverview';
import { ProtectedRoute } from '@/utils/ProtectedRoutes';

function ShopStockEntryRequestPage() {
  const { shopId } = useParams();
  return (
    <ProtectedRoute>
      <ShopStockEntryRequestPageOverview
        shopId={shopId ?? ''}
        isShop
        isWarehouse={false}
      />
    </ProtectedRoute>
  );
}

export default ShopStockEntryRequestPage;
