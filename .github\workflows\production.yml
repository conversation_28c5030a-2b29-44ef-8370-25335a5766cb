name: Retail Pilot Production CI/CD

on:
  push:
    branches: [main]

permissions:
  contents: write

jobs:
  build-and-push:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Fetch full history

      # Generate incremental version number
      - name: Generate version
        id: version
        run: |
          # Check if version file exists2
          if [ ! -f .version ]; then
            echo "1" > .version
          fi

          # Read current version
          CURRENT_VERSION=$(cat .version)

          # Increment version
          NEXT_VERSION=$((CURRENT_VERSION + 1))

          # Write new version
          echo "$NEXT_VERSION" > .version

          # Create version tag
          VERSION="v$CURRENT_VERSION"

          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "Generated version: $VERSION"

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Build and push
        uses: docker/build-push-action@v5
        with:
          context: .
          file: Dockerfile.production
          push: true
          tags: |
            ${{ secrets.DOCKER_REPOSITORY }}:latest
            ${{ secrets.DOCKER_REPOSITORY }}:${{ steps.version.outputs.version }}
          build-args: |
            VITE_BASEURL=${{ secrets.VITE_BASEURL }}

      # Commit and push version file back to repository
      - name: Commit version file
        run: |
          git config --global user.name 'GitHub Actions'
          git config --global user.email '<EMAIL>'
          git add .version
          git commit -m "Bump version to ${{ steps.version.outputs.version }}"
          git push

  deploy:
    needs: build-and-push
    runs-on: ubuntu-latest

    steps:
      - name: Install sshpass
        run: sudo apt-get update && sudo apt-get install -y sshpass

      - name: Deploy to Production
        env:
          SERVER_PASSWORD: ${{ secrets.SERVER_PASSWORD }}
        run: |
          # Debugging: Check SSH connection first
          echo "Checking SSH connection to ${{ secrets.SERVER_USERNAME }}@${{ secrets.SERVER_IP }}"
          sshpass -p "$SERVER_PASSWORD" ssh -o StrictHostKeyChecking=no ${{ secrets.SERVER_USERNAME }}@${{ secrets.SERVER_IP }} "echo 'SSH connection successful!'"

          # Pull the latest image
          echo "Pulling the latest Docker image..."
          sshpass -p "$SERVER_PASSWORD" ssh -o StrictHostKeyChecking=no ${{ secrets.SERVER_USERNAME }}@${{ secrets.SERVER_IP }} << 'EOF'
            
            # Pull the latest Docker image
            docker pull ${{ secrets.DOCKER_REPOSITORY }}:latest
            
            # Check if the pull was successful
            if [ $? -eq 0 ]; then
              echo "Docker image pulled successfully."
            else
              echo "Failed to pull Docker image."
              exit 1
            fi

            # Stop existing container if it exists
            docker ps -a --filter "name=retail-pilot-frontend" -q | grep -q . && docker stop retail-pilot-frontend && docker rm retail-pilot-frontend || true

            # Run new container with environment variables
            docker run -d \
              --name retail-pilot-frontend \
              -p 5173:80 \
              ${{ secrets.DOCKER_REPOSITORY }}:latest
          EOF
