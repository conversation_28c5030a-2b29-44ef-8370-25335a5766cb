import { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import CustomSearchButton from '../Buttons/CustomSearchButton';
import CustomDateFilterInput from '../Inputs/CustomDateFilterInput';

function StartDateEndDateWithSearch() {
  const navigate = useNavigate();
  const location = useLocation();
  const router = new URLSearchParams(location.search);
  const startDate = router.get('startDate') || `${new Date().toISOString()}`;
  const endDate = router.get('endDate') || `${new Date().toISOString()}`;

  const [tempStartDate, setTempStartDate] = useState(startDate);
  const [tempEndDate, setTempEndDate] = useState(endDate);
  const [isSearchEnabled, setIsSearchEnabled] = useState(false);

  const handleDateChange = (fieldName: string, value: string) => {
    if (fieldName === 'startDate') {
      setTempStartDate(value);
    } else {
      setTempEndDate(value);
    }
    setIsSearchEnabled(true);
  };

  const handleSearch = () => {
    router.set('startDate', tempStartDate);
    router.set('endDate', tempEndDate);
    navigate(`${location.pathname}?${router.toString()}`);
    setIsSearchEnabled(false);
  };

  return (
    <div className="flex items-center gap-x-2">
      <CustomDateFilterInput
        value={tempStartDate}
        placeholder="Select Start Date"
        label="Start Date"
        handleChange={(value: string) => handleDateChange('startDate', value)}
      />
      <CustomDateFilterInput
        value={tempEndDate}
        placeholder="Select End Date"
        label="End Date"
        handleChange={(value: string) => handleDateChange('endDate', value)}
        minimumDate={tempStartDate}
      />
      <CustomSearchButton
        handleSearch={handleSearch}
        isSearchEnabled={isSearchEnabled}
      />
    </div>
  );
}

export default StartDateEndDateWithSearch;
