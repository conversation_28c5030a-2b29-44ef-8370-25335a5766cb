import { useFormik } from 'formik';
import Cookies from 'js-cookie';
import { X } from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import * as Yup from 'yup';

import FilledSubmitButton from '../../reusable/Buttons/FilledSubmitButton';
import CustomDropdown from '../../reusable/CustomInputField/CustomDropdown';
import CustomInputField from '../../reusable/CustomInputField/CustomInputField';
import ModalTitle from '../../reusable/Modal/ModalTitle';

import ImageSelector from '@/components/reusable/ImageSelector/ImageSelector';
import { Badge } from '@/components/ui/badge';
import {
  useCreateProductMutation,
  useUpdateProductMutation,
} from '@/redux/api/warehouseApis/productsApi';
import { SingleCategoryDetails } from '@/types/warehouseTypes/categoriesTypes';
import { SingleProductDetails } from '@/types/warehouseTypes/productTypes';
import { UploadImageOnAws } from '@/utils/ImageUploadModule';

interface Props {
  type: string;
  warehouseId: string;
  handleClose: () => void;
  brands: any;
  categories?: SingleCategoryDetails[];
  productData?: SingleProductDetails;
}

const formikInitialValues = {
  name: '',
  brandId: '',
  description: '',
  currentPurchasePrice: 0,
  currentSellingPrice: 0,
  // currentWholesaleSellingPrice: 0,
  discountType: 'FIXED',
  discount: 0,
  vat: 0,
  categoryId: '',
  unitId: '',
  subUnitId: '',
  unitWithSubUnitMultiplier: 0,
  quantity: 0,
  options: [], // NEW: Added options array
};

const validation = Yup.object({
  name: Yup.string().required('Product name is required'),
  brandId: Yup.string().required('Brand name is required'),
  currentPurchasePrice: Yup.string().required('Purchase is required'),
  currentSellingPrice: Yup.string().required('Regular Price is required'),
  categoryId: Yup.string().required('Category is required'),
});

function AddOrEditProductModal({
  type,
  handleClose,
  brands,
  categories,
  productData,
}: Props) {
  const organizationId = Cookies.get('organizationId') as string;
  const [createProduct, { isLoading }] = useCreateProductMutation();
  const [updateProduct, { isLoading: isProductUpdating }] =
    useUpdateProductMutation();
  const [currentFile, setCurrentFile] = useState<any>();
  // const [subCategories, setSubCategories] = useState<SingleSubCategory[]>();
  const [optionName, setOptionName] = useState(''); // NEW: Option name input
  const [optionValue, setOptionValue] = useState(''); // NEW: Option values input

  const formik = useFormik({
    initialValues: formikInitialValues,
    validationSchema: validation,

    onSubmit: async (values) => {
      const imgUrl = currentFile
        ? await UploadImageOnAws(currentFile)
        : productData?.imgUrl ?? null;
      const data = {
        organizationId,
        imgUrl,
        brandId: values.brandId,
        name: values.name,
        description: values.description,
        currentPurchasePrice: Number(values.currentPurchasePrice),
        currentSellingPrice: Number(values.currentSellingPrice),
        // currentWholesaleSellingPrice: Number(
        //   values.currentWholesaleSellingPrice,
        // ),
        // currentWholesalePurchasePrice: Number(values.currentPurchasePrice),
        discountType: values.discountType,
        discount: values.discount,
        vat: values.vat ? values.vat : 0,
        categoryId: values.categoryId,
        // unitId: values.unitId,
        unitId: null,
        // subUnitId: values.subUnitId,
        subUnitId: null,
        // unitWithSubUnitMultiplier: values.unitWithSubUnitMultiplier,
        unitWithSubUnitMultiplier: 0,
        options: values.options ?? [],
      };
      if (type === 'new') {
        toast.promise(createProduct(data).unwrap(), {
          pending: 'Creating New Product...',
          success: {
            render({ data: res }) {
              console.log(res);

              if (res?.statusCode === 200 || res?.statusCode === 201) {
                // updateRefreshCounter();
                handleClose();
              }
              return 'Product created Successfully';
            },
          },
          error: {
            render({ data: error }) {
              console.log('i am on error');
              console.log(error);
              return 'Error on creating Product';
            },
          },
        });
      } else {
        toast.promise(
          updateProduct({
            data,
            id: productData?.id,
          }).unwrap(),
          {
            pending: 'Updating Product...',
            success: {
              render({ data: res }) {
                if (res?.statusCode === 200 || res?.statusCode === 201) {
                  // updateRefreshCounter();
                  handleClose();
                }
                return 'Product updated Successfully';
              },
            },
            error: {
              render({ data: error }) {
                console.log(error);
                return 'Error on updating Product';
              },
            },
          },
        );
      }
    },
  });

  useEffect(() => {
    if (type === 'edit' && productData) {
      formik.setFieldValue('name', productData?.name);
      formik.setFieldValue('description', productData?.description);
      formik.setFieldValue('brandId', productData?.brandId);
      formik.setFieldValue(
        'currentPurchasePrice',
        productData?.currentPurchasePrice,
      );
      formik.setFieldValue(
        'currentSellingPrice',
        productData?.currentSellingPrice,
      );
      formik.setFieldValue('categoryId', productData?.categoryId);
      formik.setFieldValue(
        'productSubCategoryId',
        productData?.productSubCategoryId,
      );
      formik.setFieldValue('vat', productData?.vat);
      formik.setFieldValue('discountType', productData?.discountType);
      formik.setFieldValue('discount', productData?.discount);
      formik.setFieldValue(
        'currentWholesaleSellingPrice',
        productData?.currentWholesaleSellingPrice,
      );
      formik.setFieldValue('options', productData?.Options);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [type, productData]);

  // useEffect(() => {
  //   if (formik.values.categoryId) {
  //     const sub = categories?.find(
  //       (sin: SingleCategoryDetails) => sin.id === formik.values.categoryId,
  //     );
  //     setSubCategories(sub?.ProductSubCategory);
  //   } else {
  //     setSubCategories([]);
  //   }
  // }, [formik.values.categoryId]);

  // Function to add option
  const handleAddOption = () => {
    if (optionName && optionValue) {
      const newOption = {
        name: optionName,
        values: optionValue.split(',').map((v) => v.trim()),
      };
      formik.setFieldValue('options', [...formik.values.options, newOption]);
      setOptionName('');
      setOptionValue('');
    }
  };

  // Function to remove an option
  const removeOption = (index: number) => {
    formik.setFieldValue(
      'options',
      formik.values.options.filter((_, i) => i !== index),
    );
  };

  return (
    <div className="flex w-[340px] flex-col gap-4 rounded-xl bg-white p-4 md:w-[500px]">
      <ModalTitle
        text={type === 'new' ? 'Add product modal' : 'Edit product modal'}
        handleClose={handleClose}
      />
      <form
        onSubmit={formik.handleSubmit}
        className="flex w-full flex-col gap-4"
      >
        <div className="flex items-start gap-2">
          <div className="w-[130px]">
            <ImageSelector
              previousImage={productData?.imgUrl ?? ''}
              setNewImage={(e) => setCurrentFile(e)}
            />
          </div>
          <div className="flex w-full flex-col gap-2">
            <CustomInputField
              type="text"
              placeholder="Enter Product Name"
              name="name"
              label="Name"
              formik={formik}
            />
            <CustomInputField
              type="text"
              placeholder="Product Description"
              name="description"
              label="Description"
              formik={formik}
            />
          </div>
        </div>
        <div className="flex items-center gap-2">
          <CustomDropdown
            placeholder="Select Brand"
            name="brandId"
            label="Brand"
            formik={formik}
            options={brands}
          />
          <CustomDropdown
            placeholder="Select Category"
            name="categoryId"
            label="Category"
            formik={formik}
            options={categories?.map((single: SingleCategoryDetails) => {
              return {
                value: single.id,
                label: single.name,
              };
            })}
          />
        </div>
        <div className="flex items-center gap-2">
          {/* <CustomDropdown
            placeholder="Select Sub Category"
            name="productSubCategoryId"
            label="Sub Category"
            formik={formik}
            options={
              subCategories?.length
                ? subCategories?.map((single: SingleSubCategory) => {
                    return {
                      value: single.id,
                      label: single.name,
                    };
                  })
                : []
            }
          /> */}
          <CustomDropdown
            placeholder="Select Discount Type"
            name="discountType"
            label="Discount Type"
            formik={formik}
            options={[
              { label: 'Percentage', value: 'PERCENTAGE' },
              { label: 'Fixed', value: 'FIXED' },
            ]}
          />
          <CustomInputField
            type="number"
            placeholder="Discount"
            name="discount"
            label="Discount"
            formik={formik}
          />
        </div>
        <div className="flex items-center gap-2">
          <CustomInputField
            type="text"
            placeholder="Buying Price"
            name="currentPurchasePrice"
            label="Purchase Price"
            formik={formik}
          />
          <CustomInputField
            type="text"
            placeholder="Regular Price"
            name="currentSellingPrice"
            label="Regular Price"
            formik={formik}
          />
        </div>
        {/* <div className="flex items-center gap-2">
          
        </div> */}

        <div className="flex items-center gap-2">
          <CustomInputField
            type="number"
            placeholder="Vat"
            name="vat"
            label="Vat (%)"
            formik={formik}
          />
          <div className="group relative z-0 mt-[-20px] w-full">
            <span className="relative left-3 top-2.5 w-auto bg-white px-1 font-mono text-[12px] font-bold text-gray-900 group-focus-within:text-red-600 dark:text-gray-300">
              Discount Price
            </span>
            <input
              disabled
              type="text"
              className="text-10 py-55-rem block h-10 w-full cursor-not-allowed rounded-lg border bg-gray-200 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
              placeholder="Price After Discount"
              value={
                formik.values.discountType === 'FIXED'
                  ? Number(formik.values.currentSellingPrice) -
                    Number(formik.values.discount)
                  : Number(formik.values.currentSellingPrice) -
                    (Number(formik.values.currentSellingPrice) *
                      Number(formik.values.discount)) /
                      100
              }
            />
          </div>
          {/* <CustomInputField
            type="number"
            placeholder="Wholesale Price"
            name="currentWholesaleSellingPrice"
            label="Wholesale Price"
            formik={formik}
          /> */}
        </div>
        {/* NEW: Options Section */}
        <div className="space-y-2 border-t pt-1">
          <h3 className="text-sm font-semibold">Product Variations</h3>
          {formik.values.options.length > 0 &&
            formik.values.options.map((option: any, index) => (
              <div key={option?.name} className="flex items-center gap-2">
                <Badge>{option?.name ?? ''}</Badge>
                {option.values.map((val: string) => (
                  <Badge key={val} variant="secondary">
                    {val}
                  </Badge>
                ))}
                <X
                  className="h-4 w-4 cursor-pointer text-red-500"
                  onClick={() => removeOption(index)}
                />
              </div>
            ))}

          {formik?.values?.options?.length < 2 ? (
            <div className="flex gap-2">
              <input
                type="text"
                placeholder="Option Name (e.g., Size, Color)"
                value={optionName ?? ''}
                onChange={(e: any) => setOptionName(e.target.value)}
                className="text-10 py-55-rem block h-10 w-full rounded-lg border bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 disabled:cursor-not-allowed disabled:bg-gray-100 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
              />
              <input
                type="text"
                placeholder="Values (comma-separated)"
                value={optionValue}
                onChange={(e) => setOptionValue(e.target.value)}
                className="text-10 py-55-rem block h-10 w-full rounded-lg border bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 disabled:cursor-not-allowed disabled:bg-gray-100 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
              />
              <button
                type="button"
                className="rounded bg-blue-500 px-3 py-2 text-white"
                onClick={handleAddOption}
              >
                Add
              </button>
            </div>
          ) : (
            ''
          )}
        </div>

        <div className="mt-[10px] flex w-full items-center justify-center">
          <FilledSubmitButton
            isLoading={isLoading || isProductUpdating}
            text={type === 'new' ? 'Add Product' : 'Done Editing'}
          />
        </div>
      </form>
    </div>
  );
}

export default AddOrEditProductModal;
