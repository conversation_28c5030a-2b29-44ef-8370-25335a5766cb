import { useParams } from 'react-router-dom';

import ShopSalesReportPageOverview from '@/components/ShopComponents/ShopSalesReportPageComponents/ShopSalesReportPageOverview';
import { useAppSelector } from '@/redux/hooks';

function ShopSalesReportPage() {
  const { shopId } = useParams();
  const { warehouseId } = useAppSelector((state) => state.shopDetails);
  return (
    <div>
      <ShopSalesReportPageOverview
        shopId={shopId ?? ''}
        warehouseId={warehouseId}
      />
    </div>
  );
}

export default ShopSalesReportPage;
