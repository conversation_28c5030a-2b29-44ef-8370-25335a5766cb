import { useFormik } from 'formik';
import Cookies from 'js-cookie';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import Swal from 'sweetalert2';
import * as Yup from 'yup';

import AddProductInCartForWholesale from './AddProductInCartForWholesale';

import {
  DeleteButton,
  EditButton,
  EntryButton,
} from '@/components/reusable/Buttons/CommonButtons';
import FilledSubmitButton from '@/components/reusable/Buttons/FilledSubmitButton';
import CustomSelectForFilter from '@/components/reusable/Inputs/CustomSelectForFilter';
import SearchInput from '@/components/reusable/Inputs/SearchInput';
import Modal from '@/components/reusable/Modal/Modal';
import Pagination from '@/components/reusable/Pagination/Pagination';
import TableSkeletonLoaderHalf from '@/components/reusable/SkeletonLoader/TableSkeletionLoaderHalf';
import ShopCustomerDetailsFormInOrderForm from '@/components/ShopComponents/ShopOrdersPageComponents/ShopCustomerDetailsFormInOrderForm';
import { useGetShopCustomerDetailsByPhoneQuery } from '@/redux/api/shopApis/shopCustomersApi';
import { useCreateShopOrderMutation } from '@/redux/api/shopApis/shopOrdersApis';
import { useGetBrandsQuery } from '@/redux/api/warehouseApis/brandsApi';
import { useGetCategoriesQuery } from '@/redux/api/warehouseApis/categoriesApi';
import { useGetProductsQuery } from '@/redux/api/warehouseApis/productsApi';
import { SingleBrandDetails } from '@/types/warehouseTypes/brandsTypes';
import { SingleCategoryDetails } from '@/types/warehouseTypes/categoriesTypes';
import { SingleProductDetails } from '@/types/warehouseTypes/productTypes';
import { PaymentMethods } from '@/utils/staticData';

interface SelectedProductType {
  productId: 'string';
  productName: 'string';
  purchasePrice: number;
  quantity: number;
  price: number;
  serialNo: string;
  availableQuantity: number;
}

interface Props {
  warehouseId: string;
}

const formikInitialValues = {
  name: '',
  mobileNumber: '',
  address: null,
  email: null,
  gender: null,
  totalProductPrice: 0,
  productDiscount: 0,
  totalDiscount: 0,
  vat: 0,
  deliveryCharge: 0,
  customerPaid: 0,
  returnAmount: 0,
  deliveryPartner: 'OTHER',
  paymentMethod: 'OTHER',
  customerId: '',
  note: '',
  employeeId: '',
  orderSource: 'OTHER',
};
const validation = Yup.object({
  name: Yup.string().required('Customer name is required'),
  mobileNumber: Yup.string().required('Mobile Number is required'),
  address: Yup.string().required('Address is required'),
});

function AddNewWholesaleOrderOverview({ warehouseId }: Props) {
  const [productName, setProductName] = useState<string>('');
  const [brandName, setBrandName] = useState<string>('');
  const [categoryName, setCategoryName] = useState<string>('');
  const [isStockEntryModalOpen, setIsStockEntryModalOpen] =
    useState<boolean>(false);
  const [isStockUpdateModalOpen, setIsStockUpdateModalOpen] =
    useState<boolean>(false);
  const [selectedProduct, setSelectedProduct] =
    useState<SingleProductDetails>();
  const [selectedStock, setSelectedStock] = useState<SelectedProductType>();
  const [selectedProducts, setSelectedProducts] =
    useState<SelectedProductType[]>();
  const [discountPercentage, setDiscountPercentage] = useState<number>(0);

  const { data: brands } = useGetBrandsQuery({ warehouseId });
  const { data: categories } = useGetCategoriesQuery({
    warehouseId,
  });
  const { data, isLoading, refetch } = useGetProductsQuery({
    warehouseId,
    name: productName ?? undefined,
    brandName: brandName ?? undefined,
    categoryName: categoryName ?? undefined,
  });

  const [createShopOrder, { isLoading: isOrderCreating }] =
    useCreateShopOrderMutation();

  const formik = useFormik({
    initialValues: formikInitialValues,
    validationSchema: validation,

    onSubmit: async (values) => {
      const orderData = {
        name: values.name,
        mobileNumber: values.mobileNumber,
        gender: values.gender ? values.gender : null,
        email: values.email ?? null,
        note: values.note ? values.note : null,
        address: values.address,
        subTotal: Number(values.totalProductPrice),
        grandTotal: Number(values.totalProductPrice),
        productDiscount: Number(values.productDiscount),
        adminDiscount: Number(values.totalDiscount),
        vat: Number(values.vat),
        deliveryCharge: Number(values.deliveryCharge),
        customerPaid: Number(values.customerPaid),
        returnAmount: Number(values.returnAmount),
        orderType: 'Wholesale',
        OrderItem: selectedProducts?.map((product: SelectedProductType) => {
          return {
            itemId: null,
            productId: product?.productId,
            quantity: product?.quantity,
          };
        }),
        shopId: null,
        warehouseId,
        deliveryPartner: values.deliveryPartner,
        // paymentMethod: values.paymentMethod,
        paymentMethods: [
          {
            method: values.paymentMethod,
            amount: Number(values.customerPaid) ?? 0,
          },
        ],
        organizationId: Cookies.get('organizationId'),
        customerId: values.customerId ? values.customerId : null,
        employeeId: values.employeeId ? values.employeeId : null,
        orderSource: formik.values.orderSource ?? null,
      };

      toast.promise(createShopOrder(orderData).unwrap(), {
        pending: 'Creating New Order...',
        success: {
          render({ data: res }) {
            if (res?.statusCode === 200 || res?.statusCode === 201) {
              // handleOrderCreation(res?.data?.data);
              // setCourierBookingOrderDetails(res?.data);
              // setIsCourierBookingModalOpen(true);
              refetch();
              // navigate(ROUTES.SHOP.INVOICE(shopId, res?.data?.data?.id));
              formik.resetForm();
              setSelectedProducts([]);
            }
            return 'Order created Successfully';
          },
        },
        error: {
          render({ data: error }) {
            console.log(error);
            return 'Error on creating order';
          },
        },
      });
    },
  });

  const handleSelectProductForEntry = (newProd: any) => {
    if (selectedProducts?.length) {
      setSelectedProducts([...selectedProducts, newProd]);
    } else {
      setSelectedProducts([newProd]);
    }
  };

  const handleUpdateSelectedProduct = (updatedProduct: SelectedProductType) => {
    setSelectedProducts((prevSelectedProducts: any) =>
      prevSelectedProducts.map((product: SelectedProductType) =>
        product.productId === updatedProduct.productId
          ? updatedProduct
          : product,
      ),
    );
  };

  const handleUnselectItem = (newProd: SelectedProductType) => {
    Swal.fire({
      title: 'Are you sure? you want to remove item from list?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, delete it!',
    }).then((result) => {
      if (result.isConfirmed) {
        const others = selectedProducts?.filter(
          (single: SelectedProductType) =>
            single.productId !== newProd.productId,
        );
        setSelectedProducts(others);
        toast.success('Item Removed');
      }
    });
  };

  const { data: customerData, isLoading: isCustomerDataLoading } =
    useGetShopCustomerDetailsByPhoneQuery(
      {
        phone: formik.values.mobileNumber,
        organizationId: Cookies.get('organizationId'),
      },
      {
        skip: formik.values.mobileNumber?.length !== 11,
      },
    );

  useEffect(() => {
    if (customerData?.data) {
      formik.setFieldValue('name', customerData?.data?.name);
      formik.setFieldValue('mobileNumber', customerData?.data?.mobileNumber);
      formik.setFieldValue('address', customerData?.data?.address);
      formik.setFieldValue('email', customerData?.data?.email);
      formik.setFieldValue('gender', customerData?.data?.gender);
      formik.setFieldValue('customerId', customerData?.data?.id);
    }
  }, [customerData]);

  useEffect(() => {
    // let discountProductPrice = 0;
    let total = 0;
    // let productDiscount = 0;
    // let vat = 0;
    selectedProducts?.map((product: SelectedProductType) => {
      total += product.price * product.quantity;

      return 0;
    });
    formik.setFieldValue('totalProductPrice', total);
    // formik.setFieldValue('totalDiscount', total - discountProductPrice);
    // formik.setFieldValue('productDiscount', total - discountProductPrice);
    // formik.setFieldValue('vat', vat);
  }, [selectedProducts]);

  return (
    <div className="grid grid-cols-12 gap-2">
      <div className="col col-span-12 w-full md:col-span-5">
        {!isLoading ? (
          <div>
            <div className="tableTop-2 w-full">
              <div className="flex w-full items-center justify-between">
                <SearchInput
                  placeholder="Search by Name"
                  handleSubmit={(value: string) => setProductName(value)}
                />
                <CustomSelectForFilter
                  options={
                    brands?.data
                      ? brands?.data?.map((brand: SingleBrandDetails) => {
                          return { label: brand?.name, value: brand?.name };
                        })
                      : []
                  }
                  selectedValue={brandName ?? ''}
                  handleSelect={(e) => setBrandName(e)}
                  placeHolder="Select Brand"
                />
                <CustomSelectForFilter
                  options={
                    categories?.data
                      ? categories?.data?.map(
                          (category: SingleCategoryDetails) => {
                            return {
                              label: category?.name,
                              value: category?.name,
                            };
                          },
                        )
                      : []
                  }
                  selectedValue={categoryName ?? ''}
                  handleSelect={(e) => setCategoryName(e)}
                  placeHolder="Select Category"
                />
              </div>
            </div>
            <div className="w-full">
              {data?.data?.length ? (
                <div className="h-custom2">
                  <table className="w-full">
                    <thead className="bg-gray-100">
                      <tr>
                        <th className="tableHead">No</th>

                        <th className="tableHead table-col-width">Name</th>
                        <th className="tableHead">TP</th>
                        <th className="tableHead">Price</th>
                        <th className="tableHead">Available</th>
                        <th className="tableHead">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y bg-slate-200">
                      {data?.data?.map(
                        (product: SingleProductDetails, index: number) => {
                          const isSelected = selectedProducts?.some(
                            (single: SelectedProductType) =>
                              single.productId === product.id,
                          );
                          return (
                            <tr
                              key={product?.id}
                              className={isSelected ? 'bg-orange-500' : ''}
                            >
                              <td className="tableData">{index + 1}</td>
                              <td className="tableData table-col-width">
                                {product?.name}
                              </td>
                              <td className="tableData">
                                {product?.currentPurchasePrice}
                              </td>
                              <td className="tableData">
                                {product?.currentWholesaleSellingPrice}
                              </td>
                              <td className="tableData">
                                {product?.totalAvailable}
                              </td>
                              <td className="tableData">
                                <div className="flex items-center justify-center gap-2">
                                  <EntryButton
                                    handleClick={() => {
                                      setSelectedProduct(product);
                                      setIsStockEntryModalOpen(true);
                                    }}
                                    disabled={
                                      isSelected || product.totalAvailable === 0
                                    }
                                  />
                                </div>
                              </td>
                            </tr>
                          );
                        },
                      )}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div>
                  <h2>No product found</h2>
                </div>
              )}
            </div>
            <div className="pagination-box flex justify-end rounded bg-white p-3">
              <Pagination
                currentPage="1"
                limit={Number(10)}
                handleFilter={(fieldName: string, value: any) =>
                  // handleFilter(fieldName, value)
                  console.log(fieldName, value)
                }
                totalCount={data?.pagination?.total}
                totalPages={Math.ceil(
                  Number(data?.pagination?.total) /
                    Number(data?.pagination?.limit),
                )}
              />
            </div>
          </div>
        ) : (
          <TableSkeletonLoaderHalf tableColumn={6} tableRow={6} />
        )}
      </div>
      <div className="col col-span-12 md:col-span-7">
        <form onSubmit={formik.handleSubmit} className="w-full">
          <div>
            <ShopCustomerDetailsFormInOrderForm
              formik={formik}
              isLoading={isCustomerDataLoading}
            />
            <div className="my-3 h-[1px] w-full bg-black text-black" />
            <div className="mt-1">
              {/* <span className="text-xl font-bold">Cart Details</span> */}
              <div className="full-table-box h-customtable">
                <table className="full-table bg-white">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="tableHead">No</th>
                      <th className="tableHead">Product Id</th>
                      <th className="tableHead">Name</th>
                      <th className="tableHead">Price</th>
                      <th className="tableHead">QTY</th>
                      <th className="tableHead">Total</th>
                      <th className="tableHead">Action</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y">
                    {selectedProducts?.length
                      ? selectedProducts?.map(
                          (stock: SelectedProductType, index: number) => (
                            <tr key={stock?.productId}>
                              <td className="tableData">{index + 1}</td>
                              <td className="tableData">{stock?.serialNo}</td>
                              <td className="tableData">
                                {stock?.productName}
                              </td>
                              <td className="tableData">{stock?.price}</td>

                              <td className="tableData">{stock?.quantity}</td>
                              <td className="tableData">
                                {(
                                  Number(stock.price) * Number(stock.quantity)
                                ).toFixed(2)}
                              </td>
                              <td className="tableData">
                                <div className="flex items-center justify-center gap-1">
                                  <EditButton
                                    handleClick={() => {
                                      setSelectedStock(stock);
                                      setIsStockUpdateModalOpen(true);
                                    }}
                                  />
                                  <DeleteButton
                                    handleClick={() =>
                                      handleUnselectItem(stock)
                                    }
                                  />
                                </div>
                              </td>
                            </tr>
                          ),
                        )
                      : ''}
                    <tr className="border">
                      <td colSpan={3} />
                      <td
                        colSpan={2}
                        className="border border-gray-300 p-1 text-right text-xs font-bold"
                      >
                        Cart Total :
                      </td>
                      <td className="border border-gray-300 p-1 text-center text-xs font-bold">
                        {formik.values.totalProductPrice.toFixed(2)}
                      </td>
                      <td />
                    </tr>
                    <tr className="border">
                      <td
                        colSpan={3}
                        className="border border-gray-300 p-1 text-right text-xs font-bold"
                      >
                        <div className="flex items-center gap-2">
                          <span className="w-full">Discount Amount :</span>
                          <input
                            type="number"
                            min={Math.ceil(formik.values.productDiscount)}
                            value={formik.values.totalDiscount}
                            className="w-[116px] rounded border border-gray-300 px-4 py-[5px]"
                            name="totalDiscount"
                            onChange={(e) => {
                              formik.setFieldValue(
                                'totalDiscount',
                                Number(e.target?.value),
                              );
                              const percentage =
                                (Number(e.target.value) * 100) /
                                Number(formik.values?.totalProductPrice);
                              setDiscountPercentage(
                                Number(percentage.toFixed(2)),
                              );
                            }}
                          />
                        </div>
                      </td>

                      <td
                        colSpan={2}
                        className="border border-gray-300 p-1 text-right text-xs font-bold"
                      >
                        Discount :
                      </td>
                      <td className="border border-gray-300 p-1 text-center text-xs font-bold">
                        {formik.values.totalDiscount?.toFixed(2)}
                      </td>
                      <td />
                    </tr>
                    <tr className="border">
                      <td
                        colSpan={3}
                        className="border border-gray-300 p-1 text-right text-xs font-bold"
                      >
                        <div className="flex items-center gap-2">
                          <span className="w-full">Discount Percentage :</span>
                          <input
                            value={discountPercentage}
                            className="w-[116px] rounded border border-gray-300 px-4 py-1"
                            type="number"
                            onChange={(e) => {
                              setDiscountPercentage(Number(e.target.value));
                              const totalDis =
                                (Number(formik.values?.totalProductPrice) *
                                  Number(e.target.value)) /
                                100;
                              formik.setFieldValue(
                                'totalDiscount',
                                Math.round(totalDis),
                              );
                            }}
                          />
                        </div>
                      </td>

                      <td
                        colSpan={2}
                        className="w-[120px] border border-gray-300 p-1 text-right text-xs font-bold"
                      >
                        Delivery Charge :
                      </td>
                      <td className="border border-gray-300 p-1 text-center text-xs font-bold">
                        <input
                          value={formik.values.deliveryCharge}
                          className="w-[80px] rounded border border-gray-300 py-1 text-center"
                          name="deliveryCharge"
                          onChange={formik.handleChange}
                        />
                      </td>
                      <td className="border border-gray-300 p-1 text-center text-xs font-bold" />
                    </tr>
                    {/* <tr className="border">
                      <td
                        colSpan={3}
                        className="border border-gray-300 p-1 text-right text-xs font-bold"
                      >
                        <div className="flex items-center gap-2">
                          <span className="w-full">Seller :</span>
                          <select
                            value={formik.values.employeeId}
                            name="employeeId"
                            onChange={formik.handleChange}
                            className="w-[120px] rounded border border-gray-300 bg-gray-50 px-2 py-1 text-xs focus:border-blue-500 focus:ring-blue-500"
                          >
                            <option value="" label="Select Seller" />
                            {!isEmployeesDataLoading &&
                                employees?.data?.map(
                                  (option: SingleSellerDetails) => (
                                    <option
                                      key={option.id}
                                      value={option.id}
                                      label={option.name}
                                    >
                                      {option.User?.name}
                                    </option>
                                  ),
                                )}
                          </select>
                        </div>
                      </td>
                      <td
                        colSpan={2}
                        className="border border-gray-300 p-1 text-right text-xs font-bold"
                      >
                        Vat :
                      </td>
                      <td className="border border-gray-300 p-1 text-center text-xs font-bold">
                        {formik.values.vat}
                      </td>
                      <td className="border border-gray-300 p-1 text-center text-xs font-bold" />
                    </tr> */}
                    <tr className="border">
                      <td
                        colSpan={3}
                        className="border border-gray-300 p-1 text-right text-xs font-bold"
                      >
                        <div className="flex items-center gap-2">
                          <span className="w-full">Delivery Partner :</span>
                          <select
                            value={formik.values.deliveryPartner}
                            name="deliveryPartner"
                            onChange={formik.handleChange}
                            className="w-[120px] rounded border border-gray-300 bg-gray-50 px-2 py-1 text-xs focus:border-blue-500 focus:ring-blue-500"
                          >
                            <option value="" label="Select Option" />
                            {[
                              { label: 'Pathao', value: 'PATHAO' },
                              { label: 'OTHER', value: 'OTHER' },
                            ]?.map((option: any) => (
                              <option
                                key={option.value}
                                value={option.value}
                                label={option.label}
                              >
                                {option.label}
                              </option>
                            ))}
                          </select>
                        </div>
                      </td>
                      <td
                        colSpan={2}
                        className="border border-gray-300 p-1 text-right text-xs font-bold"
                      >
                        Net Total :
                      </td>
                      <td className="border border-gray-300 p-1 text-center text-xs font-bold">
                        {Number(formik.values.totalProductPrice) +
                          Number(formik.values.deliveryCharge) +
                          Number(formik.values.vat) -
                          Number(formik.values.totalDiscount)}
                      </td>
                      <td className="border border-gray-300 p-1 text-center text-xs font-bold" />
                    </tr>
                    <tr className="border">
                      <td
                        colSpan={3}
                        className="border border-gray-300 p-1 text-right text-xs font-bold"
                      >
                        <div className="flex items-center gap-2">
                          <span className="w-full">Payment Method :</span>
                          <select
                            value={formik.values.paymentMethod}
                            name="paymentMethod"
                            onChange={formik.handleChange}
                            className="w-[150px] rounded border border-gray-300 bg-gray-50 px-2 py-1 text-xs focus:border-blue-500 focus:ring-blue-500"
                          >
                            <option value="" label="Select Option" />
                            {PaymentMethods?.map((option: any) => (
                              <option
                                key={option.value}
                                value={option.value}
                                label={option.label}
                              >
                                {option.label}
                              </option>
                            ))}
                          </select>
                        </div>
                      </td>

                      <td
                        colSpan={2}
                        className="border border-gray-300 p-1 text-right text-xs font-bold"
                      >
                        Paid Amount :
                      </td>
                      <td className="border border-gray-300 p-1 text-center text-xs font-bold">
                        <input
                          value={formik.values.customerPaid}
                          name="customerPaid"
                          onChange={formik.handleChange}
                          className="w-[80px] rounded border border-gray-300 py-1 text-center"
                        />
                      </td>
                      <td className="border border-gray-300 p-1 text-center text-xs font-bold" />
                    </tr>
                    <tr className="border">
                      <td colSpan={3}>
                        <input
                          value={formik.values.note}
                          name="note"
                          onChange={formik.handleChange}
                          className="w-full rounded border border-gray-300 px-4 py-1"
                          placeholder="Remarks"
                        />
                      </td>
                      <td
                        colSpan={2}
                        className="border border-gray-300 p-1 text-right text-xs font-bold"
                      >
                        Due/Return :
                      </td>
                      <td className="border border-gray-300 p-1 text-center text-xs font-bold">
                        {Number(formik.values.totalProductPrice) +
                          Number(formik.values.deliveryCharge) +
                          Number(formik.values.vat) -
                          Number(formik.values.totalDiscount) -
                          Number(formik.values.customerPaid)}
                      </td>
                      <td />
                    </tr>
                    <tr className="border">
                      <td
                        colSpan={3}
                        className="border border-gray-300 p-1 text-center text-xs font-bold"
                      />
                      <td
                        colSpan={3}
                        className="border border-gray-300 p-1 text-center text-xs font-bold"
                      >
                        <FilledSubmitButton
                          text="Submit"
                          isLoading={isOrderCreating}
                          isDisabled={!selectedProducts?.length}
                        />
                      </td>
                      <td className="border border-gray-300 p-1 text-center text-xs font-bold" />
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </form>
      </div>
      <Modal
        setShowModal={setIsStockEntryModalOpen}
        showModal={isStockEntryModalOpen}
      >
        <AddProductInCartForWholesale
          handleClose={() => setIsStockEntryModalOpen(false)}
          productData={selectedProduct}
          handleSave={(newEntryData: SelectedProductType) =>
            handleSelectProductForEntry(newEntryData)
          }
          type="new"
        />
      </Modal>
      <Modal
        setShowModal={setIsStockUpdateModalOpen}
        showModal={isStockUpdateModalOpen}
      >
        <AddProductInCartForWholesale
          handleClose={() => setIsStockUpdateModalOpen(false)}
          handleSave={(newEntryData: SelectedProductType) =>
            handleUpdateSelectedProduct(newEntryData)
          }
          type="edit"
          editData={selectedStock}
        />
      </Modal>
    </div>
  );
}

export default AddNewWholesaleOrderOverview;
