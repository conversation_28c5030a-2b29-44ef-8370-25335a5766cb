import { StyleSheet } from '@react-pdf/renderer';

export const styles = StyleSheet.create({
  page: {
    backgroundColor: 'white',
    padding: 20,
  },
  container: {
    // width: "1000px",
    display: 'flex',
    justifyContent: 'space-between',
    flexDirection: 'row',
  },
  section: {
    textAlign: 'right',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    gap: '4px',
  },
  invoiceText: {
    fontSize: '20px',
    fontWeight: 800,
    fontFamily: 'Open Sans',
  },
  invoiceNo: {
    fontSize: '12px',
    fontWeight: 'bold',
    fontFamily: 'Open Sans',
  },
  orderDate: {
    fontSize: '10px',
    fontWeight: 'bold',
    fontFamily: 'Open Sans',
  },
  barcode: {
    width: 60,
    height: 30,
    padding: 0,
    marginLeft: '-2px',
  },
  sectionRight: {
    textAlign: 'right',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-end',
    gap: '4px',
  },
  sectionLeft: {
    textAlign: 'left',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    gap: '4px',
  },
  sectionMiddleTop: {
    paddingTop: 35,
  },
  displayContent: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  companyLogo: {
    width: 50,
    marginBottom: 2,
  },
  companyAddress: {
    fontSize: '10px',
    fontFamily: 'Open Sans',
    fontWeight: 800,
    width: '380px',
  },
  companyOtherTexts: {
    fontSize: '10px',
    fontFamily: 'Open Sans',
    // fontWeight: 800,
  },
  bottomPartContainer: {
    display: 'flex',
    alignItems: 'flex-start',
    flexDirection: 'row',
    gap: '4px',
    marginTop: '4px',
  },
  invoiceToContainer: {
    width: '170px',
    border: '1px solid black',
    padding: 4,
    display: 'flex',
    flexDirection: 'column',
    gap: '4px',
  },
  invoiceToText: {
    fontSize: '14px',
    fontWeight: 'bold',
    fontFamily: 'Open Sans',
  },
  customerName: {
    fontSize: '10px',
    textAlign: 'left',
    fontFamily: 'Open Sans',
    color: 'black',
    fontWeight: 800,
  },
  customerName2: {
    fontSize: '10px',
    textAlign: 'right',
    fontFamily: 'Open Sans',
    color: '#000',
  },
  topContent: {
    marginTop: 10,
  },
  billTop: {
    backgroundColor: '#F9FAFC',
    padding: 20,
  },
  OrderAddress: {
    fontSize: '8px',
    textAlign: 'left',
    fontFamily: 'Open Sans',
    color: '#5E6470',
  },
  OrderAddress2: {
    fontSize: '8px',
    textAlign: 'right',
    fontFamily: 'Open Sans',
    color: '#5E6470',
  },
  billbyto: {
    fontSize: '12px',
    fontWeight: 'bold',
    textAlign: 'left',
    fontFamily: 'Open Sans',
  },
  billbyto2: {
    fontSize: '12px',
    fontWeight: 'bold',
    textAlign: 'left',
    fontFamily: 'Open Sans',
  },
  customerNameBangla: {
    fontSize: '12px',
    fontWeight: 'bold',
    maxWidth: '180px',
    textAlign: 'left',
    fontFamily: 'Noto Sans Bengali',
  },
  phoneNumberContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: '4px',
  },
  phoneNumber: {
    fontSize: '12px',
    fontWeight: 800,
    fontFamily: 'Open Sans',
  },
  addressContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: '4px',
  },
  addressEnglish: {
    fontSize: '10px',
    maxWidth: '115px',
    textAlign: 'left',
    fontFamily: 'Open Sans',
    fontWeight: 600,
  },
  addressBangla: {
    fontSize: '10px',
    maxWidth: '115px',
    textAlign: 'left',
    fontFamily: 'Noto Sans Bengali',
    fontWeight: 600,
  },
  note: {
    fontSize: '8px',
  },
  productsTable: {
    // marginTop: "-10px",
  },

  tableHeader: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 4,
    border: '1px solid black',
    backgroundColor: '#dedcdc',
    flexDirection: 'row',
  },
  numberTitle: {
    width: '10px',
    fontSize: '8px',
    fontWeight: 'bold',
    fontFamily: 'Open Sans',
  },
  nameTitle: {
    width: '240px',
    fontSize: '8px',
    fontWeight: 'bold',
    fontFamily: 'Open Sans',
  },
  barcodeTitle: {
    width: '40px',
    fontSize: '8px',
    fontWeight: 'bold',
    fontFamily: 'Open Sans',
    textAlign: 'center',
  },
  stockId: {
    width: '40px',
    fontSize: '8px',
    fontWeight: 'bold',
    fontFamily: 'Open Sans',
    textAlign: 'center',
  },
  productPrice: {
    width: '30px',
    fontSize: '8px',
    fontWeight: 'bold',
    fontFamily: 'Open Sans',
    textAlign: 'center',
  },
  productQuantity: {
    width: '20px',
    fontSize: '8px',
    fontWeight: 'bold',
    fontFamily: 'Open Sans',
    textAlign: 'center',
  },
  productTotal: {
    width: '30px',
    fontSize: '8px',
    fontWeight: 'bold',
    fontFamily: 'Open Sans',
    textAlign: 'right',
  },
  productRow: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 4,
    borderBottom: '1px solid black',
    borderLeft: '1px solid black',
    borderRight: '1px solid black',
    flexDirection: 'row',
  },
  productName: {
    width: '240px',
    fontSize: '8px',
  },
  duePart: {
    width: '220px',
    display: 'flex',
    alignItems: 'center',
    flexDirection: 'column',
    justifyContent: 'center',
    gap: '10px',
  },
  payableAmount: {
    fontSize: '8px',
    textAlign: 'center',
    padding: '10px  10px 0px 10px',
  },
  dueOrPaid: {
    border: '1px solid black',
    padding: '4px 20px',
    fontSize: '14px',
    backgroundColor: '#dedcdc',
  },
  printed: {
    fontSize: '8px',
  },
  systemMessage: {
    fontSize: '6px',
    fontFamily: 'Open Sans',
    fontWeight: 'bold',
  },

  subTotal: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: '4px 20px 4px 4px',
    borderBottom: '1px solid black',
    borderLeft: '1px solid black',
    borderRight: '1px solid black',
    flexDirection: 'row',
  },

  bottomBillingTextLeft: {
    width: '100px',
    fontSize: '10px',
    fontFamily: 'Open Sans',
    fontWeight: 800,
  },
  bottomBillingTextRight: {
    fontSize: '10px',
    textAlign: 'right',
    marginRight: '-18px',
    fontFamily: 'Open Sans',
    fontWeight: 800,
  },

  flexAndGap: {
    display: 'flex',
    alignItems: 'flex-start',
    flexDirection: 'row',
    gap: '2px',
  },

  text: {
    fontSize: '10px',
  },

  flexBetween: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    flexDirection: 'row',
  },
  // table design new
  textLeft: {
    textAlign: 'left',
  },
  textCenter: {
    textAlign: 'center',
  },
  textRight: {
    textAlign: 'right',
  },
  fullTable: {
    borderTop: '1px solid #E3EBF6',
    borderLeft: '1px solid #E3EBF6',
    borderRight: '1px solid #E3EBF6',
  },
  tableHead: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    flexDirection: 'row',
    backgroundColor: '#F2F2F2',
    fontSize: '10px',
    borderBottom: '1px solid #E3EBF6',
  },
  tableBody: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    flexDirection: 'row',
    fontSize: '10px',
    borderBottom: '1px solid #E3EBF6',
  },
  tableHeadOne: {
    width: '60%',
    paddingLeft: 10,
    paddingVertical: 8,
  },
  tableHeadTwo: {
    width: '10%',
    paddingVertical: 8,
  },
  tableHeadThree: {
    width: '15%',
    paddingVertical: 8,
  },
  tableHeadFour: {
    width: '15%',
    paddingRight: 10,
    paddingVertical: 8,
  },
  tableFooter: {
    marginTop: 20,
    display: 'flex',
    flexDirection: 'row',
  },
  tableFooterLeft: {
    width: '60%',
  },
  tableFooterRightFull: {
    borderLeft: '1px solid #E3EBF6',
    borderBottom: '1px solid #E3EBF6',
    borderRight: '1px solid #E3EBF6',
  },
  tableFooterRight: {
    width: '40%',
  },
  tableNote: {
    fontSize: '10px',
    color: '#5A5A5A',
    paddingRight: 100,
    marginBottom: 10,
  },
  tableNoteDes: {
    fontSize: '10px',
    color: '#181818;',
    paddingRight: 100,
    marginBottom: 5,
  },
  calculation: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 5,
    paddingHorizontal: 5,
  },
  calculationBg: {
    backgroundColor: '#F2F2F2',
    paddingVertical: 5,
  },
  calculationText: {
    fontSize: '10px',
    color: '#181818;',
  },
  copyright: {
    marginTop: 5,
  },
  copyrightText: {
    fontSize: '8px',
    color: '#5E6470',
  },
});
