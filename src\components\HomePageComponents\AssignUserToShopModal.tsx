import { useFormik } from 'formik';
import Cookies from 'js-cookie';
import { toast } from 'react-toastify';
import * as Yup from 'yup';

import FilledSubmitButton from '../reusable/Buttons/FilledSubmitButton';
import CustomDropdown from '../reusable/CustomInputField/CustomDropdown';
import ModalTitle from '../reusable/Modal/ModalTitle';

import {
  useAssignEmployeeToShopMutation,
  useGetSellersQuery,
} from '@/redux/api/warehouseApis/sellersApis';
import { SingleSellerDetails } from '@/types/warehouseTypes/sellersTypes';

const formikInitialValues = {
  role: '',
  user: '',
};

const validation = Yup.object({
  user: Yup.string().required('Employee is required'),
  role: Yup.string().required('Role is required'),
});

interface Props {
  shopId: string;
  handleClose: () => void;
  type: string;
}

function AssignUserToShopModal({ handleClose, shopId, type }: Props) {
  const { data, isLoading } = useGetSellersQuery({
    organizationId: Cookies.get('organizationId'),
    limit: '30',
  });

  const [assignEmployeeToShop, { isLoading: isSellerUpdating }] =
    useAssignEmployeeToShopMutation();

  const formik = useFormik({
    initialValues: formikInitialValues,
    validationSchema: validation,

    onSubmit: async (values) => {
      const warehouseId = Cookies.get('warehouseId');

      const userAssignData = {
        role: values.role,
        warehouseId,
        assignedShopId: type === 'Shop' ? shopId : null,
      };
      toast.promise(
        assignEmployeeToShop({
          data: userAssignData,
          id: values.user,
        }).unwrap(),
        {
          pending: 'Assigning Employee To Shop...',
          success: {
            render({ data: res }) {
              if (res.statusCode === 200 || res.statusCode === 201) {
                handleClose();
              }
              return 'Employee Assigned Successfully';
            },
          },
          error: {
            render({ data: error }) {
              console.log(error);
              return 'Error on assign employee';
            },
          },
        },
      );
    },
  });
  return (
    <div className="min-w-[400px] rounded-xl bg-white p-4">
      <ModalTitle text={`Assign User To ${type}`} handleClose={handleClose} />
      <form onSubmit={formik.handleSubmit} className="mt-4 flex flex-col gap-4">
        <CustomDropdown
          placeholder="Select Employee"
          name="user"
          label="Employee"
          formik={formik}
          options={
            !isLoading
              ? data?.data?.map((singleEmployee: SingleSellerDetails) => {
                  return {
                    value: singleEmployee?.id,
                    label: singleEmployee?.User?.name,
                  };
                })
              : []
          }
        />
        <CustomDropdown
          placeholder="Select Role"
          name="role"
          label="Role"
          formik={formik}
          options={
            type === 'Shop'
              ? [
                  // { value: 'ADMIN', label: 'ADMIN' },
                  { value: 'MANAGER', label: 'MANAGER' },
                  // { value: 'ACCOUNTANT', label: 'ACCOUNTANT' },
                  { value: 'MODERATOR', label: 'MODERATOR' },
                  { value: 'SHOP_BILLER', label: 'BILLER' },
                ]
              : [
                  // { value: 'ADMIN', label: 'ADMIN' },
                  { value: 'MANAGER', label: 'MANAGER' },
                  { value: 'STOCK_CLERK', label: 'STOCK MANAGER' },
                  // { value: 'ACCOUNTANT', label: 'ACCOUNTANT' },
                ]
          }
        />
        <div className="mt-2">
          <FilledSubmitButton text="Submit" isLoading={isSellerUpdating} />
        </div>
      </form>
    </div>
  );
}

export default AssignUserToShopModal;
