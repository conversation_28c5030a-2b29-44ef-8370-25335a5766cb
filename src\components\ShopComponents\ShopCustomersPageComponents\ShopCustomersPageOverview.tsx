import Cookies from 'js-cookie';
import { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import AddOrEditCustomerModal from './AddOrEditCustomerModal';

import {
  CartButton,
  EyeButton,
} from '@/components/reusable/Buttons/CommonButtons';
import ExportButton from '@/components/reusable/Buttons/ExportButton';
import FilledButton from '@/components/reusable/Buttons/FilledButton';
import FilterButton from '@/components/reusable/Buttons/FilterButton';
import SearchInput from '@/components/reusable/Inputs/SearchInput';
import Modal from '@/components/reusable/Modal/Modal';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import Pagination from '@/components/reusable/Pagination/Pagination';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import { useGetShopCustomersQuery } from '@/redux/api/shopApis/shopCustomersApi';
import { ROUTES } from '@/Routes';
import { ShopCustomerDetails } from '@/types/shopTypes/shopCustomersTypes';
import { handleGenerateCustomerCsv } from '@/utils/GenerateCsv';

interface Props {
  shopId: string;
  warehouseId: string;
}
function ShopCustomersPageOverview({ shopId, warehouseId }: Props) {
  const navigate = useNavigate();
  const router = new URLSearchParams(useLocation().search);
  const mobileNumber = router.get('mobileNumber');
  const name = router.get('name');
  const page = router.get('page');
  const limit = router.get('limit');
  const [isFilterModalOpen, setIsFilterModalOpen] = useState<boolean>(false);
  const organizationId = Cookies.get('organizationId') as string;
  const { data, isLoading, refetch, isFetching } = useGetShopCustomersQuery(
    {
      organizationId,
      warehouseId: warehouseId ?? undefined,
      shopId,
      page: page ?? '1',
      limit: limit ?? '10',
      mobileNumber: mobileNumber ?? undefined,
      name: name ?? undefined,
    },
    { skip: !(shopId && organizationId) },
  );
  const [isCreateCustomerModalOpen, setIsCreateCustomerModalOpen] =
    useState<boolean>(false);

  const handleFilter = (fieldName: string, value: string) => {
    let query = '';
    if (fieldName === 'page') {
      query = `page=${value}${limit && `&limit=${limit}`}`;
    }
    if (fieldName === 'limit') {
      query = `page=1&limit=${value}`;
    }
    if (fieldName === 'name') {
      query = `page=1${value && `&name=${value}`}`;
    }
    if (fieldName === 'mobileNumber') {
      query = `page=1${value && `&mobileNumber=${value}`}`;
    }
    navigate(ROUTES.SHOP.CUSTOMERS(shopId, query));
  };

  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-1">
        <div className="flex items-center">
          <div className="search-title-and-btn flex items-center">
            <div className="relative">
              <div className="block xl:hidden">
                <FilterButton
                  handleClick={() => setIsFilterModalOpen(!isFilterModalOpen)}
                />
              </div>
              <div
                className={`${isFilterModalOpen ? 'block' : 'hidden'} xl:hidden`}
              >
                {/* <CustomerPageFilterModal /> */}
              </div>
            </div>
          </div>
          <div className="hidden xl:block">
            <div className="flex items-center gap-x-2">
              <SearchInput
                placeholder="Search by Name"
                handleSubmit={(value: string) => handleFilter('name', value)}
              />
              <SearchInput
                placeholder="Search by Mobile"
                handleSubmit={(value: string) =>
                  handleFilter('mobileNumber', value)
                }
              />
            </div>
          </div>
        </div>
        <div>
          <FilledButton
            isLoading={false}
            text="Add New"
            handleClick={() => setIsCreateCustomerModalOpen(true)}
            isDisabled={false}
          />
        </div>
      </div>
      <div>
        {!isLoading && !isFetching ? (
          <div>
            <div className="tableTop w-full">
              <p>Customers List</p>
              <div className="flex items-center gap-4">
                <p>Total : {data?.pagination?.total}</p>
                <ExportButton
                  totalCount={data?.data?.length ?? 0}
                  handleExportCsv={() => handleGenerateCustomerCsv(data?.data)}
                />
              </div>
            </div>
            <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
              {data?.data?.length ? (
                <div className="full-table-box h-custom">
                  <table className="full-table">
                    <thead className="bg-gray-100">
                      <tr>
                        <th className="tableHead">No</th>
                        <th className="tableHead table-col-width">Name</th>
                        <th className="tableHead">Phone</th>
                        <th className="tableHead table-col-width">Address</th>
                        <th className="tableHead">Order Count</th>
                        {/* <th className="tableHead">Created At</th> */}
                        <th className="tableHead">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y bg-slate-200">
                      {data?.data?.map(
                        (customer: ShopCustomerDetails, index: number) => (
                          <tr key={customer?.id}>
                            <td className="tableData">{index + 1}</td>
                            <td className="tableData table-col-width">
                              {customer?.name}
                            </td>
                            <td className="tableData">
                              {customer?.mobileNumber}
                            </td>
                            <td className="tableData table-col-width">
                              {customer?.address}
                            </td>
                            <td className="tableData">
                              {customer?.orderCount}
                            </td>
                            {/* <td className="tableData">
                              <DateAndTimeViewer date={customer.createdAt} />
                            </td> */}
                            <td className="tableData">
                              <div className="flex items-center justify-center gap-4">
                                <EyeButton
                                  handleClick={() =>
                                    navigate(
                                      ROUTES.SHOP.CUSTOMER_DETAILS(
                                        shopId,
                                        customer.id,
                                      ),
                                    )
                                  }
                                />
                                <CartButton
                                  handleClick={() =>
                                    navigate(
                                      ROUTES.SHOP.ADD_NEW_ORDER(
                                        shopId,
                                        customer.id,
                                      ),
                                    )
                                  }
                                />
                                {/* <EditButton
                                  handleClick={() => {
                                    toast.warning('clicked but no action set');
                                  }}
                                />
                                <DeleteButton
                                  handleClick={() => {
                                    toast.warning('clicked but no action set');
                                  }}
                                /> */}
                              </div>
                            </td>
                          </tr>
                        ),
                      )}
                    </tbody>
                  </table>
                </div>
              ) : (
                <NoResultFound pageType="customer" />
              )}
            </div>
            <div className="pagination-box flex justify-end rounded bg-white p-3">
              <Pagination
                currentPage={page ?? '1'}
                limit={Number(limit ?? 10)}
                handleFilter={(fieldName: string, value: any) =>
                  handleFilter(fieldName, value)
                }
                totalCount={data?.pagination?.total}
                totalPages={Math.ceil(
                  Number(data?.pagination?.total) /
                    Number(data?.pagination?.limit),
                )}
              />
            </div>
          </div>
        ) : (
          <TableSkeletonLoader tableColumn={6} tableRow={6} />
        )}
      </div>
      <Modal
        setShowModal={setIsCreateCustomerModalOpen}
        showModal={isCreateCustomerModalOpen}
      >
        <AddOrEditCustomerModal
          type="new"
          shopId={shopId}
          handleClose={() => setIsCreateCustomerModalOpen(false)}
          updateRefreshCounter={() => refetch()}
        />
      </Modal>
    </div>
  );
}

export default ShopCustomersPageOverview;
