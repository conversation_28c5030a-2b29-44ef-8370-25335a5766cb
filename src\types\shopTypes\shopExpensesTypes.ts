import { Pagination } from '@/redux/commonTypes';

export interface GetShopExpensesResponse {
  success: boolean;
  statusCode: number;
  message: string;
  data: ShopExpenseDetails[];
  pagination: Pagination;
}

export interface ShopExpenseDetails {
  id: string;
  createdAt: string;
  updatedAt: string;
  name: string;
  imgUrl?: string;
  amount: number;
  shopId?: string;
  warehouseId: string;
  createdById: string;
  expenseCategoryId: string;
  Shop?: Shop;
  Warehouse: Warehouse;
  CreatedBy: CreatedBy;
  ExpenseCategory: {
    id: string;
    name: string;
  };
}

export interface Shop {
  id: string;
  createdAt: string;
  updatedAt: string;
  name: string;
  imgUrl: any;
  mobileNumber: string;
  address: string;
  street: any;
  district: string;
  websiteUrl: string;
  fbUrl: string;
  zipCode: string;
  country: string;
  warehouseId: string;
  type: string;
  lastOrderNo: number;
  createdById: any;
}

export interface Warehouse {
  id: string;
  createdAt: string;
  mobileNumber: string;
  updatedAt: string;
  name: string;
  address: any;
  street: any;
  district: any;
  zipCode: any;
  country: any;
  imgUrl: any;
  organizationId: string;
}

export interface CreatedBy {
  id: string;
  createdAt: string;
  updatedAt: string;
  name: string;
  email: string;
  username: string;
  mobileNumber: string;
  password: string;
  refreshToken: string;
  imgUrl: any;
  organizationLimit: number;
  warehouseLimit: number;
  shopLimit: number;
  type: string;
}
