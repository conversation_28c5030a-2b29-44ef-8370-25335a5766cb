interface Props {
  tableColumn: number;
  tableRow: number;
}

let uniqueIdCounter = 0;

const generateUniqueId = () => {
  uniqueIdCounter += 1;
  return `unique-${Date.now()}-${uniqueIdCounter}`;
};

export default function TableSkeletonLoaderHalf({
  tableColumn,
  tableRow,
}: Props) {
  const thKeys = Array.from({ length: tableColumn }, generateUniqueId);
  const tdRowKeys = Array.from({ length: tableRow }, generateUniqueId);
  const tdColumnKeys = (rowCount: number) =>
    Array.from({ length: rowCount }, generateUniqueId);

  return (
    <div className="full-table-container w-full">
      <div className="full-table-box h-customSkeleton">
        <table className="full-table animate-pulse">
          <thead className="bg-gray-100">
            <tr>
              {thKeys.map((key) => (
                <th key={key} className="tableHead tableHeadSkeleton">
                  <span />
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="divide-y bg-slate-200">
            {tdRowKeys.map((rowKey) => (
              <tr key={rowKey}>
                {tdColumnKeys(tableColumn).map((colKey) => (
                  <td key={colKey} className="tableData tableDataSkeleton" />
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
