import Cookies from 'js-cookie';
import { useState } from 'react';

import { EditButton } from '@/components/reusable/Buttons/CommonButtons';
import FilledButton from '@/components/reusable/Buttons/FilledButton';
import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import Modal from '@/components/reusable/Modal/Modal';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import AddOrEditCourierSettingsModal from '@/components/WarehouseComponents/CourierSettingsPageComponents/AddOrEditCourierSettingsModal';
import { useGetOrgCouriersQuery } from '@/redux/api/organizationApis/organizationBasicApis';
import { SingleCourierDetails } from '@/types/warehouseTypes/settingsTypes';
import { ProtectedRoute } from '@/utils/ProtectedRoutes';

function OrganizationCouriersPage() {
  const { data, isLoading, refetch } = useGetOrgCouriersQuery({
    organizationId: Cookies.get('organizationId'),
  });
  const [isAddNewCourierModalOpen, setIsAddNewCourierModalOpen] =
    useState<boolean>(false);
  const [isEditCourierModalOpen, setIsEditCourierModalOpen] =
    useState<boolean>(false);
  const [editCourierData, setEditCourierData] =
    useState<SingleCourierDetails>();
  return (
    <ProtectedRoute>
      <div>
        <div className="search-filters mb-4 flex items-center justify-end rounded bg-white px-3 py-3 lg:py-2">
          <div>
            <FilledButton
              isLoading={false}
              text="Add New Courier"
              handleClick={() => setIsAddNewCourierModalOpen(true)}
              isDisabled={false}
            />
          </div>
        </div>
        {!isLoading ? (
          <div>
            <div className="tableTop w-full">
              <p>Courier List</p>
              <p>Total : {data?.pagination?.total}</p>
            </div>
            <div className="full-table-container w-full">
              {data?.data?.length ? (
                <div className="full-table-box h-custom">
                  <table className="full-table">
                    <thead className="bg-gray-100">
                      <tr>
                        <th className="tableHead">No</th>
                        <th className="tableHead">Courier</th>
                        <th className="tableHead">Nickname</th>
                        <th className="tableHead">Assigned Shop</th>
                        <th className="tableHead">Created At</th>
                        <th className="tableHead">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y bg-slate-200">
                      {data?.data?.map(
                        (brand: SingleCourierDetails, index: number) => (
                          <tr key={brand?.id}>
                            <td className="tableData">{index + 1}</td>
                            <td className="tableData">{brand?.type}</td>
                            <td className="tableData">{brand?.nickName}</td>
                            <td className="tableData">
                              {brand?.AssignedShop?.id
                                ? `${brand?.AssignedShop?.name} (
                            ${brand?.AssignedShop?.nickName})`
                                : 'Not Assigned Yet'}
                            </td>
                            <td className="tableData">
                              <DateAndTimeViewer date={brand?.createdAt} />
                            </td>
                            <td className="tableData">
                              <div className="flex items-center justify-center gap-2">
                                <EditButton
                                  handleClick={() => {
                                    setEditCourierData(brand);
                                    setIsEditCourierModalOpen(true);
                                  }}
                                />
                                {/* <DeleteButton
                                  handleClick={() => {
                                    toast.warning('clicked');
                                  }}
                                /> */}
                              </div>
                            </td>
                          </tr>
                        ),
                      )}
                    </tbody>
                  </table>
                </div>
              ) : (
                <NoResultFound pageType="courier" />
              )}
            </div>
          </div>
        ) : (
          <TableSkeletonLoader tableColumn={6} tableRow={6} />
        )}
        <Modal
          setShowModal={setIsAddNewCourierModalOpen}
          showModal={isAddNewCourierModalOpen}
        >
          <AddOrEditCourierSettingsModal
            type="new"
            handleClose={() => setIsAddNewCourierModalOpen(false)}
            updateRefreshCounter={refetch}
          />
        </Modal>
        <Modal
          setShowModal={setIsEditCourierModalOpen}
          showModal={isEditCourierModalOpen}
        >
          <AddOrEditCourierSettingsModal
            type="edit"
            handleClose={() => setIsEditCourierModalOpen(false)}
            updateRefreshCounter={refetch}
            courierDetails={editCourierData}
          />
        </Modal>
      </div>
    </ProtectedRoute>
  );
}

export default OrganizationCouriersPage;
