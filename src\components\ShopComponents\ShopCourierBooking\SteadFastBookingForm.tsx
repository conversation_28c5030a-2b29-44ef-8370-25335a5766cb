import { useFormik } from 'formik';
import { useEffect } from 'react';
import { toast } from 'react-toastify';
import * as Yup from 'yup';

import FilledSubmitButton from '@/components/reusable/Buttons/FilledSubmitButton';
import CustomInputField from '@/components/reusable/CustomInputField/CustomInputField';
import CustomTextArea from '@/components/reusable/CustomInputField/CustomTextArea';
import { useBookOrderOnSteadfastMutation } from '@/redux/api/shopApis/shopOrdersApis';
import { useAppSelector } from '@/redux/hooks';
import { ShopOrderDetails } from '@/types/shopTypes/shopOrderTypes';
import { handleGenerateSingleOrderPdf } from '@/utils/GenerateOrderPdf';
import { OrderTotalDueCalculator } from '@/utils/OrderTotalPriceCalculator';

interface Props {
  orderDetails?: ShopOrderDetails;
  courierId?: string;
  handleClose: () => void;
}

const formikInitialValues = {
  invoice: null,
  recipientName: null,
  recipientPhone: null,
  recipientAddress: null,
  codAmount: 0,
  note: null,
  orderId: null,
};

const validation = Yup.object({
  invoice: Yup.string().required('Order Id is required'),
});

function SteadFastBookingForm({ orderDetails, courierId, handleClose }: Props) {
  const { shopSettings } = useAppSelector((state) => state);
  const [bookOrderOnSteadfast, { isLoading: isOrderCreating }] =
    useBookOrderOnSteadfastMutation();

  const formik = useFormik({
    initialValues: formikInitialValues,
    validationSchema: validation,

    onSubmit: async (values) => {
      const afterBooking = localStorage.getItem('afterBooking');
      toast.promise(bookOrderOnSteadfast({ ...values, courierId }).unwrap(), {
        pending: 'Order Booking on Courier...',
        success: {
          render({ data: res }) {
            if (res?.statusCode === 200 || res?.statusCode === 201) {
              if (afterBooking === 'print-invoice') {
                handleGenerateSingleOrderPdf(
                  res?.data,
                  shopSettings.returnPolicyText,
                );
              }
              handleClose();
            }
            return 'Order booked Successfully';
          },
        },
        error: {
          render({ data: error }) {
            console.log(error);
            return 'Error on booking order';
          },
        },
      });
    },
  });

  useEffect(() => {
    if (orderDetails) {
      const cod = OrderTotalDueCalculator({
        productPrice: orderDetails.subTotal,
        deliveryCharge: orderDetails.deliveryCharge,
        productDiscount: orderDetails?.productDiscount,
        adminDiscount: orderDetails.adminDiscount,
        paidAmount: orderDetails.totalPaid,
      });
      formik.setFieldValue('invoice', orderDetails?.serialNo?.toString());
      formik.setFieldValue(
        'recipientName',
        orderDetails?.customerName ?? orderDetails?.Customer?.name,
      );
      formik.setFieldValue(
        'recipientPhone',
        orderDetails?.customerMobileNumber ??
          orderDetails?.Customer?.mobileNumber,
      );
      formik.setFieldValue('recipientAddress', orderDetails?.address);
      formik.setFieldValue('codAmount', cod);
      formik.setFieldValue('note', orderDetails?.orderNotes[0]?.notes ?? '');
      formik.setFieldValue('orderId', orderDetails?.id);
    }
  }, [orderDetails]);

  return (
    <div className="flex flex-col gap-4">
      <form
        onSubmit={formik.handleSubmit}
        className="flex w-full flex-col gap-4"
      >
        <div className="flex w-full gap-2">
          <CustomInputField
            type="text"
            placeholder="Enter Phone Number"
            name="recipientName"
            label="Name"
            formik={formik}
          />
          <CustomInputField
            type="text"
            placeholder="Enter Phone Number"
            name="recipientPhone"
            label="Phone"
            formik={formik}
          />
        </div>
        <div className="flex w-full gap-2">
          <CustomInputField
            type="text"
            placeholder="Order Id"
            name="invoice"
            label="Order ID"
            formik={formik}
          />
          <CustomInputField
            type="text"
            placeholder="COD Amount"
            name="codAmount"
            label="COD Amount"
            formik={formik}
          />
        </div>
        <CustomTextArea
          placeholder="Enter Customer Address"
          name="recipientAddress"
          label="Address"
          formik={formik}
        />
        <CustomTextArea
          placeholder="Note"
          name="note"
          label="Note"
          formik={formik}
        />

        {/* <div>
          <span>After Registration Customer</span>
          <div className="ml-4 flex items-center gap-4">
            <div className="flex items-center space-x-2">
              <input
                type="radio"
                id="stay-here"
                name="stay-here"
                value="stay"
                checked={afterRegistrationOption === 'go-billing'}
                onClick={() => setAfterRegistrationOption('go-billing')}
                className="form-radio h-4 w-4 text-blue-600 transition duration-150 ease-in-out"
              />
              <label
                htmlFor="stay-here"
                className="cursor-pointer select-none text-gray-700"
                onClick={() => setAfterRegistrationOption('go-billing')}
              >
                Go To Billing
              </label>
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="radio"
                id="stay-here"
                name="stay-here"
                value="stay"
                checked={afterRegistrationOption === 'stay-here'}
                onClick={() => setAfterRegistrationOption('stay-here')}
                className="form-radio h-4 w-4 text-blue-600 transition duration-150 ease-in-out"
              />
              <label
                htmlFor="stay-here"
                className="cursor-pointer select-none text-gray-700"
                onClick={() => setAfterRegistrationOption('stay-here')}
              >
                Stay On This Page
              </label>
            </div>
          </div>
        </div> */}
        <FilledSubmitButton
          text="Book On Steadfast"
          isLoading={isOrderCreating}
        />
      </form>
    </div>
  );
}

export default SteadFastBookingForm;
