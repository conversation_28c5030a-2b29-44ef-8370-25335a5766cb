import { useState } from 'react';

import ShopTransferCashPageFilterModal from './ShopTransferCashPageFilterModal';
import ShopTransferCashToOwnerModal from './ShopTransferCashToOwnerModal';

import FilledButton from '@/components/reusable/Buttons/FilledButton';
import FilterButton from '@/components/reusable/Buttons/FilterButton';
import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import SearchInput from '@/components/reusable/Inputs/SearchInput';
import Modal from '@/components/reusable/Modal/Modal';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import Pagination from '@/components/reusable/Pagination/Pagination';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import { useGetShopCashTransferListQuery } from '@/redux/api/shopApis/shopCashTransferApis';
import { SingleCashTransferDetails } from '@/types/shopTypes/shopTransferAmountToOwnerTypes';

interface Props {
  shopId: string;
  warehouseId: string;
}

function ShopTransferCashPageOverview({ shopId, warehouseId }: Props) {
  const [isFilterModalOpen, setIsFilterModalOpen] = useState<boolean>(false);
  const [isCreateBrandModalOpen, setIsCreateBrandModalOpen] =
    useState<boolean>(false);
  const { data, isLoading, refetch } = useGetShopCashTransferListQuery(
    {
      shopId,
      warehouseId,
    },
    { skip: !(shopId && warehouseId) },
  );

  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 lg:py-1">
        <div className="flex items-center">
          <div className="search-title-and-btn flex items-center">
            <div className="relative">
              <div className="block lg:hidden">
                <FilterButton
                  handleClick={() => setIsFilterModalOpen(!isFilterModalOpen)}
                />
              </div>
              <div
                className={`${isFilterModalOpen ? 'block' : 'hidden'} xl:hidden`}
              >
                <ShopTransferCashPageFilterModal />
              </div>
            </div>
          </div>
          <div className="hidden lg:block">
            <div className="flex items-center gap-x-2">
              <SearchInput
                placeholder="Search by Name"
                handleSubmit={(value: string) => console.log(value)}
              />
            </div>
          </div>
        </div>
        <div>
          <FilledButton
            isLoading={false}
            text="Transfer Amount"
            handleClick={() => setIsCreateBrandModalOpen(true)}
            isDisabled={false}
          />
        </div>
      </div>
      <div>
        {!isLoading ? (
          <div>
            <div className="tableTop w-full">
              <p>Amount Transfer List</p>
              <p>Total : {data?.pagination?.total}</p>
            </div>
            <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
              {data?.data?.length ? (
                <div className="full-table-box h-custom">
                  <table className="full-table">
                    <thead className="bg-gray-100">
                      <tr>
                        <th className="tableHead">No</th>
                        <th className="tableHead">Received By</th>
                        <th className="tableHead">Transferred By</th>
                        <th className="tableHead">Amount</th>
                        <th className="tableHead">Created At</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y bg-slate-200">
                      {data?.data?.map(
                        (expense: SingleCashTransferDetails, index: number) => (
                          <tr key={expense?.id}>
                            <td className="tableData">{index + 1}</td>
                            <td className="tableData">
                              {expense?.transferredTo}
                            </td>
                            <td className="tableData">
                              {expense?.Sender?.name}
                            </td>
                            <td className="tableData">{expense?.amount}</td>
                            <td className="tableData">
                              <DateAndTimeViewer date={expense?.createdAt} />
                            </td>
                          </tr>
                        ),
                      )}
                    </tbody>
                  </table>
                </div>
              ) : (
                <NoResultFound pageType="cash transfer" />
              )}
            </div>
            <div className="pagination-box flex justify-end rounded bg-white p-3">
              <Pagination
                currentPage="1"
                limit={Number(10)}
                handleFilter={(fieldName: string, value: any) =>
                  // handleFilter(fieldName, value)
                  console.log(fieldName, value)
                }
                totalCount={data?.pagination?.total}
                totalPages={Math.ceil(
                  Number(data?.pagination?.total) /
                    Number(data?.pagination?.limit),
                )}
              />
            </div>
          </div>
        ) : (
          <TableSkeletonLoader tableColumn={5} tableRow={6} />
        )}
      </div>
      <Modal
        setShowModal={setIsCreateBrandModalOpen}
        showModal={isCreateBrandModalOpen}
      >
        <ShopTransferCashToOwnerModal
          shopId={shopId}
          handleClose={() => setIsCreateBrandModalOpen(false)}
          updateRefreshCounter={refetch}
        />
      </Modal>
    </div>
  );
}

export default ShopTransferCashPageOverview;
