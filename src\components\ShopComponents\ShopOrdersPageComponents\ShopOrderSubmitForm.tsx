import { Plus, ShoppingBag } from 'lucide-react';

import { DeleteButton } from '@/components/reusable/Buttons/CommonButtons';
import FilledSubmitButton from '@/components/reusable/Buttons/FilledSubmitButton';
import { useAppSelector } from '@/redux/hooks';
import {
  GetSellersResponse,
  SingleSellerDetails,
} from '@/types/warehouseTypes/sellersTypes';
import { SingleStockDetails } from '@/types/warehouseTypes/stockTypes';
import { CalculateDiscountPrice } from '@/utils/CalculateDiscountPrice';
import { PaymentMethods } from '@/utils/staticData';
import StockName from '@/utils/StockName';

interface Props {
  formik: any;
  selectedProducts?: SingleStockDetails[];
  handleSelectProduct: (stock: any) => void;
  isOrderCreating: boolean;
  netTotal: number;
  setIsMultiplePaymentModalOpen: (open: boolean) => void;
  selectedPayments: any[];
  setSelectedPayments: any;
  totalPaidAmount: number;
  discountPercentage: number;
  setDiscountPercentage: (percentage: number) => void;
  employees?: GetSellersResponse;
  isEmployeesDataLoading?: boolean;
}

function ShopOrderSubmitForm({
  selectedProducts,
  handleSelectProduct,
  formik,
  setDiscountPercentage,
  discountPercentage,
  netTotal,
  selectedPayments,
  setSelectedPayments,
  setIsMultiplePaymentModalOpen,
  totalPaidAmount,
  isOrderCreating,
  employees,
  isEmployeesDataLoading,
}: Props) {
  const { userDetails } = useAppSelector((state) => state);
  return (
    <div className="mt-1">
      {/* <span className="text-xl font-bold">Cart Details</span> */}
      <div className="full-table-box h-customtable">
        <table className="full-table bg-white">
          <thead className="bg-gray-100">
            <tr>
              <th className="tableHead">No</th>
              <th className="tableHead">Barcode</th>
              <th className="tableHeadLeftAlign">Name</th>
              <th className="tableHeadRightAlign">Regular Price</th>
              <th className="tableHeadRightAlign">Discount</th>
              <th className="tableHead">QTY</th>
              <th className="tableHeadRightAlign">Total</th>
              <th className="tableHead">Action</th>
            </tr>
          </thead>
          <tbody className="divide-y">
            {selectedProducts?.length ? (
              selectedProducts?.map(
                (stock: SingleStockDetails, index: number) => (
                  <tr key={stock?.id}>
                    <td className="tableData">{index + 1}</td>
                    <td className="tableData">{stock?.barcode}</td>
                    <td className="tableDataLeftAlign">
                      <StockName stock={stock} name={stock.name} />
                    </td>
                    <td className="tableDataRightAlign">
                      {stock?.retailPrice.toFixed(2)}
                    </td>
                    <td className="tableDataRightAlign">
                      {(
                        Number(stock.retailPrice) -
                        CalculateDiscountPrice({
                          retailPrice: stock?.retailPrice,
                          discountType: stock.discountType,
                          discount: stock.discount,
                        })
                      ).toFixed(2)}
                    </td>
                    <td className="tableData">1</td>
                    <td className="tableDataRightAlign">
                      {CalculateDiscountPrice({
                        retailPrice: stock?.retailPrice,
                        discountType: stock.discountType,
                        discount: stock.discount,
                      }).toFixed(2)}
                    </td>
                    <td className="tableData">
                      <DeleteButton
                        handleClick={() => {
                          handleSelectProduct(stock);
                        }}
                      />
                    </td>
                  </tr>
                ),
              )
            ) : (
              <tr>
                <td className="tableData" colSpan={8}>
                  <div className="blink-animation flex items-center justify-center gap-4 bg-orange-400 py-2 text-lg font-bold italic text-white">
                    <ShoppingBag />
                    <p>No Product Added in cart</p>
                  </div>
                </td>
              </tr>
            )}
            <tr className="border">
              <td
                colSpan={4}
                className="border border-gray-300 p-1 text-right text-xs font-bold"
              >
                {userDetails?.shopType === 'ONLINE' ? (
                  <div className="flex items-center gap-2">
                    <span className="w-full">Order Source :</span>
                    <select
                      value={formik.values.orderSource}
                      name="orderSource"
                      onChange={formik.handleChange}
                      className="w-[120px] rounded border border-gray-300 bg-gray-50 px-2 py-1 text-xs focus:border-blue-500 focus:ring-blue-500"
                    >
                      <option value="" label="Select Platform" />
                      {[
                        { value: 'FACEBOOK', label: 'FACEBOOK' },
                        { value: 'TIKTOK', label: 'TIKTOK' },
                        { value: 'WEBSITE', label: 'WEBSITE' },
                        { value: 'TWITTER', label: 'TWITTER' },
                        { value: 'YOUTUBE', label: 'YOUTUBE' },
                        { value: 'INSTAGRAM', label: 'INSTAGRAM' },
                        { value: 'OTHER', label: 'OTHER' },
                      ]?.map((option) => (
                        <option
                          key={option.value}
                          value={option.value}
                          label={option.label}
                        >
                          {option.label}
                        </option>
                      ))}
                    </select>
                  </div>
                ) : (
                  ''
                )}
              </td>
              <td
                colSpan={2}
                className="border border-gray-300 p-1 text-right text-xs font-bold"
              >
                Cart Total :
              </td>
              <td className="border border-gray-300 p-1 text-right text-xs font-bold">
                {formik.values.totalProductPrice.toFixed(2)}
              </td>
              <td />
            </tr>
            <tr className="border">
              <td
                colSpan={4}
                className="border border-gray-300 p-1 text-right text-xs font-bold"
              >
                <div className="flex items-center gap-2">
                  <span className="w-full">Discount Amount :</span>
                  <input
                    type="number"
                    min={Math.ceil(formik.values.productDiscount)}
                    value={
                      formik.values.totalDiscount === 0
                        ? ''
                        : formik.values.totalDiscount
                    }
                    className="w-[116px] rounded border border-gray-300 px-4 py-[5px]"
                    name="totalDiscount"
                    onChange={(e) => {
                      formik.setFieldValue(
                        'totalDiscount',
                        Number(e.target?.value),
                      );
                      const percentage =
                        (Number(e.target.value) * 100) /
                        Number(formik.values?.totalProductPrice);
                      setDiscountPercentage(Number(percentage.toFixed(2)));
                    }}
                  />
                </div>
              </td>

              <td
                colSpan={2}
                className="border border-gray-300 p-1 text-right text-xs font-bold"
              >
                Discount :
              </td>
              <td className="border border-gray-300 p-1 text-right text-xs font-bold">
                (-){formik.values.totalDiscount?.toFixed(2)}
              </td>
              <td />
            </tr>
            <tr className="border">
              <td
                colSpan={4}
                className="border border-gray-300 p-1 text-right text-xs font-bold"
              >
                <div className="flex items-center gap-2">
                  <span className="w-full">Discount Percentage :</span>
                  <input
                    value={discountPercentage === 0 ? '' : discountPercentage}
                    className="w-[116px] rounded border border-gray-300 px-4 py-1"
                    type="number"
                    onChange={(e) => {
                      setDiscountPercentage(Number(e.target.value));
                      const totalDis =
                        (Number(formik.values?.totalProductPrice) *
                          Number(e.target.value)) /
                        100;
                      formik.setFieldValue(
                        'totalDiscount',
                        Math.round(totalDis),
                      );
                    }}
                  />
                </div>
              </td>

              {userDetails?.shopType === 'ONLINE' ? (
                <>
                  <td
                    colSpan={2}
                    className="w-[120px] border border-gray-300 p-1 text-right text-xs font-bold"
                  >
                    Delivery Charge :
                  </td>
                  <td className="border border-gray-300 p-1 text-right text-xs font-bold">
                    <input
                      value={
                        formik.values.deliveryCharge === 0
                          ? ''
                          : formik.values.deliveryCharge
                      }
                      className="w-[80px] rounded border border-gray-200 py-1 pr-4 text-right"
                      name="deliveryCharge"
                      onChange={formik.handleChange}
                    />
                  </td>
                </>
              ) : (
                <>
                  <td
                    colSpan={2}
                    className="border border-gray-300 p-1 text-right text-xs font-bold"
                  >
                    Vat :
                  </td>
                  <td className="border border-gray-300 p-1 text-right text-xs font-bold">
                    (+){formik.values.vat.toFixed(2)}
                  </td>
                </>
              )}
              <td className="border border-gray-300 p-1 text-right text-xs font-bold" />
            </tr>
            {userDetails?.shopType === 'ONLINE' ? (
              <tr className="border">
                <td
                  colSpan={4}
                  className="border border-gray-300 p-1 text-right text-xs font-bold"
                >
                  <div className="flex items-center gap-2">
                    <span className="w-full">Seller :</span>
                    <select
                      value={formik.values.employeeId}
                      name="employeeId"
                      onChange={formik.handleChange}
                      className="w-[120px] rounded border border-gray-300 bg-gray-50 px-2 py-1 text-xs focus:border-blue-500 focus:ring-blue-500"
                    >
                      <option value="" label="Select Seller" />
                      {!isEmployeesDataLoading &&
                        employees?.data?.map((option: SingleSellerDetails) => (
                          <option
                            key={option.id}
                            value={option.id}
                            label={option.name}
                          >
                            {option.User?.name}
                          </option>
                        ))}
                    </select>
                  </div>
                </td>
                <td
                  colSpan={2}
                  className="border border-gray-300 p-1 text-right text-xs font-bold"
                >
                  Vat :
                </td>
                <td className="border border-gray-300 p-1 text-right text-xs font-bold">
                  (+){formik.values.vat.toFixed(2)}
                </td>
                <td className="border border-gray-300 p-1 text-right text-xs font-bold" />
              </tr>
            ) : (
              ''
            )}
            <tr className="border">
              <td
                colSpan={4}
                className="border border-gray-300 p-1 text-right text-xs font-bold"
              >
                {userDetails?.shopType === 'ONLINE' ? (
                  <div className="flex items-center gap-2">
                    <span className="w-full">Delivery Partner :</span>
                    <select
                      value={formik.values.deliveryPartner}
                      name="deliveryPartner"
                      onChange={formik.handleChange}
                      className="w-[120px] rounded border border-gray-300 bg-gray-50 px-2 py-1 text-xs focus:border-blue-500 focus:ring-blue-500"
                    >
                      <option value="" label="Select Option" />
                      {[
                        { label: 'Pathao', value: 'PATHAO' },
                        { label: 'OTHER', value: 'OTHER' },
                      ]?.map((option: any) => (
                        <option
                          key={option.value}
                          value={option.value}
                          label={option.label}
                        >
                          {option.label}
                        </option>
                      ))}
                    </select>
                  </div>
                ) : (
                  ''
                )}
              </td>
              <td
                colSpan={2}
                className="border border-gray-300 p-1 text-right text-xs font-bold"
              >
                Net Total :
              </td>
              <td className="border border-gray-300 p-1 text-right text-xs font-bold">
                {netTotal.toFixed(2)}
              </td>
              <td className="border border-gray-300 p-1 text-right text-xs font-bold" />
            </tr>
            <tr className="border">
              <td
                colSpan={4}
                className="border border-gray-300 p-1 text-right text-xs font-bold"
              >
                {selectedPayments?.length === 1 ? (
                  <div className="flex items-center gap-2">
                    <span className="w-full">Payment Method :</span>
                    <select
                      value={
                        selectedPayments?.length
                          ? selectedPayments[0]?.method
                          : ''
                      }
                      name="paymentMethod"
                      className="w-[150px] rounded border border-gray-300 bg-gray-50 px-2 py-1 text-xs focus:border-blue-500 focus:ring-blue-500"
                      onChange={(e) =>
                        setSelectedPayments([
                          {
                            method: e.target.value,
                            amount: selectedPayments[0]?.amount ?? 0,
                          },
                        ])
                      }
                    >
                      <option value="" label="Select Option" />
                      {PaymentMethods?.map((option: any) => (
                        <option
                          key={option.value}
                          value={option.value}
                          label={option.label}
                        >
                          {option.label}
                        </option>
                      ))}
                    </select>
                  </div>
                ) : (
                  <div className="flex items-center justify-end gap-2">
                    {selectedPayments?.map((sin) => (
                      <span>
                        {sin.method}-{sin.amount}
                      </span>
                    ))}
                  </div>
                )}
              </td>

              <td
                colSpan={2}
                className="border border-gray-300 p-1 text-right text-xs font-bold"
              >
                Paid Amount :
              </td>
              <td className="border border-gray-300 p-1 text-right text-xs font-bold">
                {selectedPayments?.length === 1 ? (
                  <input
                    value={
                      selectedPayments[0]?.amount > 0
                        ? selectedPayments[0]?.amount
                        : ''
                    }
                    name="customerPaid"
                    onChange={(e) =>
                      setSelectedPayments([
                        {
                          method: selectedPayments[0]?.method,
                          amount: Number(e.target.value) ?? '',
                        },
                      ])
                    }
                    className="w-[80px] rounded border border-gray-200 py-1 pr-4 text-right"
                  />
                ) : (
                  totalPaidAmount
                )}
              </td>
              <td className="border border-gray-300 p-1 text-left text-xs font-bold">
                <button
                  onClick={() => setIsMultiplePaymentModalOpen(true)}
                  type="button"
                >
                  <Plus size={20} />
                </button>
              </td>
            </tr>
            <tr className="border">
              <td colSpan={4}>
                <input
                  value={formik.values.note}
                  name="note"
                  onChange={formik.handleChange}
                  className="w-full rounded border border-gray-300 px-4 py-1"
                  placeholder="Remarks"
                />
              </td>
              <td
                colSpan={2}
                className="border border-gray-300 p-1 text-right text-xs font-bold"
              >
                Due/Return :
              </td>
              <td
                className={`border border-gray-300 p-1 text-right text-xs font-bold ${netTotal - Number(totalPaidAmount) > 0 ? 'blink-animation text-red-600' : ''}`}
              >
                {(netTotal - Number(totalPaidAmount)).toFixed(2)}
              </td>
              <td />
            </tr>
            <tr className="border">
              <td
                colSpan={4}
                className="border border-gray-300 p-1 text-center text-xs font-bold"
              />
              <td
                colSpan={3}
                className="border border-gray-300 p-1 text-center text-xs font-bold"
              >
                <FilledSubmitButton
                  text="Submit"
                  isLoading={isOrderCreating}
                  isDisabled={
                    !selectedProducts?.length ||
                    (userDetails?.shopType === 'PHYSICAL' &&
                      Number(totalPaidAmount) <= 0) ||
                    (userDetails?.shopType === 'ONLINE' &&
                      Number(formik.values.address) <= 0)
                  }
                />
              </td>
              <td className="border border-gray-300 p-1 text-center text-xs font-bold" />
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
}

export default ShopOrderSubmitForm;
