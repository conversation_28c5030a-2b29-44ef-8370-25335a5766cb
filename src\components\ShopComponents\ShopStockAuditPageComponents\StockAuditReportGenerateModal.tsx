import { useEffect } from 'react';

import PdfGeneratingLoader from '@/components/reusable/PdfGeneratingLoader/PdfGeneratingLoader';
import { useGetShopStockAuditDetailsListQuery } from '@/redux/api/shopApis/shopStockAuditApis';
import { handleGenerateStockAuditReportPdf } from '@/utils/GenerateReportPdf';

interface Props {
  auditId?: string;
  handleClose: () => void;
}

function StockAuditReportGenerateModal({ auditId, handleClose }: Props) {
  const { data, isLoading, isFetching } = useGetShopStockAuditDetailsListQuery(
    auditId,
    {
      skip: auditId === '',
    },
  );

  useEffect(() => {
    if (!isFetching && !isLoading && data?.data) {
      handleGenerateStockAuditReportPdf(data.data);
      handleClose();
    }
  }, [data]);

  return (
    <div>
      <PdfGeneratingLoader />
    </div>
  );
}

export default StockAuditReportGenerateModal;
