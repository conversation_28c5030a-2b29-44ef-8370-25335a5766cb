import { Font, Image, Page, Text, View } from '@react-pdf/renderer';
import JsBarcode from 'jsbarcode';
import { useEffect, useState } from 'react';

import { styles } from './SingleOrderInvoiceStyle';

import {
  ShopOrderDetails,
  ShopSingleOrderItem,
  SingleOrderProducts,
} from '@/types/shopTypes/shopOrderTypes';
import { numberToWords } from '@/utils/NumberToText';

Font.register({
  family: 'Open Sans',
  fonts: [
    {
      src: 'https://cdn.jsdelivr.net/npm/open-sans-all@0.1.3/fonts/open-sans-regular.ttf',
    },
    {
      src: 'https://cdn.jsdelivr.net/npm/open-sans-all@0.1.3/fonts/open-sans-600.ttf',
      fontWeight: 800,
    },
  ],
});

// Register Noto Sans Bengali fonts
Font.register({
  family: 'Noto Sans Bengali',
  fonts: [
    {
      src: 'https://fonts.gstatic.com/ea/notosansbengali/v3/NotoSansBengali-Regular.ttf',
    },
    {
      src: 'https://fonts.gstatic.com/ea/notosansbengali/v3/NotoSansBengali-Bold.ttf',
      fontWeight: 600,
    },
  ],
});

interface Props {
  orderDetails?: ShopOrderDetails;
  returnPolicyText?: string;
}

function SingleOrderInvoicePdf({ orderDetails, returnPolicyText }: Props) {
  const [barcode, setBarcode] = useState<string>('');

  const isBengaliText = (text: string) => {
    const bengaliRegex = /[\u0980-\u09FF]/; // Unicode range for Bengali script
    return bengaliRegex.test(text);
  };

  useEffect(() => {
    if (orderDetails?.serialNo) {
      const canvas = document.createElement('canvas');
      JsBarcode(canvas, orderDetails.serialNo.toString(), {
        format: 'CODE128',
        displayValue: false,
      });
      setBarcode(canvas.toDataURL('image/png'));
    }
  }, [orderDetails]);
  return (
    <Page size="A4" style={styles.page}>
      <View style={styles.container}>
        <View style={styles.section}>
          <Text style={styles.invoiceText}>INVOICE</Text>
          <Text style={styles.invoiceNo}>
            INVOICE NO : {orderDetails?.serialNo}
          </Text>

          <View style={styles.flexAndGap}>
            <Text style={styles.orderDate}>
              {new Date(orderDetails?.createdAt || '')
                .toDateString()
                .toUpperCase()}
            </Text>
            <Text style={styles.orderDate}>| </Text>
            <Text style={styles.orderDate}>
              {new Date(orderDetails?.createdAt || '').toLocaleTimeString()}
            </Text>
          </View>
          <Image src={barcode} style={styles.barcode} />
        </View>
        <View style={styles.sectionRight}>
          {/* <Image
            src="https://i.ibb.co.com/7KrJGNw/logo.png"
            style={styles.companyLogo}
          /> */}
          {orderDetails?.Shop?.imgUrl ? (
            <Image
              src={{
                uri: `https://retail-pluse-upload.s3.ap-southeast-1.amazonaws.com/${orderDetails?.Shop?.imgUrl}`,
                method: 'GET',
                headers: { 'Cache-Control': 'no-cache' },
                body: '',
              }}
              style={styles.companyLogo}
            />
          ) : (
            <Text style={styles.companyAddress}>
              {orderDetails?.Shop?.name}
            </Text>
          )}
          <Text style={styles.companyAddress}>
            {orderDetails?.Shop?.address}
          </Text>
          <Text style={styles.companyAddress}>
            Phone : {orderDetails?.Shop?.mobileNumber}
          </Text>
          <Text style={styles.companyOtherTexts}>
            {orderDetails?.Shop?.websiteUrl}
          </Text>
          <Text style={styles.companyOtherTexts}>
            {orderDetails?.Shop?.fbUrl}
          </Text>
        </View>
      </View>
      <View style={styles.bottomPartContainer}>
        <View>
          <View style={styles.invoiceToContainer}>
            <Text style={styles.invoiceToText}>INVOICE TO</Text>
            {isBengaliText(
              orderDetails?.customerName ?? orderDetails?.Customer?.name ?? '',
            ) ? (
              <Text style={styles.customerNameBangla}>
                {orderDetails?.customerName ?? orderDetails?.Customer?.name}
              </Text>
            ) : (
              <Text style={styles.customerName}>
                {orderDetails?.customerName ?? orderDetails?.Customer?.name}
              </Text>
            )}
            {/* <Text style={styles.customerName}>

            {orderDetails?.Customer?.name}
          </Text> */}
            <View style={styles.phoneNumberContainer}>
              <Text style={styles.text}>Phone :</Text>
              <Text style={styles.phoneNumber}>
                {orderDetails?.customerMobileNumber ??
                  orderDetails?.Customer?.mobileNumber}
              </Text>
            </View>
            <View style={styles.addressContainer}>
              <Text style={styles.text}>Address :</Text>
              {isBengaliText(orderDetails?.address ?? '') ? (
                <Text style={styles.addressBangla}>
                  {orderDetails?.address}
                </Text>
              ) : (
                <Text style={styles.addressEnglish}>
                  {orderDetails?.address}
                </Text>
              )}
              {/* <Text style={styles.address}>{orderDetails?.address}</Text> */}
            </View>
            <Text style={styles.note}>
              Note :{' '}
              {orderDetails?.orderNotes?.length
                ? orderDetails?.orderNotes[0]?.notes
                : ''}
            </Text>
            <Text style={styles.note}>
              Delivery Partner : {orderDetails?.deliveryPartner}
            </Text>
            <View style={styles.flexAndGap}>
              <Text style={styles.note}>Tracking Id :</Text>
              <Text style={styles.note}>
                {orderDetails?.trackingNumber ?? ''}
              </Text>
            </View>
          </View>
          {returnPolicyText?.length ? (
            <View
              style={{
                width: '170px',
                marginTop: '10px',
              }}
            >
              <Text
                style={{
                  fontSize: '10px',
                  fontWeight: 'normal',
                  textAlign: 'left',
                  fontStyle: 'italic',
                }}
              >
                Note: {returnPolicyText}
              </Text>
            </View>
          ) : (
            ''
          )}
        </View>
        <View style={styles.productsTable}>
          <View style={styles.tableHeader}>
            <Text style={styles.numberTitle}>#</Text>
            <Text style={styles.barcodeTitle}>Barcode</Text>
            <Text style={styles.nameTitle}>Product</Text>
            <Text style={styles.productPrice}>Price</Text>
            <Text style={styles.productQuantity}>QTY</Text>
            <Text style={styles.productTotal}>Total</Text>
          </View>
          <View>
            {orderDetails?.OrderItem?.map(
              (
                singleProduct: SingleOrderProducts | ShopSingleOrderItem,
                index: number,
              ) => (
                <View key={singleProduct?.id} style={styles.productRow}>
                  <Text style={styles.numberTitle}>{index + 1}.</Text>
                  <Text style={styles.stockId}>
                    {singleProduct?.Stock?.barcode}
                  </Text>
                  <Text style={styles.productName}>
                    {singleProduct?.Stock?.Product?.name}
                    {singleProduct?.Stock?.Variant?.name
                      ? `-${singleProduct?.Stock?.Variant?.name}`
                      : ''}
                  </Text>
                  <Text style={styles.productPrice}>
                    {singleProduct?.Stock?.retailPrice}
                  </Text>
                  <Text style={styles.productQuantity}>1</Text>
                  <Text style={styles.productTotal}>
                    {singleProduct?.Stock?.retailPrice}
                  </Text>
                </View>
              ),
            )}
          </View>
          <View style={styles.flexBetween}>
            <View>
              <View style={styles.duePart}>
                <Text style={styles.payableAmount}>
                  PAYABLE AMOUNT :{' '}
                  {numberToWords(
                    Number(orderDetails?.totalDue),
                  ).toLocaleUpperCase()}{' '}
                  TAKA ONLY
                </Text>
                <Text style={styles.dueOrPaid}>
                  {Number(orderDetails?.totalDue) > 0 ? 'DUE' : 'PAID'}
                </Text>
                <Text style={styles.printed}>
                  PRINTED BY : {orderDetails?.CreatedBy?.name}
                </Text>
                <Text style={styles.printed}>
                  PRINTED AT : {new Date().toDateString().toUpperCase()}{' '}
                  {new Date().toLocaleTimeString()}
                </Text>
                <Text style={styles.systemMessage}>
                  ** THIS IS A GENERATE INVOICE, NO NEED TO HAVE SIGNATURE **
                </Text>
              </View>
            </View>
            <View style={{ width: '150px', marginTop: '0px' }}>
              <View style={styles.subTotal}>
                <Text style={styles.bottomBillingTextLeft}>Cart Total</Text>
                <Text style={styles.bottomBillingTextRight}>
                  {Number(orderDetails?.grandTotal) +
                    Number(orderDetails?.productDiscount) +
                    Number(orderDetails?.adminDiscount)}
                </Text>
              </View>
              <View style={styles.subTotal}>
                <Text style={styles.bottomBillingTextLeft}>Discount</Text>
                <Text style={styles.bottomBillingTextRight}>
                  {Number(orderDetails?.productDiscount) +
                    Number(orderDetails?.adminDiscount)}
                </Text>
              </View>
              <View style={styles.subTotal}>
                <Text style={styles.bottomBillingTextLeft}>Subtotal</Text>
                <Text style={styles.bottomBillingTextRight}>
                  {Number(orderDetails?.grandTotal)}
                </Text>
              </View>
              <View style={styles.subTotal}>
                <Text style={styles.bottomBillingTextLeft}>
                  Delivery Charge
                </Text>
                <Text style={styles.bottomBillingTextRight}>
                  {orderDetails?.deliveryCharge}
                </Text>
              </View>
              <View style={styles.subTotal}>
                <Text style={styles.bottomBillingTextLeft}>Total</Text>
                <Text style={styles.bottomBillingTextRight}>
                  {Number(orderDetails?.grandTotal) +
                    Number(orderDetails?.deliveryCharge)}
                </Text>
              </View>
              <View style={styles.subTotal}>
                <Text style={styles.bottomBillingTextLeft}>Paid Amount</Text>
                <Text style={styles.bottomBillingTextRight}>
                  {orderDetails?.totalPaid}
                </Text>
              </View>
              <View style={styles.subTotal}>
                <Text style={styles.bottomBillingTextLeft}>Due Amount</Text>
                <Text style={styles.bottomBillingTextRight}>
                  {orderDetails?.totalDue}
                </Text>
              </View>
            </View>
          </View>
        </View>
      </View>
      <View
        style={{
          marginTop: 10,
        }}
      >
        <Text style={{ textAlign: 'center', fontSize: '8px' }}>
          Software Made By SOFTS.AI.
        </Text>
        <Text style={{ textAlign: 'center', fontSize: '8px' }}>
          visit : www.softs.ai
        </Text>
      </View>
    </Page>
  );
}

export default SingleOrderInvoicePdf;
