import { useParams } from 'react-router-dom';

import ProductDetailsPageOverview from '@/components/WarehouseComponents/ProductDetailsComponents/ProductDetailsPageOverview';
import { shopOrWarehouse } from '@/redux/commonTypes';

interface Props {
  type: shopOrWarehouse;
}
function ProductDetailsPage({ type }: Props) {
  const { warehouseId, productId, shopId } = useParams();
  return (
    <div>
      <ProductDetailsPageOverview
        productId={productId ?? ''}
        warehouseId={warehouseId ?? ''}
        shopId={shopId ?? ''}
        type={type}
      />
    </div>
  );
}

export default ProductDetailsPage;
