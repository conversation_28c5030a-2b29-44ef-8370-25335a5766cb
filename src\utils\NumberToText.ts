export function numberToWords(num: number): string {
  const belowTwenty: string[] = [
    'zero',
    'one',
    'two',
    'three',
    'four',
    'five',
    'six',
    'seven',
    'eight',
    'nine',
    'ten',
    'eleven',
    'twelve',
    'thirteen',
    'fourteen',
    'fifteen',
    'sixteen',
    'seventeen',
    'eighteen',
    'nineteen',
  ];
  const tens: string[] = [
    '',
    '',
    'twenty',
    'thirty',
    'forty',
    'fifty',
    'sixty',
    'seventy',
    'eighty',
    'ninety',
  ];
  const thousands: string[] = [
    '',
    'thousand',
    'million',
    'billion',
    'trillion',
  ];

  if (num === 0) return 'zero';

  let words = '';

  function helper(n: number): string {
    if (n < 20) return belowTwenty[n];
    if (n < 100)
      return (
        tens[Math.floor(n / 10)] +
        (n % 10 !== 0 ? ' ' + belowTwenty[n % 10] : '')
      );
    if (n < 1000)
      return (
        belowTwenty[Math.floor(n / 100)] +
        ' hundred' +
        (n % 100 !== 0 ? ' ' + helper(n % 100) : '')
      );
    for (let i = 0; i < thousands.length; i++) {
      const unit = 1000 ** (i + 1);
      if (n < unit)
        return (
          helper(Math.floor(n / (unit / 1000))) +
          ' ' +
          thousands[i] +
          (n % (unit / 1000) !== 0 ? ' ' + helper(n % (unit / 1000)) : '')
        );
    }
    return ''; // Added to satisfy TypeScript return type requirement
  }

  words = helper(num);
  return words;
}
