import { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import FilterButton from '@/components/reusable/Buttons/FilterButton';
import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import Pagination from '@/components/reusable/Pagination/Pagination';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import SubCategoryFilterOption from '@/components/WarehouseComponents/SubCategoriesPageComponents/SubCategoryFilterOption';
import SubCategoryPageFilterModal from '@/components/WarehouseComponents/SubCategoriesPageComponents/SubCategoryPageFilterModal';
import { useGetShopSubCategoriesQuery } from '@/redux/api/shopApis/shopSubCategoriesApis';
import { ROUTES } from '@/Routes';
import { SingleSubCategory } from '@/types/warehouseTypes/subCategoriesTypes';
import { generateFilterParams } from '@/utils/generateFilterParams';

interface Props {
  shopId: string;
}
function ShopSubcategoriesOverview({ shopId }: Props) {
  const navigate = useNavigate();
  const router = new URLSearchParams(useLocation().search);
  const name = router.get('name');
  const page = router.get('page');
  const limit = router.get('limit');
  const { data, isLoading } = useGetShopSubCategoriesQuery({
    shopId,
    page: page ?? '1',
    name: name ?? undefined,
    limit: limit ?? '10',
  });
  const [isFilterModalOpen, setIsFilterModalOpen] = useState<boolean>(false);

  const handleFilter = (fieldName: string, value: string) => {
    const query = generateFilterParams(fieldName, value);
    navigate(ROUTES.SHOP.SUB_CATEGORIES(shopId, query));
  };

  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 lg:py-2">
        <div className="flex items-center">
          <div className="search-title-and-btn flex items-center">
            <div className="relative">
              <div className="block lg:hidden">
                <FilterButton
                  handleClick={() => setIsFilterModalOpen(!isFilterModalOpen)}
                />
              </div>
              <div
                className={`${isFilterModalOpen ? 'block' : 'hidden'} xl:hidden`}
              >
                <SubCategoryPageFilterModal
                  handleClearAndClose={() => setIsFilterModalOpen(false)}
                  handleFilter={handleFilter}
                />
              </div>
            </div>
          </div>
          <div className="hidden lg:block">
            <div className="flex items-center gap-x-2">
              <SubCategoryFilterOption handleFilter={handleFilter} />
            </div>
          </div>
        </div>
      </div>
      {!isLoading ? (
        <div>
          <div className="tableTop w-full">
            <p>Sub Category List</p>
            <p>Total : {data?.pagination?.total}</p>
          </div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {data?.data?.length ? (
              <div className="full-table-box h-custom">
                <table className="full-table">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="tableHead">No</th>
                      <th className="tableHead">Name</th>
                      <th className="tableHead">Created At</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y bg-slate-200">
                    {data?.data?.map(
                      (subCategory: SingleSubCategory, index: number) => (
                        <tr key={subCategory?.id}>
                          <td className="tableData">{index + 1}</td>
                          <td className="tableData">{subCategory?.name}</td>
                          <td className="tableData">
                            <DateAndTimeViewer date={subCategory.createdAt} />
                          </td>
                        </tr>
                      ),
                    )}
                  </tbody>
                </table>
              </div>
            ) : (
              <NoResultFound pageType="sub category" />
            )}
          </div>
          <div className="pagination-box flex justify-end rounded bg-white p-3">
            <Pagination
              currentPage={page ?? '1'}
              limit={Number(limit ?? 10)}
              handleFilter={(fieldName: string, value: any) =>
                handleFilter(fieldName, value)
              }
              totalCount={data?.pagination?.total}
              totalPages={Math.ceil(
                Number(data?.pagination?.total) /
                  Number(data?.pagination?.limit),
              )}
            />
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={3} tableRow={6} />
      )}
    </div>
  );
}

export default ShopSubcategoriesOverview;
