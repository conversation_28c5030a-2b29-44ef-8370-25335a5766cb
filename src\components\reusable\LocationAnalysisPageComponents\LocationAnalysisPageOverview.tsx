import { ChevronDown, ChevronUp, Map, MapPin } from 'lucide-react';
import { useState } from 'react';
import Chart from 'react-apexcharts';

import { useGetShopOrderLocationAnalysisQuery } from '@/redux/api/shopApis/shopReportsApis';
import { useGetOrderLocationAnalysisQuery } from '@/redux/api/warehouseApis/reportsApis';
import {
  DistrictOnLocation,
  SingleDivisionData,
} from '@/types/shopTypes/shopStockTransferReportTypes';

interface Props {
  type: 'warehouse' | 'shop';
  id: string;
}

function LocationAnalysisPageOverview({ type, id }: Props) {
  const [expandedDivision, setExpandedDivision] = useState<string>('');

  const { data: warehouseData } = useGetOrderLocationAnalysisQuery(
    { warehouseId: id },
    { skip: type === 'shop' },
  );

  const { data: shopData } = useGetShopOrderLocationAnalysisQuery(
    { shopId: id },
    { skip: type === 'warehouse' },
  );

  // Select the correct dataset based on type
  const locationData =
    type === 'warehouse' ? warehouseData?.data : shopData?.data;

  // Prepare data for the division-wise chart
  const divisionChartSeries = [
    {
      name: 'Orders',
      data: locationData?.map((division) => division.totalOrderCount) || [],
    },
  ];

  const divisionChartOptions = {
    chart: {
      height: 300,
      toolbar: { show: false },
    },
    xaxis: {
      categories:
        locationData?.map((division) => division.divisionName ?? 'Not Found') ||
        [],
    },
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: '50%',
      },
    },
  };

  // Prepare data for the district-wise chart
  // const districtData =
  //   locationData?.flatMap((division) =>
  //     division.districts.map((district) => ({
  //       districtName: district.districtName,
  //       orderCount: district.totalOrderCount,
  //     })),
  //   ) || [];

  // const districtChartSeries = [
  //   {
  //     name: 'Orders',
  //     data: districtData.map((district) => district.orderCount),
  //   },
  // ];

  // const districtChartOptions = {
  //   chart: {
  //     height: 300,
  //     toolbar: { show: false },
  //   },
  //   xaxis: {
  //     categories: districtData.map((district) => district.districtName),
  //   },
  //   plotOptions: {
  //     bar: {
  //       horizontal: false,
  //       columnWidth: '50%',
  //     },
  //   },
  // };

  return (
    <div className="mb-10 space-y-4 p-4">
      {/* Division List */}
      {locationData?.map((division: SingleDivisionData) => (
        <div key={division.divisionName} className="rounded-lg shadow-lg">
          {/* Division Header */}
          <button
            type="button"
            className="flex w-full cursor-pointer items-center justify-between rounded-lg border border-slate-300 bg-white px-6 py-3 transition-all hover:shadow-md"
            onClick={() =>
              setExpandedDivision(
                expandedDivision === division.divisionName
                  ? ''
                  : division.divisionName,
              )
            }
          >
            <div className="flex items-center gap-3">
              <Map className="h-6 w-6 text-blue-500" />
              <span className="font-semibold">{division?.divisionName}</span>
            </div>
            <div className="flex items-center gap-3">
              <span className="text-sm text-gray-600">
                {division?.totalOrderCount} Orders
              </span>
              {expandedDivision === division.divisionName ? (
                <ChevronUp className="h-5 w-5 text-gray-600 transition-transform duration-200" />
              ) : (
                <ChevronDown className="h-5 w-5 text-gray-600 transition-transform duration-200" />
              )}
            </div>
          </button>

          {/* Districts List (CSS Animation) */}
          <div
            className={`ml-6 mt-2 overflow-hidden transition-all duration-300 ${
              expandedDivision === division.divisionName
                ? 'max-h-screen opacity-100'
                : 'max-h-0 opacity-0'
            }`}
          >
            {division?.districts?.map((district: DistrictOnLocation) => (
              <div
                key={district.districtName}
                className="flex items-center justify-between rounded-md border border-gray-200 bg-gray-50 px-5 py-2"
              >
                <div className="flex items-center gap-2">
                  <MapPin className="h-5 w-5 text-green-500" />
                  <span>{district.districtName}</span>
                </div>
                <span className="text-sm text-gray-600">
                  {district.totalOrderCount} Orders
                </span>
              </div>
            ))}
          </div>
        </div>
      ))}

      {/* Chart Section */}
      {/* Division-wise Chart */}
      <div>
        {Number(locationData?.length) > 0 && (
          <div className="rounded-lg bg-white p-4 shadow-md">
            <h2 className="mb-2 text-lg font-semibold">Orders per Division</h2>
            <Chart
              options={divisionChartOptions}
              series={divisionChartSeries}
              type="bar"
              height={300}
            />
          </div>
        )}
      </div>
      {/* District-wise Chart */}
      {/* <div className="mb-20">
        {Number(districtData?.length) > 0 && (
          <div className="rounded-lg bg-white p-4 shadow-md">
            <h2 className="mb-2 text-lg font-semibold">Orders per District</h2>
            <Chart
              options={districtChartOptions}
              series={districtChartSeries}
              type="bar"
              height={300}
            />
          </div>
        )}
      </div> */}
    </div>
  );
}

export default LocationAnalysisPageOverview;
