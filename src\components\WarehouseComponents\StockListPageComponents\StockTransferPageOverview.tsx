import Cookies from 'js-cookie';
import { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import StockTransferFilterOptions from './StockTransferFilterOptions';
import StockTransferModal from './StockTransferModal';
import StockTransferPageFilterModal from './StockTransferPageFilterModal';

import FilledButton from '@/components/reusable/Buttons/FilledButton';
import FilterButton from '@/components/reusable/Buttons/FilterButton';
import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import Modal from '@/components/reusable/Modal/Modal';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import Pagination from '@/components/reusable/Pagination/Pagination';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import { useGetOrgShopsQuery } from '@/redux/api/organizationApis/orgShopAndWarehouseApis';
import { useGetStockListQuery } from '@/redux/api/warehouseApis/stockApis';
import { ROUTES } from '@/Routes';
import { SingleStockDetails } from '@/types/warehouseTypes/stockTypes';
import { CalculateDiscountPrice } from '@/utils/CalculateDiscountPrice';
import { generateFilterParams } from '@/utils/generateFilterParams';
import StockName from '@/utils/StockName';

interface Props {
  warehouseId: string;
}
function StockTransferPageOverview({ warehouseId }: Props) {
  const navigate = useNavigate();
  const router = new URLSearchParams(useLocation().search);
  const page = router.get('page');
  const productName = router.get('productName');
  const limit = router.get('limit');
  const barcode = router.get('barcode');
  const productSerialNo = router.get('productSerialNo');

  const { data, isLoading, refetch, isFetching } = useGetStockListQuery(
    {
      organizationId: Cookies.get('organizationId') ?? '',
      warehouseId,
      status: 'IN_WAREHOUSE_AVAILABLE',
      page: page ?? '1',
      limit: limit ?? '10',
      barcode: barcode ?? undefined,
      productName: productName ?? undefined,
      productSerialNo: productSerialNo ?? undefined,
    },
    { skip: !warehouseId },
  );
  const { data: shopList } = useGetOrgShopsQuery({
    organizationId: Cookies.get('organizationId') ?? '',
  });

  const [selectedStocks, setSelectedStocks] = useState<string[]>();
  const [isStockTransferModalOpen, setIsStockTransferModalOpen] =
    useState(false);
  const [isFilterModalOpen, setIsFilterModalOpen] = useState<boolean>(false);

  const handleSelectAndUnselect = (stockId: string) => {
    if (selectedStocks?.length) {
      if (selectedStocks.includes(stockId)) {
        setSelectedStocks(selectedStocks.filter((item) => item !== stockId));
      } else {
        setSelectedStocks([...selectedStocks, stockId]);
      }
    } else {
      setSelectedStocks([stockId]);
    }
  };

  const handleFilter = (fieldName: string, value: string) => {
    const query = generateFilterParams(fieldName, value);
    navigate(ROUTES.STOCK.TRANSFER(warehouseId, query));
  };

  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-1">
        <div className="flex items-center">
          <div className="search-title-and-btn flex items-center">
            <div className="relative">
              <div className="block xl:hidden">
                <FilterButton
                  handleClick={() => setIsFilterModalOpen(!isFilterModalOpen)}
                />
              </div>
              <div
                className={`${isFilterModalOpen ? 'block' : 'hidden'} xl:hidden`}
              >
                <StockTransferPageFilterModal
                  handleClearAndClose={() => setIsFilterModalOpen(false)}
                  handleFilter={handleFilter}
                />
              </div>
            </div>
          </div>
          <div className="hidden xl:block">
            <div className="flex items-center gap-x-2">
              <StockTransferFilterOptions handleFilter={handleFilter} />
            </div>
          </div>
        </div>
        <div>
          <FilledButton
            text="Transfer"
            isLoading={false}
            handleClick={() => setIsStockTransferModalOpen(true)}
            isDisabled={!selectedStocks?.length}
          />
        </div>
      </div>
      {!isLoading && !isFetching ? (
        <div>
          <div className="tableTop w-full">
            <p>Stock List</p>
            <p>Total : {data?.pagination?.total}</p>
          </div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {data?.data?.length ? (
              <div>
                <div className="full-table-box h-custom">
                  <table className="full-table">
                    <thead className="bg-gray-100">
                      <tr>
                        <th className="tableHead">
                          <input
                            type="checkbox"
                            name=""
                            id=""
                            checked={
                              data?.data?.length === selectedStocks?.length
                            }
                            onClick={() => {
                              if (
                                data?.data?.length === selectedStocks?.length
                              ) {
                                setSelectedStocks([]);
                              } else {
                                const ids = data?.data?.map(
                                  (single: SingleStockDetails) => single?.id,
                                );
                                setSelectedStocks(ids);
                              }
                            }}
                          />
                        </th>
                        <th className="tableHead">No</th>

                        <th className="tableHead">Barcode</th>
                        <th className="tableHead">Product Id</th>
                        <th className="tableHead table-col-width">Name</th>
                        <th className="tableHead">Stock Location</th>
                        <th className="tableHead">TP</th>
                        <th className="tableHead">Regular Price</th>
                        <th className="tableHead">Discount Price</th>
                        <th className="tableHead">Status</th>
                        <th className="tableHead">Supplier</th>
                        <th className="tableHead">Created At</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y bg-slate-200">
                      {data?.data?.map(
                        (stock: SingleStockDetails, index: number) => (
                          <tr
                            key={stock?.id}
                            // className={
                            //   selectedStocks?.includes(stock?.id)
                            //     ? 'tableRowYellow'
                            //     : 'tableRowGreen'
                            // }
                          >
                            <td className="tableData">
                              <input
                                type="checkbox"
                                name=""
                                id=""
                                onClick={() =>
                                  handleSelectAndUnselect(stock.id)
                                }
                                checked={selectedStocks?.includes(stock.id)}
                                disabled={
                                  stock.isSold || stock?.assignedShopId !== null
                                }
                              />
                            </td>
                            <td className="tableData">{index + 1}</td>
                            <td className="tableData">{stock?.barcode}</td>
                            <td className="tableData">
                              {stock?.Product?.serialNo ?? 0}
                            </td>
                            <td className="tableData table-col-width">
                              <StockName stock={stock} name={stock.name} />
                            </td>
                            <td className="tableData">
                              {stock?.AssignedShop?.name ?? 'Warehouse'}
                            </td>
                            <td className="tableData">
                              {stock?.purchasePrice}
                            </td>
                            <td className="tableData">{stock?.retailPrice}</td>
                            <td className="tableData">
                              {CalculateDiscountPrice({
                                retailPrice: stock?.retailPrice,
                                discountType: stock.discountType,
                                discount: stock.discount,
                              })}
                            </td>
                            <td className="tableData">
                              {stock?.isSold ? 'Sold' : 'Available'}
                            </td>
                            <td className="tableData">
                              {stock?.Supplier?.User?.name}
                            </td>
                            <td className="tableData">
                              <DateAndTimeViewer date={stock?.createdAt} />
                            </td>
                          </tr>
                        ),
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            ) : (
              <NoResultFound pageType="stock" />
            )}
          </div>
          <div className="pagination-box flex justify-end rounded bg-white p-3">
            <Pagination
              currentPage={page ?? '1'}
              limit={Number(limit ?? 10)}
              handleFilter={(fieldName: string, value: any) =>
                handleFilter(fieldName, value)
              }
              totalCount={data?.pagination?.total}
              totalPages={Math.ceil(
                Number(data?.pagination?.total) /
                  Number(data?.pagination?.limit),
              )}
            />
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={12} tableRow={6} />
      )}
      <Modal
        setShowModal={setIsStockTransferModalOpen}
        showModal={isStockTransferModalOpen}
      >
        <StockTransferModal
          shopList={shopList?.data ?? []}
          selectedStocks={selectedStocks}
          handleClose={() => setIsStockTransferModalOpen(false)}
          updateRefreshCounter={() => {
            setSelectedStocks([]);
            refetch();
          }}
          warehouseId={warehouseId}
        />
      </Modal>
    </div>
  );
}

export default StockTransferPageOverview;
