import BaseApi from '../baseApi';

import { GetCashTransferListResponse } from '@/types/shopTypes/shopTransferAmountToOwnerTypes';

interface GetShopCustomersParams {
  warehouseId?: string;
  shopId?: string;
  mobileNumber?: string;
  page?: number;
  limit?: number;
}

const ShopTransferCashApi = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getShopCashTransferList: builder.query<
      GetCashTransferListResponse,
      GetShopCustomersParams
    >({
      query: (params) => ({
        url: '/amount-transfer',
        method: 'GET',
        params,
      }),
      // providesTags: [TagTypes.SHOP_CUSTOMERS],
    }),
    createNewShopCashTransfer: builder.mutation({
      query: (data) => ({
        url: '/amount-transfer/new',
        method: 'POST',
        data,
      }),
      // invalidatesTags: [TagTypes.SHOP_CUSTOMERS],
    }),
  }),
});

export const {
  useGetShopCashTransferListQuery,
  useCreateNewShopCashTransferMutation,
} = ShopTransferCashApi;
