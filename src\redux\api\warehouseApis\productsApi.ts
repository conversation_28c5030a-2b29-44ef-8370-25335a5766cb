import BaseApi from '../baseApi';

import { TagTypes } from '@/redux/tag-types';
import {
  GetProductsResponse,
  ProductStockLevelResponse,
  SingleProductDetailsResponse,
} from '@/types/warehouseTypes/productTypes';

interface GetProductsParams {
  warehouseId?: string;
  name?: string;
  serialNo?: string;
  brandName?: string;
  categoryName?: string;
  page?: string;
  limit?: string;
  customerId?: string;
}

const ProductsApi = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getProducts: builder.query<GetProductsResponse, GetProductsParams>({
      query: (params) => ({
        url: '/products',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.PRODUCTS],
    }),
    getProductDetails: builder.query<SingleProductDetailsResponse, any>({
      query: (id) => ({
        url: `/products/${id}`,
        method: 'GET',
      }),
      providesTags: [TagTypes.PRODUCTS],
    }),
    getProductTopSellingVariants: builder.query<any, any>({
      query: (params) => ({
        url: `/products/top-selling-variants`,
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.PRODUCTS],
    }),
    getProductStockLevel: builder.query<ProductStockLevelResponse, any>({
      query: (params) => ({
        url: `/products/stocks-level`,
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.PRODUCTS],
    }),
    getProductPurchaseTrend: builder.query<SingleProductDetailsResponse, any>({
      query: (params) => ({
        url: `/products/purchase-trend`,
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.PRODUCTS],
    }),
    getProductProfitability: builder.query<SingleProductDetailsResponse, any>({
      query: (params) => ({
        url: `/products/profitability`,
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.PRODUCTS],
    }),
    createProduct: builder.mutation({
      query: (data) => ({
        url: '/products/new',
        method: 'POST',
        data,
      }),
      invalidatesTags: [TagTypes.PRODUCTS],
    }),
    updateProduct: builder.mutation({
      query: ({ data, id }) => ({
        url: `/products/${id}`,
        method: 'PATCH',
        data,
      }),
      invalidatesTags: [TagTypes.PRODUCTS],
    }),
    deleteProduct: builder.mutation({
      query: (id) => ({
        url: `/products/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: [TagTypes.PRODUCTS],
    }),
  }),
});

export const {
  useGetProductsQuery,
  useGetProductDetailsQuery,
  useGetProductTopSellingVariantsQuery,
  useGetProductStockLevelQuery,
  useGetProductPurchaseTrendQuery,
  useGetProductProfitabilityQuery,
  useCreateProductMutation,
  useUpdateProductMutation,
  useDeleteProductMutation,
} = ProductsApi;
