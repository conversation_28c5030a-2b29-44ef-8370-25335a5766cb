import { useFormik } from 'formik';
import Cookies from 'js-cookie';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import * as Yup from 'yup';

import FilledSubmitButton from '../../reusable/Buttons/FilledSubmitButton';
import CustomInputField from '../../reusable/CustomInputField/CustomInputField';
import ModalTitle from '../../reusable/Modal/ModalTitle';

import ImageSelector from '@/components/reusable/ImageSelector/ImageSelector';
import {
  useCreateBrandMutation,
  useUpdateBrandMutation,
} from '@/redux/api/superAdminOrganizationsApi';
import { SingleBrandDetails } from '@/types/warehouseTypes/brandsTypes';
import { UploadImageOnAws } from '@/utils/ImageUploadModule';

interface Props {
  type: string;
  warehouseId: string | undefined;
  handleClose: () => void;
  updateRefreshCounter: () => void;
  brandData?: SingleBrandDetails;
}

const formikInitialValues = {
  name: '',
};

const validation = Yup.object({
  name: Yup.string().required('Brand name is required'),
});

function AddOrEditBrandModal({
  type,
  warehouseId,
  handleClose,
  updateRefreshCounter,
  brandData,
}: Props) {
  const [createBrand, { isLoading }] = useCreateBrandMutation();
  const [updateBrand, { isLoading: isUpdatingBrand }] =
    useUpdateBrandMutation();
  const [currentFile, setCurrentFile] = useState<any>();

  const formik = useFormik({
    initialValues: formikInitialValues,
    validationSchema: validation,

    onSubmit: async (values) => {
      if (type === 'new') {
        const imgUrl = currentFile ? await UploadImageOnAws(currentFile) : null;
        toast.promise(
          createBrand({
            name: values.name,
            warehouseId,
            imgUrl,
            organizationId: Cookies.get('organizationId'),
          }).unwrap(),
          {
            pending: 'Creating New Brand...',
            success: {
              render({ data: res }) {
                if (res?.statusCode === 200 || res?.statusCode === 201) {
                  updateRefreshCounter();
                  handleClose();
                }
                return 'Brand created Successfully';
              },
            },
            error: {
              render({ data: error }) {
                console.log(error);
                return 'Error on creating brand';
              },
            },
          },
        );
      } else {
        const imgUrl = currentFile
          ? await UploadImageOnAws(currentFile)
          : brandData?.imgUrl
            ? brandData?.imgUrl
            : null;
        toast.promise(
          updateBrand({
            data: { name: values.name },
            id: brandData?.id,
            imgUrl,
          }).unwrap(),
          {
            pending: 'Updating Brand...',
            success: {
              render({ data: res }) {
                if (res?.statusCode === 200 || res?.statusCode === 201) {
                  updateRefreshCounter();
                  handleClose();
                }
                return 'Brand updated Successfully';
              },
            },
            error: {
              render({ data: error }) {
                console.log(error);
                return 'Error on update brand';
              },
            },
          },
        );
      }
    },
  });

  useEffect(() => {
    if (type === 'edit' && brandData) {
      formik.setFieldValue('name', brandData?.name);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [type, brandData]);

  return (
    <div className="flex w-[400px] flex-col gap-4 rounded-xl bg-white p-4">
      <ModalTitle
        text={type === 'new' ? 'Create Brand' : 'Edit Brand'}
        handleClose={handleClose}
      />
      <form
        onSubmit={formik.handleSubmit}
        className="flex w-full flex-col items-center justify-center gap-2"
      >
        <div className="w-[100px]">
          <ImageSelector
            previousImage={brandData?.imgUrl ?? ''}
            setNewImage={(e) => setCurrentFile(e)}
          />
        </div>
        <CustomInputField
          type="text"
          placeholder="Enter Brand Name"
          name="name"
          label="Name"
          formik={formik}
        />
        <div className="mt-[10px] flex w-full items-center justify-center">
          <FilledSubmitButton
            text={type === 'new' ? 'Create Brand' : 'Done Editing'}
            isLoading={isLoading || isUpdatingBrand}
          />
        </div>
      </form>
    </div>
  );
}

export default AddOrEditBrandModal;
