import UserDropdown from '../Dropdown/UserDropdown';

function HomePageNavbar() {
  // const type = Cookies.get('type');
  return (
    <nav className="flex h-[60px] items-center bg-[#28243D]">
      <div className="w-full px-6 sm:px-10 lg:px-20">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-8">
            <h1 className="text-xl font-bold text-white">SOFTS.AI</h1>
            {/* {type === 'CUSTOMER' ? (
              <div className="flex items-center gap-2 text-white">
                <Link to="/shops">Shops</Link>
                <Link to="/employees">Employees</Link>
              </div>
            ) : (
              ''
            )} */}
          </div>
          <div className="flex items-center">
            <UserDropdown />
          </div>
        </div>
      </div>
    </nav>
  );
}

export default HomePageNavbar;
