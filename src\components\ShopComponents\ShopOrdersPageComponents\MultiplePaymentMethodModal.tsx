import ModalTitle from '@/components/reusable/Modal/ModalTitle';
import { PaymentMethods } from '@/utils/staticData';
import { useEffect, useState } from 'react';
import murubbi from './../../../../public/murubbi.mp3';

interface Props {
  handleClose: () => void;
  setSelectedPayments: (val: any) => void;
  selectedPayments: any;
  netTotal: number;
}

const MultiplePaymentMethodModal = ({
  handleClose,
  setSelectedPayments,
  selectedPayments,
  netTotal,
}: Props) => {
  const [totalAmount, setTotalAmount] = useState(0);
  const [payments, setPayments] = useState(
    PaymentMethods.map((method: any) => ({ method: method.value, amount: 0 })),
  );

  const handleInputChange = (index: number, value: string) => {
    const updatedPayments = [...payments];
    updatedPayments[index].amount = Number(value);
    setPayments(updatedPayments);
  };

  const handleSave = () => {
    const filteredValues = payments?.filter((sin: any) => sin.amount > 0);
    setSelectedPayments(filteredValues);
    handleClose();
  };

  useEffect(() => {
    if (selectedPayments?.length) {
      // Update the payments array with values from selectedPayments
      const updatedPayments = payments.map((payment) => {
        const selectedPayment = selectedPayments.find(
          (sp: any) => sp.method === payment.method,
        );
        return {
          ...payment,
          amount: selectedPayment ? selectedPayment.amount : '',
        };
      });
      setPayments(updatedPayments);
    }
  }, [selectedPayments]); // Runs when selectedPayments changes

  useEffect(() => {
    let tot = 0;
    payments.forEach((payment: any) => {
      tot += Number(payment.amount);
    });
    setTotalAmount(tot);
  }, [payments]);

  return (
    <div className="flex w-[350px] flex-col gap-4 rounded-xl bg-white p-4">
      <ModalTitle text="Multiple Payments" handleClose={handleClose} />
      <div className="mt-2 flex flex-col gap-4">
        {payments.map((payment, index) => (
          <div
            key={payment.method}
            className="grid grid-cols-12 items-center gap-4"
          >
            <h2 className="col col-span-3 font-bold">{payment.method}:</h2>
            <input
              type="number"
              className="col col-span-9 w-full rounded border border-black py-2 pl-4 text-left"
              value={payment.amount > 0 ? payment.amount : ''}
              onChange={(e) => handleInputChange(index, e.target.value)}
            />
          </div>
        ))}
        <div className="flex items-center justify-between">
          <span>Net Total: {netTotal}</span>
          <span>Paid Amount: {totalAmount}</span>
        </div>
      </div>
      {totalAmount > netTotal ? (
        <div className="flex flex-col gap-2">
          <span className="blink-animation text-red-600">
            For multiple payment total payment can not be greater then total
          </span>
          <div className="flex items-center justify-center">
            <img
              src="https://media.tenor.com/5x6U2SGz81QAAAAM/kiss-good-morning.gif"
              alt=""
              height="100px"
              width="100px"
            />
          </div>
          <audio autoPlay loop>
            <source src={murubbi} type="audio/mp3" />
          </audio>
        </div>
      ) : (
        <button
          className="mt-4 rounded bg-primary px-4 py-2 text-white"
          onClick={handleSave}
          type="button"
        >
          Done
        </button>
      )}
    </div>
  );
};

export default MultiplePaymentMethodModal;
