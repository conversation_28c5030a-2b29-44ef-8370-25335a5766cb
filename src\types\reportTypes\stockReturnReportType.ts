export interface StockReturnReportResponseType {
  success: boolean;
  message: string;
  statusCode: number;
  data: SingleStockReturnReport[];
}

export interface SingleStockReturnReport {
  id: string;
  createdAt: string;
  updatedAt: string;
  warehouseId: string;
  productId: string;
  supplierId: string;
  assignedShopId: string;
  isSold: boolean;
  purchasePrice: number;
  retailPrice: number;
  barcode: number;
  manufactureDate: any;
  expireDate: any;
  discountType: string;
  discount: number;
  vat: number;
  lastTransferDate: string;
  purchaseId: string;
  createdById: string;
  isHold: boolean;
  isLocked: boolean;
  isDeleted: boolean;
  wholesalePrice: number;
  variantId: string;
  name: string;
  status: string;
  retailProfit: number;
  wholesaleProfit: number;
  OrderItem: OrderItem[];
}

export interface OrderItem {
  Order: Order;
}

export interface Order {
  id: string;
  serialNo: number;
}
