import {
  Document,
  Page,
  PDFViewer,
  StyleSheet,
  Text,
  View,
} from '@react-pdf/renderer';

import ModalTitle from '@/components/reusable/Modal/ModalTitle';
import SpinnerLoader from '@/components/reusable/SpinnerLoader/SpinnerLoader';
import { useGetShopSingleStockEntryDetailsQuery } from '@/redux/api/shopApis/shopStockApis';

// PDF styles
const styles = StyleSheet.create({
  page: {
    padding: 24,
    fontSize: 10,
    fontFamily: 'Helvetica',
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#222',
    textAlign: 'center',
  },
  section: {
    marginBottom: 12,
    padding: 8,
    borderRadius: 4,
    backgroundColor: '#f5f5f5',
    display: 'flex',
    flexDirection: 'column',
    gap: 4,
  },
  label: {
    fontWeight: 'bold',
    color: '#333',
  },
  table: {
    width: 'auto',
    marginTop: 8,
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: '#bbb',
    borderRadius: 4,
  },
  tableRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    borderBottomStyle: 'solid',
    alignItems: 'center',
  },
  tableHeader: {
    backgroundColor: '#e5e7eb',
    fontWeight: 'bold',
    color: '#222',
  },
  cellNo: {
    padding: 6,
    fontSize: 10,
    width: '5%',
    minWidth: 20,
    textAlign: 'center',
    borderRightWidth: 1,
    borderRightColor: '#eee',
    borderRightStyle: 'solid',
  },
  cellProduct: {
    padding: 6,
    fontSize: 10,
    width: '30%',
    minWidth: 60,
    textAlign: 'left',
    borderRightWidth: 1,
    borderRightColor: '#eee',
    borderRightStyle: 'solid',
  },
  cellQty: {
    padding: 6,
    fontSize: 10,
    width: '5%',
    minWidth: 30,
    textAlign: 'center',
    borderRightWidth: 1,
    borderRightColor: '#eee',
    borderRightStyle: 'solid',
  },
  cellBarcodes: {
    padding: 6,
    fontSize: 10,
    width: '60%',
    minWidth: 40,
    textAlign: 'left',
  },
});

interface Props {
  entryId: string;
  shopId: string;
  handleClose: () => void;
}

function SingleStockTransferDetailsPdf({
  entryId,
  handleClose,
  shopId,
}: Props) {
  const { data, isLoading } = useGetShopSingleStockEntryDetailsQuery({
    id: entryId,
    shopId,
  });
  return (
    <div className="rounded-lg bg-white p-4 shadow-md">
      <ModalTitle text="Stock Transfer Details" handleClose={handleClose} />
      {!isLoading ? (
        <div>
          <PDFViewer height={500} width={600}>
            <Document>
              <Page size="A4" style={styles.page}>
                <Text style={styles.title}>Stock Entry Request Details</Text>
                <View style={styles.section}>
                  <Text>
                    <Text style={styles.label}>Transferred To: </Text>
                    {data?.data?.id}
                  </Text>
                  <Text>
                    <Text style={styles.label}>Status: </Text>
                    {data?.data?.status}
                  </Text>
                  <Text>
                    <Text style={styles.label}>Created By: </Text>
                    {data?.data?.CreatedBy?.name || '-'}
                  </Text>
                  <Text>
                    <Text style={styles.label}>Created At: </Text>
                    {data?.data?.createdAt
                      ? new Date(data?.data?.createdAt).toLocaleString()
                      : '-'}
                  </Text>
                </View>
                <View style={styles.table}>
                  <View style={[styles.tableRow, styles.tableHeader]}>
                    <Text style={styles.cellNo}>No</Text>
                    <Text style={styles.cellProduct}>Product Name</Text>
                    <Text style={styles.cellQty}>Qty</Text>
                    <Text style={styles.cellBarcodes}>Barcodes</Text>
                  </View>
                  {data?.data?.products?.length ? (
                    data?.data?.products.map((prod, idx) => (
                      <View style={styles.tableRow} key={prod.productId}>
                        <Text style={styles.cellNo}>{idx + 1}</Text>
                        <Text style={styles.cellProduct}>
                          {prod.productName}
                        </Text>
                        <Text style={styles.cellQty}>{prod.quantity}</Text>
                        <Text style={styles.cellBarcodes}>
                          {prod.stocks && prod.stocks.length > 0
                            ? prod.stocks.map((s) => s.barcode).join(', ')
                            : '-'}
                        </Text>
                      </View>
                    ))
                  ) : (
                    <View style={styles.tableRow}>
                      <Text style={styles.cellNo}>-</Text>
                      <Text style={styles.cellProduct}>No products found.</Text>
                      <Text style={styles.cellQty} />
                      <Text style={styles.cellBarcodes} />
                    </View>
                  )}
                </View>
              </Page>
            </Document>
          </PDFViewer>
        </div>
      ) : (
        <SpinnerLoader />
      )}
    </div>
  );
}

export default SingleStockTransferDetailsPdf;
