function OrderStatusViewer({ status }: { status: string }) {
  return (
    <div
      className={`rounded px-0 py-1 font-semibold text-white ${
        status?.toUpperCase() === 'DELIVERED' ||
        status?.toUpperCase() === 'PAID' ||
        status?.toUpperCase() === 'ACTIVE' ||
        status?.toUpperCase() === 'COMPLETED'
          ? 'bg-green-500'
          : status?.toUpperCase() === 'CANCELLED'
            ? 'bg-red-600'
            : status?.toUpperCase() === 'RETURNED'
              ? 'bg-red-400'
              : status?.toUpperCase() === 'PENDING'
                ? 'blink-animation bg-orange-400'
                : status?.toUpperCase() === 'UNPAID' ||
                    status?.toUpperCase() === 'IN-ACTIVE'
                  ? 'bg-red-500'
                  : 'bg-orange-400'
      }`}
    >
      {status?.replace(/_/g, ' ')?.toUpperCase()}
    </div>
  );
}

export default OrderStatusViewer;
