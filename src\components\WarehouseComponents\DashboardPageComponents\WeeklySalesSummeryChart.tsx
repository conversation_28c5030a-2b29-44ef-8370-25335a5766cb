import { ApexOptions } from 'apexcharts'; // Import the correct ApexOptions type
import ReactApexChart from 'react-apexcharts';

import {
  DashboardLastSevenDaysSalesSummeryResponse,
  ShopDetailsInSalesSummery,
  SingleDaySalesSummery,
} from '@/types/warehouseTypes/dashboardTypes';
import { formatNumberWithComma } from '@/utils/formatNumberWithComma';

interface Props {
  chartData?: DashboardLastSevenDaysSalesSummeryResponse;
  mrpTotal?: string | number;
  filterType?: 'weekly' | 'last7days' | 'monthly' | 'custom';
}

function WeeklySalesSummeryChart({ chartData, mrpTotal, filterType }: Props) {
  // Extracting categories (dates) and series (totalAmount per shop)
  const dates = chartData?.data.map(
    (entry: SingleDaySalesSummery) => entry.date,
  );

  const series = chartData?.data[0].shops.map(
    (singleShop: ShopDetailsInSalesSummery) => ({
      name: `${singleShop.shopName}(${singleShop.nickName})`,
      data: chartData.data.map((entry: SingleDaySalesSummery) => {
        // Find the matching shop in each day's sales data by shopId
        const shopEntry = entry.shops.find(
          (shop: ShopDetailsInSalesSummery) =>
            shop.shopId === singleShop.shopId,
        );
        return shopEntry ? shopEntry.totalAmount : 0;
      }),
    }),
  );

  // Defining chartOptions and explicitly typing it as ApexOptions
  const chartOptions: ApexOptions = {
    chart: {
      type: 'line', // Specify a valid ApexCharts chart type like 'line', 'bar', etc.
    },
    xaxis: {
      categories: dates, // X-axis will show the dates
      title: {
        text: 'Date',
      },
    },
    yaxis: {
      title: {
        text: 'Total Amount',
      },
    },
    stroke: {
      curve: 'smooth',
    },
    markers: {
      size: 5,
    },
    dataLabels: {
      enabled: true,
    },
    tooltip: {
      shared: true,
      intersect: false,
    },
    title: {
      text: `${filterType === 'last7days' ? 'Last 7 Days' : filterType === 'monthly' ? 'Current Month' : 'Custom Date'} Sales Summery (${formatNumberWithComma(Number(mrpTotal))} BDT)`,
    },
  };

  return (
    <div className="w-full">
      <ReactApexChart
        options={chartOptions} // Ensure options has the correct type
        series={series}
        type="line" // Set the type explicitly here
        height={350}
      />
    </div>
  );
}

export default WeeklySalesSummeryChart;
