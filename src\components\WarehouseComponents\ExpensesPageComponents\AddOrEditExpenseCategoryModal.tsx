import { useFormik } from 'formik';
import { useEffect } from 'react';
import { toast } from 'react-toastify';
import * as Yup from 'yup';

import FilledSubmitButton from '../../reusable/Buttons/FilledSubmitButton';
import CustomInputField from '../../reusable/CustomInputField/CustomInputField';
import ModalTitle from '../../reusable/Modal/ModalTitle';

import { useUpdateBrandMutation } from '@/redux/api/superAdminOrganizationsApi';
import { useCreateExpenseCategoryMutation } from '@/redux/api/warehouseApis/expensesApis';
import { SingleBrandDetails } from '@/types/warehouseTypes/brandsTypes';

interface Props {
  type: string;
  organizationId: string | undefined;
  handleClose: () => void;
  updateRefreshCounter: () => void;
  brandData?: SingleBrandDetails;
}

const formikInitialValues = {
  name: '',
};

const validation = Yup.object({
  name: Yup.string().required('Brand name is required'),
});

function AddOrEditExpenseCategoryModal({
  type,
  organizationId,
  handleClose,
  updateRefreshCounter,
  brandData,
}: Props) {
  const [createExpenseCategory, { isLoading }] =
    useCreateExpenseCategoryMutation();
  const [updateBrand, { isLoading: isUpdatingBrand }] =
    useUpdateBrandMutation();

  const formik = useFormik({
    initialValues: formikInitialValues,
    validationSchema: validation,

    onSubmit: async (values) => {
      if (type === 'new') {
        // const imgUrl = currentFile ? await UploadImageOnAws(currentFile) : null;
        toast.promise(
          createExpenseCategory({
            name: values.name,
            organizationId,
            purpose: 'EXPENSE',
          }).unwrap(),
          {
            pending: 'Creating New Expense Category...',
            success: {
              render({ data: res }) {
                if (res?.statusCode === 200 || res?.statusCode === 201) {
                  updateRefreshCounter();
                  handleClose();
                }
                return 'Expense Category created Successfully';
              },
            },
            error: {
              render({ data: error }) {
                console.log(error);
                return 'Error on creating Expense Category';
              },
            },
          },
        );
      } else {
        toast.promise(
          updateBrand({
            data: { name: values.name },
            id: brandData?.id,
          }).unwrap(),
          {
            pending: 'Updating Expense Category...',
            success: {
              render({ data: res }) {
                if (res?.statusCode === 200 || res?.statusCode === 201) {
                  updateRefreshCounter();
                  handleClose();
                }
                return 'Expense Category updated Successfully';
              },
            },
            error: {
              render({ data: error }) {
                console.log(error);
                return 'Error on update Expense Category';
              },
            },
          },
        );
      }
    },
  });

  useEffect(() => {
    if (type === 'edit' && brandData) {
      formik.setFieldValue('name', brandData?.name);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [type, brandData]);

  return (
    <div className="flex w-[400px] flex-col gap-4 rounded-xl bg-white p-4">
      <ModalTitle
        text={
          type === 'new' ? 'Create Expense Category' : 'Edit Expense Category'
        }
        handleClose={handleClose}
      />
      <form
        onSubmit={formik.handleSubmit}
        className="flex w-full flex-col items-center justify-center gap-2"
      >
        <CustomInputField
          type="text"
          placeholder="Enter Expense Category Name"
          name="name"
          label="Name"
          formik={formik}
        />
        <div className="mt-[10px] flex w-full items-center justify-center">
          <FilledSubmitButton
            text={type === 'new' ? 'Create Expense Category' : 'Done Editing'}
            isLoading={isLoading || isUpdatingBrand}
          />
        </div>
      </form>
    </div>
  );
}

export default AddOrEditExpenseCategoryModal;
