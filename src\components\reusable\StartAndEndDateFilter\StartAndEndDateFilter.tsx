import { useEffect, useState } from 'react';

import CustomDateFilterInput from '../Inputs/CustomDateFilterInput';

import { generateDateString } from '@/utils/generateDateFormat';

interface Props {
  startDate: string;
  endDate: string;
  setStartDate: (value: string) => void;
  setEndDate: (value: string) => void;
}

function StartAndEndDateFilter({
  startDate,
  endDate,
  setStartDate,
  setEndDate,
}: Props) {
  console.log(startDate);
  console.log(endDate);

  const [selectedStartDate, setSelectedStartDate] = useState<any>(new Date());
  const [selectedEndDate, setSelectedEndDate] = useState<any>(new Date());

  useEffect(() => {
    if (startDate) {
      setSelectedStartDate(startDate);
    }
    if (endDate) {
      setSelectedEndDate(endDate);
    }
  }, [startDate, endDate]);

  return (
    <div className="flex items-center gap-x-2">
      <CustomDateFilterInput
        value={selectedStartDate}
        placeholder="Select Start Date"
        label="Start Date"
        handleChange={(value: string) => setSelectedStartDate(value)}
      />
      <CustomDateFilterInput
        value={selectedEndDate}
        placeholder="Select Start Date"
        label="End Date"
        handleChange={(value: string) => setSelectedEndDate(value)}
        // minimumDate={new Date(selectedStartDate).toISOString().substring(0, 10)}
      />
      <button
        type="button"
        className="ml-4 rounded-lg bg-primary px-8 py-2 text-white"
        onClick={() => {
          setStartDate(generateDateString(selectedStartDate));
          setEndDate(generateDateString(selectedEndDate));
        }}
      >
        Search
      </button>
    </div>
  );
}

export default StartAndEndDateFilter;
