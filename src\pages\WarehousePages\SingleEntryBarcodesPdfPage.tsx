import { Document, PDFViewer } from '@react-pdf/renderer';
import { useLocation } from 'react-router-dom';

import SingleStockBarcodePdf from '@/components/reusable/BarcodePdf/SingleStockBarcodePdf';
import Pagination from '@/components/reusable/Pagination/Pagination';
import SpinnerLoader from '@/components/reusable/SpinnerLoader/SpinnerLoader';
import { useGetSingleEntryBarcodeListQuery } from '@/redux/api/warehouseApis/purchaseApis';
import { SingleBarcodeDetails } from '@/types/warehouseTypes/purchaseTypes';

function SingleEntryBarcodesPdfPage() {
  const searchParams = new URLSearchParams(useLocation().search);
  const entryId = searchParams.get('entryId') ?? '';
  const warehouseId = searchParams.get('warehouseId') ?? '';
  const limit = searchParams.get('limit') ?? '20';
  const page = searchParams.get('page') ?? '1';
  const { data, isLoading, isFetching } = useGetSingleEntryBarcodeListQuery({
    supplierBillId: entryId,
    warehouseId,
    limit: limit ?? undefined,
    page: page ?? undefined,
  });

  return (
    <div className="flex w-full flex-col items-center justify-center">
      <div className="h-[40px] pb-4 pt-2 text-xl font-bold">
        <h2>{data?.data?.data?.length} Barcode Loaded</h2>
      </div>
      {!isLoading && !isFetching ? (
        <>
          <PDFViewer style={{ height: `calc(100vh - 100px)`, width: '100%' }}>
            <Document>
              {data?.data?.data?.map((singleBarcode: SingleBarcodeDetails) => {
                let name = singleBarcode?.Product?.name;
                if (
                  singleBarcode?.name &&
                  singleBarcode?.name !== 'Default Title'
                ) {
                  name = singleBarcode?.name;
                } else if (
                  singleBarcode?.Variant?.name &&
                  singleBarcode?.Variant?.name !== 'Default Title'
                ) {
                  name = singleBarcode?.Variant?.name;
                }
                return (
                  <>
                    {!singleBarcode?.isHold && !singleBarcode?.isSold ? (
                      <SingleStockBarcodePdf
                        key={singleBarcode.barcode}
                        barcode={singleBarcode?.barcode.toString()}
                        name={name}
                        price={singleBarcode?.retailPrice.toString()}
                      />
                    ) : (
                      ''
                    )}
                  </>
                );
              })}
            </Document>
          </PDFViewer>
          <div className="pagination-box flex justify-end rounded bg-white p-3">
            <Pagination
              totalCount={data?.data?.pagination.total}
              limits={[20, 50, 100, 200, 500]}
              totalPages={Math.ceil(
                Number(data?.data?.pagination?.total) /
                  Number(data?.data?.pagination?.limit),
              )}
            />
          </div>
        </>
      ) : (
        <SpinnerLoader />
      )}
    </div>
  );
}

export default SingleEntryBarcodesPdfPage;
