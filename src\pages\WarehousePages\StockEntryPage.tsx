import { useParams } from 'react-router-dom';

import StockEntryPageOverview from '@/components/WarehouseComponents/StockEntryPageComponents/StockEntryPageOverview';
import { ProtectedRoute } from '@/utils/ProtectedRoutes';

function StockEntryPage() {
  const { warehouseId } = useParams();
  return (
    <ProtectedRoute>
      <StockEntryPageOverview warehouseId={warehouseId ?? ''} />
    </ProtectedRoute>
  );
}

export default StockEntryPage;
