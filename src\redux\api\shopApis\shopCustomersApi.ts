import BaseApi from '../baseApi';

import { TagTypes } from '@/redux/tag-types';
import {
  GetShopCustomerDetailsResponse,
  GetShopCustomersResponse,
} from '@/types/shopTypes/shopCustomersTypes';

interface GetShopCustomersParams {
  organizationId: string;
  shopId?: string;
  warehouseId?: string;
  mobileNumber?: string;
  name?: string;
  page?: string;
  limit?: string;
}

const ShopCustomersApi = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getShopCustomers: builder.query<
      GetShopCustomersResponse,
      GetShopCustomersParams
    >({
      query: (params) => ({
        url: '/customers',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP_CUSTOMERS],
    }),
    getShopCustomerDetails: builder.query<GetShopCustomerDetailsResponse, any>({
      query: (data) => ({
        url: `/customers/${data.id}?organizationId=${data.organizationId}`,
        method: 'GET',
      }),
      providesTags: [TagTypes.SHOP_CUSTOMERS],
    }),
    getShopCustomerDetailsByPhone: builder.query<
      GetShopCustomerDetailsResponse,
      any
    >({
      query: (data) => ({
        url: `/customers/mobileNumber/${data.phone}?organizationId=${data.organizationId}`,
        method: 'GET',
      }),
      providesTags: [TagTypes.SHOP_CUSTOMERS],
    }),
    createNewShopCustomer: builder.mutation({
      query: (data) => ({
        url: '/customers/new',
        method: 'POST',
        data,
      }),
      invalidatesTags: [TagTypes.SHOP_CUSTOMERS],
    }),
  }),
});

export const {
  useGetShopCustomersQuery,
  useGetShopCustomerDetailsQuery,
  useGetShopCustomerDetailsByPhoneQuery,
  useCreateNewShopCustomerMutation,
} = ShopCustomersApi;
