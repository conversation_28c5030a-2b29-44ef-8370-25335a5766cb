import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import SpinnerLoader from '@/components/reusable/SpinnerLoader/SpinnerLoader';
import { useGetSupplierPaymentsQuery } from '@/redux/api/warehouseApis/suppliersApi';
import { SupplierSinglePayment } from '@/types/warehouseTypes/suppliersTypes';

interface Props {
  supplierId: string;
}
function SupplierPaymentsList({ supplierId }: Props) {
  const { data, isLoading } = useGetSupplierPaymentsQuery({ supplierId });
  console.log(data);

  return (
    <div>
      <div className="tableTop w-full">
        <p>Payment List</p>
        <p>Total : {data?.pagination.total}</p>
      </div>
      {!isLoading ? (
        <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
          {data?.data?.length ? (
            <div className="full-table-box h-custom">
              <table className="full-table">
                <thead className="bg-gray-100">
                  <tr>
                    <th className="tableHead">No</th>
                    <th className="tableHead">Paid Amount</th>
                    <th className="tableHead">Payment Method</th>
                    <th className="tableHead">Paid By</th>
                    <th className="tableHead">Date & Time</th>
                  </tr>
                </thead>
                <tbody className="divide-y bg-slate-200">
                  {data?.data?.map(
                    (singleBill: SupplierSinglePayment, index: number) => (
                      <tr key={singleBill?.id}>
                        <td className="tableData">{index + 1}</td>
                        <td className="tableData">{singleBill?.amount ?? 0}</td>
                        <td className="tableData">
                          {singleBill?.paymentMethod}
                        </td>
                        <td className="tableData">
                          {singleBill?.CreatedBy?.name ?? 'N/A'}
                        </td>
                        <td className="tableData">
                          <DateAndTimeViewer date={singleBill?.createdAt} />
                        </td>
                      </tr>
                    ),
                  )}
                </tbody>
              </table>
            </div>
          ) : (
            <div>
              <h2>No product found</h2>
            </div>
          )}
        </div>
      ) : (
        <SpinnerLoader />
      )}
      {/* <div className="pagination-box flex justify-end rounded bg-white p-3">
                  <Pagination
                    currentPage={page ?? '1'}
                    limit={Number(limit ?? 10)}
                    handleFilter={(fieldName: string, value: any) =>
                      handleFilter(fieldName, value)
                    }
                    totalCount={bills?.pagination?.total}
                    totalPages={Math.ceil(
                      Number(bills?.pagination?.total) /
                        Number(bills?.pagination?.limit),
                    )}
                  />
                </div> */}
    </div>
  );
}

export default SupplierPaymentsList;
