import { useLocation } from 'react-router-dom';

import CustomSelectForFilter from '@/components/reusable/Inputs/CustomSelectForFilter';
import SearchInput from '@/components/reusable/Inputs/SearchInput';
import { SingleBrandDetails } from '@/types/warehouseTypes/brandsTypes';
import { SingleCategoryDetails } from '@/types/warehouseTypes/categoriesTypes';

interface Props {
  handleFilter: (fieldName: string, value: string) => void;
  brands: SingleBrandDetails[];
  categories: SingleCategoryDetails[];
}

function ProductFilterOptions({ handleFilter, brands, categories }: Props) {
  const router = new URLSearchParams(useLocation().search);
  const serialNo = router.get('serialNo');
  const name = router.get('name');
  const brandName = router.get('brandName');
  const categoryName = router.get('categoryName');

  return (
    <div className="flex flex-col gap-3 md:flex-row md:gap-4">
      <SearchInput
        placeholder="Search by Name"
        handleSubmit={(value: string) => handleFilter('name', value)}
        value={name ?? ''}
      />
      <SearchInput
        placeholder="Search by Id"
        handleSubmit={(value: string) => handleFilter('serialNo', value)}
        value={serialNo ?? ''}
      />
      <CustomSelectForFilter
        options={
          brands
            ? brands?.map((brand: SingleBrandDetails) => {
                return { label: brand?.name, value: brand?.name };
              })
            : []
        }
        selectedValue={brandName ?? ''}
        handleSelect={(e) => handleFilter('brandName', e)}
        placeHolder="Select Brand"
      />
      <CustomSelectForFilter
        options={
          categories
            ? categories?.map((category: SingleCategoryDetails) => {
                return {
                  label: category?.name,
                  value: category?.name,
                };
              })
            : []
        }
        selectedValue={categoryName ?? ''}
        handleSelect={(e) => handleFilter('categoryName', e)}
        placeHolder="Select Category"
      />
    </div>
  );
}

export default ProductFilterOptions;
