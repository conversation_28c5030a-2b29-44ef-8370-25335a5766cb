import Cookies from 'js-cookie';

import SpinnerLoader from '@/components/reusable/SpinnerLoader/SpinnerLoader';
import { useGetSingleOrgDetailsQuery } from '@/redux/api/organizationApis/organizationBasicApis';

function OrganizationDetailsComponent() {
  const organizationId = Cookies.get('organizationId');
  const { data, isLoading } = useGetSingleOrgDetailsQuery(organizationId, {
    skip: !organizationId,
  });
  if (isLoading) {
    return <SpinnerLoader />;
  }
  const {
    name,
    address,
    street,
    district,
    imgUrl,
    warehouseLimit,
    shopLimit,
    lastStockNo,
    lastProductNo,
    lastCustomerNo,
    lastEmployeeNo,
  } = data.data;
  return (
    <div>
      <h1 className="mb-6 text-center text-3xl font-bold text-gray-800">
        Welcome Back to {name}
      </h1>
      {imgUrl && (
        <div className="mb-6 flex justify-center">
          <img
            src={`https://retail-pluse-upload.s3.ap-southeast-1.amazonaws.com/${imgUrl}`}
            alt={`${name} Logo`}
            className="object-fit h-32 shadow-lg transition-transform duration-300 hover:scale-105"
          />
        </div>
      )}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* Organization Details Card */}
        <div className="group rounded-xl bg-white p-6 shadow-md transition-all duration-300 hover:shadow-xl">
          <h2 className="mb-4 text-xl font-semibold text-gray-700 group-hover:text-primary">
            Organization Details
          </h2>
          <div className="space-y-3">
            <p className="flex items-center text-gray-600">
              <span className="mr-2 font-medium">Address:</span>
              <span className="text-gray-700">{address || 'N/A'}</span>
            </p>
            <p className="flex items-center text-gray-600">
              <span className="mr-2 font-medium">Street:</span>
              <span className="text-gray-700">{street || 'N/A'}</span>
            </p>
            <p className="flex items-center text-gray-600">
              <span className="mr-2 font-medium">District:</span>
              <span className="text-gray-700">{district || 'N/A'}</span>
            </p>
          </div>
        </div>

        {/* Limits Card */}
        <div className="group rounded-xl bg-white p-6 shadow-md transition-all duration-300 hover:shadow-xl">
          <h2 className="mb-4 text-xl font-semibold text-gray-700 group-hover:text-primary">
            Limits
          </h2>
          <div className="grid grid-cols-1 gap-3 sm:grid-cols-2">
            <div className="rounded-lg bg-gray-50 p-3">
              <p className="text-sm font-medium text-gray-500">Warehouse</p>
              <p className="mt-1 text-lg font-semibold text-gray-700">
                {warehouseLimit}
              </p>
            </div>
            <div className="rounded-lg bg-gray-50 p-3">
              <p className="text-sm font-medium text-gray-500">Shop</p>
              <p className="mt-1 text-lg font-semibold text-gray-700">
                {shopLimit}
              </p>
            </div>
          </div>
        </div>

        {/* Last Numbers Card */}
        <div className="group rounded-xl bg-white p-6 shadow-md transition-all duration-300 hover:shadow-xl">
          <h2 className="mb-4 text-xl font-semibold text-gray-700 group-hover:text-primary">
            Last Numbers
          </h2>
          <div className="grid grid-cols-1 gap-3 sm:grid-cols-2">
            <div className="rounded-lg bg-gray-50 p-3">
              <p className="text-sm font-medium text-gray-500">Last Stock No</p>
              <p className="mt-1 text-lg font-semibold text-gray-700">
                {lastStockNo}
              </p>
            </div>
            <div className="rounded-lg bg-gray-50 p-3">
              <p className="text-sm font-medium text-gray-500">
                Last Product No
              </p>
              <p className="mt-1 text-lg font-semibold text-gray-700">
                {lastProductNo}
              </p>
            </div>
            <div className="rounded-lg bg-gray-50 p-3">
              <p className="text-sm font-medium text-gray-500">
                Last Customer No
              </p>
              <p className="mt-1 text-lg font-semibold text-gray-700">
                {lastCustomerNo}
              </p>
            </div>
            <div className="rounded-lg bg-gray-50 p-3">
              <p className="text-sm font-medium text-gray-500">
                Last Employee No
              </p>
              <p className="mt-1 text-lg font-semibold text-gray-700">
                {lastEmployeeNo}
              </p>
            </div>
          </div>
        </div>

        {/* Donut Chart Card */}
        {/*  <div className="col-span-1 rounded-lg bg-white p-6 shadow-lg md:col-span-2 lg:col-span-1">
            <h2 className="mb-4 text-xl font-semibold text-gray-700">
              Sales Distribution
            </h2>
            <DonutChart />
          </div> */}

        {/* Profit Pie Chart Card */}
        {/* <div className="col-span-1 rounded-lg bg-white p-6 shadow-lg md:col-span-2 lg:col-span-1">
            <h2 className="mb-4 text-xl font-semibold text-gray-700">
              Profit Distribution
            </h2>
            <ProfitPieChart data={profitData} />
          </div> */}
      </div>
    </div>
  );
}

export default OrganizationDetailsComponent;
