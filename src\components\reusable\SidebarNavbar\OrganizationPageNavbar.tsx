import Cookies from 'js-cookie';

import logo from '../../../images/logo_4.png';
import NotificationDropdown from '../Dropdown/NotificationDropdown';
import UserDropdown from '../Dropdown/UserDropdown';

import { useGetSingleOrgDetailsQuery } from '@/redux/api/organizationApis/organizationBasicApis';

function OrganizationPageNavbar() {
  const organizationId = Cookies.get('organizationId');
  const { data } = useGetSingleOrgDetailsQuery(organizationId);

  return (
    <nav className="flex h-[60px] items-center bg-[#28243D]">
      <div className="w-full px-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-8">
            <img src={logo} alt="SOFTS.AI" className="h-12 object-contain" />
          </div>
          <div className="text-xl font-bold text-white">{data?.data?.name}</div>
          <div className="flex items-center gap-4">
            <NotificationDropdown organizationId={organizationId} />
            <UserDropdown />
          </div>
        </div>
      </div>
    </nav>
  );
}

export default OrganizationPageNavbar;
