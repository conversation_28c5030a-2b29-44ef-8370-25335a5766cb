import { StyleSheet } from '@react-pdf/renderer';

export const reportPdfStyles = StyleSheet.create({
  page: {
    backgroundColor: 'white',
    padding: 20,
    fontSize: '8px',
  },
  header: {
    textAlign: 'center',
    marginBottom: 5,
    paddingBottom: 4,
  },
  userName: {
    fontSize: '12px',
    marginBottom: 2,
  },
  address: {
    fontSize: '12px',
    marginBottom: 2,
  },
  phone: {
    fontSize: '12px',
  },
  table: {
    width: '100%',
    marginTop: 10,
    borderWidth: 1,
    borderStyle: 'solid',
    borderColor: '#000',
  },
  tableRow: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    borderBottomWidth: 1,
    borderStyle: 'solid',
    borderColor: '#000',
  },
  tableCol: {
    padding: 3,
    borderRightWidth: 1,
    borderStyle: 'solid',
    borderColor: '#000',
  },
  currentStockCol: {
    width: '16.66%',
  },
  currentStockNumCol: {
    width: '4%',
  },
  tableColLast: {
    borderRightWidth: 0,
  },
  tableRowLast: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    borderBottomWidth: 0,
    borderStyle: 'solid',
    borderColor: '#000',
  },
  currentStockTabeName: {
    width: '48%',
  },
  headerCol: {
    fontWeight: 'bold',
    backgroundColor: '#f0f0f0',
    paddingVertical: 4,
  },
  tnxMessage: {
    textAlign: 'center',
  },
  org: {
    marginTop: 10,
  },
  tableCalculationContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginTop: 5,
  },
  tableCalculation: {
    width: '25%',
  },
  tableSingleCalculation: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 2,
  },
  stockEntryCol: {
    width: '11.66%',
  },
  purchaseDateTableCol: {
    width: '30%',
  },
  stockTransferCol: {
    width: '10%',
  },
  stockTransferDateTableCol: {
    width: '17%',
  },
  stockTransferToTableCol: {
    width: '35%',
  },
  stockTransferQuantityTableCol: {
    width: '8%',
  },
  stockTransferStatusTableCol: {
    width: '12%',
  },
  stockTransferNoTableCol: {
    width: '5%',
  },
  salesReportCol: {
    width: '0%',
  },
  salesReportCreatedAtTableCol: {
    width: '17%',
  },
  salesReportPhoneTableCol: {
    width: '16%',
  },
  salesReportNoTableCol: {
    width: '5%',
  },
  salesReportOrderNumberCol: {
    width: '8%',
  },
  salesReportnameCol: {
    width: '30%',
  },
  salesReportTotalCol: {
    width: '8%',
  },
  salesReportTotalPaidCol: {
    width: '8%',
  },
  salesReportTotalDueCol: {
    width: '8%',
  },

  // salesReportSellerTableCol: {
  //   width: '10%',
  // },
  // salesReportPaymentMethodTableCol: {
  //   width: '13%',
  // },
  employeeCol: {
    width: '10%',
  },
  employeePhoneTableCol: {
    width: '20%',
  },
  employeeNameTableCol: {
    width: '20%',
  },
  salesCol: {
    width: '20%',
  },
  salesName: {
    width: '18%',
  },
  salesQty: {
    width: '6%',
  },
  salesDiscount: {
    width: '8%',
  },
  categorySalesCol: {
    width: '25%',
  },
  shopStockReportCol: {
    width: '12.5%',
  },
  shopStockReportNameCol: {
    width: '50%',
  },
  shopExpensesReportCol: {
    width: '16.66%',
  },
  shopExpensesReportNameCol: {
    width: '50%',
  },
  shopStockTransferReportCol: {
    width: '10.75%',
  },
  shopStockTransferReportDateCol: {
    width: '17%',
  },
  shopStockTransferReportNameCol: {
    width: '40%',
  },
  stockAnalysisCol: {
    width: '8.5%',
  },
  stockAnalysisNameCol: {
    width: '46%',
  },
  stockAnalysisMonthEntryCol: {
    width: '12%',
  },
  stockAnalysisMonthSoldCol: {
    width: '12%',
  },
  stockAnalysisTotalSoldCol: {
    width: '8%',
  },
  stockAnalysisTotalCol: {
    width: '5%',
  },
  StockDeleteCol: {
    width: '23.75%',
  },
  StockDeleteNoCol: {
    width: '5%',
  },
  expenseReportCol: {
    width: '0%',
  },
  expenseReportNoCol: {
    width: '4%',
  },
  expenseReportNameCol: {
    width: '30%',
  },
  expenseReportAmountCol: {
    width: '14%',
  },
  expenseReportLocationCol: {
    width: '20%',
  },
  expenseReportCreatedByCol: {
    width: '15%',
  },
  expenseReportCreatedAtCol: {
    width: '17%',
  },
});
