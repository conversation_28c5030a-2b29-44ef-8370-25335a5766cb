import { ApexOptions } from 'apexcharts'; // Import the correct ApexOptions type
import ReactApex<PERSON><PERSON> from 'react-apexcharts';

import {
  DashboardLastSevenDaysSalesSummeryResponse,
  ShopDetailsInSalesSummery,
  SingleDaySalesSummery,
  SingleOrderStatusDetailsInSalesSummery,
} from '@/types/warehouseTypes/dashboardTypes';

interface Props {
  chartData?: DashboardLastSevenDaysSalesSummeryResponse;
}

function ShopWeeklySalesSummeryChart({ chartData }: Props) {
  // Step 1: Extract the unique dates (X-axis)
  const dates = chartData?.data.map(
    (entry: SingleDaySalesSummery) => entry.date,
  );

  // Step 2: Extract the unique order statuses
  const uniqueStatuses = Array.from(
    new Set(
      chartData?.data
        .flatMap((entry: SingleDaySalesSummery) =>
          entry.shops.flatMap(
            (shop: ShopDetailsInSalesSummery) => shop.orderStatuses,
          ),
        )
        .map(
          (status: SingleOrderStatusDetailsInSalesSummery) =>
            status.orderStatus,
        ),
    ),
  );

  // Step 3: Generate series for each order status
  const statusSeries = uniqueStatuses.map((status) => ({
    name: status.replace(/_/g, ' ').toUpperCase(), // Each series is named after the order status (e.g., PENDING, DELIVERED)
    data:
      chartData?.data.map((entry: SingleDaySalesSummery) => {
        const shop = entry.shops[0]; // Assuming there is always one shop
        const statusEntry = shop?.orderStatuses.find(
          (os: SingleOrderStatusDetailsInSalesSummery) =>
            os.orderStatus === status,
        );
        return statusEntry ? statusEntry.totalAmount : 0; // Return 0 if the status is not present
      }) ?? [],
  }));

  // Step 4: Add a series for totalAmount
  const totalAmountSeries = {
    name: 'TOTAL',
    data:
      chartData?.data.map((entry: SingleDaySalesSummery) =>
        entry.shops.reduce(
          (sum, shop: ShopDetailsInSalesSummery) => sum + shop.totalAmount,
          0,
        ),
      ) ?? [],
  };

  // Combine all series
  const series = [totalAmountSeries, ...statusSeries];

  // Defining chartOptions and explicitly typing it as ApexOptions
  const chartOptions: ApexOptions = {
    chart: {
      type: 'line', // Specify a valid ApexCharts chart type like 'line', 'bar', etc.
    },
    xaxis: {
      categories: dates, // X-axis will show the dates
      title: {
        text: 'Date',
      },
    },
    yaxis: {
      title: {
        text: 'Total Amount',
      },
    },
    stroke: {
      curve: 'smooth',
    },
    markers: {
      size: 5,
    },
    dataLabels: {
      enabled: true,
    },
    tooltip: {
      shared: true,
      intersect: false,
    },
    title: {
      text: 'Sales Summery',
    },
  };

  return (
    <div className="w-full">
      <ReactApexChart
        options={chartOptions} // Ensure options has the correct type
        series={series}
        type="line" // Set the type explicitly here
        height={350}
      />
    </div>
  );
}

export default ShopWeeklySalesSummeryChart;
