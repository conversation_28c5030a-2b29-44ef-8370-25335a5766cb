import { useLocation } from 'react-router-dom';

import CustomDateFilterInput from '@/components/reusable/Inputs/CustomDateFilterInput';
import CustomSelectForFilter from '@/components/reusable/Inputs/CustomSelectForFilter';
import SearchInput from '@/components/reusable/Inputs/SearchInput';

interface Props {
  handleFilter: (fieldName: string, value: string) => void;
}

function StockListFilterOptions({ handleFilter }: Props) {
  const router = new URLSearchParams(useLocation().search);
  const productName = router.get('productName');
  const categoryName = router.get('categoryName');
  const barcode = router.get('barcode');
  // const productSerialNo = router.get('productSerialNo');
  const status = router.get('status');
  const startDate = router.get('startDate');
  const endDate = router.get('endDate');

  const stockStatusFilter = [
    { label: 'AVAILABLE', value: 'AVAILABLE' },
    { label: 'ASSIGNED', value: 'ASSIGNED' },
    { label: 'HOLD', value: 'HOLD' },
    { label: 'SOLD', value: 'ORDERED' },
    { label: 'RESTOCK REQUESTED', value: 'RESTOCK_REQUESTED' },
  ];

  return (
    <div className="flex flex-col gap-3 md:flex-row md:gap-4">
      <SearchInput
        placeholder="Search by Name"
        handleSubmit={(value: string) => handleFilter('productName', value)}
        value={productName ?? ''}
      />
      <SearchInput
        placeholder="Search by Barcode"
        handleSubmit={(value: string) => handleFilter('barcode', value)}
        value={barcode ?? ''}
      />
      <SearchInput
        placeholder="Search by Category"
        handleSubmit={(value: string) => handleFilter('categoryName', value)}
        value={categoryName ?? ''}
      />
      {/* <SearchInput
        placeholder="Product Id"
        handleSubmit={(value: string) => handleFilter('productSerialNo', value)}
        value={productSerialNo ?? ''}
      /> */}
      <CustomSelectForFilter
        options={stockStatusFilter}
        selectedValue={status ?? ''}
        handleSelect={(e) => handleFilter('status', e)}
        placeHolder="Status"
      />
      <CustomDateFilterInput
        value={startDate ?? ''}
        placeholder="Select Start Date"
        label="Start Date"
        handleChange={(value: string) => handleFilter('startDate', value)}
      />
      <CustomDateFilterInput
        value={endDate ?? ''}
        placeholder="Select Start Date"
        label="End Date"
        handleChange={(value: string) => handleFilter('endDate', value)}
        minimumDate={startDate ?? ''}
      />
    </div>
  );
}

export default StockListFilterOptions;
