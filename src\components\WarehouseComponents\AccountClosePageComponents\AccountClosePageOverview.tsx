import Cookies from 'js-cookie';
import { useState } from 'react';
import { useLocation } from 'react-router-dom';

import AccountCloseDataRenderer from './AccountCloseDataRenderer';
import AccountClosePageFilterModal from './AccountClosePageFilterModal';

import FilterButton from '@/components/reusable/Buttons/FilterButton';
import StartDateEndDateWithSearch from '@/components/reusable/ReusableFilters/StartDateEndDateWithSearch';
import TableSkeletonLoaderHalf from '@/components/reusable/SkeletonLoader/TableSkeletionLoaderHalf';
import { useGetAccountsSummeryReportQuery } from '@/redux/api/warehouseApis/reportsApis';

interface Props {
  warehouseId: string;
}

function AccountClosePageOverview({ warehouseId }: Props) {
  const [isFilterModalOpen, setIsFilterModalOpen] = useState<boolean>(false);
  const router = new URLSearchParams(useLocation().search);
  const startDate = router.get('startDate') || `${new Date().toISOString()}`;
  const endDate = router.get('endDate') || `${new Date().toISOString()}`;

  const { data, isLoading, isFetching } = useGetAccountsSummeryReportQuery({
    organizationId: Cookies.get('organizationId') ?? '',
    warehouseId,
    type: 'custom',
    startDate,
    endDate,
  });

  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-1">
        <div className="flex items-center">
          <div className="search-title-and-btn flex items-center">
            <div className="relative">
              <div className="block xl:hidden">
                <FilterButton
                  handleClick={() => setIsFilterModalOpen(!isFilterModalOpen)}
                />
              </div>
              <div
                className={`${isFilterModalOpen ? 'block' : 'hidden'} xl:hidden`}
              >
                <AccountClosePageFilterModal />
              </div>
            </div>
          </div>
          <div className="hidden xl:block">
            <StartDateEndDateWithSearch />
          </div>
        </div>
      </div>
      <div>
        {!isLoading && !isFetching ? (
          <AccountCloseDataRenderer data={data} />
        ) : (
          <TableSkeletonLoaderHalf tableColumn={2} tableRow={3} />
        )}
      </div>
    </div>
  );
}

export default AccountClosePageOverview;
