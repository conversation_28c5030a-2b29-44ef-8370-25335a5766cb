import { Document, pdf } from '@react-pdf/renderer';

// import SingleOrderInvoicePdfTwo from '@/components/reusable/SingeOrderInvoicePdf/SingleOrderInvoicePdfTwo';
// import SingleOrderPosInvoiceTwo from '@/components/reusable/SingeOrderInvoicePdf/SingleOrderPosInvoiceTwo';
import SingleOrderInvoicePdf from '@/components/reusable/SingeOrderInvoicePdf/SingleOrderInvoicePdf';
import SingleOrderPosInvoice from '@/components/reusable/SingeOrderInvoicePdf/SingleOrderPosInvoice';
import SingleWholesaleOrderInvoicePdf from '@/components/reusable/SingeOrderInvoicePdf/SingleWholesaleOrderInvoicePdf';
import { ShopOrderDetails } from '@/types/shopTypes/shopOrderTypes';

export const handleGenerateSingleOrderPdf = async (
  order?: ShopOrderDetails,
  returnPolicyText?: string,
) => {
  const blob = await pdf(
    <Document>
      <SingleOrderInvoicePdf
        orderDetails={order}
        returnPolicyText={returnPolicyText}
      />
      {/* <SingleOrderInvoicePdfTwo orderDetails={order} /> */}
    </Document>,
  ).toBlob();

  const url = URL.createObjectURL(blob);
  window.open(url);
};

export const handleGenerateBulkOrderPdf = async (
  orders: ShopOrderDetails[],
  setIsPdfGenerating: (value: boolean) => void,
) => {
  setIsPdfGenerating(true);
  const blob = await pdf(
    <Document>
      {orders?.map((order: ShopOrderDetails) => (
        <SingleOrderInvoicePdf orderDetails={order} key={order.id} />
      ))}
    </Document>,
  ).toBlob();
  setIsPdfGenerating(false);

  const url = URL.createObjectURL(blob);
  window.open(url);
};

export const handleGenerateSingleWholesaleOrderPdf = async (
  order: ShopOrderDetails,
) => {
  const blob = await pdf(
    <Document>
      <SingleWholesaleOrderInvoicePdf orderDetails={order} />
    </Document>,
  ).toBlob();

  const url = URL.createObjectURL(blob);
  window.open(url);
};

export const handleGenerateSingleOrderPosPdf = async (
  order: ShopOrderDetails,
  returnPolicyText?: string,
) => {
  const blob = await pdf(
    <Document>
      <SingleOrderPosInvoice
        orderDetails={order}
        returnPolicyText={returnPolicyText}
      />
      {/* <SingleOrderPosInvoiceTwo orderDetails={order} /> */}
    </Document>,
  ).toBlob();

  const url = URL.createObjectURL(blob);
  window.open(url);
};
