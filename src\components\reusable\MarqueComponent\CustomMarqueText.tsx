import { useEffect, useState } from 'react';
import Marquee from 'react-fast-marquee';
import { Link } from 'react-router-dom';

function CustomMarqueText() {
  const [showFirst, setShowFirst] = useState(true);

  useEffect(() => {
    const interval = setInterval(() => {
      setShowFirst((prev) => !prev);
    }, 15000); // change message every 15 seconds (adjust as needed)

    return () => clearInterval(interval);
  }, []);
  return (
    <Marquee pauseOnHover speed={50} gradient={false}>
      <div className="flex items-center gap-2">
        {showFirst ? (
          <span>
            আমাদের সফটওয়্যার আপনার বন্ধু ও পরিবারের মাঝে এখনই রেফার করুন!
            পাচ্ছেন ১৫০০ টাকা ক্যাশ অথবা ১ মাসের সার্ভিস চার্জ একদম ফ্রি!
          </span>
        ) : (
          <>
            <span>যেকোন সমস্যা হলে, অনুগ্রহ করে</span>
            <Link
              to="tel:01723-714141"
              className="hover:text-blue-600 hover:underline"
            >
              01723-714141
            </Link>
            <span>নাম্বারে যোগাযোগ করুন।</span>
          </>
        )}
      </div>
    </Marquee>
  );
}

export default CustomMarqueText;
