import SearchInput from '@/components/reusable/Inputs/SearchInput';

export default function ShopOrderPageFilterModal() {
  return (
    <div className="fixed z-50 w-full p-4">
      <div className="absolute left-0 top-0 p-4">
        <div className="">
          <div className="rounded-xl bg-white p-4">
            <SearchInput
              placeholder="Search by Name"
              handleSubmit={(value: string) => console.log(value)}
            />
            <SearchInput
              placeholder="Search by Brand"
              handleSubmit={(value: string) => console.log(value)}
            />
            <SearchInput
              placeholder="Search by Category"
              handleSubmit={(value: string) => console.log(value)}
            />
            <SearchInput
              placeholder="Search by Sub Category"
              handleSubmit={(value: string) => console.log(value)}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
