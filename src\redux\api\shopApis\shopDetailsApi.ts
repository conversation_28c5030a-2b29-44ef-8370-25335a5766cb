import BaseApi from '../baseApi';

import { TagTypes } from '@/redux/tag-types';
import { ShopSettingsData } from './shopSettingsApi';

export interface ShopDetailsResponse {
  success: boolean;
  message: string;
  statusCode: number;
  data: Data;
}

export interface Data {
  id: string;
  createdAt: string;
  updatedAt: string;
  name: string;
  imgUrl: string;
  mobileNumber: string;
  address: any;
  street: string;
  district: string;
  websiteUrl: string;
  fbUrl: string;
  zipCode: string;
  country: string;
  warehouseId: string;
  type: string;
  lastOrderNo: number;
  ShopSettings: ShopSettingsData;
}

const ShopDetailsApi = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getShopDetails: builder.query<ShopDetailsResponse, any>({
      query: (id) => ({
        url: `/shop/${id}`,
        method: 'GET',
      }),
      providesTags: [TagTypes.SHOP_CUSTOMERS],
    }),
  }),
});

export const { useGetShopDetailsQuery } = ShopDetailsApi;
