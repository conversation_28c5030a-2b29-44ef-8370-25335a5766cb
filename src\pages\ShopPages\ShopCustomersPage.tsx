import { useParams } from 'react-router-dom';

import ShopCustomersPageOverview from '@/components/ShopComponents/ShopCustomersPageComponents/ShopCustomersPageOverview';
import { useAppSelector } from '@/redux/hooks';

function ShopCustomersPage() {
  const { shopId } = useParams();
  const { warehouseId } = useAppSelector((state) => state.shopDetails);
  return (
    <div>
      <ShopCustomersPageOverview
        shopId={shopId ?? ''}
        warehouseId={warehouseId}
      />
    </div>
  );
}

export default ShopCustomersPage;
