import SpinnerLoader from '../reusable/SpinnerLoader/SpinnerLoader';

import { useGetUserProfileQuery } from '@/redux/api/userApi';

function ProfilePageOverview() {
  const { data: userDetails, isLoading: isUserDetailsLoading } =
    useGetUserProfileQuery({});
  return (
    <div className="container px-4">
      {!isUserDetailsLoading ? (
        <div className="mt-16 flex items-center justify-center">
          <div className="max-w-4xl rounded-xl bg-[#cfd8e4] shadow-lg">
            <div className="grid grid-cols-1 md:grid-cols-11">
              <div className="col-span-1 flex items-center rounded-xl bg-gradient-to-r from-[#28243D] to-[#332966] p-4 md:col-span-4">
                <div>
                  <div className="flex justify-center">
                    <img
                      className="h-20 w-20 rounded-full"
                      src="https://i.ibb.co/wc7Hz45/Avatar.png"
                      alt="user"
                    />
                  </div>
                  <div className="user-details mt-4">
                    <h2 className="text-center text-2xl font-semibold text-white">
                      {userDetails?.data?.name}
                    </h2>
                    <p className="text-center text-sm text-white">
                      {userDetails?.data?.type}
                    </p>
                    <p className="mt-3 text-center text-gray-300">
                      Lorem ipsum dolor sit amet consectetur, adipisicing elit.
                    </p>
                  </div>
                  {/* <div className="user-stats mt-8">
                  <div className="grid grid-cols-1 gap-2 lg:grid-cols-2">
                    <div className="stats-card flex items-center">
                      <div className="flex h-12 w-12 items-center justify-center rounded-md bg-[#8b67cc]">
                        <Check className="text-white" />
                      </div>
                      <div className="ml-2">
                        <h5 className="text-xl text-white">1.23K</h5>
                        <p className="text-gray-300">Task Done</p>
                      </div>
                    </div>
                    <div className="stats-card flex items-center">
                      <div className="flex h-12 w-12 items-center justify-center rounded-md bg-[#8b67cc]">
                        <Star className="text-white" />
                      </div>
                      <div className="ml-2">
                        <h5 className="text-xl text-white">1.23K</h5>
                        <p className="text-gray-300">Task Done</p>
                      </div>
                    </div>
                  </div>
                </div> */}
                </div>
              </div>
              <div className="col-span-1 px-8 py-4 md:col-span-7">
                <h3 className="border-b border-[#28243D] text-2xl font-semibold uppercase">
                  Information
                </h3>
                <div className="mt-5">
                  <ul className="space-y-2">
                    <li>
                      <div className="info-card flex flex-col space-x-0 sm:flex-row sm:space-x-2">
                        <h4 className="text-lg font-semibold">Name:</h4>
                        <p className="text-lg text-gray-600">
                          {userDetails?.data?.name}
                        </p>
                      </div>
                    </li>
                    <li>
                      <div className="info-card flex flex-col space-x-0 sm:flex-row sm:space-x-2">
                        <h4 className="text-nowrap text-lg font-semibold">
                          Billing Email:
                        </h4>
                        <p className="text-wrap text-lg text-gray-600">
                          {userDetails?.data?.email}
                        </p>
                      </div>
                    </li>
                    <li>
                      <div className="info-card flex flex-col space-x-0 sm:flex-row sm:space-x-2">
                        <h4 className="text-lg font-semibold">Status:</h4>
                        <p className="text-lg text-gray-600">Active</p>
                      </div>
                    </li>
                    <li>
                      <div className="info-card flex flex-col space-x-0 sm:flex-row sm:space-x-2">
                        <h4 className="text-lg font-semibold">Role:</h4>
                        <p className="text-lg text-gray-600">
                          {userDetails?.data?.type}
                        </p>
                      </div>
                    </li>
                    <li>
                      <div className="info-card flex flex-col space-x-0 sm:flex-row sm:space-x-2">
                        <h4 className="text-lg font-semibold">Contact:</h4>
                        <p className="text-lg text-gray-600">
                          {userDetails?.data?.mobileNumber}
                        </p>
                      </div>
                    </li>
                    <li>
                      <div className="info-card flex flex-col space-x-0 sm:flex-row sm:space-x-2">
                        <h4 className="text-lg font-semibold">Country:</h4>
                        <p className="text-lg text-gray-600">Bangladesh</p>
                      </div>
                    </li>
                  </ul>
                </div>
                <div className="mt-5 flex justify-end">
                  <div className="space-x-2">
                    <button
                      type="submit"
                      className="rounded-md bg-[#28243D] px-6 py-2 uppercase text-white"
                    >
                      Edit
                    </button>
                    <button
                      type="submit"
                      className="rounded-md border border-[#FF4C51] px-6 py-2 uppercase text-[#FF4C51]"
                    >
                      Suspend
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <SpinnerLoader />
      )}
    </div>
  );
}

export default ProfilePageOverview;
