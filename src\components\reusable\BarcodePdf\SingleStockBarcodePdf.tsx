import { Image, Page, Text, View } from '@react-pdf/renderer';
import JsBarcode from 'jsbarcode';
import { useEffect, useState } from 'react';

interface Props {
  barcode: string;
  name?: string;
  price: string;
}
function SingleStockBarcodePdf({ barcode, name, price }: Props) {
  const [barcodeImage, setBarcode] = useState<string>('');

  useEffect(() => {
    if (barcode) {
      const canvas = document.createElement('canvas');
      JsBarcode(canvas, barcode.toString(), {
        format: 'CODE128',
        displayValue: false,
      });
      setBarcode(canvas.toDataURL('image/png'));
    }
  }, [barcode]);
  return (
    <Page size={[144, 72]} style={{ padding: '0px', overflow: 'hidden' }}>
      <View style={{ height: '100%', overflow: 'hidden' }}>
        <view
          style={{
            display: 'flex',
            justifyContent: 'center',
            flexDirection: 'row',
            padding: '4px 0px 2px 0px',
          }}
        >
          <Image
            src={barcodeImage}
            style={{
              width: 120,
              height: 25,
              padding: 0,
              marginLeft: '-2px',
            }}
          />
        </view>
        <View
          style={{
            display: 'flex',
            alignItems: 'center',
            flexDirection: 'row',
            fontSize: '8px',
            justifyContent: 'center',
            gap: '8px',
          }}
        >
          <Text>[{barcode}]</Text>
          <Text>BDT- {price}</Text>
        </View>
        <Text
          style={{
            fontSize: '8px',
            fontWeight: 600,
            textAlign: 'center',
            padding: '0px 4px',
          }}
        >
          {name}
        </Text>
      </View>
    </Page>
  );
}
export default SingleStockBarcodePdf;
