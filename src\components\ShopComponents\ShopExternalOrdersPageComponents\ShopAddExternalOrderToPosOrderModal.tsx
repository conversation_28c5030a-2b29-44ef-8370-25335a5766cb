import { Document, PDFViewer } from '@react-pdf/renderer';
import { useFormik } from 'formik';
import Cookies from 'js-cookie';
import { useEffect, useState } from 'react';
import Lottie from 'react-lottie';
import { toast } from 'react-toastify';
import * as Yup from 'yup';

import animationData from '../../../utils/lottieFiles/success-lottie.json';
import ShopCourierBookingModal from '../ShopCourierBooking/ShopCourierBookingModal';
import MultiplePaymentMethodModal from '../ShopOrdersPageComponents/MultiplePaymentMethodModal';
import ShopCreateOrderPageStockTable from '../ShopOrdersPageComponents/ShopCreateOrderPageStockTable';
import ShopCustomerDetailsFormInOrderForm from '../ShopOrdersPageComponents/ShopCustomerDetailsFormInOrderForm';
import ShopCustomerLastOrderDetails from '../ShopOrdersPageComponents/ShopCustomerLastOrderDetails';
import ShopOrderConfirmModal from '../ShopOrdersPageComponents/ShopOrderConfirmModal';
import ShopOrderSubmitForm from '../ShopOrdersPageComponents/ShopOrderSubmitForm';

import SearchInput from '@/components/reusable/Inputs/SearchInput';
import Modal from '@/components/reusable/Modal/Modal';
import ModalTitle from '@/components/reusable/Modal/ModalTitle';
import NoResultFoundForOthers from '@/components/reusable/NoResultFound/NoResultFoundForOthers';
import SingleOrderInvoicePdf from '@/components/reusable/SingeOrderInvoicePdf/SingleOrderInvoicePdf';
import TableSkeletonLoaderHalf from '@/components/reusable/SkeletonLoader/TableSkeletionLoaderHalf';
import { useGetShopCustomerDetailsByPhoneQuery } from '@/redux/api/shopApis/shopCustomersApi';
import {
  SingleProductOfExternalOrder,
  useGetShopSingleExternalOrderDetailsQuery,
} from '@/redux/api/shopApis/shopExternalOrdersApis';
import { useCreateShopOrderMutation } from '@/redux/api/shopApis/shopOrdersApis';
import { useGetShopStockListQuery } from '@/redux/api/shopApis/shopStockApis';
import { usePathaoAddressParserMutation } from '@/redux/api/warehouseApis/couriersApi';
import { useAppSelector } from '@/redux/hooks';
import { ShopOrderDetails } from '@/types/shopTypes/shopOrderTypes';
import { PathaoParsedAddressDetails } from '@/types/warehouseTypes/settingsTypes';
import { SingleStockDetails } from '@/types/warehouseTypes/stockTypes';
import {
  CalculateDiscountPrice,
  CalculateVat,
} from '@/utils/CalculateDiscountPrice';
import { handleGenerateSingleOrderPdf } from '@/utils/GenerateOrderPdf';
import { getDivisionByDistrict } from '@/utils/getDivisionName';

interface Props {
  shopId: string;
  handleClose: () => void;
  selectedOrderId?: string;
}

const formikInitialValues = {
  name: '',
  mobileNumber: '',
  address: null,
  email: null,
  gender: null,
  totalProductPrice: 0,
  productDiscount: 0,
  totalDiscount: 0,
  vat: 0,
  deliveryCharge: 0,
  customerPaid: 0,
  returnAmount: 0,
  deliveryPartner: 'OTHER',
  paymentMethod: 'CASH',
  customerId: '',
  note: '',
  employeeId: '',
  orderSource: 'WEBSITE',
};

function ShopAddExternalOrderToPosOrderModal({
  shopId,
  handleClose,
  selectedOrderId,
}: Props) {
  const { data } = useGetShopSingleExternalOrderDetailsQuery(selectedOrderId);

  const { warehouseId } = useAppSelector((state) => state.shopDetails);
  // const dispatch = useAppDispatch();
  const { userDetails, shopSettings } = useAppSelector((state) => state);
  const [isCourierBookingModalOpen, setIsCourierBookingModalOpen] =
    useState<boolean>(false);
  const [courierBookingOrderDetails, setCourierBookingOrderDetails] =
    useState<ShopOrderDetails>();
  const [isOrderSuccessModalOpen, setIsOrderSuccessModalOpen] =
    useState<boolean>(false);
  const [selectedProducts, setSelectedProducts] =
    useState<SingleStockDetails[]>();
  const [discountPercentage, setDiscountPercentage] = useState<number>(0);
  const [isBarcodeScanned, setIsBarcodeScanned] = useState(false);
  const [barcode, setBarcode] = useState<string>();
  const [productName, setProductName] = useState<string>();
  const [totalPaidAmount, setTotalPaidAmount] = useState(0);
  const [isMultiplePaymentModalOpen, setIsMultiplePaymentModalOpen] =
    useState(false);
  const [selectedPayments, setSelectedPayments] = useState<
    [{ method: string; amount: number }]
  >([
    {
      method: userDetails?.shopType === 'ONLINE' ? 'BKASH' : 'CASH',
      amount: 0,
    },
  ]);
  const [netTotal, setNetTotal] = useState(0);
  const [isConfirmOrderModalOpen, setIsConfirmOrderModalOpen] =
    useState<boolean>(false);

  // pathao address parser api
  const [pathaoAddressParser] = usePathaoAddressParserMutation();

  const {
    data: stockListData,
    isLoading,
    refetch,
    isFetching,
  } = useGetShopStockListQuery(
    {
      organizationId: Cookies.get('organizationId') ?? '',
      warehouseId,
      shopId,
      barcode,
      productName,
      status: 'ASSIGNED',
      page: '1',
      limit: '5',
    },
    { skip: !shopId },
  );

  // create order api
  const [createShopOrder, { isLoading: isOrderCreating }] =
    useCreateShopOrderMutation();

  const formik = useFormik({
    initialValues: formikInitialValues,
    validationSchema: Yup.object({
      name: Yup.string().required('Customer name is required'),
      mobileNumber: Yup.string()
        .required('Mobile Number is required')
        .min(11)
        .max(11),
    }),

    onSubmit: async () => {
      setIsConfirmOrderModalOpen(true);
    },
  });
  const handleSubmitOrder = async () => {
    let pathaoParsedAddress: PathaoParsedAddressDetails | null = null;
    if (
      formik.values.address &&
      Cookies.get('pathaoAccessToken') &&
      userDetails?.shopType === 'ONLINE'
    ) {
      const res = await pathaoAddressParser({
        pathaoToken: Cookies.get('pathaoAccessToken'),
        address: formik?.values?.address,
      });
      if (res.data?.data.data) {
        pathaoParsedAddress = res.data.data.data;
      }
    }
    const orderData = {
      name: formik.values.name,
      mobileNumber: formik.values.mobileNumber,
      // email: formik.values.email ?? null,
      email: null,
      note: formik.values.note ? formik.values.note : null,
      address: formik.values.address,
      adminDiscount:
        Number(formik.values.totalDiscount) -
        Number(formik.values.productDiscount),
      // vat: Number(values.vat),
      deliveryCharge: Number(formik.values.deliveryCharge),
      customerPaid:
        Number(totalPaidAmount) > netTotal ? netTotal : Number(totalPaidAmount),
      orderType: 'Normal',
      OrderItem: selectedProducts?.map((product: SingleStockDetails) => {
        return {
          itemId: product?.id,
          productId: null,
          quantity: 1,
        };
      }),
      warehouseId: null,
      shopId,
      paymentMethods:
        selectedPayments?.length > 1
          ? selectedPayments?.filter((sin) => sin.amount > 0)
          : [
              {
                method: selectedPayments[0]?.method,
                amount:
                  selectedPayments[0]?.amount > netTotal
                    ? netTotal
                    : selectedPayments[0]?.amount,
              },
            ],
      organizationId: Cookies.get('organizationId'),
      customerId: formik.values.customerId ? formik.values.customerId : null,
      employeeId: formik.values.employeeId ? formik.values.employeeId : null,
      orderSource: formik.values.orderSource ?? null,
      recipientCity: pathaoParsedAddress?.district_id?.toString() ?? null,
      recipientZone: pathaoParsedAddress?.zone_id?.toString() ?? null,
      recipientArea: pathaoParsedAddress?.area_id?.toString() ?? null,
      division:
        getDivisionByDistrict(pathaoParsedAddress?.district_name ?? '') ?? null,
      district: pathaoParsedAddress?.district_name ?? null,
      street: pathaoParsedAddress?.zone_name ?? null,
      externalOrderId: selectedOrderId ?? null,
    };

    toast.promise(createShopOrder(orderData).unwrap(), {
      pending: 'Creating New Order...',
      success: {
        render({ data: res }) {
          if (res?.statusCode === 200 || res?.statusCode === 201) {
            // setLastOrderDetails(undefined);
            setIsConfirmOrderModalOpen(false);
            formik.setFieldValue('name', '');
            formik.setFieldValue('mobileNumber', '');
            // handle after order process in online shop
            setIsConfirmOrderModalOpen(false);
            // dispatch({ type: 'socket/subscribeOrder', payload: res.data.id });
            setIsOrderSuccessModalOpen(true);
            setCourierBookingOrderDetails(res?.data);
            // handleClose();
            refetch();
            // navigate(ROUTES.SHOP.INVOICE(shopId, res?.data?.data?.id));
            formik.resetForm();
            setSelectedProducts([]);
            setSelectedPayments([
              {
                method: 'CASH',
                amount: 0,
              },
            ]);
          }
          return 'Order created Successfully';
        },
      },
      error: {
        render({ data: error }) {
          console.log(error);
          return 'Error on creating order';
        },
      },
    });
  };

  // select stock for order function
  const handleSelectProduct = (stock: SingleStockDetails) => {
    if (selectedProducts?.length) {
      const isProductExist = selectedProducts?.find(
        (product) => product?.id === stock?.id,
      );
      if (isProductExist) {
        const remaining = selectedProducts?.filter(
          (product) => product.id !== stock.id,
        );
        setSelectedProducts(remaining);
        toast.success(`${stock?.name} from Cart`);
      } else {
        setSelectedProducts([...selectedProducts, stock]);
        toast.success(`${stock?.name} Added To Cart`);
      }
    } else {
      setSelectedProducts([stock]);
      toast.success(`${stock?.name} Added To Cart`);
    }
    setBarcode('');
    setProductName('');
  };

  // select product automatically on scan
  useEffect(() => {
    if (
      stockListData?.data[0] &&
      Number(stockListData?.data[0]?.barcode) === Number(barcode) &&
      isBarcodeScanned
    ) {
      handleSelectProduct(stockListData?.data[0]);
      setBarcode('');
      setIsBarcodeScanned(false);
    }
  }, [data]);

  // calculate pricing
  useEffect(() => {
    let discountProductPrice = 0;
    let total = 0;
    // let productDiscount = 0;
    let vat = 0;
    selectedProducts?.map((product: SingleStockDetails) => {
      total += product.retailPrice;
      discountProductPrice += CalculateDiscountPrice({
        retailPrice: product?.retailPrice,
        discountType: product.discountType,
        discount: product.discount,
      });
      vat += CalculateVat({
        retailPrice: product?.retailPrice,
        discountType: product.discountType,
        discount: product.discount,
        vat: product?.vat,
      });
      return 0;
    });
    formik.setFieldValue('totalProductPrice', total);
    formik.setFieldValue('totalDiscount', total - discountProductPrice);
    formik.setFieldValue('productDiscount', total - discountProductPrice);
    formik.setFieldValue('vat', vat);
  }, [selectedProducts]);

  // calculate discount price
  useEffect(() => {
    if (formik.values?.totalProductPrice) {
      const discount =
        (Number(formik.values?.totalDiscount) * 100) /
        Number(formik.values?.totalProductPrice);
      setDiscountPercentage(Math.ceil(discount));
    }
  }, [formik.values.totalProductPrice]);

  // read barcode and make api call
  useEffect(() => {
    let scannedBarcode = '';
    let timeout: NodeJS.Timeout;

    const handleBarcodeInput = (e: KeyboardEvent) => {
      if (timeout) clearTimeout(timeout);

      if (e.key === 'Enter') {
        if (scannedBarcode) {
          setIsBarcodeScanned(true);
          setBarcode(scannedBarcode);
        }
        scannedBarcode = '';
      } else {
        scannedBarcode += e.key;
        timeout = setTimeout(() => {
          scannedBarcode = '';
        }, 200);
      }
    };

    window.addEventListener('keydown', handleBarcodeInput);
    return () => {
      window.removeEventListener('keydown', handleBarcodeInput);
    };
  }, []);

  // calculate total paid amount
  useEffect(() => {
    let tot = 0;
    if (selectedPayments?.length) {
      selectedPayments.forEach((payment) => {
        tot += Number(payment.amount);
      });
    }
    setTotalPaidAmount(tot);
  }, [selectedPayments]);

  // calculate net total
  useEffect(() => {
    setNetTotal(
      Number(formik.values.totalProductPrice) +
        Number(formik.values.deliveryCharge) +
        Number(formik.values.vat) -
        Number(formik.values.totalDiscount),
    );
  }, [
    formik.values.totalProductPrice,
    formik.values.deliveryCharge,
    formik.values.vat,
    formik.values.totalDiscount,
  ]);

  useEffect(() => {
    if (data?.data.firstName) {
      const address = data?.data?.shippingAddress;
      const oneLineAddress = [
        address.address1,
        address.address2,
        address.city,
        address.province,
        address.zip,
        address.country,
      ]
        .filter(Boolean)
        .join(', ');
      formik.setFieldValue(
        'name',
        `${data?.data?.firstName} ${data?.data?.lastName}`,
      );
      formik.setFieldValue('mobileNumber', data?.data.phone);
      formik.setFieldValue('email', data?.data.email ?? '');
      formik.setFieldValue('address', oneLineAddress ?? '');
      formik.setFieldValue('note', data.data.note ?? '');
      formik.setFieldValue('deliveryCharge', data.data.totalShippingCost ?? '');
      if (data?.data?.paymentMethods?.length) {
        setSelectedPayments(
          () =>
            data.data.paymentMethods.map((sin) => ({
              method: sin.method,
              amount: sin.amount,
            })) as [{ method: string; amount: number }],
        );
      }
    }
  }, [data]);
  const [lastOrderDetails, setLastOrderDetails] = useState<ShopOrderDetails>();
  // get customer details by phone number
  const { data: customerData, isLoading: isCustomerDataLoading } =
    useGetShopCustomerDetailsByPhoneQuery(
      {
        phone: formik.values.mobileNumber,
        organizationId: Cookies.get('organizationId'),
      },
      {
        skip: formik.values.mobileNumber?.length !== 11,
      },
    );
  useEffect(() => {
    if (customerData?.data) {
      setLastOrderDetails(customerData.data.Order[0]);
    }
  }, [customerData]);

  // lottie animation data
  const defaultOptions = {
    loop: false,
    autoplay: true,
    animationData,
    rendererSettings: {
      preserveAspectRatio: 'xMidYMid slice',
    },
  };

  return (
    <div className="flex max-h-[95vh] w-[95vw] flex-col gap-4 overflow-y-auto rounded-xl bg-white p-4">
      <ModalTitle text="Entry website order to POS" handleClose={handleClose} />
      <div className="grid grid-cols-12 gap-4">
        <div className="col col-span-12 md:col-span-5">
          <div className="mx-auto max-w-5xl rounded-lg bg-white p-2 shadow-lg">
            <h1 className="mb-2 text-xl font-bold text-gray-800">
              Website Order Details
            </h1>
            {/* Order Summary */}
            <div className="mb-1">
              <div className="grid grid-cols-2 gap-1 text-sm text-gray-600">
                <p>
                  <span className="font-medium">Customer Name:</span>{' '}
                  {data?.data?.firstName} {data?.data?.lastName}
                </p>
                <p>
                  <span className="font-medium">Email:</span>{' '}
                  {data?.data?.email}
                </p>
                <p>
                  <span className="font-medium">Phone:</span>{' '}
                  {data?.data?.phone}
                </p>
                <p>
                  <span className="font-medium">Total Price:</span> ৳
                  {data?.data?.totalPrice.toFixed(2)}
                </p>
                <p>
                  <span className="font-medium">Subtotal:</span> ৳
                  {data?.data?.subtotalPrice.toFixed(2)}
                </p>
                <p>
                  <span className="font-medium">Total Discount:</span> ৳
                  {data?.data?.totalDiscount.toFixed(2)}
                </p>
                <p>
                  <span className="font-medium">Shipping Cost:</span> ৳
                  {data?.data?.totalShippingCost.toFixed(2)}
                </p>
                <p>
                  <span className="font-medium">Outstanding:</span> ৳
                  {data?.data?.totalOutstanding.toFixed(2)}
                </p>
              </div>
            </div>
            {/* Line Items */}
            <div>
              <table className="relative w-full border border-black">
                <thead className="bg-yellow-400">
                  <tr>
                    {/* <th className="tableHead">No</th> */}
                    <th className="tableHead bg-yellow-400">#</th>
                    <th className="tableHead table-col-width">Name</th>
                    <th className="tableHead">Quantity</th>
                    <th className="tableHead">Price</th>
                    <th className="tableHead">Discount</th>
                    <th className="tableHead">Total</th>
                  </tr>
                </thead>
                <tbody className="table-tbody divide-y bg-slate-200">
                  {data?.data.lineItems?.map(
                    (product: SingleProductOfExternalOrder, index) => {
                      return (
                        <tr key={product?.id}>
                          {/* <td className="tableData">{index + 1}</td> */}
                          <td className="tableData">{index + 1}</td>
                          <td className="tableData table-col-width">
                            {product.name}
                          </td>
                          <td className="tableData"> {product.quantity}</td>
                          <td className="tableData">
                            {' '}
                            {product.price.toFixed(2)}
                          </td>
                          <td className="tableData">
                            {product.discount.toFixed(2)}%
                          </td>
                          <td className="tableData">
                            {(
                              product.price * product.quantity -
                              product.price *
                                product.quantity *
                                (product.discount / 100)
                            ).toFixed(2)}
                          </td>
                        </tr>
                      );
                    },
                  )}
                </tbody>
              </table>
            </div>
          </div>
          <div>
            <div className="tableTop-2 w-full">
              <div className="flex w-full items-center justify-between">
                <SearchInput
                  value={productName}
                  placeholder="Search by Name"
                  handleSubmit={(value: string) => setProductName(value)}
                />
                <SearchInput
                  value={barcode}
                  placeholder="Barcode"
                  handleSubmit={(value: string) => setBarcode(value)}
                />
              </div>
            </div>
            {!isLoading && !isFetching ? (
              <div>
                {stockListData?.data?.length ? (
                  <div className="h-[25vh] overflow-auto">
                    <ShopCreateOrderPageStockTable
                      stocks={stockListData?.data}
                      selectedProducts={selectedProducts ?? []}
                      handleSelectProduct={handleSelectProduct}
                    />
                  </div>
                ) : (
                  <NoResultFoundForOthers pageType="product" />
                )}
              </div>
            ) : (
              <div className="h-[25vh] overflow-hidden">
                <TableSkeletonLoaderHalf tableColumn={3} tableRow={4} />
              </div>
            )}
          </div>
        </div>
        <div className="col col-span-12 md:col-span-7">
          <form onSubmit={formik.handleSubmit} className="w-full">
            <div>
              <ShopCustomerDetailsFormInOrderForm
                formik={formik}
                isLoading={isCustomerDataLoading}
              />
              {customerData?.data ? (
                <div className="mt-1">
                  <ShopCustomerLastOrderDetails
                    shopId={shopId}
                    lastOrderDetails={lastOrderDetails}
                  />
                </div>
              ) : (
                ''
              )}
              <div className="my-3 h-[1px] w-full bg-black text-black" />
              <div>
                <ShopOrderSubmitForm
                  selectedProducts={selectedProducts}
                  handleSelectProduct={handleSelectProduct}
                  formik={formik}
                  setDiscountPercentage={setDiscountPercentage}
                  discountPercentage={discountPercentage}
                  netTotal={netTotal}
                  selectedPayments={selectedPayments}
                  setSelectedPayments={setSelectedPayments}
                  setIsMultiplePaymentModalOpen={setIsMultiplePaymentModalOpen}
                  totalPaidAmount={totalPaidAmount}
                  isOrderCreating={isOrderCreating}
                />
              </div>
            </div>
          </form>
        </div>
      </div>
      <Modal
        showModal={isCourierBookingModalOpen}
        setShowModal={setIsCourierBookingModalOpen}
      >
        <ShopCourierBookingModal
          orderDetails={courierBookingOrderDetails}
          handleClose={() => {
            setIsCourierBookingModalOpen(false);
            handleClose();
          }}
        />
      </Modal>
      <Modal
        showModal={isMultiplePaymentModalOpen}
        setShowModal={setIsMultiplePaymentModalOpen}
      >
        <MultiplePaymentMethodModal
          handleClose={() => setIsMultiplePaymentModalOpen(false)}
          setSelectedPayments={setSelectedPayments}
          selectedPayments={selectedPayments}
          netTotal={netTotal}
        />
      </Modal>
      <Modal
        showModal={isConfirmOrderModalOpen}
        setShowModal={setIsConfirmOrderModalOpen}
      >
        <ShopOrderConfirmModal
          selectedProducts={selectedProducts}
          formik={formik}
          netTotal={netTotal}
          totalPaidAmount={totalPaidAmount}
          selectedPayments={selectedPayments}
          shopType={userDetails?.shopType}
          handleClose={() => setIsConfirmOrderModalOpen(false)}
          handleSubmit={handleSubmitOrder}
          isOrderCreating={isOrderCreating}
        />
      </Modal>
      <Modal
        showModal={isOrderSuccessModalOpen}
        setShowModal={setIsOrderSuccessModalOpen}
      >
        <div className="flex h-[90vh] flex-col gap-4 rounded-xl bg-white p-4">
          <ModalTitle
            text="Order Success"
            handleClose={() => {
              setIsOrderSuccessModalOpen(false);
              handleClose();
            }}
          />
          <div className="flex items-start gap-8">
            <div className="mt-2 flex w-[200px] flex-col gap-4">
              <p>
                Order Created Successfully. Now Choose what next you want to do?
              </p>
              <Lottie options={defaultOptions} height={150} width={150} />
              <button
                type="button"
                className="cursor-pointer whitespace-nowrap rounded-lg bg-[#28243D] px-8 py-2 font-semibold text-white hover:bg-[#28243dd4] disabled:cursor-not-allowed disabled:bg-slate-300"
                onClick={() => {
                  handleGenerateSingleOrderPdf(
                    courierBookingOrderDetails,
                    shopSettings.returnPolicyText,
                  );
                  setIsOrderSuccessModalOpen(false);
                }}
              >
                Print Invoice
              </button>
              <button
                type="button"
                className="cursor-pointer whitespace-nowrap rounded-lg bg-[#28243D] px-8 py-2 font-semibold text-white hover:bg-[#28243dd4] disabled:cursor-not-allowed disabled:bg-slate-300"
                onClick={() => {
                  setIsCourierBookingModalOpen(true);
                  setIsOrderSuccessModalOpen(false);
                }}
              >
                Book on Courier
              </button>
              <button
                type="button"
                className="cursor-pointer whitespace-nowrap rounded-lg bg-red-600 px-8 py-2 font-semibold text-white hover:bg-[#28243dd4] disabled:cursor-not-allowed disabled:bg-slate-300"
                onClick={() => setIsOrderSuccessModalOpen(false)}
              >
                Close
              </button>
            </div>
            <div>
              <PDFViewer height={550} width={600}>
                <Document>
                  <SingleOrderInvoicePdf
                    orderDetails={courierBookingOrderDetails}
                  />
                </Document>
              </PDFViewer>
            </div>
          </div>
        </div>
      </Modal>
    </div>
  );
}

export default ShopAddExternalOrderToPosOrderModal;
