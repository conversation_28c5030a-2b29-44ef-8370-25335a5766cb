/* eslint-disable no-underscore-dangle */
import axios from 'axios';
import Cookies from 'js-cookie';

const AxiosInstance = axios.create();
AxiosInstance.defaults.headers.post['Content-Type'] = 'application/json';
AxiosInstance.defaults.headers.Accept = 'application/json';
AxiosInstance.defaults.timeout = 60000;

// function appendQueryParamsIfMissing(
//   url: string,
//   paramsToAdd: Record<string, string | undefined>,
// ) {
//   const [baseUrl, queryString] = url.split('?');
//   const searchParams = new URLSearchParams(queryString || '');

//   Object.entries(paramsToAdd).forEach(([key, value]) => {
//     if (value && !searchParams.has(key)) {
//       searchParams.set(key, value);
//     }
//   });

//   return `${baseUrl}?${searchParams.toString()}`;
// }

// Add a request interceptor
AxiosInstance.interceptors.request.use(
  (config: any) => {
    const newConfig = { ...config };
    const accessToken = Cookies.get('accessToken');
    const organizationId = Cookies.get('organizationId');
    const shopOrWarehouseId = window.location.pathname.split('/')[2];
    const routeType = window.location.pathname.split('/')[1];

    const queryParams: Record<string, string | undefined> = {};

    if (accessToken) {
      newConfig.headers.Authorization = `Bearer ${accessToken}`;
      newConfig.headers.organizationId = organizationId ?? undefined;

      if (routeType === 'shop') {
        newConfig.headers.shopId = shopOrWarehouseId ?? undefined;
        queryParams.shopId = shopOrWarehouseId;
      } else if (routeType === 'warehouse') {
        newConfig.headers.warehouseId = shopOrWarehouseId ?? undefined;
        queryParams.warehouseId = shopOrWarehouseId;
      }
    }
    // Append missing query parameters
    // if (newConfig?.url) {
    //   newConfig.url = appendQueryParamsIfMissing(newConfig.url, queryParams);
    // }
    return newConfig;
  },
  (error) => {
    // Do something with the request error

    return Promise.reject(error);
  },
);

// Add a response interceptor
AxiosInstance.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;
    const refreshToken = Cookies.get('refreshToken');
    // console.log(refreshToken);

    if (refreshToken) {
      if (error.response.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true; // Mark the request to avoid infinite loops
        try {
          const res = await axios.get(
            `${import.meta.env.VITE_BASEURL}/auth/refresh`,
            {
              headers: {
                Authorization: `Bearer ${refreshToken}`,
              },
            },
          );
          // console.log(res);

          if (res.status === 200) {
            Cookies.set('accessToken', res?.data?.data?.accessToken);
            Cookies.set('refreshToken', res?.data?.data?.refreshToken);
            console.log('Access token refreshed!');
            // Retry the original request with the new access token
            originalRequest.headers.Authorization = `Bearer ${res?.data?.data?.accessToken}`;
            return await axios(originalRequest);
          }
          Cookies.remove('accessToken');
          Cookies.remove('refreshToken');
          Cookies.remove('warehouseId');
          Cookies.remove('organizationId');
          Cookies.remove('type');
          window.location.href = '/login';
        } catch (err: any) {
          console.log(err);
          Cookies.remove('accessToken');
          Cookies.remove('refreshToken');
          Cookies.remove('warehouseId');
          Cookies.remove('organizationId');
          Cookies.remove('type');
          window.location.href = '/login';
        }
      }
    } else {
      Cookies.remove('accessToken');
      Cookies.remove('refreshToken');
      Cookies.remove('warehouseId');
      Cookies.remove('organizationId');
      Cookies.remove('type');
      window.location.href = '/login';
    }

    return Promise.reject(error);
  },
);

export default AxiosInstance;
