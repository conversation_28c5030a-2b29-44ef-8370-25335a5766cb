import { useFormik } from 'formik';
import { useEffect } from 'react';
import { toast } from 'react-toastify';
import * as Yup from 'yup';

import FilledSubmitButton from '@/components/reusable/Buttons/FilledSubmitButton';
import CustomDropdown from '@/components/reusable/CustomInputField/CustomDropdown';
import CustomInputField from '@/components/reusable/CustomInputField/CustomInputField';
import ModalTitle from '@/components/reusable/Modal/ModalTitle';
import { useGetProductDetailsQuery } from '@/redux/api/warehouseApis/productsApi';
import { ProductVariant } from '@/types/warehouseTypes/productTypes';

interface SelectedProductType {
  productId: 'string';
  productName: 'string';
  manufactureDate: 'string';
  expireDate: 'string';
  purchasePrice: number;
  retailPrice: number;
  quantity: number;
  discountType: 'string';
  discount: number;
  vat: number;
  // wholesalePrice: number;
  variantId: string;
}

interface Props {
  handleClose: () => void;
  productId?: string | null;
  handleSave: (data: any) => void;
  type: string;
  editData?: SelectedProductType;
}

const formikInitialValues = {
  purchasePrice: 0,
  retailPrice: 0,
  quantity: 0,
  discountType: '',
  discount: 0,
  vat: 0,
  // wholesalePrice: 0,
  manufactureDate: '',
  expireDate: '',
  variantId: '',
};

const validation = Yup.object({
  purchasePrice: Yup.string().required('Purchase Price is required'),
  retailPrice: Yup.string().required('Retails Price is required'),
  quantity: Yup.number().required('Quantity is required').min(1),
  variantId: Yup.string().required('Variant is required'),
});
function StockEntryModal({
  handleClose,
  productId,
  handleSave,
  type,
  editData,
}: Props) {
  const { data: productData, isLoading } = useGetProductDetailsQuery(
    productId,
    { skip: !productId },
  );

  const formik = useFormik({
    initialValues: formikInitialValues,
    validationSchema: validation,

    onSubmit: async (values) => {
      const addData = {
        ...values,
        productId: productData?.data?.id,
        productName: productData?.data?.name,
      };

      const updateData = {
        ...values,
        productId: editData?.productId,
        productName: editData?.productName,
      };
      handleSave(type === 'new' ? addData : updateData);
      handleClose();
      toast.success(
        type === 'new' ? 'Stock Added for entry' : 'Stock Edited Successfully',
      );
    },
  });
  useEffect(() => {
    if (productData && type === 'new') {
      formik.setFieldValue(
        'purchasePrice',
        productData?.data?.currentPurchasePrice,
      );
      formik.setFieldValue(
        'retailPrice',
        productData?.data?.currentSellingPrice,
      );
      formik.setFieldValue('discountType', productData?.data?.discountType);
      formik.setFieldValue('discount', productData?.data?.discount);
      formik.setFieldValue('vat', productData?.data?.vat);
      // formik.setFieldValue(
      //   'wholesalePrice',
      //   productData?.data?.currentWholesaleSellingPrice,
      // );
      if (productData?.data?.Variants?.length === 1) {
        formik.setFieldValue('variantId', productData?.data?.Variants[0]?.id);
      }
    }
  }, [productData, type]);

  useEffect(() => {
    if (editData && type === 'edit') {
      formik.setFieldValue('purchasePrice', editData?.purchasePrice);
      formik.setFieldValue('retailPrice', editData?.retailPrice);
      formik.setFieldValue('discountType', editData?.discountType);
      formik.setFieldValue('discount', editData?.discount);
      formik.setFieldValue('vat', editData?.vat);
      formik.setFieldValue('quantity', editData?.quantity);
      formik.setFieldValue('manufactureDate', editData?.manufactureDate);
      formik.setFieldValue('expireDate', editData?.expireDate);
      // formik.setFieldValue('wholesalePrice', editData?.wholesalePrice);
      formik.setFieldValue('variantId', editData?.variantId);
    }
  }, [productData, type]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="flex w-[450px] flex-col gap-4 rounded-xl bg-white p-4">
      <ModalTitle text="Stock Entry" handleClose={handleClose} />
      <form
        onSubmit={formik.handleSubmit}
        className="flex w-full flex-col gap-4"
      >
        <div className="flex items-center gap-2">
          <CustomInputField
            type="number"
            placeholder="Purchase Price"
            name="purchasePrice"
            label="Buying Price"
            formik={formik}
          />
          <CustomInputField
            type="number"
            placeholder="Enter Retail Price"
            name="retailPrice"
            label="Retail Price"
            formik={formik}
          />
          <div className="group relative z-0 mt-[-20px] w-full">
            <span className="relative left-3 top-2.5 w-auto bg-white px-1 font-mono text-[12px] font-bold text-gray-900 group-focus-within:text-red-600 dark:text-gray-300">
              Discount Price
            </span>
            <input
              disabled
              type="text"
              className="text-10 py-55-rem block h-10 w-full rounded-lg border bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
              placeholder="Price After Discount"
              value={
                formik.values.discountType === 'FIXED'
                  ? Number(formik.values.retailPrice) -
                    Number(formik.values.discount)
                  : Number(formik.values.retailPrice) -
                    (Number(formik.values.retailPrice) *
                      Number(formik.values.discount)) /
                      100
              }
            />
          </div>
        </div>
        <div className="flex items-center gap-2">
          <CustomDropdown
            placeholder="Select Product Variant"
            name="variantId"
            label="Select Variant"
            formik={formik}
            options={productData?.data?.Variants?.map(
              (variant: ProductVariant) => {
                return { label: variant.name, value: variant.id };
              },
            )}
          />
        </div>
        <div className="flex items-center gap-2">
          {/* <CustomInputField
            type="number"
            placeholder="Enter Wholesale price"
            name="wholesalePrice"
            label="Wholesale Price"
            formik={formik}
          /> */}

          <CustomInputField
            type="number"
            placeholder="Enter Quantity"
            name="quantity"
            label="Quantity"
            formik={formik}
          />
          <CustomInputField
            type="number"
            placeholder="Enter Vat percentage"
            name="vat"
            label="Vat(%)"
            formik={formik}
          />
        </div>
        <div className="flex items-center gap-2">
          <CustomDropdown
            placeholder="Select Discount Type"
            name="discountType"
            label="Discount Type"
            formik={formik}
            options={[
              { label: 'Percentage', value: 'PERCENTAGE' },
              { label: 'Fixed', value: 'FIXED' },
            ]}
          />
          <CustomInputField
            type="number"
            placeholder="Discount"
            name="discount"
            label="Discount"
            formik={formik}
          />
        </div>

        <div className="flex items-center gap-2">
          <CustomInputField
            type="date"
            placeholder="Manufacture Date"
            name="manufactureDate"
            label="Manufacture Date"
            formik={formik}
          />
          <CustomInputField
            type="date"
            placeholder="Expire Date"
            name="expireDate"
            label="Expire Date"
            formik={formik}
          />
        </div>
        <div className="mt-[10px] flex w-full items-center justify-center">
          <FilledSubmitButton text="Entry Stock" isLoading={false} />
        </div>
      </form>
    </div>
  );
}

export default StockEntryModal;
