import { useLocation, useNavigate } from 'react-router-dom';

import SupplierPaymentsList from './SupplierPaymentsList';

import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import Pagination from '@/components/reusable/Pagination/Pagination';
import SpinnerLoader from '@/components/reusable/SpinnerLoader/SpinnerLoader';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  useGetSupplierBillsListQuery,
  useGetSupplierDetailsQuery,
} from '@/redux/api/warehouseApis/suppliersApi';
import { ROUTES } from '@/Routes';
import { SupplierSingleBill } from '@/types/warehouseTypes/suppliersTypes';
import { generateFilterParams } from '@/utils/generateFilterParams';

interface Props {
  supplierId: string;
}

function SupplierDetailsPageOverview({ supplierId }: Props) {
  const navigate = useNavigate();
  const router = new URLSearchParams(useLocation().search);
  const page = router.get('page');
  const limit = router.get('limit');

  const { data, isLoading } = useGetSupplierDetailsQuery(supplierId);
  const { data: bills, isLoading: isBillsLoading } =
    useGetSupplierBillsListQuery(supplierId);

  const handleFilter = (fieldName: string, value: string) => {
    const query = generateFilterParams(fieldName, value);
    navigate(
      ROUTES.WAREHOUSE.SUPPLIER_DETAILS(
        data?.data.warehouseId,
        supplierId,
        query,
      ),
    );
  };

  return (
    <div>
      <div>
        <div className="mb-4 rounded-xl border border-gray-200 bg-white p-6 shadow-md transition-all duration-300 hover:shadow-lg">
          {!isLoading ? (
            <>
              <h2 className="mb-4 text-2xl font-semibold text-gray-800">
                Supplier Information
              </h2>
              <div className="grid gap-3 text-sm text-gray-700 md:grid-cols-2 lg:grid-cols-3">
                <div>
                  <span className="block text-gray-500">Name</span>
                  <span className="font-medium text-gray-800">
                    {data?.data?.User?.name}
                  </span>
                </div>
                <div>
                  <span className="block text-gray-500">Phone Number</span>
                  <span className="font-medium text-gray-800">
                    {data?.data?.User?.mobileNumber}
                  </span>
                </div>
                <div>
                  <span className="block text-gray-500">Username</span>
                  <span className="font-medium text-gray-800">
                    {data?.data?.User?.username}
                  </span>
                </div>
                <div>
                  <span className="block text-gray-500">Total Paid</span>
                  <span className="font-semibold text-green-600">
                    ৳ {data?.data?.totalPaid}
                  </span>
                </div>
                <div>
                  <span className="block text-gray-500">Total Due</span>
                  <span className="font-semibold text-red-500">
                    ৳ {data?.data?.totalDue}
                  </span>
                </div>
                <div>
                  <span className="block text-gray-500">Serial No</span>
                  <span className="font-medium text-gray-800">
                    #{data?.data?.serialNo}
                  </span>
                </div>
              </div>
            </>
          ) : (
            <SpinnerLoader />
          )}
        </div>
      </div>
      <div className="mt-6">
        <Tabs defaultValue="account" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="account">Purchase Invoices</TabsTrigger>
            <TabsTrigger value="password">Payment List</TabsTrigger>
          </TabsList>
          <TabsContent value="account">
            <div className="mb-20">
              <div>
                <div className="tableTop w-full">
                  <p>Purchase Invoice List</p>
                  <p>Total : {bills?.pagination.total}</p>
                </div>
                {!isBillsLoading ? (
                  <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
                    {bills?.data?.length ? (
                      <div className="full-table-box h-custom">
                        <table className="full-table">
                          <thead className="bg-gray-100">
                            <tr>
                              <th className="tableHead">No</th>
                              <th className="tableHead">Invoice No</th>
                              <th className="tableHead">Supplier Invoice No</th>
                              <th className="tableHead">Quantity</th>
                              <th className="tableHead">Total</th>
                              <th className="tableHead">Paid</th>
                              <th className="tableHead">Due</th>
                              <th className="tableHead">Created At</th>
                              {/* <th className="tableHead">Updated At</th> */}
                            </tr>
                          </thead>
                          <tbody className="divide-y bg-slate-200">
                            {bills?.data?.map(
                              (
                                singleBill: SupplierSingleBill,
                                index: number,
                              ) => (
                                <tr key={singleBill?.id}>
                                  <td className="tableData">{index + 1}</td>
                                  <td className="tableData">
                                    {singleBill?.serialNo ?? 0}
                                  </td>
                                  <td className="tableData">
                                    {singleBill?.supplierInvoiceNo}
                                  </td>
                                  <td className="tableData">
                                    {/* {singleBill?.supplierInvoiceNo} */}
                                    N/A
                                  </td>
                                  <td className="tableData">
                                    {singleBill?.totalPrice}
                                  </td>
                                  <td className="tableData">
                                    {singleBill?.totalPaid}
                                  </td>
                                  <td className="tableData">
                                    {singleBill?.totalDue}
                                  </td>
                                  <td className="tableData">
                                    <DateAndTimeViewer
                                      date={singleBill?.createdAt}
                                    />
                                  </td>
                                  {/* <td className="tableData">
                                    <DateAndTimeViewer
                                      date={singleBill?.updatedAt}
                                    />
                                  </td> */}
                                </tr>
                              ),
                            )}
                          </tbody>
                        </table>
                      </div>
                    ) : (
                      <div>
                        <h2>No product found</h2>
                      </div>
                    )}
                  </div>
                ) : (
                  <SpinnerLoader />
                )}
                <div className="pagination-box flex justify-end rounded bg-white p-3">
                  <Pagination
                    currentPage={page ?? '1'}
                    limit={Number(limit ?? 10)}
                    handleFilter={(fieldName: string, value: any) =>
                      handleFilter(fieldName, value)
                    }
                    totalCount={bills?.pagination?.total}
                    totalPages={Math.ceil(
                      Number(bills?.pagination?.total) /
                        Number(bills?.pagination?.limit),
                    )}
                  />
                </div>
              </div>
            </div>
          </TabsContent>
          <TabsContent value="password">
            <SupplierPaymentsList supplierId={supplierId} />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}

export default SupplierDetailsPageOverview;
