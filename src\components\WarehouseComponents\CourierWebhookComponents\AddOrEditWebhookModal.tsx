import { useFormik } from 'formik';
import Cookies from 'js-cookie';
import { useEffect } from 'react';
import { toast } from 'react-toastify';
import * as Yup from 'yup';

import FilledSubmitButton from '../../reusable/Buttons/FilledSubmitButton';
import ModalTitle from '../../reusable/Modal/ModalTitle';

import CustomDropdown from '@/components/reusable/CustomInputField/CustomDropdown';
import CustomInputField from '@/components/reusable/CustomInputField/CustomInputField';
import { useGetCouriersQuery } from '@/redux/api/warehouseApis/couriersApi';
import { useCreateWebhookMutation } from '@/redux/api/warehouseApis/webhookApis';
import { SingleShopDetails } from '@/types/shopTypes';
import { SingleCourierDetails } from '@/types/warehouseTypes/settingsTypes';

interface Props {
  type: string;
  handleClose: () => void;
  updateRefreshCounter: () => void;
  courierDetails?: SingleCourierDetails;
  shopList: SingleShopDetails[];
}

const formikInitialValues = {
  shopId: '',
  courierId: null,
  callBackUrl: '',
  event: '',
  type: '',
  domainName: '',
};

const steadFastValidation = Yup.object({
  shopId: Yup.string().required('Shop is required'),
});

function AddOrEditWebhookModal({
  type,
  handleClose,
  updateRefreshCounter,
  courierDetails,
  shopList,
}: Props) {
  const [createWebhook, { isLoading }] = useCreateWebhookMutation();
  const { data } = useGetCouriersQuery({
    organizationId: Cookies.get('organizationId'),
  });

  const formik = useFormik({
    initialValues: formikInitialValues,
    validationSchema: steadFastValidation,

    onSubmit: async (values) => {
      if (type === 'new') {
        toast.promise(
          createWebhook({
            shopId: values.shopId,
            courierId: values.type === 'COURIER' ? values.courierId : null,
            callBackUrl:
              values.type === 'CUSTOM'
                ? 'https://pos-staging-api.softs.ai/api/v1/webhook/listen/ecommerce'
                : `https://pos-staging-api.softs.ai/api/v1/webhook/listen/${values?.type?.toLowerCase()}`,
            event: values.event,
            type: values.type,
            domainName: values.domainName,
          }).unwrap(),
          {
            pending: 'Adding New Courier Webhook...',
            success: {
              render({ data: res }) {
                if (res?.statusCode === 200 || res?.statusCode === 201) {
                  updateRefreshCounter();
                  handleClose();
                }
                return 'Courier Webhook Added Successfully';
              },
            },
            error: {
              render({ data: error }) {
                console.log(error);
                return 'Error on creating courier webhook';
              },
            },
          },
        );
      }
    },
  });

  useEffect(() => {
    if (courierDetails && type === 'edit') {
      formik.setFieldValue('type', courierDetails?.type);
    }
  }, [type, courierDetails]);

  return (
    <div className="flex w-[400px] flex-col gap-4 rounded-xl bg-white p-4">
      <ModalTitle
        text={type === 'new' ? 'Add New Webhook' : 'Edit Webhook'}
        handleClose={handleClose}
      />
      <form
        onSubmit={formik.handleSubmit}
        className="flex w-full flex-col gap-4"
      >
        <CustomDropdown
          placeholder="Webhook Type"
          name="type"
          label="Select Type"
          formik={formik}
          options={[
            { value: 'COURIER', label: 'Courier Webhook' },
            { value: 'CUSTOM', label: 'Custom Website' },
            { value: 'SHOPIFY', label: 'Shopify Website' },
            { value: 'WOOCOMMERCE', label: 'Woocomerce Website' },
          ]}
        />
        {formik.values.type === 'WEBSITE' ? (
          <CustomInputField
            type="text"
            placeholder="Enter Webhook URL"
            name="callBackUrl"
            label="Webhook URL"
            formik={formik}
          />
        ) : (
          ''
        )}
        <CustomDropdown
          placeholder="Select Shop"
          name="shopId"
          label="Select Shop"
          formik={formik}
          options={
            shopList?.length
              ? shopList
                  ?.filter((sin: SingleShopDetails) => sin.type === 'ONLINE')
                  ?.map((single: SingleShopDetails) => {
                    return {
                      value: single.id,
                      label: `${single.name} (${single.nickName})`,
                    };
                  })
              : []
          }
        />
        {formik.values.type === 'COURIER' ? (
          <CustomDropdown
            placeholder="Select Courier"
            name="courierId"
            label="Courier"
            formik={formik}
            options={data?.data?.map((district: SingleCourierDetails) => {
              return {
                value: district.id,
                label: district.nickName,
              };
            })}
          />
        ) : (
          ''
        )}
        <CustomDropdown
          placeholder="Select Event"
          name="event"
          label="Event"
          formik={formik}
          options={
            formik.values.type === 'WEBSITE'
              ? [
                  {
                    value: 'ORDER_STATUS_UPDATE',
                    label: 'Order Status Updated',
                  },
                ]
              : formik.values.type === 'SHOPIFY' ||
                  formik.values.type === 'CUSTOM' ||
                  formik.values.type === 'WOOCOMMERCE'
                ? [{ value: 'ORDER_CREATE', label: 'Order Create' }]
                : [
                    { value: 'PATHAO', label: 'Pathao Courier' },
                    { value: 'STEADFAST', label: 'Steadfast' },
                  ]
          }
        />
        {formik.values.type !== 'COURIER' ? (
          <CustomInputField
            type="text"
            placeholder={
              formik.values.type === 'SHOPIFY'
                ? 'abcd.myshopify.com'
                : 'your-domain.com'
            }
            name="domainName"
            label="Domain Name"
            formik={formik}
          />
        ) : (
          ''
        )}
        <div className="mt-[10px] flex w-full items-center justify-center">
          <FilledSubmitButton
            text={type === 'new' ? 'Add Webhook' : 'Update Webhook'}
            isLoading={isLoading}
          />
        </div>
      </form>
    </div>
  );
}

export default AddOrEditWebhookModal;
