import { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

import StockTransferModal from './StockTransferModal';

import { DeleteButton } from '@/components/reusable/Buttons/CommonButtons';
import FilledButton from '@/components/reusable/Buttons/FilledButton';
import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import Modal from '@/components/reusable/Modal/Modal';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import { useGetShopsQuery } from '@/redux/api/shopApi';
import { useGetSingleStockByBarcodeQuery } from '@/redux/api/warehouseApis/stockApis';
import { ROUTES } from '@/Routes';
import { SingleStockDetails } from '@/types/warehouseTypes/stockTypes';
import { CalculateDiscountPrice } from '@/utils/CalculateDiscountPrice';
import StockName from '@/utils/StockName';

interface Props {
  warehouseId: string;
}
function StockTransferByScanPageOverview({ warehouseId }: Props) {
  const navigate = useNavigate();
  const router = new URLSearchParams(useLocation().search);
  const barcode = router.get('barcode');

  const { data, isLoading, refetch } = useGetSingleStockByBarcodeQuery(
    {
      warehouseId,
      barcode: barcode ?? undefined,
    },
    { skip: !barcode },
  );
  const { data: shopList } = useGetShopsQuery({
    warehouseId,
    isFromStockTransfer: true,
  });
  const [selectedStocks, setSelectedStocks] = useState<SingleStockDetails[]>();
  const [isStockTransferModalOpen, setIsStockTransferModalOpen] =
    useState(false);

  const handleSelectAndUnselect = (stock: SingleStockDetails) => {
    if (selectedStocks?.length) {
      const isExist = selectedStocks.some(
        (single: SingleStockDetails) => single.id === stock?.id,
      );
      if (isExist) {
        toast.error(`${stock?.barcode} barcode is Already Added`);
      } else {
        setSelectedStocks([...selectedStocks, stock]);
        toast.success(`${stock?.barcode} barcode added to list`);
      }
    } else {
      setSelectedStocks([stock]);
      toast.success(`${stock?.barcode} barcode added to list`);
    }
  };
  useEffect(() => {
    if (data?.data) {
      if (data?.data?.isSold || data?.data?.assignedShopId) {
        toast.error(`${data?.data?.barcode} is not available on warehouse`);
      } else {
        handleSelectAndUnselect(data?.data);
      }
    }
  }, [data]);

  const handleFilter = (fieldName: string, value: string) => {
    let query = '';
    if (fieldName === 'barcode') {
      query = `barcode=${value}`;
    }
    navigate(ROUTES.STOCK.TRANSFER_SCAN(warehouseId, query));
  };

  useEffect(() => {
    let scannedBarcode = '';
    let timeout: NodeJS.Timeout;

    const handleBarcodeInput = (e: KeyboardEvent) => {
      if (timeout) clearTimeout(timeout);

      if (e.key === 'Enter') {
        if (scannedBarcode) {
          handleFilter('barcode', scannedBarcode);
        }
        scannedBarcode = '';
      } else {
        scannedBarcode += e.key;
        timeout = setTimeout(() => {
          scannedBarcode = '';
        }, 200);
      }
    };

    window.addEventListener('keydown', handleBarcodeInput);
    return () => {
      window.removeEventListener('keydown', handleBarcodeInput);
    };
  }, []);

  return (
    <div>
      <div className="tableTop w-full">
        <p>Selected Stock List</p>
        <div className="flex items-center gap-4">
          <p>Total : {selectedStocks?.length}</p>

          <FilledButton
            text="Transfer"
            isLoading={false}
            handleClick={() => setIsStockTransferModalOpen(true)}
            isDisabled={!selectedStocks?.length}
          />
        </div>
      </div>
      {!isLoading ? (
        <div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {selectedStocks?.length ? (
              <div>
                <div className="full-table-box h-custom">
                  <table className="full-table">
                    <thead className="bg-gray-100">
                      <tr>
                        <th className="tableHead">No</th>

                        <th className="tableHead">Barcode</th>
                        <th className="tableHead">Product Id</th>
                        <th className="tableHead table-col-width">Name</th>
                        <th className="tableHead">Stock Location</th>
                        <th className="tableHead">TP</th>
                        <th className="tableHead">Regular Price</th>
                        <th className="tableHead">Discount Price</th>
                        <th className="tableHead">Status</th>
                        <th className="tableHead">Supplier</th>
                        <th className="tableHead">Created At</th>
                        <th className="tableHead">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y bg-slate-200">
                      {selectedStocks?.map(
                        (stock: SingleStockDetails, index: number) => (
                          <tr
                            key={stock?.id}
                            /* className={
                              selectedStocks?.includes(stock?.id)
                                ? 'tableRowYellow'
                                : 'tableRowGreen'
                            } */
                          >
                            <td className="tableData">{index + 1}</td>
                            <td className="tableData">{stock?.barcode}</td>
                            <td className="tableData">
                              {stock?.Product?.serialNo ?? 0}
                            </td>
                            <td className="tableData table-col-width">
                              <StockName stock={stock} name={stock.name} />
                            </td>
                            <td className="tableData">
                              {stock?.AssignedShop?.name ?? 'Warehouse'}
                            </td>
                            <td className="tableData">
                              {stock?.purchasePrice}
                            </td>
                            <td className="tableData">{stock?.retailPrice}</td>
                            <td className="tableData">
                              {CalculateDiscountPrice({
                                retailPrice: stock?.retailPrice,
                                discountType: stock.discountType,
                                discount: stock.discount,
                              })}
                            </td>
                            <td className="tableData">
                              {stock?.isSold ? 'Sold' : 'Available'}
                            </td>
                            <td className="tableData">
                              {stock?.Supplier?.User?.name}
                            </td>
                            <td className="tableData">
                              <DateAndTimeViewer date={stock?.createdAt} />
                            </td>
                            <td className="tableData">
                              <div className="flex items-center justify-center gap-2">
                                <DeleteButton
                                  handleClick={() =>
                                    handleSelectAndUnselect(stock)
                                  }
                                />
                              </div>
                            </td>
                          </tr>
                        ),
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            ) : (
              <NoResultFound pageType="stock" />
            )}
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={12} tableRow={6} />
      )}
      <Modal
        setShowModal={setIsStockTransferModalOpen}
        showModal={isStockTransferModalOpen}
      >
        <StockTransferModal
          shopList={shopList?.data ?? []}
          selectedStocks={selectedStocks?.map(
            (single: SingleStockDetails) => single.id,
          )}
          handleClose={() => setIsStockTransferModalOpen(false)}
          updateRefreshCounter={() => {
            setSelectedStocks([]);
            refetch();
          }}
          warehouseId={warehouseId}
        />
      </Modal>
    </div>
  );
}

export default StockTransferByScanPageOverview;
