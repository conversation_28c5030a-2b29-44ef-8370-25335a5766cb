import BaseApi from '../baseApi';

import { Pagination } from '@/redux/commonTypes';

interface GetWebhooksParams {
  warehouseId?: string;
  shopId?: string;
  limit?: string;
  page?: string;
}

const WebhookApis = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getWarehouseWebhooks: builder.query<
      GetWebhooksListResponse,
      GetWebhooksParams
    >({
      query: (params) => ({
        url: '/warehouse/webhooks',
        method: 'GET',
        params,
      }),
      // providesTags: [TagTypes.BRAND],
    }),
    getWebhooks: builder.query<GetWebhooksListResponse, GetWebhooksParams>({
      query: (params) => ({
        url: '/shop/webhooks',
        method: 'GET',
        params,
      }),
      // providesTags: [TagTypes.BRAND],
    }),
    createWebhook: builder.mutation({
      query: (data) => ({
        url: '/shop/webhook/new',
        method: 'POST',
        data,
      }),
      // invalidatesTags: [TagTypes.BRAND],
    }),
  }),
});

export const {
  useGetWebhooksQuery,
  useGetWarehouseWebhooksQuery,
  useCreateWebhookMutation,
} = WebhookApis;

export interface GetWebhooksListResponse {
  success: boolean;
  message: string;
  statusCode: number;
  pagination: Pagination;
  data: SingleWebhookDetails[];
}

export interface SingleWebhookDetails {
  id: string;
  createdAt: string;
  updatedAt: string;
  callBackUrl: string;
  type: string;
  event: string;
  secret: string;
  accessToken: string;
  shopId: string;
  courierId: string;
  WebhookLog: any[];
  Courier: Courier;
  Shop: Shop;
}

export interface Courier {
  id: string;
  nickName: string;
}

export interface Shop {
  id: string;
  name: string;
}
