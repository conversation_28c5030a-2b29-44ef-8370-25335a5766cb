import { useState } from 'react';

interface Props {
  previousImage: string;
  setNewImage: (e: any) => void;
}

function ImageSelector({ previousImage, setNewImage }: Props) {
  const [image, setImage] = useState<any>();
  const baseUrl = 'https://retail-pluse-upload.s3.ap-southeast-1.amazonaws.com';

  return (
    <div style={{ position: 'relative' }}>
      {image ? (
        <img
          src={URL?.createObjectURL(image)}
          alt="Profile"
          className="relative h-[100px] w-[100px] rounded border border-slate-400"
        />
      ) : previousImage ? (
        <img
          src={`${baseUrl}/${previousImage}`}
          alt="Profile"
          className="relative h-[100px] w-[100px] rounded border border-slate-400"
        />
      ) : (
        <img
          src="https://cdn0.iconfinder.com/data/icons/set-app-incredibles/24/Image-01-64.png"
          alt="Profile"
          className="relative h-[100px] w-[100px] rounded border border-slate-400"
        />
      )}

      <form>
        <label htmlFor="imageInput">
          <input
            id="imageInput"
            type="file"
            accept="image/*"
            onChange={(e) => {
              setNewImage(e?.target?.files ? e?.target?.files[0] : null);
              setImage(e?.target?.files ? e.target.files[0] : null);
            }}
            style={{
              display: 'none',
            }}
            multiple={false}
          />
          <img
            src="https://cdn-icons-png.flaticon.com/512/7398/7398464.png"
            alt="edit icon"
            className="absolute bottom-[2px] right-[2px] h-[25px] w-[25px] cursor-pointer rounded bg-white"
          />
        </label>
      </form>
    </div>
  );
}

export default ImageSelector;
