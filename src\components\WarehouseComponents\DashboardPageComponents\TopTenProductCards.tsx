import Cookies from 'js-cookie';

import ImageViewer from '@/components/reusable/ImageViewer/ImageViewer';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import { useGetTopTenProductListQuery } from '@/redux/api/warehouseApis/dashboardApis';
import { SingleItemOfTopTenProduct } from '@/types/warehouseTypes/dashboardTypes';

interface Props {
  type: 'warehouse' | 'shop';
  warehouseId?: string;
  shopId?: string;
}

function TopTenProductCards({ warehouseId, type, shopId }: Props) {
  const { data, isLoading } = useGetTopTenProductListQuery({
    organizationId: Cookies.get('organizationId') ?? '',
    warehouseId: type === 'warehouse' ? warehouseId ?? undefined : undefined,
    shopId: type === 'shop' ? shopId ?? undefined : undefined,
  });
  return (
    <div>
      <div className="tableTop w-full">
        <p>Top Ten Selling Items</p>
        <p>Total : {data?.data?.length}</p>
      </div>
      {!isLoading ? (
        <div className="w-full">
          {data?.data?.length ? (
            <div className="full-table-box h-auto">
              <table className="full-table">
                <thead className="bg-gray-100">
                  <tr>
                    <th className="tableHead">No</th>
                    <th className="tableHead">Image</th>
                    <th className="tableHead table-col-width">Product Name</th>
                    <th className="tableHead">Total Sold</th>
                  </tr>
                </thead>
                <tbody className="divide-y bg-slate-200">
                  {data?.data?.map(
                    (product: SingleItemOfTopTenProduct, index: number) => (
                      <tr key={product?.name}>
                        <td className="tableData">{index + 1}</td>
                        <td className="tableData">
                          <ImageViewer imageUrl={product.imgUrl} />
                        </td>
                        <td className="tableData table-col-width">
                          {product?.name}
                        </td>
                        <td className="tableData">{product?.totalSold}</td>
                      </tr>
                    ),
                  )}
                </tbody>
              </table>
            </div>
          ) : (
            <div>
              <h2>No product found</h2>
            </div>
          )}
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={4} tableRow={10} />
      )}
    </div>
  );
}
export default TopTenProductCards;
