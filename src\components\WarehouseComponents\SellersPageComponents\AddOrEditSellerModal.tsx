import { useFormik } from 'formik';
import { useEffect } from 'react';
import { toast } from 'react-toastify';
import * as Yup from 'yup';

import FilledSubmitButton from '../../reusable/Buttons/FilledSubmitButton';
import CustomDropdown from '../../reusable/CustomInputField/CustomDropdown';
import CustomInputField from '../../reusable/CustomInputField/CustomInputField';
import ModalTitle from '../../reusable/Modal/ModalTitle';

import {
  useCreateSellerMutation,
  useUpdateSellerMutation,
} from '@/redux/api/warehouseApis/sellersApis';
import { SingleShopDetails } from '@/types/shopTypes';
import { SingleSellerDetails } from '@/types/warehouseTypes/sellersTypes';

interface Props {
  type: string;
  warehouseId: string;
  handleClose: () => void;
  shopList: SingleShopDetails[];
  sellerData?: SingleSellerDetails;
}

const formikInitialValues = {
  name: '',
  mobileNumber: '',
  assignedShopId: '',
};

const validation = Yup.object({
  name: Yup.string().required('Seller name is required'),
  mobileNumber: Yup.string().required('Phone Number is required'),
});
function AddOrEditSellerModal({
  type,
  warehouseId,
  handleClose,
  shopList,
  sellerData,
}: Props) {
  const [createSeller, { isLoading }] = useCreateSellerMutation();
  const [updateSeller, { isLoading: isSellerUpdating }] =
    useUpdateSellerMutation();

  const formik = useFormik({
    initialValues: formikInitialValues,
    validationSchema: validation,

    onSubmit: async (values) => {
      const data = {
        warehouseId,
        ...values,
        imgUrl:
          'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcS3RCcez38sduC9iEjWm9nsq0mVyD5IAJGDog&s',
      };
      if (type === 'new') {
        toast.promise(createSeller(data).unwrap(), {
          pending: 'Creating New Seller...',
          success: {
            render({ data: res }) {
              if (res?.statusCode === 200 || res?.statusCode === 201) {
                handleClose();
              }
              return 'Seller created Successfully';
            },
          },
          error: {
            render({ data: error }) {
              console.log(error);
              return 'Error on creating Seller';
            },
          },
        });
      } else {
        toast.promise(
          updateSeller({
            data,
            id: sellerData?.id,
          }).unwrap(),
          {
            pending: 'Updating Seller...',
            success: {
              render({ data: res }) {
                if (res?.statusCode === 200 || res?.statusCode === 201) {
                  handleClose();
                }
                return 'Seller updated Successfully';
              },
            },
            error: {
              render({ data: error }) {
                console.log(error);
                return 'Error on updating Seller';
              },
            },
          },
        );
      }
    },
  });

  useEffect(() => {
    if (type === 'edit' && sellerData) {
      formik.setFieldValue('name', sellerData.name);
      formik.setFieldValue('mobileNumber', sellerData.mobileNumber);
      formik.setFieldValue('assignedShopId', sellerData.assignedShopId);
    }
  }, [sellerData, type]);

  return (
    <div className="flex w-[340px] flex-col gap-4 rounded-xl bg-white p-4 md:w-[400px]">
      <ModalTitle
        text={type === 'new' ? 'Add Seller modal' : 'Edit Seller modal'}
        handleClose={handleClose}
      />
      <form
        onSubmit={formik.handleSubmit}
        className="flex w-full flex-col gap-4"
      >
        <div>
          <CustomInputField
            type="text"
            placeholder="Enter Seller Name"
            name="name"
            label="Name"
            formik={formik}
          />
        </div>
        <div>
          <CustomInputField
            type="text"
            placeholder="Enter Seller Phone Number"
            name="mobileNumber"
            label="Phone Number"
            formik={formik}
          />
        </div>
        <div className="flex items-center gap-2">
          <CustomDropdown
            placeholder="Select Shop"
            name="assignedShopId"
            label="Shop"
            formik={formik}
            options={
              shopList?.length
                ? shopList?.map((single: SingleShopDetails) => {
                    return {
                      value: single.id,
                      label: single.name,
                    };
                  })
                : []
            }
          />
        </div>
        <div className="mt-[10px] flex w-full items-center justify-center">
          <FilledSubmitButton
            isLoading={isLoading || isSellerUpdating}
            text={type === 'new' ? 'Add Seller' : 'Done Editing'}
          />
        </div>
      </form>
    </div>
  );
}

export default AddOrEditSellerModal;
