import { CircleDollarSign, DollarSign, Minus, Plus } from 'lucide-react';

// import { Progress } from '@/components/ui/progress';
import { GetDashboardUpdatesResponse } from '@/types/warehouseTypes/dashboardTypes';
import ProtectedDataViewer from '@/utils/ProtectedDataViewer';

interface Props {
  dashboardUpdates?: GetDashboardUpdatesResponse;
}

function DashboardUpdatesSection({ dashboardUpdates }: Props) {
  return (
    <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-5 xl:grid-cols-5">
      <div className="h-full rounded-lg bg-[#FF676C] px-2 py-3 2xl:px-4 2xl:py-5">
        <div className="card-box">
          <div className="mb-4 flex items-center justify-between">
            <h3 className="text-xl font-semibold uppercase text-white lg:text-lg 2xl:text-lg">
              earning
            </h3>
            <button type="button" className="rounded bg-white">
              <CircleDollarSign className="text-[#FF676C]" size={30} />
            </button>
          </div>
          <div className="progress-bar mt-3 lg:mt-5">
            <div className="mb-3 flex justify-end">
              <p className="text-xl font-semibold text-white">
                <span>{dashboardUpdates?.data?.totalPaidAmount || 0}</span> TK
              </p>
            </div>
            {/* <Progress value={0} />
            <div className="mt-2 flex justify-start">
              <p className="text-sm font-medium text-white">
                <span>0</span>% compared to last day
              </p>
            </div> */}
          </div>
        </div>
      </div>
      <div className="h-full rounded-lg bg-[#f3953d] px-2 py-3 2xl:px-4 2xl:py-5">
        <div className="card-box">
          <div className="mb-4 flex items-center justify-between">
            <h3 className="text-xl font-semibold uppercase text-white lg:text-lg 2xl:text-lg">
              stock received
            </h3>
            <div className="action flex items-center">
              <button type="button" className="rounded bg-white">
                <Plus className="text-[#00BB4B]" size={30} />
              </button>
            </div>
          </div>
          <div className="progress-bar mt-3 lg:mt-5">
            <div className="mb-3 flex justify-end">
              <p className="text-xl font-semibold text-white">
                <span>
                  {dashboardUpdates?.data?.totalStockEntryAmount || 0}
                </span>{' '}
                TK
              </p>
            </div>
            {/* <Progress value={0} />
            <div className="mt-2 flex justify-start">
              <p className="text-sm font-medium text-white">
                <span>0</span>% compared to last day
              </p>
            </div> */}
          </div>
        </div>
      </div>
      <div className="h-full rounded-lg bg-[#F1BC00] px-2 py-3 2xl:px-4 2xl:py-5">
        <div className="card-box">
          <div className="mb-4 flex items-center justify-between">
            <h3 className="text-xl font-semibold uppercase text-white lg:text-lg 2xl:text-lg">
              stock sold
            </h3>
            <div className="action flex items-center">
              <button type="button" className="rounded bg-white">
                <Minus className="text-[#F1BC00]" size={30} />
              </button>
            </div>
          </div>
          <div className="progress-bar mt-3 lg:mt-5">
            <div className="mb-3 flex justify-end">
              <p className="text-xl font-semibold text-white">
                <span>{dashboardUpdates?.data?.totalWholesalePrice || 0}</span>{' '}
                TK
              </p>
            </div>
            {/* <Progress value={0} />
            <div className="mt-2 flex justify-start">
              <p className="text-sm font-medium text-white">
                <span>0</span>% compared to last day
              </p>
            </div> */}
          </div>
        </div>
      </div>
      <div className="h-full rounded-lg bg-[#00B3EB] px-2 py-3 2xl:px-4 2xl:py-5">
        <div className="card-box">
          <div className="mb-4 flex items-center justify-between">
            <h3 className="text-xl font-semibold uppercase text-white lg:text-lg 2xl:text-lg">
              stock pulled
            </h3>
            <div className="action flex items-center">
              <button type="button" className="rounded bg-white">
                <Minus className="text-[#00B3EB]" size={30} />
              </button>
            </div>
          </div>
          <div className="progress-bar mt-3 lg:mt-5">
            <div className="mb-3 flex justify-end">
              <p className="text-xl font-semibold text-white">
                <span>{dashboardUpdates?.data?.totalStockPull}</span>
              </p>
            </div>
            {/* <Progress value={0} />
            <div className="mt-2 flex justify-start">
              <p className="text-sm font-medium text-white">
                <span>0</span>% compared to last day
              </p>
            </div> */}
          </div>
        </div>
      </div>
      <div className="h-full rounded-lg bg-[#00BB4B] px-2 py-3 2xl:px-4 2xl:py-5">
        <div className="card-box">
          <div className="mb-4 flex items-center justify-between">
            <h3 className="text-xl font-semibold uppercase text-white lg:text-lg 2xl:text-lg">
              Est. Profit
            </h3>
            <div className="action flex items-center">
              <button type="button" className="rounded bg-white">
                <DollarSign className="text-[#00B3EB]" size={30} />
              </button>
            </div>
          </div>
          <div className="progress-bar mt-3 lg:mt-5">
            <div className="mb-3 flex justify-end">
              <p className="text-xl font-semibold text-white">
                <ProtectedDataViewer>
                  <span>{dashboardUpdates?.data?.totalProfit}</span>
                </ProtectedDataViewer>{' '}
                TK
              </p>
            </div>
            {/* <Progress value={0} />
            <div className="mt-2 flex justify-start">
              <p className="text-sm font-medium text-white">
                <span>0</span>% compared to last day
              </p>
            </div> */}
          </div>
        </div>
      </div>
    </div>
  );
}

export default DashboardUpdatesSection;
