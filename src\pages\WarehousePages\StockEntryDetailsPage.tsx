import { useParams } from 'react-router-dom';

import StockEntryDetailsPageOverview from '@/components/WarehouseComponents/StockEntryListPageComponents/StockEntryDetailsPageOverview';

function StockEntryDetailsPage() {
  const { entryId, warehouseId } = useParams();
  return (
    <div>
      <StockEntryDetailsPageOverview
        entryId={entryId ?? ''}
        warehouseId={warehouseId ?? ''}
      />
    </div>
  );
}

export default StockEntryDetailsPage;
