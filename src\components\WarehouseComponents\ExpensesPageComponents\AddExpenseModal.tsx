import { useFormik } from 'formik';
import { toast } from 'react-toastify';
import * as Yup from 'yup';

import FilledSubmitButton from '../../reusable/Buttons/FilledSubmitButton';
import CustomInputField from '../../reusable/CustomInputField/CustomInputField';
import ModalTitle from '../../reusable/Modal/ModalTitle';

import CustomDropdown from '@/components/reusable/CustomInputField/CustomDropdown';
import { useAddExpenseMutation } from '@/redux/api/warehouseApis/expensesApis';

interface Option {
  value: number | string;
  label: string;
}

interface Props {
  warehouseId: string | undefined;
  handleClose: () => void;
  updateRefreshCounter: () => void;
  categories: Option[];
}

const formikInitialValues = {
  name: '',
  amount: 0,
  expenseCategoryId: '',
};

const validation = Yup.object({
  name: Yup.string().required('Expense name is required'),
  amount: Yup.string().required('Expense Amount is required'),
});

function AddExpenseModal({
  warehouseId,
  handleClose,
  updateRefreshCounter,
  categories,
}: Props) {
  const [addShopExpense, { isLoading }] = useAddExpenseMutation();

  const formik = useFormik({
    initialValues: formikInitialValues,
    validationSchema: validation,

    onSubmit: async (values) => {
      toast.promise(
        addShopExpense({
          ...values,
          warehouseId,
        }).unwrap(),
        {
          pending: 'Creating New Expense...',
          success: {
            render({ data: res }) {
              if (res?.statusCode === 200 || res?.statusCode === 201) {
                updateRefreshCounter();
                handleClose();
              }
              return 'Expense created Successfully';
            },
          },
          error: {
            render({ data: error }) {
              console.log(error);
              return 'Error on creating Expense';
            },
          },
        },
      );
    },
  });

  return (
    <div className="flex w-[400px] flex-col gap-4 rounded-xl bg-white p-4">
      <ModalTitle text="Add Expense" handleClose={handleClose} />
      <form
        onSubmit={formik.handleSubmit}
        className="flex w-full flex-col gap-2"
      >
        <CustomDropdown
          placeholder="Select Expense Category"
          name="expenseCategoryId"
          label="Expense Category"
          formik={formik}
          options={categories}
        />
        <CustomInputField
          type="text"
          placeholder="Enter Expense Name"
          name="name"
          label="Expense Note"
          formik={formik}
        />
        <CustomInputField
          type="number"
          placeholder="Enter Expense Amount"
          name="amount"
          label="Amount"
          formik={formik}
        />
        <div className="mt-[10px] flex w-full items-center justify-center">
          <FilledSubmitButton
            text="Add Expense"
            isLoading={isLoading}
            isDisabled={isLoading || formik.values.amount <= 0}
          />
        </div>
      </form>
    </div>
  );
}

export default AddExpenseModal;
