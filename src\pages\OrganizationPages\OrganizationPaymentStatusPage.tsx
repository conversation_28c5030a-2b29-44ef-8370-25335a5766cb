import OrganizationPaymentStatusPageOverview from '@/components/OrganizationsComponents/OrganizationPaymentStatusPageComponents/OrganizationPaymentStatusPageOverview';

interface Props {
  type: 'SUCCESS' | 'FAILED' | 'CANCELLED';
}
function OrganizationPaymentStatusPage({ type }: Props) {
  return <OrganizationPaymentStatusPageOverview type={type} />;
}

export default OrganizationPaymentStatusPage;
