import { useParams } from 'react-router-dom';

import CustomersPageOverview from '@/components/WarehouseComponents/CustomerPageComponents/CustomersPageOverview';
import { ProtectedRoute } from '@/utils/ProtectedRoutes';

function CustomerPage() {
  const { warehouseId } = useParams();
  return (
    <ProtectedRoute>
      <CustomersPageOverview warehouseId={warehouseId ?? ''} />
    </ProtectedRoute>
  );
}

export default CustomerPage;
