import BaseApi from '../baseApi';

import { TagTypes } from '@/redux/tag-types';
import {
  GetShopOrdersListResponse,
  ShopSingleOrderDetailsResponse,
} from '@/types/shopTypes/shopOrderTypes';

interface GetShopBrandParams {
  warehouseId?: string;
  shopId?: string;
  isBooked?: boolean;
  serialNo?: string;
  customerName?: string;
  customerId?: string;
  page?: string;
  limit?: string;
  mobileNumber?: string;
  orderStatus?: string;
  startDate?: string;
  endDate?: string;
  bulkBookingStatus?: string;
  type?: string;
  paymentStatus?: string;
  invoiceNo?: string;
  hasDue?: boolean;
}

const ShopOrdersApi = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getShopOrders: builder.query<GetShopOrdersListResponse, GetShopBrandParams>(
      {
        query: (params) => ({
          url: '/order',
          method: 'GET',
          params,
        }),
        providesTags: [TagTypes.SHOP_ORDERS],
      },
    ),
    getShopSingleOrderDetails: builder.query<
      ShopSingleOrderDetailsResponse,
      any
    >({
      query: (id) => ({
        url: `/order/${id}`,
        method: 'GET',
      }),
      providesTags: [TagTypes.SHOP_ORDERS],
    }),
    createShopOrder: builder.mutation<ShopSingleOrderDetailsResponse, any>({
      query: (data) => ({
        url: '/order/new',
        method: 'POST',
        data,
      }),
      invalidatesTags: [
        TagTypes.SHOP_ORDERS,
        TagTypes.DASHBOARD,
        // TagTypes.SHOP_CUSTOMERS,
        TagTypes.STATUS_WISE_REPORT,
        TagTypes.STOCK,
      ],
    }),
    bookOrderOnSteadfast: builder.mutation({
      query: (data) => ({
        url: '/order/courier-steadfast/new',
        method: 'POST',
        data,
      }),
      invalidatesTags: [TagTypes.SHOP_ORDERS],
    }),
    bookBulkOrderOnSteadfast: builder.mutation({
      query: (data) => ({
        url: '/order/courier-steadfast/bulk',
        method: 'POST',
        data,
      }),
      invalidatesTags: [TagTypes.SHOP_ORDERS],
    }),
    bookOrderOnPathao: builder.mutation({
      query: (data) => ({
        url: '/order/courier-pathao/new',
        method: 'POST',
        data,
      }),
      invalidatesTags: [TagTypes.SHOP_ORDERS],
    }),
    bookBulkOrderOnPathao: builder.mutation({
      query: (data) => ({
        url: '/order/courier-pathao/bulk',
        method: 'POST',
        data,
      }),
      invalidatesTags: [TagTypes.SHOP_ORDERS],
    }),
    returnOrderItems: builder.mutation({
      query: (data) => ({
        url: '/return/new',
        method: 'POST',
        data,
      }),
      invalidatesTags: [TagTypes.SHOP_ORDERS],
    }),
    cancelOrder: builder.mutation({
      query: ({ id, data }) => ({
        url: `/order/cancel/${id}`,
        method: 'PATCH',
        data,
      }),
      invalidatesTags: [TagTypes.SHOP_ORDERS],
    }),
    addOrderNote: builder.mutation({
      query: (data) => ({
        url: '/order/note/new',
        method: 'POST',
        data,
      }),
      invalidatesTags: [TagTypes.SHOP_ORDERS],
    }),
    updateSingleOrder: builder.mutation({
      query: ({ orderId, data }) => ({
        url: `/order/${orderId}`,
        method: 'PUT',
        data,
      }),
      invalidatesTags: [TagTypes.SHOP_ORDERS],
    }),
    updateSingleOrderAddress: builder.mutation({
      query: ({ orderId, data }) => ({
        url: `/order/address/${orderId}`,
        method: 'PATCH',
        data,
      }),
      invalidatesTags: [TagTypes.SHOP_ORDERS],
    }),
  }),
});

export const {
  useGetShopOrdersQuery,
  useGetShopSingleOrderDetailsQuery,
  useCreateShopOrderMutation,
  useBookOrderOnSteadfastMutation,
  useBookBulkOrderOnSteadfastMutation,
  useBookOrderOnPathaoMutation,
  useBookBulkOrderOnPathaoMutation,
  useReturnOrderItemsMutation,
  useCancelOrderMutation,
  useAddOrderNoteMutation,
  useUpdateSingleOrderMutation,
  useUpdateSingleOrderAddressMutation,
} = ShopOrdersApi;
