import { SingleShopDetails } from '../shopTypes';

import { ShopExpenseDetails, Warehouse } from './shopExpensesTypes';
import { OrderSinglePaymentDetails, ShopOrderDetails } from './shopOrderTypes';

import { CreatedBy } from '@/redux/commonTypes';

export interface GetShopStockTransferReportResponse {
  success: boolean;
  message: string;
  data: {
    result: ShopStockTransferDetails[];
    warehouse?: Warehouse;
    shop?: SingleShopDetails;
  };
}

export interface GetShopStockDeleteReportResponse {
  success: boolean;
  message: string;
  statusCode: number;
  data: {
    result: SingleStockDeleteDetails[];
    warehouse: Warehouse;
  };
}

export interface ShopStockTransferDetails {
  id: string;
  createdAt: string;
  date: string;
  updatedAt: string;
  stockIds: string[];
  shopId: string;
  warehouseId: string;
  onHold: boolean;
  createdById: string;
  CreatedBy: CreatedBy2;
  totalRetailPrice: number;
  status: string;
  Shop: {
    name: string;
    nickName: string;
  };
  products: TransferredProduct[];
}

export interface CreatedBy2 {
  id: string;
  name: string;
}

export interface TransferredProduct {
  id: string;
  name: string;
  total: number;
  totalPurchasePrice: number;
  totalRetailPrice: number;
}

export interface ShopExpensesReportResponse {
  success: boolean;
  message: string;
  data: { result: ShopExpenseDetails[]; shop: SingleShopDetails };
}

export interface ShopStockReportResponse {
  success: boolean;
  message: string;
  data: {
    result: SingleProductStockReport[];
    totalPurchasePrice: number;
    totalQuantity: number;
    totalRetailPrice: number;
    warehouse?: Warehouse;
  };
}

export interface SingleProductStockReport {
  purchasePrice: number;
  retailPrice: number;
  quantity: number;
  id: string;
  name: string;
}

export interface GetShopSalesReportResponse {
  success: boolean;
  message: string;
  data: {
    cashReceive: number;
    totalDue: number;
    totalOrderAmount: number;
    orderList: ShopOrderDetails[];
  };
}

export interface GetShopAccountsSummeryResponse {
  success: boolean;
  message: string;
  data: {
    cashReceive: number;
    totalSubtotal: number;
    totalDeliveryCharge: number;
    totalDiscount: number;
    totalPurchasePrice: number;
    totalVat: number;
    totalPayable: number;
    totalDue: number;
    totalExpense: number;
    totalOrderCount: number;
    totalCashTransfer: number;
    totalStockSold: number;
    shop: SingleShopDetails;
    cashReceiveList: SingleCashReceiveDetailsOnReport[];
    totalExpenseList: SingleExpenseDetailsOnReport[];
    totalCashTransferList: SingleCashTransferDetailsOnReport[];
    methodBasedPaymentList: SingleReceivedMethod[];
    shops?: SingleShopSalesSummary[];
  };
}

export interface SingleReceivedMethod {
  paymentMethod: string;
  totalAmount: number;
}

export interface SingleCashReceiveDetailsOnReport {
  id: string;
  createdAt: string;
  updatedAt: string;
  shopId: string;
  customerId: string;
  note: any;
  address: string;
  trackingNumber: any;
  subTotal: number;
  grandTotal: number;
  additionalCost: number;
  deliveryCharge: number;
  productDiscount: number;
  adminDiscount: number;
  vat: number;
  totalPaid: number;
  totalDue: number;
  customerPaid: number;
  returnAmount: number;
  deliveryPartner: string;
  paymentMethod: string;
  orderStatus: string;
  serialNo: number;
  employeeId: string;
  OrderPayment: OrderSinglePaymentDetails[];
}

export interface SingleExpenseDetailsOnReport {
  id: string;
  createdAt: string;
  updatedAt: string;
  name: string;
  imgUrl: string;
  amount: number;
  shopId: string;
  warehouseId: string;
}

export interface SingleCashTransferDetailsOnReport {
  id: string;
  createdAt: string;
  updatedAt: string;
  imgUrl: any;
  paymentDate: any;
  transferredTo: string;
  amount: number;
  shopId: string;
  warehouseId: string;
  senderId: string;
}

export interface GetShopCategoryWiseSummeryResponse {
  success: boolean;
  message: string;
  data: {
    shop?: SingleShopDetails;
    warehouse: Warehouse;
    result: SingleCategorySale[];
  };
  statusCode: number;
}

export interface GetShopProductWiseSummeryResponse {
  success: boolean;
  message: string;
  data: {
    shop?: SingleShopDetails;
    warehouse: Warehouse;
    result: SingleProductSale[];
  };
  statusCode: number;
}

export interface SingleCategorySale {
  categoryName: string;
  subCategoryName: string;
  quantity: number;
  totalRetailPrice: number;
}
export interface SingleProductSale {
  productName: string;
  quantity: number;
  totalRetailPrice: number;
  totalWholesalePrice: number;
}

export interface SingleStockDeleteDetails {
  id: string;
  createdAt: string;
  updatedAt: string;
  stockIds: string[];
  reason: string;
  warehouseId: string;
  createdById: string;
  CreatedBy: CreatedBy;
}

export interface SingleShopSalesSummary {
  totalStockSold: number;
  cashReceive: number;
  totalSubtotal: number;
  totalDeliveryCharge: number;
  totalDiscount: number;
  totalVat: number;
  totalPayable: number;
  totalDue: number;
  totalExpense: number;
  totalOrderCount: number;
  totalCashTransfer: number;
  totalPurchasePrice: number;
  methodBasedPaymentList: MethodBasedPaymentList[];
  shop: SingleShopDetails;
}

export interface MethodBasedPaymentList {
  paymentMethod: string;
  totalAmount: number;
}

export interface GetShopOrderStatusWiseSummeryResponse {
  success: boolean;
  message: string;
  statusCode: number;
  data: SingleStatusWiseSummary[];
}

export interface SingleStatusWiseSummary {
  count: number;
  subTotal: number;
  grandTotal: number;
  deliveryCost: number;
  deliveryCharge: number;
  adminDiscount: number;
  productDiscount: number;
  totalPaid: number;
  totalDue: number;
  customerPaid: number;
  returnAmount: number;
  vat: number;
  orderStatus: string;
}

export interface GetLocationAnalysisResponse {
  success: boolean;
  message: string;
  statusCode: number;
  data: SingleDivisionData[];
}

export interface SingleDivisionData {
  divisionName: string;
  totalOrderCount: number;
  districts: DistrictOnLocation[];
}

export interface DistrictOnLocation {
  districtName: string;
  totalOrderCount: number;
}
