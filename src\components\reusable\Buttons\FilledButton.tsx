import { useState } from 'react';

interface Props {
  text: string;
  handleClick: () => void;
  isLoading: boolean;
  isDisabled: boolean;
}

function FilledButton({ text, handleClick, isLoading, isDisabled }: Props) {
  const [isProcessing, setIsProcessing] = useState(false);

  const handleButtonClick = () => {
    if (!isProcessing) {
      setIsProcessing(true);
      handleClick(); // Call the provided handleClick function
      setTimeout(() => {
        setIsProcessing(false); // Re-enable the button after 1 second
      }, 1000);
    }
  };

  return (
    <button
      disabled={isLoading || isDisabled || isProcessing}
      onClick={handleButtonClick}
      type="button"
      className="cursor-pointer whitespace-nowrap rounded-lg bg-[#28243D] px-8 py-2 font-semibold text-white hover:bg-[#28243dd4] disabled:cursor-not-allowed disabled:bg-slate-300"
    >
      {isLoading || isProcessing ? (
        <div className="flex h-6 items-center justify-center">
          <div className="h-6 w-6 animate-spin rounded-full border-t-4 border-blue-500" />
        </div>
      ) : (
        <span>{text}</span>
      )}
    </button>
  );
}

export default FilledButton;
