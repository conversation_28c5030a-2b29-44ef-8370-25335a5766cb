import { Alert<PERSON><PERSON><PERSON>, Bell, CheckCircle2, PersonStanding } from 'lucide-react';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useGetAllNotificationsQuery } from '@/redux/api/organizationApis/orgNotificationApis';

interface Notification {
  id: string | number;
  title: string;
  message: string;
  createdAt: string;
  type: 'info' | 'success' | 'warning';
}

interface NotificationDropdownProps {
  organizationId?: string;
  warehouseId?: string;
  shopId?: string;
}

function NotificationDropdown({
  organizationId,
  warehouseId,
  shopId,
}: NotificationDropdownProps) {
  const { data } = useGetAllNotificationsQuery({
    organizationId: organizationId ?? undefined,
    warehouseId: warehouseId ?? undefined,
    shopId: shopId ?? undefined,
    limit: 10,
    page: 1,
  });

  const getIcon = (type: Notification['type']) => {
    switch (type) {
      case 'success':
        return <CheckCircle2 className="text-green-500" size={20} />;
      case 'warning':
        return <AlertCircle className="text-yellow-500" size={20} />;
      default:
        return <PersonStanding className="text-blue-500" size={20} />;
    }
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60),
    );

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return date.toLocaleDateString();
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger style={{ outline: 'none' }}>
        <div className="relative">
          <Bell className="h-6 w-6 text-white transition-colors hover:text-gray-200" />
          {data && data?.pagination.total > 0 && (
            <span className="absolute right-0 top-0 flex h-4 min-w-[1rem] -translate-y-1/2 translate-x-1/2 items-center justify-center rounded-full bg-red-500 px-1 text-[10px] leading-none text-white">
              {data?.pagination.total > 99 ? '99+' : data?.pagination.total}
            </span>
          )}
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        side="bottom"
        align="end"
        className="max-h-[80vh] w-[300px] overflow-y-auto rounded-lg bg-white p-2 shadow-lg"
      >
        <div className="space-y-2">
          {data?.data?.map((notification) => (
            <div
              key={notification.id}
              className="flex cursor-pointer items-start gap-3 rounded-md p-2 transition-colors hover:bg-gray-50"
            >
              <div className="mt-1">{getIcon('success')}</div>
              <div className="min-w-0 flex-1">
                <h3 className="truncate text-sm font-semibold text-gray-900">
                  {notification.title}
                </h3>
                <p className="mt-0.5 line-clamp-2 text-sm text-gray-600">
                  {notification.message}
                </p>
                <p className="mt-1 text-xs text-gray-500">
                  {formatTime(notification.createdAt)}
                </p>
              </div>
            </div>
          ))}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export default NotificationDropdown;
