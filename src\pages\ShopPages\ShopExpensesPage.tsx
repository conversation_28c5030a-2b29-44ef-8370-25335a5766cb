import { useParams } from 'react-router-dom';

import ShopExpensesPageOverview from '@/components/ShopComponents/ShopExpensesPageComponents/ShopExpensesPageOverview';
import { useAppSelector } from '@/redux/hooks';

function ShopExpensesPage() {
  const { shopId } = useParams();
  const { warehouseId } = useAppSelector((state) => state.shopDetails);
  return (
    <ShopExpensesPageOverview shopId={shopId ?? ''} warehouseId={warehouseId} />
  );
}

export default ShopExpensesPage;
