import { Route, BrowserRouter as Router, Routes } from 'react-router-dom';

import 'react-toastify/dist/ReactToastify.css';
import 'sweetalert2/src/sweetalert2.scss';
import CustomToastMessage from './components/reusable/CustomToastMessage/CustomToastMessage';
import NoResultFound from './components/reusable/NoResultFound/NoResultFound';
import HomeLayout from './layouts/HomeLayout';
import OrganizationLayout from './layouts/OrganizationLayout';
import ShopLayout from './layouts/ShopLayout';
import SuperAdminLayout from './layouts/SuperAdminLayout';
import WarehouseLayout from './layouts/WarehouseLayout';
import ChangePasswordPage from './pages/ChangePasswordPage';
import ContactPage from './pages/ContactPage';
import FeatureRequestsPage from './pages/FeatureRequestsPage';
import HomePage from './pages/HomePage';
import LoginPage from './pages/LoginPage';
import OrganizationCouriersPage from './pages/OrganizationPages/OrganizationCouriersPage';
import OrganizationDashboardPage from './pages/OrganizationPages/OrganizationDashboardPage';
import OrganizationEmployeeDetailsPage from './pages/OrganizationPages/OrganizationEmployeeDetailsPage';
import OrganizationEmployeesPage from './pages/OrganizationPages/OrganizationEmployeesPage';
import OrganizationPaymentsPage from './pages/OrganizationPages/OrganizationPaymentsPage';
import OrganizationPaymentStatusPage from './pages/OrganizationPages/OrganizationPaymentStatusPage';
import OrganizationShopsPage from './pages/OrganizationPages/OrganizationShopsPage';
import OrganizationStockAuditPage from './pages/OrganizationPages/OrganizationStockAuditPage';
import OrganizationSubscriptionDetailsPage from './pages/OrganizationPages/OrganizationSubscriptionDetailsPage';
import OrganizationSubscriptionsPage from './pages/OrganizationPages/OrganizationSubscriptionsPage';
import OrganizationWarehousesPage from './pages/OrganizationPages/OrganizationWarehousesPage';
import OrganizationWebhooksPage from './pages/OrganizationPages/OrganizationWebhooksPage';
import ProfilePage from './pages/ProfilePage';
import SettingsPage from './pages/SettingsPage';
import ShopAccountClosePage from './pages/ShopPages/ShopAccountClosePage';
import ShopAccountsSummeryPage from './pages/ShopPages/ShopAccountsSummeryPage';
import ShopAddNewOrderPage from './pages/ShopPages/ShopAddNewOrderPage';
import ShopApiDocumentationPage from './pages/ShopPages/ShopApiDocumentationPage';
import ShopBookNewParcelPage from './pages/ShopPages/ShopBookNewParcelPage';
import ShopBrandsPage from './pages/ShopPages/ShopBrandsPage';
import ShopCategoriesPage from './pages/ShopPages/ShopCategoriesPage';
import ShopCourierBookingsPage from './pages/ShopPages/ShopCourierBookingsPage';
import ShopCourierBulkEntryPage from './pages/ShopPages/ShopCourierBulkEntryPage';
import ShopCourierWebhookPage from './pages/ShopPages/ShopCourierWebhookPage';
import ShopCustomersPage from './pages/ShopPages/ShopCustomersPage';
import ShopDashboardPage from './pages/ShopPages/ShopDashboardPage';
import ShopExpensesPage from './pages/ShopPages/ShopExpensesPage';
import ShopExpensesReportPage from './pages/ShopPages/ShopExpensesReportPage';
import ShopExternalOrderDetailsPage from './pages/ShopPages/ShopExternalOrderDetailsPage';
import ShopExternalOrdersPage from './pages/ShopPages/ShopExternalOrdersPage';
import ShopInventoryPage from './pages/ShopPages/ShopInventoryPage';
import ShopOrderInvoicePage from './pages/ShopPages/ShopOrderInvoicePage';
import ShopOrderLocationAnalysisPage from './pages/ShopPages/ShopOrderLocationAnalysisPage';
import ShopOrderPaymentsPage from './pages/ShopPages/ShopOrderPaymentsPage';
import ShopOrdersPage from './pages/ShopPages/ShopOrdersPage';
import ShopProductsPage from './pages/ShopPages/ShopProductsPage';
import ShopSalesReportPage from './pages/ShopPages/ShopSalesReportPage';
import ShopSalesSummeryPage from './pages/ShopPages/ShopSalesSummeryPage';
import ShopSettingsPage from './pages/ShopPages/ShopSettingsPage';
import ShopStockAnalysisReportPage from './pages/ShopPages/ShopStockAnalysisReportPage';
import ShopStockAuditDetailsAndPerformPage from './pages/ShopPages/ShopStockAuditDetailsAndPerformPage';
import ShopStockAuditListPage from './pages/ShopPages/ShopStockAuditListPage';
import ShopStockAuditPage from './pages/ShopPages/ShopStockAuditPage';
import ShopStockEntryRequestPage from './pages/ShopPages/ShopStockEntryRequestPage';
import ShopStockListPage from './pages/ShopPages/ShopStockListPage';
import ShopStockReportPage from './pages/ShopPages/ShopStockReportPage';
import ShopStockSearchPage from './pages/ShopPages/ShopStockSearchPage';
import ShopStockTransferReportPage from './pages/ShopPages/ShopStockTransferReportPage';
import ShopSubCategoriesPage from './pages/ShopPages/ShopSubCategoriesPage';
import ShopTransferCashPage from './pages/ShopPages/ShopTransferCashPage';
import SuperAdminDashboardPage from './pages/SuperAdminPages/SuperAdminDashboardPage';
import SuperAdminManageAdminsPage from './pages/SuperAdminPages/SuperAdminManageAdminsPage';
import SuperAdminOrganizationDetailsPage from './pages/SuperAdminPages/SuperAdminOrganizationDetailsPage';
import SuperAdminOrganizationPage from './pages/SuperAdminPages/SuperAdminOrganizationPage';
import SuperAdminPlansPage from './pages/SuperAdminPages/SuperAdminPlansPage';
import SuperAdminSubscriptionsPage from './pages/SuperAdminPages/SuperAdminSubscriptionsPage';
import SuperAdminTransactionsPage from './pages/SuperAdminPages/SuperAdminTransactionsPage';
import UnitPage from './pages/UnitPage';
import AccountsClosePage from './pages/WarehousePages/AccountsClosePage';
import AddNewWholesaleOrder from './pages/WarehousePages/AddNewWholesaleOrder';
import BookingsPage from './pages/WarehousePages/BookingsPage';
import BookNewParcelPage from './pages/WarehousePages/BookNewParcelPage';
import BrandsPage from './pages/WarehousePages/BrandsPage';
import CategoriesPage from './pages/WarehousePages/CategoriesPage';
import CourierSettingsPage from './pages/WarehousePages/CourierSettingsPage';
import CourierWebhookPage from './pages/WarehousePages/CourierWebhookPage';
import CurrentStocksReportPage from './pages/WarehousePages/CurrentStocksReportPage';
import CustomerDetailsPage from './pages/WarehousePages/CustomerDetailsPage';
import CustomerPage from './pages/WarehousePages/CustomerPage';
import DashboardPage from './pages/WarehousePages/DashboardPage';
import EmployeesSalesReportPage from './pages/WarehousePages/EmployeesSalesReportPage';
import ExpensesCategoriesPage from './pages/WarehousePages/ExpensesCategoriesPage';
import ExpensesPage from './pages/WarehousePages/ExpensesPage';
import ExpensesReportPage from './pages/WarehousePages/ExpensesReportPage';
import LocationAnalysisPage from './pages/WarehousePages/LocationAnalysisPage';
import OrderDetailsPage from './pages/WarehousePages/OrderDetailsPage';
import OrdersPage from './pages/WarehousePages/OrdersPage';
import PendingReturnsPage from './pages/WarehousePages/PendingReturnsPage';
import ProductDetailsPage from './pages/WarehousePages/ProductDetailsPage';
import ProductsPage from './pages/WarehousePages/ProductsPage';
import SalesReportPage from './pages/WarehousePages/SalesReportPage';
import SalesSummeryPage from './pages/WarehousePages/SalesSummeryPage';
import SellersPage from './pages/WarehousePages/SellersPage';
import SendMessagePage from './pages/WarehousePages/SendMessagePage';
import SingleEntryBarcodesPage from './pages/WarehousePages/SingleEntryBarcodesPage';
import SingleEntryBarcodesPdfPage from './pages/WarehousePages/SingleEntryBarcodesPdfPage';
import SingleProductBarcodesPage from './pages/WarehousePages/SingleProductBarcodesPage';
import SingleStockBarcodePage from './pages/WarehousePages/SingleStockBarcodePage';
import SingleStockDetailsPage from './pages/WarehousePages/SingleStockDetailsPage';
import SmsBalanceAndHistoryPage from './pages/WarehousePages/SmsBalanceAndHistoryPage';
import SmsSettingsPage from './pages/WarehousePages/SmsSettingsPage';
import StockAnalysisReportPage from './pages/WarehousePages/StockAnalysisReportPage';
import StockDeleteReportPage from './pages/WarehousePages/StockDeleteReportPage';
import StockEntryDetailsPage from './pages/WarehousePages/StockEntryDetailsPage';
import StockEntryListPage from './pages/WarehousePages/StockEntryListPage';
import StockEntryPage from './pages/WarehousePages/StockEntryPage';
import StockEntryReportPage from './pages/WarehousePages/StockEntryReportPage';
import StockListPage from './pages/WarehousePages/StockListPage';
import StockPullPage from './pages/WarehousePages/StockPullPage';
import StockReturnReportPage from './pages/WarehousePages/StockReturnReportPage';
import StockTransferByScanPage from './pages/WarehousePages/StockTransferByScanPage';
import StockTransferHistoryPage from './pages/WarehousePages/StockTransferHistoryPage';
import StockTransferPage from './pages/WarehousePages/StockTransferPage';
import StockTransferReportPage from './pages/WarehousePages/StockTransferReportPage';
import SubCategoriesPage from './pages/WarehousePages/SubCategoriesPage';
import SupplierDetailsPage from './pages/WarehousePages/SupplierDetailsPage';
import SuppliersPage from './pages/WarehousePages/SuppliersPage';
import WarehouseInventoryPage from './pages/WarehousePages/WarehouseInventoryPage';
import WarehouseStockHandOverPage from './pages/WarehousePages/WarehouseStockHandOverPage';
import WholeSaleOrdersPage from './pages/WarehousePages/WholeSaleOrdersPage';

export default function App() {
  return (
    <div>
      <Router>
        <Routes>
          <Route path="/login" element={<LoginPage />} />
          <Route
            path="single-stock-barcode"
            element={<SingleStockBarcodePage />}
          />
          <Route
            path="single-entry-barcode"
            element={<SingleEntryBarcodesPage />}
          />
          <Route
            path="single-entry-barcode-pdf"
            element={<SingleEntryBarcodesPdfPage />}
          />
          <Route
            path="single-product-barcode"
            element={<SingleProductBarcodesPage />}
          />

          <Route path="/" element={<HomeLayout />}>
            <Route path="/" element={<HomePage />} />
            <Route path="/shops" element={<HomePage />} />
            <Route path="/employees" element={<OrganizationEmployeesPage />} />
            <Route path="/profile" element={<ProfilePage />} />
            <Route path="/update-password" element={<ChangePasswordPage />} />
          </Route>
          {/* organizations routes  */}
          <Route path="/organization" element={<OrganizationLayout />}>
            <Route path="dashboard" element={<OrganizationDashboardPage />} />
            <Route path="warehouses" element={<OrganizationWarehousesPage />} />
            <Route path="shops" element={<OrganizationShopsPage />} />
            <Route path="employees" element={<OrganizationEmployeesPage />} />
            <Route path="feature-requests" element={<FeatureRequestsPage />} />
            <Route
              path="employee/:employeeId"
              element={<OrganizationEmployeeDetailsPage />}
            />
            <Route path="couriers" element={<OrganizationCouriersPage />} />
            <Route path="webhooks" element={<OrganizationWebhooksPage />} />
            <Route
              path="stock-audit"
              element={<OrganizationStockAuditPage />}
            />
            <Route
              path="subscriptions"
              element={<OrganizationSubscriptionsPage />}
            />
            <Route
              path="subscriptions/:subscriptionId"
              element={<OrganizationSubscriptionDetailsPage />}
            />
            <Route path="billing" element={<OrganizationPaymentsPage />} />
            <Route
              path="payment/success"
              element={<OrganizationPaymentStatusPage type="SUCCESS" />}
            />
            <Route
              path="payment/failure"
              element={<OrganizationPaymentStatusPage type="FAILED" />}
            />
            <Route
              path="payment/cancel"
              element={<OrganizationPaymentStatusPage type="CANCELLED" />}
            />
          </Route>
          {/* warehouse routes  */}
          <Route path="/warehouse/:warehouseId/*" element={<WarehouseLayout />}>
            <Route path="dashboard" element={<DashboardPage />} />
            <Route path="products" element={<ProductsPage />} />
            <Route
              path="products/:productId"
              element={<ProductDetailsPage type="WAREHOUSE" />}
            />
            <Route path="brands" element={<BrandsPage />} />
            <Route path="categories" element={<CategoriesPage />} />
            <Route path="sub-categories" element={<SubCategoriesPage />} />
            <Route path="unit" element={<UnitPage />} />

            <Route path="stock-list" element={<StockListPage />} />
            <Route
              path="stock-list/:stockId"
              element={<SingleStockDetailsPage />}
            />
            <Route path="stock-entry" element={<StockEntryPage />} />
            <Route
              path="stocks-transfer-history"
              element={<StockTransferHistoryPage />}
            />
            <Route path="purchase-invoices" element={<StockEntryListPage />} />
            <Route
              path="purchase-invoices/:entryId"
              element={<StockEntryDetailsPage />}
            />
            <Route path="stock-transfer" element={<StockTransferPage />} />
            {/* <Route path="stock-search" element={<ShopStockSearchPage />} /> */}
            <Route
              path="scan-stock-transfer"
              element={<StockTransferByScanPage />}
            />
            <Route path="stock-pull" element={<StockPullPage />} />
            <Route path="suppliers" element={<SuppliersPage />} />
            <Route path="pending-returns" element={<PendingReturnsPage />} />
            <Route
              path="suppliers/:supplierId"
              element={<SupplierDetailsPage />}
            />
            <Route path="customers" element={<CustomerPage />} />
            <Route
              path="customers/:customerId"
              element={<CustomerDetailsPage />}
            />
            <Route path="sellers" element={<SellersPage />} />
            <Route path="orders" element={<OrdersPage />} />
            <Route path="orders/:orderId" element={<OrderDetailsPage />} />
            <Route path="invoice/:orderId" element={<ShopOrderInvoicePage />} />
            <Route path="settings" element={<SettingsPage />} />
            <Route path="contact" element={<ContactPage />} />
            <Route
              path="current-stock-report"
              element={<CurrentStocksReportPage />}
            />
            <Route
              path="date-wise-stock-report"
              element={<WarehouseInventoryPage />}
            />
            <Route
              path="date-wise-stock-handover-report"
              element={<WarehouseStockHandOverPage />}
            />
            <Route
              path="stock-analysis-report"
              element={<StockAnalysisReportPage />}
            />
            <Route
              path="location-analysis-report"
              element={<LocationAnalysisPage />}
            />
            <Route
              path="stock-entry-report"
              element={<StockEntryReportPage />}
            />
            <Route
              path="stock-transfer-report"
              element={<StockTransferReportPage />}
            />
            <Route
              path="stock-delete-report"
              element={<StockDeleteReportPage />}
            />
            <Route path="sales-report" element={<SalesReportPage />} />
            <Route
              path="stock-return-report"
              element={<StockReturnReportPage viewFrom="warehouse" />}
            />
            <Route path="expenses-report" element={<ExpensesReportPage />} />
            <Route
              path="employees-report"
              element={<EmployeesSalesReportPage />}
            />
            <Route path="courier-settings" element={<CourierSettingsPage />} />
            <Route path="courier-webhook" element={<CourierWebhookPage />} />
            <Route path="sms-settings" element={<SmsSettingsPage />} />
            <Route path="courier-bookings" element={<BookingsPage />} />
            <Route path="book-new-parcel" element={<BookNewParcelPage />} />
            <Route
              path="balance-and-history"
              element={<SmsBalanceAndHistoryPage />}
            />
            <Route path="send-sms" element={<SendMessagePage />} />
            <Route path="expenses" element={<ExpensesPage />} />
            <Route
              path="expense-categories"
              element={<ExpensesCategoriesPage />}
            />
            <Route path="sales-summery" element={<SalesSummeryPage />} />
            <Route path="account-close" element={<AccountsClosePage />} />
            <Route
              path="add-new-wholesale-order"
              element={<AddNewWholesaleOrder />}
            />
            <Route path="wholesale-orders" element={<WholeSaleOrdersPage />} />
            <Route path="*" element={<NoResultFound pageType="page" />} />
          </Route>

          {/* Shop Pages */}
          <Route path="/shop/:shopId/*" element={<ShopLayout />}>
            <Route path="dashboard" element={<ShopDashboardPage />} />
            <Route path="products" element={<ShopProductsPage />} />
            <Route
              path="products/:productId"
              element={<ProductDetailsPage type="SHOP" />}
            />
            <Route path="brands" element={<ShopBrandsPage />} />
            <Route path="categories" element={<ShopCategoriesPage />} />
            <Route path="sub-categories" element={<ShopSubCategoriesPage />} />
            <Route path="unit" element={<UnitPage />} />
            <Route path="stock-list" element={<ShopStockListPage />} />
            <Route path="stock-search" element={<ShopStockSearchPage />} />
            <Route
              path="date-wise-stock-report"
              element={<ShopInventoryPage />}
            />
            <Route
              path="stock-audit/:productId"
              element={<ShopStockAuditPage />}
            />
            <Route path="stock-report" element={<ShopStockReportPage />} />
            <Route path="stock-audit" element={<ShopStockAuditListPage />} />
            <Route
              path="stock-audit/:auditId/details"
              element={<ShopStockAuditDetailsAndPerformPage />}
            />
            <Route
              path="stock-list/:stockId"
              element={<SingleStockDetailsPage />}
            />
            <Route
              path="stock-analysis-report"
              element={<ShopStockAnalysisReportPage />}
            />
            <Route
              path="location-analysis-report"
              element={<ShopOrderLocationAnalysisPage />}
            />
            <Route path="sales-report" element={<ShopSalesReportPage />} />
            <Route
              path="stock-transfer-report"
              element={<ShopStockTransferReportPage />}
            />
            <Route
              path="stock-return-report"
              element={<StockReturnReportPage viewFrom="shop" />}
            />
            <Route path="expense-report" element={<ShopExpensesReportPage />} />
            <Route path="customers" element={<ShopCustomersPage />} />
            <Route
              path="customers/:customerId"
              element={<CustomerDetailsPage />}
            />
            <Route path="orders" element={<ShopOrdersPage />} />
            <Route
              path="external-orders"
              element={<ShopExternalOrdersPage />}
            />
            <Route
              path="external-orders/:orderId"
              element={<ShopExternalOrderDetailsPage />}
            />
            <Route path="order-payments" element={<ShopOrderPaymentsPage />} />
            <Route path="orders/:orderId" element={<OrderDetailsPage />} />
            <Route path="invoice/:orderId" element={<ShopOrderInvoicePage />} />
            <Route path="expenses" element={<ShopExpensesPage />} />
            <Route path="transfer-cash" element={<ShopTransferCashPage />} />
            <Route
              path="accounts-summery"
              element={<ShopAccountsSummeryPage />}
            />
            <Route path="new-order" element={<ShopAddNewOrderPage />} />
            <Route path="settings" element={<ShopSettingsPage />} />
            <Route path="contact" element={<ContactPage />} />
            <Route
              path="courier-bookings"
              element={<ShopCourierBookingsPage />}
            />
            <Route
              path="courier-bulk-entry"
              element={<ShopCourierBulkEntryPage />}
            />
            <Route
              path="courier-webhook"
              element={<ShopCourierWebhookPage />}
            />
            <Route path="book-new-parcel" element={<ShopBookNewParcelPage />} />
            <Route path="sales-summery" element={<ShopSalesSummeryPage />} />
            <Route path="account-close" element={<ShopAccountClosePage />} />
            <Route
              path="stock-entry-request"
              element={<ShopStockEntryRequestPage />}
            />
            <Route path="api-docs" element={<ShopApiDocumentationPage />} />
            <Route path="*" element={<NoResultFound pageType="page" />} />
          </Route>

          {/* Super Admin Pages */}
          <Route path="/super-admin/*" element={<SuperAdminLayout />}>
            <Route path="dashboard" element={<SuperAdminDashboardPage />} />
            <Route
              path="organizations"
              element={<SuperAdminOrganizationPage />}
            />
            <Route path="admins" element={<SuperAdminManageAdminsPage />} />
            <Route path="plans" element={<SuperAdminPlansPage />} />
            <Route
              path="subscriptions"
              element={<SuperAdminSubscriptionsPage />}
            />
            <Route
              path="transactions"
              element={<SuperAdminTransactionsPage />}
            />
            <Route
              path="organization/:orgId"
              element={<SuperAdminOrganizationDetailsPage />}
            />
          </Route>
        </Routes>
      </Router>
      <CustomToastMessage />
    </div>
  );
}
