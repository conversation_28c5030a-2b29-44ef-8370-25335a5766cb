import { Page, Text, View, StyleSheet } from '@react-pdf/renderer';

import PrintedTime from '@/components/WarehouseComponents/ReportsPdf/PrintedTime';
import { SingleAuditData } from '@/redux/api/shopApis/shopStockAuditApis';

// Helper to format date and time
const formatDate = (dateStr: any) => {
  if (!dateStr) return '-';
  const date = new Date(dateStr);
  return `${date.toLocaleDateString()} ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
};

// PDF styles
const styles = StyleSheet.create({
  page: {
    padding: 24,
    fontSize: 10,
    fontFamily: 'Helvetica',
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#222',
    textAlign: 'center',
  },
  section: {
    marginBottom: 12,
    padding: 8,
    borderRadius: 4,
    backgroundColor: '#f5f5f5',
    display: 'flex',
    flexDirection: 'column',
    gap: 4,
  },
  label: {
    fontWeight: 'bold',
    color: '#333',
  },
  table: {
    width: 'auto',
    marginTop: 8,
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: '#bbb',
    borderRadius: 4,
  },
  tableRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    borderBottomStyle: 'solid',
    alignItems: 'center',
  },
  tableHeader: {
    backgroundColor: '#e5e7eb',
    fontWeight: 'bold',
    color: '#222',
  },
  cellNo: {
    padding: 6,
    fontSize: 10,
    width: '7%',
    minWidth: 24,
    textAlign: 'center',
    borderRightWidth: 1,
    borderRightColor: '#eee',
    borderRightStyle: 'solid',
  },
  cellProduct: {
    padding: 6,
    fontSize: 10,
    width: '38%',
    minWidth: 80,
    textAlign: 'left',
    borderRightWidth: 1,
    borderRightColor: '#eee',
    borderRightStyle: 'solid',
  },
  cellCount: {
    padding: 6,
    fontSize: 10,
    width: '10%',
    minWidth: 30,
    textAlign: 'center',
    borderRightWidth: 1,
    borderRightColor: '#eee',
    borderRightStyle: 'solid',
  },
  cellExpected: {
    padding: 6,
    fontSize: 10,
    width: '15%',
    minWidth: 40,
    textAlign: 'center',
    borderRightWidth: 1,
    borderRightColor: '#eee',
    borderRightStyle: 'solid',
  },
  cellDiscrepancy: {
    padding: 6,
    fontSize: 10,
    width: '10%',
    minWidth: 30,
    textAlign: 'center',
    borderRightWidth: 1,
    borderRightColor: '#eee',
    borderRightStyle: 'solid',
  },
  cellReason: {
    padding: 6,
    fontSize: 10,
    width: '20%',
    minWidth: 60,
    textAlign: 'left',
  },
});

interface Props {
  auditDetails: SingleAuditData;
}

function StockAuditReportPdf({ auditDetails }: Props) {
  return (
    <Page size="A4" style={styles.page}>
      <Text style={styles.title}>Stock Audit Report</Text>
      <PrintedTime />
      <View style={styles.section}>
        <Text>
          <Text style={styles.label}>Shop: </Text>
          {auditDetails.Shop?.name || '-'}
        </Text>
        <Text>
          <Text style={styles.label}>Status: </Text>
          {auditDetails.status}
        </Text>
        <Text>
          <Text style={styles.label}>Created By: </Text>
          {auditDetails.CreatedBy?.name || '-'}
        </Text>
        <Text>
          <Text style={styles.label}>Started At: </Text>
          {formatDate(auditDetails.startedAt)}
        </Text>
        <Text>
          <Text style={styles.label}>Completed At: </Text>
          {formatDate(auditDetails.completedAt)}
        </Text>
        <Text>
          <Text style={styles.label}>Final Verdict: </Text>
          {auditDetails.finalVerdict ?? '-'}
        </Text>
      </View>
      <View style={styles.table}>
        <View style={[styles.tableRow, styles.tableHeader]}>
          <Text style={styles.cellNo}>No</Text>
          <Text style={styles.cellProduct}>Product Name</Text>
          <Text style={styles.cellCount}>Count</Text>
          <Text style={styles.cellExpected}>Expected</Text>
          <Text style={styles.cellDiscrepancy}>Discrepancy</Text>
          <Text style={styles.cellReason}>Discrepancy Reason</Text>
        </View>
        {auditDetails.Products?.map((prod, idx) => (
          <View style={styles.tableRow} key={prod.id}>
            <Text style={styles.cellNo}>{idx + 1}</Text>
            <Text style={styles.cellProduct}>{prod.Product?.name || '-'}</Text>
            <Text style={styles.cellCount}>{prod.count}</Text>
            <Text style={styles.cellExpected}>{prod.expectedCount}</Text>
            <Text style={styles.cellDiscrepancy}>{prod.discrepancy}</Text>
            <Text style={styles.cellReason}>
              {prod.discrepancyReason ?? '-'}
            </Text>
          </View>
        ))}
        {/* Total Row */}
        {auditDetails.Products && auditDetails.Products.length > 0 && (
          <View style={[styles.tableRow, { backgroundColor: '#f1f5f9' }]}>
            <Text style={styles.cellNo} />
            <Text style={styles.cellProduct}>Total</Text>
            <Text style={styles.cellCount}>
              {auditDetails.Products.reduce(
                (sum, prod) => sum + (prod.count || 0),
                0,
              )}
            </Text>
            <Text style={styles.cellExpected}>
              {auditDetails.Products.reduce(
                (sum, prod) => sum + (prod.expectedCount || 0),
                0,
              )}
            </Text>
            <Text style={styles.cellDiscrepancy}>
              {auditDetails.Products.reduce(
                (sum, prod) => sum + (prod.discrepancy || 0),
                0,
              )}
            </Text>
            <Text style={styles.cellReason} />
          </View>
        )}
        {(!auditDetails.Products || auditDetails.Products.length === 0) && (
          <View style={styles.tableRow}>
            <Text style={styles.cellNo}>-</Text>
            <Text style={styles.cellProduct}>No products found.</Text>
            <Text style={styles.cellCount} />
            <Text style={styles.cellExpected} />
            <Text style={styles.cellDiscrepancy} />
            <Text style={styles.cellReason} />
          </View>
        )}
      </View>
    </Page>
  );
}

export default StockAuditReportPdf;
