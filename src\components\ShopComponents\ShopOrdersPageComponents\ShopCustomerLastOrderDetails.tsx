import { useNavigate } from 'react-router-dom';

import { EyeButton } from '@/components/reusable/Buttons/CommonButtons';
import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import { ROUTES } from '@/Routes';

function ShopCustomerLastOrderDetails({
  shopId,
  lastOrderDetails,
}: {
  shopId: string;
  lastOrderDetails: any;
}) {
  const navigate = useNavigate();
  return (
    <div className="full-table-container">
      {lastOrderDetails?.id ? (
        <div className="full-table-box">
          <table className="full-table">
            <thead className="bg-gray-100">
              <tr>
                <th className="tableHead">Last Order</th>
                <th className="tableHead">Branch</th>
                <th className="tableHead">Total</th>
                <th className="tableHead">Paid</th>
                <th className="tableHead">Due</th>
                <th className="tableHead">Status</th>
                <th className="tableHead">Order Date</th>
                <th className="tableHead">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y bg-slate-200">
              <tr>
                <td className="tableDataRed">{lastOrderDetails?.serialNo}</td>
                <td className="tableDataRed">
                  {lastOrderDetails?.Shop?.nickName}
                </td>
                <td className="tableDataRed">
                  {Number(lastOrderDetails?.grandTotal) +
                    Number(lastOrderDetails?.deliveryCharge)}
                </td>
                <td className="tableDataRed">{lastOrderDetails?.totalPaid}</td>
                <td className="tableDataRed">{lastOrderDetails?.totalDue}</td>
                <td className="tableDataRed">
                  {lastOrderDetails?.orderStatus}
                </td>
                <td className="tableDataRed">
                  <DateAndTimeViewer date={lastOrderDetails?.createdAt} />
                </td>
                <td className="tableDataRed">
                  <EyeButton
                    handleClick={() =>
                      navigate(
                        ROUTES.SHOP.ORDER_DETAILS(shopId, lastOrderDetails?.id),
                      )
                    }
                  />
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      ) : (
        <div>
          <h2>No Orders found</h2>
        </div>
      )}
    </div>
  );
}

export default ShopCustomerLastOrderDetails;
