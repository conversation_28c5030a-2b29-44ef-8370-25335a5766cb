import { useLocation } from 'react-router-dom';

import { TransparentPrintButtonTop } from '@/components/reusable/Buttons/CommonButtons';
import FilterButton from '@/components/reusable/Buttons/FilterButton';
import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import StartDateEndDateWithSearch from '@/components/reusable/ReusableFilters/StartDateEndDateWithSearch';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import BrandPageFilterModal from '@/components/WarehouseComponents/BrandPageComponents/BrandPageFilterModal';
import { useGetShopExpensesReportQuery } from '@/redux/api/shopApis/shopReportsApis';
import { ShopExpenseDetails } from '@/types/shopTypes/shopExpensesTypes';
import { handleShopExpensesReportPdf } from '@/utils/GenerateReportPdf';

interface Props {
  shopId: string;
  warehouseId: string;
}
function ShopExpensesReportOverview({ shopId, warehouseId }: Props) {
  const router = new URLSearchParams(useLocation().search);
  const startDate = router.get('startDate') || `${new Date().toISOString()}`;
  const endDate = router.get('endDate') || `${new Date().toISOString()}`;
  const { data, isLoading, isFetching } = useGetShopExpensesReportQuery(
    {
      warehouseId,
      shopId,
      type: 'custom',
      startDate,
      endDate,
    },
    { skip: !(warehouseId && shopId && startDate && endDate) },
  );

  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 lg:py-1">
        <div className="flex items-center gap-x-2">
          <div className="search-title-and-btn flex items-center gap-x-3">
            {/* <p className="whitespace-nowrap">Search Filters</p> */}
            <div className="relative">
              <div className="block lg:hidden">
                <FilterButton handleClick={() => console.log('higbig')} />
              </div>
              <div className="block lg:hidden">
                <BrandPageFilterModal />
              </div>
            </div>
          </div>
          <div className="hidden lg:block">
            <StartDateEndDateWithSearch />
          </div>
        </div>
      </div>
      {!isLoading && !isFetching ? (
        <div>
          <div className="tableTop w-full">
            <p>Expenses List</p>
            <div className="flex items-center">
              <p>Total : {data?.data?.result?.length}</p>
              <div className="ml-4">
                <TransparentPrintButtonTop
                  handleClick={() => {
                    handleShopExpensesReportPdf({
                      expenses: data?.data.result ?? [],
                      shopDetails: data?.data.shop,
                    });
                  }}
                />
              </div>
            </div>
          </div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {data?.data?.result?.length ? (
              <div className="full-table-box h-customExc">
                <table className="full-table">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="tableHead">No</th>
                      <th className="tableHead table-col-width">Name</th>
                      <th className="tableHead">Amount</th>
                      <th className="tableHead">Created At</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y bg-slate-200">
                    {data?.data?.result?.map(
                      (expense: ShopExpenseDetails, index: number) => (
                        <tr key={expense?.id}>
                          <td className="tableData">{index + 1}</td>
                          <td className="tableData table-col-width">
                            {expense?.name}
                          </td>
                          <td className="tableData">{expense?.amount}</td>
                          <td className="tableData">
                            <DateAndTimeViewer date={expense?.createdAt} />
                          </td>
                        </tr>
                      ),
                    )}
                  </tbody>
                </table>
              </div>
            ) : (
              <NoResultFound pageType="expense" />
            )}
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={4} tableRow={6} />
      )}
    </div>
  );
}

export default ShopExpensesReportOverview;
