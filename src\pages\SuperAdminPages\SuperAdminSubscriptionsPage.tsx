import { useLocation } from 'react-router-dom';

import Date<PERSON>iewer from '@/components/reusable/DateAndTimeViewer/DateViewer';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import OrderStatusViewer from '@/components/reusable/OrdersPagesReusableComponents/OrderStatusViewer';
import Pagination from '@/components/reusable/Pagination/Pagination';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import {
  SingleSubscription,
  useGetOrgSubscriptionsListQuery,
} from '@/redux/api/organizationApis/orgSubscriptionsApis';

function SuperAdminSubscriptionsPage() {
  const router = new URLSearchParams(useLocation().search);
  const page = router.get('page');
  const limit = router.get('limit');
  const { data, isFetching, isLoading } = useGetOrgSubscriptionsListQuery({
    page: page ?? '1',
    limit: limit ?? '10',
  });
  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-2">
        <div className="flex items-center">
          <div className="search-title-and-btn flex items-center">
            <div className="relative">
              <div className="block xl:hidden">
                {/* <FilterButton
                handleClick={() => setIsFilterModalOpen(!isFilterModalOpen)}
              /> */}
              </div>
              {/* <div
              className={`${isFilterModalOpen ? 'block' : 'hidden'} xl:hidden`}
            >
              <StockListPageFilterModal
                handleClearAndClose={() => setIsFilterModalOpen(false)}
                handleFilter={handleFilter}
              />
            </div> */}
            </div>
          </div>
          <div className="hidden xl:block">
            {/* <div className="flex items-center gap-x-2">
            <StockListFilterOptions handleFilter={handleFilter} />
          </div> */}
            <h2>Filters Will be here</h2>
          </div>
        </div>
      </div>
      {!isLoading && !isFetching ? (
        <div>
          <div className="tableTop w-full">
            <p>Subscriptions</p>
            <div className="flex items-center gap-2">
              <p>Total : {data?.pagination?.total}</p>
            </div>
          </div>
          <div className="full-table-container w-full">
            {data?.data?.length ? (
              <div>
                <div className="full-table-box h-custom">
                  <table className="full-table">
                    <thead className="bg-gray-100">
                      <tr>
                        <th className="tableHead">#</th>
                        <th className="tableHead">Organization</th>
                        <th className="tableHead">Invoice No</th>
                        <th className="tableHead">Plan</th>
                        <th className="tableHead">Billing Type</th>
                        <th className="tableHead">Billing Cycle</th>
                        <th className="tableHead">Amount</th>
                        <th className="tableHead">Status</th>
                        <th className="tableHead">Payment Status</th>
                        <th className="tableHead">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y">
                      {data?.data?.map(
                        (
                          singleSubscription: SingleSubscription,
                          index: number,
                        ) => (
                          <tr key={singleSubscription?.id}>
                            <td className="tableData">{index + 1}</td>
                            <td className="tableData">
                              {singleSubscription.Organization?.name}
                            </td>
                            <td className="tableData">
                              {singleSubscription.invoiceNo}
                            </td>
                            <td className="tableData">
                              {singleSubscription?.Plan.name}
                            </td>
                            <td className="tableData">
                              {singleSubscription?.billingCycle}
                            </td>
                            <td className="tableData">
                              <div className="flex items-center justify-center gap-1">
                                {singleSubscription?.startDate && (
                                  <DateViewer
                                    date={singleSubscription?.startDate}
                                  />
                                )}
                                -
                                {singleSubscription?.endDate && (
                                  <DateViewer
                                    date={singleSubscription?.endDate}
                                  />
                                )}
                              </div>
                            </td>
                            <td className="tableData">
                              {singleSubscription?.amount}
                            </td>
                            <td className="tableData">
                              {/* <OrderStatusViewer
                                status={singleSubscription?.status}
                              /> */}
                              {singleSubscription?.status}
                            </td>
                            <td className="tableData">
                              <OrderStatusViewer
                                status={singleSubscription?.paymentStatus}
                              />
                            </td>
                            <td className="tableData">
                              {/* <div className="flex items-center justify-center">
                                <Menubar
                                  style={{
                                    border: 'none',
                                    backgroundColor: 'transparent',
                                  }}
                                >
                                  <MenubarMenu>
                                    <MenubarTrigger className="cursor-pointer">
                                      <EllipsisVertical />
                                    </MenubarTrigger>
                                    <MenubarContent
                                      style={{
                                        marginRight: '25px',
                                        borderColor: 'black',
                                      }}
                                    >
                                      <MenubarItem
                                        onClick={() =>
                                          navigate(
                                            ROUTES.ORGANIZATION.SUBSCRIPTION_DETAILS(
                                              singleSubscription.id,
                                            ),
                                          )
                                        }
                                        className="flex cursor-pointer items-center gap-1 bg-primary font-semibold text-white"
                                      >
                                        <Eye size={20} />
                                        <span>View Details</span>
                                      </MenubarItem>

                                      {singleSubscription?.paymentStatus.toLowerCase() !==
                                      'paid' ? (
                                        <>
                                          <MenubarSeparator />
                                          <MenubarItem
                                            onClick={() => {
                                              setSelectedSubscription(
                                                singleSubscription,
                                              );
                                              setIsPayNowModalOpen(true);
                                            }}
                                            className="flex cursor-pointer items-center gap-1 bg-primary font-semibold text-white"
                                          >
                                            <Wallet size={20} />
                                            <span>pay Now</span>
                                          </MenubarItem>
                                        </>
                                      ) : (
                                        ''
                                      )}
                                    </MenubarContent>
                                  </MenubarMenu>
                                </Menubar>
                              </div> */}
                            </td>
                          </tr>
                        ),
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            ) : (
              <NoResultFound pageType="stock" />
            )}
          </div>
          <div className="pagination-box flex justify-end rounded bg-white p-3">
            <Pagination
              totalCount={data?.pagination?.total}
              totalPages={Math.ceil(
                Number(data?.pagination?.total) /
                  Number(data?.pagination?.limit),
              )}
            />
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={12} tableRow={6} />
      )}
    </div>
  );
}

export default SuperAdminSubscriptionsPage;
