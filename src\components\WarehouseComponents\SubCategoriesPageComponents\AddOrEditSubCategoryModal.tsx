import { useFormik } from 'formik';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import * as Yup from 'yup';

import FilledSubmitButton from '../../reusable/Buttons/FilledSubmitButton';
import CustomInputField from '../../reusable/CustomInputField/CustomInputField';
import ModalTitle from '../../reusable/Modal/ModalTitle';

import CustomDropdown from '@/components/reusable/CustomInputField/CustomDropdown';
import ImageSelector from '@/components/reusable/ImageSelector/ImageSelector';
import {
  useCreateSubCategoryMutation,
  useUpdateSubCategoryMutation,
} from '@/redux/api/warehouseApis/subCategoriesApi';
import { SingleSubCategory } from '@/types/warehouseTypes/subCategoriesTypes';
import { UploadImageOnAws } from '@/utils/ImageUploadModule';

interface Props {
  type: string;
  warehouseId: string | undefined;
  handleClose: () => void;
  updateRefreshCounter: () => void;
  subCategoryData?: SingleSubCategory;
  categories: any;
}

const formikInitialValues = {
  name: '',
  productCategoryId: '',
};

const validation = Yup.object({
  name: Yup.string().required('Name is required'),
  productCategoryId: Yup.string().required('Category is required'),
});

function AddOrEditSubCategoryModal({
  type,
  warehouseId,
  handleClose,
  updateRefreshCounter,
  subCategoryData,
  categories,
}: Props) {
  const [currentFile, setCurrentFile] = useState<any>();
  const [createSubCategory, { isLoading }] = useCreateSubCategoryMutation();

  const [updateSubCategory, { isLoading: isUpdatingCategory }] =
    useUpdateSubCategoryMutation();

  const formik = useFormik({
    initialValues: formikInitialValues,
    validationSchema: validation,

    onSubmit: async (values) => {
      const imgUrl = currentFile
        ? await UploadImageOnAws(currentFile)
        : subCategoryData?.imgUrl
          ? subCategoryData?.imgUrl
          : null;
      if (type === 'new') {
        toast.promise(
          createSubCategory({
            ...values,
            warehouseId,
            imgUrl,
          }).unwrap(),
          {
            pending: 'Creating New Sub Category...',
            success: {
              render({ data: res }) {
                if (res?.statusCode === 200 || res?.statusCode === 201) {
                  updateRefreshCounter();
                  handleClose();
                }
                return 'Sub Category created Successfully';
              },
            },
            error: {
              render({ data: error }) {
                console.log(error);
                return 'Error on creating Sub Category';
              },
            },
          },
        );
      } else {
        toast.promise(
          updateSubCategory({
            data: { ...values, imgUrl },
            id: subCategoryData?.id,
          }).unwrap(),
          {
            pending: 'Updating Sub Category...',
            success: {
              render({ data: res }) {
                if (res?.statusCode === 200 || res?.statusCode === 201) {
                  updateRefreshCounter();
                  handleClose();
                }
                return 'Sub Category updated Successfully';
              },
            },
            error: {
              render({ data: error }) {
                console.log(error);
                return 'Error on update Sub Category';
              },
            },
          },
        );
      }
    },
  });

  useEffect(() => {
    if (type === 'edit' && subCategoryData) {
      formik.setFieldValue('name', subCategoryData?.name);
      formik.setFieldValue(
        'productCategoryId',
        subCategoryData?.productCategoryId,
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [type, subCategoryData]);
  return (
    <div className="flex w-[400px] flex-col gap-4 rounded-xl bg-white p-4">
      <ModalTitle
        text={type === 'new' ? 'Create Sub Category' : 'Edit Sub Category'}
        handleClose={handleClose}
      />
      <form
        onSubmit={formik.handleSubmit}
        className="flex w-full flex-col gap-4"
      >
        <div className="flex gap-2">
          <div className="w-[100px]">
            <ImageSelector
              previousImage={subCategoryData?.imgUrl ?? ''}
              setNewImage={(e) => setCurrentFile(e)}
            />
          </div>
          <div className="flex w-full flex-col gap-4">
            <CustomDropdown
              placeholder="Select Category"
              name="productCategoryId"
              label="Category"
              formik={formik}
              options={categories}
            />
            <CustomInputField
              type="text"
              placeholder="Enter Sub Category Name"
              name="name"
              label="Name"
              formik={formik}
            />
          </div>
        </div>
        <div className="mt-[10px] flex w-full items-center justify-center">
          <FilledSubmitButton
            text={type === 'new' ? 'Create Sub Category' : 'Done Editing'}
            isLoading={isLoading || isUpdatingCategory}
          />
        </div>
      </form>
    </div>
  );
}

export default AddOrEditSubCategoryModal;
