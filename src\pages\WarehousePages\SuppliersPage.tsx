import { useParams } from 'react-router-dom';

import SuppliersPageOverview from '@/components/WarehouseComponents/SuppliersPageComponents/SuppliersPageOverview';
import { ProtectedRoute } from '@/utils/ProtectedRoutes';

function SuppliersPage() {
  const { warehouseId } = useParams();
  return (
    <ProtectedRoute>
      <SuppliersPageOverview warehouseId={warehouseId ?? ''} />
    </ProtectedRoute>
  );
}

export default SuppliersPage;
