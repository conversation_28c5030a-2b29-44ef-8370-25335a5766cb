import { GetCourierSettingsListResponse } from '@/types/warehouseTypes/settingsTypes';

import { TagTypes } from '@/redux/tag-types';
import BaseApi from '../baseApi';
import { GetWebhooksListResponse } from '../warehouseApis/webhookApis';

const OrgBasicApis = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getOrgCouriers: builder.query<GetCourierSettingsListResponse, any>({
      query: (params) => ({
        url: `/orgs/couriers`,
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP],
    }),

    getSingleOrgDetails: builder.query({
      query: (id) => ({
        url: `/orgs/${id}`,
        method: 'GET',
      }),
      providesTags: [TagTypes.ORGANIZATIONS],
    }),
    getOrgWebhooks: builder.query<GetWebhooksListResponse, any>({
      query: (params) => ({
        url: `/orgs/webhooks`,
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP],
    }),
  }),
});

export const {
  useGetOrgCouriersQuery,
  useGetSingleOrgDetailsQuery,
  useGetOrgWebhooksQuery,
} = OrgBasicApis;
