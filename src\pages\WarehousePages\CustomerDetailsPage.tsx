import { useParams } from 'react-router-dom';

import CustomerDetailsPageOverview from '@/components/WarehouseComponents/CustomerPageComponents/CustomerDetailsPageOverview';

function CustomerDetailsPage() {
  const { customerId, shopId, warehouseId } = useParams();

  return (
    <div>
      <CustomerDetailsPageOverview
        customerId={customerId ?? ''}
        shopId={shopId ?? ''}
        warehouseId={warehouseId ?? ''}
      />
    </div>
  );
}

export default CustomerDetailsPage;
