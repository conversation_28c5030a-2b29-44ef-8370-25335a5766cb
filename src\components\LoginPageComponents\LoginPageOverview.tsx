/* eslint-disable jsx-a11y/label-has-associated-control */
import Cookies from 'js-cookie';
import { Eye, EyeOff } from 'lucide-react';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

import { useLoginMutation } from '@/redux/api/loginApi';
import { ROUTES } from '@/Routes';

function LoginPageOverview() {
  const navigate = useNavigate();
  const [login, { isLoading }] = useLoginMutation();
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [passwordFieldType, setPasswordFieldType] =
    useState<string>('password');

  const handleSubmit = async (e: any) => {
    e.preventDefault();

    toast.promise(
      login({
        username,
        password,
      }).unwrap(),
      {
        pending: 'Please wait Logging in...',
        success: {
          render({ data: res }) {
            if (res.statusCode === 200 || res?.statusCode === 201) {
              Cookies.set('accessToken', res?.data?.accessToken);
              Cookies.set('refreshToken', res?.data?.refreshToken);
              Cookies.set('type', res?.data?.type);
              if (res?.data?.type === 'CUSTOMER') {
                Cookies.set('organizationId', res?.data?.organizationId);
                navigate(ROUTES.ORGANIZATION.DASHBOARD);
              } else if (res?.data?.type === 'EMPLOYEE') {
                Cookies.set('organizationId', res?.data?.organizationId);
                navigate(ROUTES.HOME);
              } else if (res?.data?.type === 'SUPER_ADMIN') {
                navigate(ROUTES.SUPER_ADMIN.DASHBOARD);
              }
            }
            return 'Logged in Successfully';
          },
        },
        error: {
          render({ data: error }) {
            console.log(error);
            return 'Incorrect Username Or Password';
          },
        },
      },
    );
  };

  return (
    <div className="flex h-screen w-full bg-gray-50">
      {/* Left Section - Image */}
      <div className="hidden w-1/2 bg-[#140364bf] md:block">
        <div className="flex h-full flex-col items-center justify-center bg-gradient-to-br from-[#140364bf] to-[#140364bf] p-8">
          <div className="mb-8 text-center text-white">
            <h1 className="mb-2 text-4xl font-bold">Retail Pilot</h1>
            <p className="text-xl">Streamline your retail operations</p>
          </div>
          <div className="relative h-96 w-96">
            <div className="absolute inset-0 rounded-lg bg-white/10 backdrop-blur-sm" />
            <div className="absolute inset-0 flex items-center justify-center">
              <svg
                className="h-48 w-48 text-white"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="1.5"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M9 17.25v1.007a3 3 0 01-.879 2.122L7.5 21h9l-.621-.621A3 3 0 0115 18.257V17.25m6-12V15a2.25 2.25 0 01-2.25 2.25H5.25A2.25 2.25 0 013 15V5.25m18 0A2.25 2.25 0 0018.75 3H5.25A2.25 2.25 0 003 5.25m18 0V12a2.25 2.25 0 01-2.25 2.25H5.25A2.25 2.25 0 013 12V5.25"
                />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Right Section - Login Form */}
      <div className="flex w-full items-center justify-center px-4 md:w-1/2">
        <div className="w-full max-w-md">
          <div className="mb-6 text-center">
            <h2 className="mb-2 text-3xl font-bold text-gray-900">
              Welcome Back
            </h2>
            <p className="text-gray-600">
              Sign in to your Retail Pilot account
            </p>
          </div>

          <form className="space-y-6" onSubmit={handleSubmit}>
            <div className="space-y-2">
              <label
                htmlFor="username"
                className="block text-sm font-medium text-gray-700"
              >
                Username
              </label>
              <input
                id="username"
                type="text"
                required
                className="block w-full rounded-lg border border-gray-300 bg-white px-4 py-3 text-gray-900 focus:border-[#140364bf] focus:outline-none focus:ring-1 focus:ring-[#140364bf]"
                placeholder="Enter your username"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <label
                htmlFor="password"
                className="block text-sm font-medium text-gray-700"
              >
                Password
              </label>
              <div className="relative">
                <input
                  id="password"
                  type={passwordFieldType}
                  required
                  className="block w-full rounded-lg border border-gray-300 bg-white px-4 py-3 text-gray-900 focus:border-[#140364bf] focus:outline-none focus:ring-1 focus:ring-[#140364bf]"
                  placeholder="Enter your password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                />
                <button
                  type="button"
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
                  onClick={() =>
                    setPasswordFieldType(
                      passwordFieldType === 'text' ? 'password' : 'text',
                    )
                  }
                >
                  {passwordFieldType === 'text' ? (
                    <EyeOff size={20} />
                  ) : (
                    <Eye size={20} />
                  )}
                </button>
              </div>
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="flex w-full justify-center rounded-lg bg-[#140364bf] px-4 py-3 text-sm font-medium text-white shadow-sm hover:bg-[#140364bf] focus:outline-none focus:ring-2 focus:ring-[#140364bf] focus:ring-offset-2 disabled:opacity-75"
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <div className="h-5 w-5 animate-spin rounded-full border-2 border-white border-t-transparent" />
                  <span className="ml-2">Signing in...</span>
                </div>
              ) : (
                'Login'
              )}
            </button>
          </form>
        </div>
      </div>
    </div>
    // <div>
    //   <div className="grid grid-cols-12 gap-4">
    //     <div className="col col-span-12 hidden lg:col-span-6 lg:block">
    //       <img
    //         src="https://i.ibb.co/n6bNS6r/photo-2024-06-08-23-57-29.jpg"
    //         alt=""
    //         className="max-h-[100vh] w-full"
    //       />
    //     </div>
    //     <div className="col col-span-12 flex h-[100vh] items-center justify-center p-4 lg:col-span-6 lg:p-0">
    //       <form className="flex flex-col gap-4" onSubmit={handleSubmit}>
    //         <h2 className="text-center text-4xl font-black">Softs.ai</h2>
    //         <span className="text-center text-2xl font-bold text-[#50565F]">
    //           Welcome to Softs.ai! 👋🏻
    //         </span>
    //         <span className="text-center">
    //           Please sign-in to your account and start the adventure
    //         </span>
    //         <Input
    //           required
    //           type="text"
    //           placeholder="Username"
    //           onChange={(e: any) => setUsername(e.target.value)}
    //         />
    //         <div className="relative">
    //           <Input
    //             required
    //             type={passwordFieldType}
    //             placeholder="password"
    //             onChange={(e: any) => setPassword(e.target.value)}
    //           />
    //           <button
    //             type="button"
    //             className="absolute right-[4px] top-[3px] bg-white p-2 text-slate-500"
    //             onClick={() =>
    //               passwordFieldType === 'text'
    //                 ? setPasswordFieldType('password')
    //                 : setPasswordFieldType('text')
    //             }
    //           >
    //             {passwordFieldType === 'text' ? (
    //               <span>
    //                 <Eye size={17} />
    //               </span>
    //             ) : (
    //               <span>
    //                 <EyeOff size={17} />
    //               </span>
    //             )}
    //           </button>
    //         </div>
    //         <button
    //           disabled={isLoading}
    //           type="submit"
    //           className="rounded-lg bg-[#140364bf] px-8 py-2 font-semibold text-white disabled:bg-gray-500"
    //         >
    //           {isLoading ? (
    //             <div className="flex h-6 items-center justify-center">
    //               <div className="h-6 w-6 animate-spin rounded-full border-t-4 border-blue-500" />
    //             </div>
    //           ) : (
    //             'Submit'
    //           )}
    //         </button>
    //       </form>
    //     </div>
    //   </div>
    // </div>
  );
}

export default LoginPageOverview;
