import Cookies from 'js-cookie';
import { useLocation, useNavigate } from 'react-router-dom';

import ShopOrderPageFilterModal from '../ShopOrdersPageComponents/ShopOrderPageFilterModal';

import ExportButton from '@/components/reusable/Buttons/ExportButton';
import FilterButton from '@/components/reusable/Buttons/FilterButton';
import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import CustomDateFilterInput from '@/components/reusable/Inputs/CustomDateFilterInput';
import CustomSelectForFilter from '@/components/reusable/Inputs/CustomSelectForFilter';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import OrderStatusViewer from '@/components/reusable/OrdersPagesReusableComponents/OrderStatusViewer';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import { useGetShopSellersQuery } from '@/redux/api/shopApis/shopEmployeesApis';
import { useGetShopSalesReportQuery } from '@/redux/api/shopApis/shopReportsApis';
import { ROUTES } from '@/Routes';
import {
  OrderSinglePaymentDetails,
  ShopOrderDetails,
} from '@/types/shopTypes/shopOrderTypes';
import { SingleSellerDetails } from '@/types/warehouseTypes/sellersTypes';
import { formatNumberWithComma } from '@/utils/formatNumberWithComma';
import { handleGenerateSalesSummaryCsv } from '@/utils/GenerateCsv';
import { generateFilterParams } from '@/utils/generateFilterParams';
import { handleGenerateSalesReportPdf } from '@/utils/GenerateReportPdf';
import { statusList } from '@/utils/staticData';

interface Props {
  shopId: string;
  warehouseId: string;
}

function ShopSalesReportPageOverview({ shopId, warehouseId }: Props) {
  const organizationId = Cookies.get('organizationId');
  const navigate = useNavigate();
  const router = new URLSearchParams(useLocation().search);
  const orderStatus = router.get('orderStatus');
  const createdById = router.get('createdById');
  const startDate = router.get('startDate') || `${new Date().toISOString()}`;
  const endDate = router.get('endDate') || `${new Date().toISOString()}`;
  const { data, isLoading, isFetching } = useGetShopSalesReportQuery(
    {
      organizationId,
      warehouseId,
      shopId,
      type: 'custom',
      startDate,
      endDate,
      orderStatus: orderStatus ?? undefined,
      createdById: createdById ?? undefined,
    },
    { skip: !(shopId && organizationId && startDate && endDate) },
  );
  const { data: employees, isLoading: isEmployeesDataLoading } =
    useGetShopSellersQuery(
      { shopId, role: 'SHOP_BILLER', organizationId },
      { skip: !(shopId && warehouseId) },
    );

  const handleFilter = (fieldName: string, value: string) => {
    const query = generateFilterParams(fieldName, value);
    navigate(ROUTES.SHOP.SALES_REPORT(shopId, query));
  };
  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-1">
        <div className="flex items-center gap-x-2">
          <div className="search-title-and-btn flex items-center gap-x-3">
            <div className="relative">
              <div className="block xl:hidden">
                <FilterButton handleClick={() => console.log('higbig')} />
              </div>
              <div className="block xl:hidden">
                <ShopOrderPageFilterModal />
              </div>
            </div>
          </div>
          <div className="hidden xl:block">
            <div className="flex items-center gap-x-2">
              <CustomDateFilterInput
                value={startDate}
                placeholder="Select Start Date"
                label="Start Date"
                handleChange={(value: string) =>
                  handleFilter('startDate', value)
                }
              />
              <CustomDateFilterInput
                value={endDate}
                placeholder="Select Start Date"
                label="End Date"
                handleChange={(value: string) => handleFilter('endDate', value)}
                minimumDate={startDate}
              />
              <CustomSelectForFilter
                options={statusList}
                selectedValue={orderStatus ?? ''}
                handleSelect={(e) => handleFilter('orderStatus', e)}
                placeHolder="Status"
              />
              <CustomSelectForFilter
                options={
                  !isEmployeesDataLoading && employees?.data?.length
                    ? employees?.data?.map((sin: SingleSellerDetails) => {
                        return {
                          label: sin.User?.name,
                          value: sin.User?.id,
                        };
                      })
                    : []
                }
                selectedValue={createdById ?? ''}
                handleSelect={(e) => handleFilter('createdById', e)}
                placeHolder="Biller"
              />
            </div>
          </div>
        </div>
      </div>
      {!isLoading && !isFetching ? (
        <div>
          <div className="tableTop w-full">
            <p>Sales Report</p>
            <div className="ml-4">
              <ExportButton
                totalCount={data?.data?.orderList.length ?? 0}
                handleExportCsv={() =>
                  handleGenerateSalesSummaryCsv({
                    orderList: data?.data?.orderList,
                    startDate,
                    endDate,
                  })
                }
                handleExportPdf={() => handleGenerateSalesReportPdf(data?.data)}
              />
            </div>
          </div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {data?.data?.orderList?.length ? (
              <div className="full-table-box h-customExc">
                <table className="full-table">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="tableHead">No</th>
                      <th className="tableHead">Order No</th>
                      <th className="tableHead table-col-width">Name</th>
                      <th className="tableHead">Phone</th>
                      <th className="tableHead">Status</th>
                      <th className="tableHead">Payment Status</th>
                      <th className="tableHead">Total</th>
                      <th className="tableHead">Paid</th>
                      <th className="tableHead">Due</th>
                      <th className="tableHeadLeftAlign">Seller</th>
                      <th className="tableHeadLeftAlign">Payment Method</th>
                      <th className="tableHead">Created At</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y bg-slate-200">
                    {data?.data?.orderList?.map(
                      (order: ShopOrderDetails, index: number) => (
                        <tr key={order?.id}>
                          <td className="tableData">{index + 1}</td>
                          <td className="tableData">{order?.serialNo}</td>
                          <td className="tableData table-col-width">
                            {order?.Customer?.name}
                          </td>
                          <td className="tableData">
                            {order?.Customer?.mobileNumber}
                          </td>
                          <td className="tableData">
                            <OrderStatusViewer status={order?.orderStatus} />
                          </td>
                          <td className="tableData">
                            <OrderStatusViewer status={order?.paymentStatus} />
                          </td>
                          <td className="tableData">
                            {formatNumberWithComma(
                              Number(order?.grandTotal) +
                                Number(order?.deliveryCharge),
                            )}
                          </td>
                          <td className="tableData">
                            {formatNumberWithComma(order?.totalPaid)}
                          </td>
                          <td className="tableData">
                            {formatNumberWithComma(order?.totalDue)}
                          </td>
                          <td className="tableDataLeftAlign">
                            {order?.Employee?.User?.name}
                          </td>
                          <td className="tableDataLeftAlign">
                            {order?.Payment?.map(
                              (singlePayment: OrderSinglePaymentDetails) => (
                                <div key={singlePayment?.id}>
                                  {singlePayment?.paymentMethod} -{' '}
                                  {formatNumberWithComma(singlePayment?.amount)}
                                </div>
                              ),
                            )}
                          </td>
                          <td className="tableData">
                            <DateAndTimeViewer date={order?.createdAt} />
                          </td>
                        </tr>
                      ),
                    )}
                    <tr>
                      <td className="tableData" colSpan={5} />
                      <td className="tableData">Total Order Amount</td>
                      <td className="tableData">
                        {formatNumberWithComma(data?.data?.totalOrderAmount)}
                      </td>
                      <td className="tableData" colSpan={5} />
                    </tr>
                    <tr>
                      <td className="tableData" colSpan={5} />
                      <td className="tableData">Cash Received</td>
                      <td className="tableData">
                        {formatNumberWithComma(data?.data?.cashReceive)}
                      </td>
                      <td className="tableData" colSpan={5} />
                    </tr>
                    <tr>
                      <td className="tableData" colSpan={5} />
                      <td className="tableData">Due Amount</td>
                      <td className="tableData">
                        {formatNumberWithComma(data?.data?.totalDue)}
                      </td>
                      <td className="tableData" colSpan={5} />
                    </tr>
                  </tbody>
                </table>
              </div>
            ) : (
              <NoResultFound pageType="data" />
            )}
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={10} tableRow={6} />
      )}
    </div>
  );
}

export default ShopSalesReportPageOverview;
