import { SingleShopDetails } from '../shopTypes';
import { Warehouse } from '../shopTypes/shopTransferAmountToOwnerTypes';

import { Pagination } from '@/redux/commonTypes';

export interface GetSellersResponse {
  success: boolean;
  statusCode: number;
  message: string;
  data: SingleSellerDetails[];
  pagination: Pagination;
}
export interface ArektaNameDiyaDensResponse {
  success: boolean;
  statusCode: number;
  message: string;
  data: {
    result: SingleSellerDetails[];
    warehouse: Warehouse;
  };
  pagination: Pagination;
}

export interface SingleSellerDetails {
  name: string;
  mobileNumber: string;
  imgUrl: any;
  id: string;
  createdAt: string;
  updatedAt: string;
  lastSalary: number;
  currentSalary: number;
  type: string;
  userId: string;
  warehouseId: string;
  assignedShopId: string;
  serialNo: number;
  isActive: boolean;
  orderCount: number;
  totalSales: number;
  User: SingleSellerUserDetails;
  AssignedShop: {
    name: string;
  };
  EmployeePermission: EmployeePermissionDetails[];
}

export interface SingleSellerUserDetails {
  id: string;
  createdAt: string;
  updatedAt: string;
  name: string;
  email: any;
  username: string;
  mobileNumber: string;
  password: string;
  refreshToken: any;
  imgUrl: any;
  organizationLimit: number;
  warehouseLimit: number;
  shopLimit: number;
  type: string;
}

export interface EmployeePermissionDetails {
  id: string;
  createdAt: string;
  updatedAt: string;
  employeeId: string;
  assignedShopId: string;
  warehouseId: string;
  isActive: boolean;
  role: string;
  createdById: string;
  AssignedShop: SingleShopDetails;
  Warehouse: {
    id: string;
    name: string;
  };
}
