import { CreatedBy, Pagination } from '@/redux/commonTypes';
import { SingleSubCategory } from './subCategoriesTypes';

export interface GetCategoriesResponse {
  success: boolean;
  statusCode: number;
  message: string;
  data: SingleCategoryDetails[];
  pagination: Pagination;
}

export interface SingleCategoryDetails {
  id: string;
  createdAt: string;
  updatedAt: string;
  name: string;
  imgUrl: any;
  shopId: string;
  ProductSubCategory: SingleSubCategory[];
  CreatedBy: CreatedBy;
}
