import ExportButton from '@/components/reusable/Buttons/ExportButton';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import { useGetShopStockReportQuery } from '@/redux/api/shopApis/shopReportsApis';
import { useAppSelector } from '@/redux/hooks';
import { SingleProductStockReport } from '@/types/shopTypes/shopStockTransferReportTypes';
import { handleShopStockReportPdf } from '@/utils/GenerateReportPdf';
import ProtectedDataViewer from '@/utils/ProtectedDataViewer';
import { handleGenerateCurrentStockCsv } from '@/utils/ReportExport/ExportCurrentStockReport';

interface Props {
  shopId: string;
  warehouseId?: string;
}

function ShopStockReportPageOverview({ shopId }: Props) {
  const { shopDetails } = useAppSelector((state) => state);

  const { data, isLoading, isFetching } = useGetShopStockReportQuery(
    {
      shopId,
      status: 'ASSIGNED',
    },
    { skip: !shopId },
  );

  return (
    <div>
      {!isLoading && !isFetching ? (
        <div>
          <div className="tableTop w-full">
            <p>Stock Report</p>
            <div className="flex items-center">
              <p>Total : {data?.data?.result?.length}</p>
              <div className="ml-4">
                <ExportButton
                  totalCount={data?.data?.result.length ?? 0}
                  handleExportCsv={() =>
                    handleGenerateCurrentStockCsv({
                      data,
                      userRole: 'ADMIN',
                    })
                  }
                  handleExportPdf={() =>
                    handleShopStockReportPdf(data, shopDetails)
                  }
                />
              </div>
            </div>
          </div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {data?.data?.result?.length ? (
              <div className="full-table-box h-customExc">
                <table className="full-table">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="tableHead">No</th>
                      <th className="tableHead table-col-width">Name</th>
                      <th className="tableHead">Quantity</th>
                      <ProtectedDataViewer>
                        <th className="tableHead">Purchase Price</th>
                      </ProtectedDataViewer>
                      <th className="tableHead">Retail Price</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y bg-slate-200">
                    {data?.data?.result?.map(
                      (product: SingleProductStockReport, index: number) => (
                        <tr key={product?.id}>
                          <td className="tableData">{index + 1}</td>
                          <td className="tableData table-col-width">
                            {product?.name}
                          </td>
                          <td className="tableData">{product?.quantity}</td>
                          <ProtectedDataViewer>
                            <td className="tableData">
                              {product?.purchasePrice}
                            </td>
                          </ProtectedDataViewer>
                          <td className="tableData">{product?.retailPrice}</td>
                        </tr>
                      ),
                    )}
                    <tr>
                      <td className="tableData" />
                      <td className="tableData table-col-width">Total</td>
                      <td className="tableData">{data?.data?.totalQuantity}</td>
                      <ProtectedDataViewer>
                        <td className="tableData">
                          {data?.data?.totalPurchasePrice}
                        </td>
                      </ProtectedDataViewer>
                      <td className="tableData">
                        {data?.data?.totalRetailPrice}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            ) : (
              <NoResultFound pageType="products" />
            )}
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={5} tableRow={6} />
      )}
    </div>
  );
}

export default ShopStockReportPageOverview;
