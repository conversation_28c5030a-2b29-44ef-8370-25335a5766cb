import * as TooltipPrimitive from '@radix-ui/react-tooltip';
import {
  ArrowLeft,
  ArrowRight,
  Barcode,
  Check,
  EyeIcon,
  FileScan,
  FileText,
  HandCoins,
  LocateFixed,
  NotepadText,
  PencilLine,
  Printer,
  Sheet,
  ShoppingBag,
  ShoppingCart,
  Trash2,
  Truck,
  Undo2,
  Wallet,
  X,
} from 'lucide-react';

import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface CommonButtonProps {
  handleClick: () => void;
}
interface CommonButtonProps2 {
  handleClick: () => void;
  disabled?: boolean;
}
export function EditButton({ handleClick }: CommonButtonProps) {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={200}>
        <TooltipTrigger>
          <button
            type="button"
            onClick={handleClick}
            className="commonActionButton editButton"
          >
            <PencilLine size={18} className="editButtonIcon" />
          </button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Edit</p>
          <TooltipPrimitive.Arrow
            width={11}
            height={5}
            className="tooltipArrow"
          />
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
export function TransparentEditButton({ handleClick }: CommonButtonProps) {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={200}>
        <TooltipTrigger>
          <button
            type="button"
            onClick={handleClick}
            className="commonTransparentActionButton transparentEditButton"
          >
            <PencilLine size={18} />
          </button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Edit</p>
          <TooltipPrimitive.Arrow
            width={11}
            height={5}
            className="tooltipArrow"
          />
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

export function DeleteButton({ handleClick }: CommonButtonProps) {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={200}>
        <TooltipTrigger>
          <button
            type="button"
            onClick={handleClick}
            className="commonActionButton deleteButton"
          >
            <Trash2 size={18} className="deleteButtonIcon" />
          </button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Delete</p>
          <TooltipPrimitive.Arrow
            width={11}
            height={5}
            className="tooltipArrow"
          />
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
export function TransparentDeleteButton({ handleClick }: CommonButtonProps) {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={200}>
        <TooltipTrigger>
          <button
            type="button"
            onClick={handleClick}
            className="commonTransparentActionButton transparentDeleteButton"
          >
            <Trash2 size={18} />
          </button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Delete</p>
          <TooltipPrimitive.Arrow
            width={11}
            height={5}
            className="tooltipArrow"
          />
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

export function EyeButton({ handleClick }: CommonButtonProps) {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={200}>
        <TooltipTrigger>
          <button
            type="button"
            onClick={handleClick}
            className="commonActionButton watchButton"
          >
            <EyeIcon size={18} className="watchButtonIcon" />
          </button>
        </TooltipTrigger>
        <TooltipContent>
          <p>View Details</p>
          <TooltipPrimitive.Arrow
            width={11}
            height={5}
            className="tooltipArrow"
          />
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
export function LocationButton({ handleClick }: CommonButtonProps) {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={200}>
        <TooltipTrigger>
          <button
            type="button"
            onClick={handleClick}
            className="commonActionButton watchButton"
          >
            <LocateFixed size={18} className="watchButtonIcon" />
          </button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Stock Location</p>
          <TooltipPrimitive.Arrow
            width={11}
            height={5}
            className="tooltipArrow"
          />
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
export function GreenAcceptButton({ handleClick }: CommonButtonProps) {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={200}>
        <TooltipTrigger>
          <button
            type="button"
            onClick={handleClick}
            className="commonActionButton watchButton"
          >
            <Check size={18} className="watchButtonIcon" />
          </button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Accept</p>
          <TooltipPrimitive.Arrow
            width={11}
            height={5}
            className="tooltipArrow"
          />
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
export function RedCloseButton({ handleClick }: CommonButtonProps) {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={200}>
        <TooltipTrigger>
          <button
            type="button"
            onClick={handleClick}
            className="commonActionButton watchButton"
          >
            <X size={18} className="watchButtonIcon" />
          </button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Close</p>
          <TooltipPrimitive.Arrow
            width={11}
            height={5}
            className="tooltipArrow"
          />
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
export function TransparentEyeButton({ handleClick }: CommonButtonProps) {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={200}>
        <TooltipTrigger>
          <button
            type="button"
            onClick={handleClick}
            className="commonTransparentActionButton transparentWatchButton"
          >
            <EyeIcon size={18} />
          </button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Watch</p>
          <TooltipPrimitive.Arrow
            width={11}
            height={5}
            className="tooltipArrow"
          />
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

export function PayButton({ handleClick }: CommonButtonProps) {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={200}>
        <TooltipTrigger>
          <button
            type="button"
            onClick={handleClick}
            className="commonActionButton payButton"
          >
            <HandCoins size={18} className="payButtonIcon" />
          </button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Pay</p>
          <TooltipPrimitive.Arrow
            width={11}
            height={5}
            className="tooltipArrow"
          />
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

export function PrintButton({ handleClick }: CommonButtonProps) {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={200}>
        <TooltipTrigger>
          <button
            type="button"
            onClick={handleClick}
            className="commonActionButton printButton"
          >
            <Printer size={18} className="printButtonIcon" />
          </button>
        </TooltipTrigger>
        <TooltipContent className="TooltipContent">
          <p>Pos Invoice</p>
          <TooltipPrimitive.Arrow
            width={11}
            height={5}
            className="tooltipArrow"
          />
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
export function TransparentPrintButtonTop({ handleClick }: CommonButtonProps) {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={200}>
        <TooltipTrigger>
          <button
            type="button"
            onClick={handleClick}
            className="commonTransparentActionButton transparentPrintButton"
          >
            <Printer size={18} />
          </button>
        </TooltipTrigger>
        <TooltipContent side="top" align="center" className="tooltipContent">
          <p>Print</p>
          <div className="tooltipArrow">
            <TooltipPrimitive.Arrow
              width={11}
              height={5}
              className="tooltipArrow"
            />
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
export function TransparentCSVButtonTop({ handleClick }: CommonButtonProps) {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={200}>
        <TooltipTrigger>
          <button
            type="button"
            onClick={handleClick}
            className="commonTransparentActionButton transparentPrintButton"
          >
            <Sheet size={18} />
          </button>
        </TooltipTrigger>
        <TooltipContent side="top" align="center" className="tooltipContent">
          <p>Download CSV</p>
          <div className="tooltipArrow">
            <TooltipPrimitive.Arrow
              width={11}
              height={5}
              className="tooltipArrow"
            />
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

export function PrintA4Button({ handleClick }: CommonButtonProps) {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={200}>
        <TooltipTrigger>
          <button
            type="button"
            onClick={handleClick}
            className="commonActionButton printA4Button"
          >
            <FileText size={18} className="printA4ButtonIcon" />
          </button>
        </TooltipTrigger>
        <TooltipContent>
          <p>A4 Invoice</p>
          <TooltipPrimitive.Arrow
            width={11}
            height={5}
            className="tooltipArrow"
          />
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
export function AddNoteButton({ handleClick }: CommonButtonProps) {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={200}>
        <TooltipTrigger>
          <button
            type="button"
            onClick={handleClick}
            className="commonActionButton printA4Button"
          >
            <NotepadText size={18} className="printA4ButtonIcon" />
          </button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Add Note</p>
          <TooltipPrimitive.Arrow
            width={11}
            height={5}
            className="tooltipArrow"
          />
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

export function DueButton({ handleClick }: CommonButtonProps) {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={200}>
        <TooltipTrigger>
          <button
            type="button"
            onClick={handleClick}
            className="commonActionButton dueButton"
          >
            <Wallet size={18} className="dueButtonIcon" />
          </button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Collect Due</p>
          <TooltipPrimitive.Arrow
            width={11}
            height={5}
            className="tooltipArrow"
          />
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

export function CartButton({ handleClick }: CommonButtonProps) {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={200}>
        <TooltipTrigger>
          <button
            type="button"
            onClick={handleClick}
            className="commonActionButton cartButton"
          >
            <ShoppingCart size={18} className="cartButtonIcon" />
          </button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Cart</p>
          <TooltipPrimitive.Arrow
            width={11}
            height={5}
            className="tooltipArrow"
          />
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

export function CartButtonv2({ handleClick, disabled }: CommonButtonProps2) {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={200}>
        <TooltipTrigger>
          <button
            type="button"
            onClick={handleClick}
            disabled={disabled}
            className={`commonActionButton cartButton ${disabled ? 'disabled' : ''}`}
          >
            <ShoppingCart size={18} className="cartButtonIcon" />
          </button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Cart</p>
          <TooltipPrimitive.Arrow
            width={11}
            height={5}
            className="tooltipArrow"
          />
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
export function TransparentCartButton({ handleClick }: CommonButtonProps) {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={200}>
        <TooltipTrigger>
          <button
            type="button"
            onClick={handleClick}
            className="commonTransparentActionButton transparentCartButton"
          >
            <ShoppingCart size={18} />
          </button>
        </TooltipTrigger>
        <TooltipContent side="top" align="center" className="tooltipContent">
          <p>Cart</p>
          <TooltipPrimitive.Arrow
            width={11}
            height={5}
            className="tooltipArrow"
          />
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

export function CourierButton({ handleClick }: CommonButtonProps) {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={200}>
        <TooltipTrigger>
          <button
            type="button"
            onClick={handleClick}
            className="commonActionButton courierButton"
          >
            <Truck size={18} className="courierButtonIcon" />
          </button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Book On Courier</p>
          <TooltipPrimitive.Arrow
            width={11}
            height={5}
            className="tooltipArrow"
          />
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

export function ReturnButton({ handleClick }: CommonButtonProps) {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={200}>
        <TooltipTrigger>
          <button
            type="button"
            onClick={handleClick}
            className="commonActionButton returnButton"
          >
            <Undo2 size={18} className="returnButtonIcon" />
          </button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Return</p>
          <TooltipPrimitive.Arrow
            width={11}
            height={5}
            className="tooltipArrow"
          />
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

export function EntryButton({ handleClick, disabled }: CommonButtonProps2) {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={200}>
        <TooltipTrigger>
          <button
            type="button"
            onClick={handleClick}
            disabled={disabled}
            className={`commonActionButton returnButton ${disabled ? 'disabled' : ''}`}
          >
            <ShoppingBag size={18} className="returnButtonIcon" />
          </button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Stock Entry</p>
          <TooltipPrimitive.Arrow
            width={11}
            height={5}
            className="tooltipArrow"
          />
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

export function BarcodeButton({ handleClick }: CommonButtonProps) {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={200}>
        <TooltipTrigger>
          <button
            type="button"
            onClick={handleClick}
            className="commonTransparentActionButton transparentWatchButton"
          >
            <Barcode size={18} />
          </button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Barcode</p>
          <TooltipPrimitive.Arrow
            width={11}
            height={5}
            className="tooltipArrow"
          />
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
export function BarcodeButtonv2({ handleClick }: CommonButtonProps) {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={200}>
        <TooltipTrigger>
          <button
            type="button"
            onClick={handleClick}
            className="commonActionButton barcodeButton"
          >
            <Barcode size={18} className="barcodeButtonIcon" />
          </button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Barcode</p>
          <TooltipPrimitive.Arrow
            width={11}
            height={5}
            className="tooltipArrow"
          />
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

export function BarcodePDFButton({ handleClick }: CommonButtonProps) {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={200}>
        <TooltipTrigger>
          <button
            type="button"
            onClick={handleClick}
            className="commonActionButton barcodeButton"
          >
            <FileScan size={18} className="barcodeButtonIcon" />
          </button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Barcode PDF</p>
          <TooltipPrimitive.Arrow
            width={11}
            height={5}
            className="tooltipArrow"
          />
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
export function CancelButton({ handleClick }: CommonButtonProps) {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={200}>
        <TooltipTrigger>
          <button
            type="button"
            onClick={handleClick}
            className="commonActionButton barcodeButton"
          >
            <X size={18} className="barcodeButtonIcon" />
          </button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Cancel</p>
          <TooltipPrimitive.Arrow
            width={11}
            height={5}
            className="tooltipArrow"
          />
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

export function MoveToRightButton({ handleClick }: CommonButtonProps) {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={200}>
        <TooltipTrigger>
          <button
            type="button"
            onClick={handleClick}
            className="commonActionButton barcodeButton"
          >
            <ArrowRight size={18} className="barcodeButtonIcon" />
          </button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Mark As Found</p>
          <TooltipPrimitive.Arrow
            width={11}
            height={5}
            className="tooltipArrow"
          />
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

export function MoveToLeftButton({ handleClick }: CommonButtonProps) {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={200}>
        <TooltipTrigger>
          <button
            type="button"
            onClick={handleClick}
            className="commonActionButton barcodeButton"
          >
            <ArrowLeft size={18} className="barcodeButtonIcon" />
          </button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Mark As not Found</p>
          <TooltipPrimitive.Arrow
            width={11}
            height={5}
            className="tooltipArrow"
          />
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
