import { useFormik } from 'formik';
import * as Yup from 'yup';

import FilledSubmitButton from '../../reusable/Buttons/FilledSubmitButton';
import ModalTitle from '../../reusable/Modal/ModalTitle';

import CustomTextArea from '@/components/reusable/CustomInputField/CustomTextArea';

interface Props {
  handleClose: () => void;
  handleSubmit: (message: string) => void;
}

const formikInitialValues = {
  message: '',
};

const validation = Yup.object({
  message: Yup.string().required('Message is required'),
});

function SendMessageModal({ handleClose, handleSubmit }: Props) {
  const formik = useFormik({
    initialValues: formikInitialValues,
    validationSchema: validation,

    onSubmit: async (values) => {
      console.log(values);
      handleSubmit(values.message);
    },
  });

  return (
    <div className="flex w-[400px] flex-col gap-4 rounded-xl bg-white p-4">
      <ModalTitle text="Send Message" handleClose={handleClose} />
      <form
        onSubmit={formik.handleSubmit}
        className="flex w-full flex-col items-center justify-center gap-2"
      >
        <CustomTextArea
          placeholder="Type Your Message Here"
          name="message"
          label="Message"
          formik={formik}
        />
        <div className="mt-[10px] flex w-full items-center justify-center">
          <FilledSubmitButton text="Send Message" isLoading={false} />
        </div>
      </form>
    </div>
  );
}

export default SendMessageModal;
