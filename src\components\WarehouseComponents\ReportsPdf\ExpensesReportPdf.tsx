import { Page, Text, View } from '@react-pdf/renderer';

import PrintedTime from './PrintedTime';
import { reportPdfStyles } from './ReportPdfStyles';
import SoftwareMarketingOnPdf from './SoftwareMarketingOnPdf';
import WarehousePdfHeader from './WarehousePdfHeader';

import { WarehouseDetailsInRedux } from '@/redux/slice/warehouseSlice';
import { ShopExpenseDetails } from '@/types/shopTypes/shopExpensesTypes';
import { ShopExpensesReportResponse } from '@/types/shopTypes/shopStockTransferReportTypes';
import { truncateText } from '@/utils/stringTruncate';

interface Props {
  expenseReportData?: ShopExpensesReportResponse;
  warehouseDetails: WarehouseDetailsInRedux;
}
// need to fix
function ExpensesReportPdf({ expenseReportData, warehouseDetails }: Props) {
  return (
    <Page size="A4" style={reportPdfStyles.page}>
      <View>
        <WarehousePdfHeader warehouseDetails={warehouseDetails} />
        <PrintedTime />

        <View style={reportPdfStyles.table}>
          <View style={reportPdfStyles.tableRow}>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.expenseReportCol,
                reportPdfStyles.expenseReportNoCol,
              ]}
            >
              No
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.expenseReportCol,
                reportPdfStyles.expenseReportNameCol,
              ]}
            >
              Name
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.expenseReportCol,
                reportPdfStyles.expenseReportAmountCol,
              ]}
            >
              Amount
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.expenseReportCol,
                reportPdfStyles.expenseReportLocationCol,
              ]}
            >
              Expense Location
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.expenseReportCol,
                reportPdfStyles.expenseReportCreatedByCol,
              ]}
            >
              Created By
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.tableColLast,
                reportPdfStyles.expenseReportCol,
                reportPdfStyles.expenseReportCreatedAtCol,
              ]}
            >
              Created At
            </Text>
          </View>
          {expenseReportData?.data?.result.map(
            (singleExpense: ShopExpenseDetails, index) => (
              <View
                key={singleExpense.id}
                style={
                  index === Number(expenseReportData?.data?.result?.length) - 1
                    ? [reportPdfStyles.tableRowLast]
                    : [reportPdfStyles.tableRow]
                }
              >
                <Text
                  style={[
                    reportPdfStyles.tableCol,
                    reportPdfStyles.expenseReportCol,
                    reportPdfStyles.expenseReportNoCol,
                  ]}
                >
                  {index + 1}
                </Text>
                <Text
                  style={[
                    reportPdfStyles.tableCol,
                    reportPdfStyles.expenseReportCol,
                    reportPdfStyles.expenseReportNameCol,
                  ]}
                >
                  {truncateText(singleExpense?.name, 35)}
                </Text>
                <Text
                  style={[
                    reportPdfStyles.tableCol,
                    reportPdfStyles.expenseReportCol,
                    reportPdfStyles.expenseReportAmountCol,
                  ]}
                >
                  {singleExpense.amount}
                </Text>
                <Text
                  style={[
                    reportPdfStyles.tableCol,
                    reportPdfStyles.expenseReportCol,
                    reportPdfStyles.expenseReportLocationCol,
                  ]}
                >
                  {singleExpense.Shop?.name ?? '--'}
                </Text>
                <Text
                  style={[
                    reportPdfStyles.tableCol,
                    reportPdfStyles.expenseReportCol,
                    reportPdfStyles.expenseReportCreatedByCol,
                  ]}
                >
                  {singleExpense.CreatedBy?.name ?? '--'}
                </Text>
                <Text
                  style={[
                    reportPdfStyles.tableCol,
                    reportPdfStyles.tableColLast,
                    reportPdfStyles.expenseReportCol,
                    reportPdfStyles.expenseReportCreatedAtCol,
                  ]}
                >
                  {new Intl.DateTimeFormat('en-US', {
                    day: '2-digit',
                    month: 'short',
                    year: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                  }).format(new Date(singleExpense.createdAt))}
                </Text>
              </View>
            ),
          )}
        </View>
        <SoftwareMarketingOnPdf />
      </View>
    </Page>
  );
}

export default ExpensesReportPdf;
