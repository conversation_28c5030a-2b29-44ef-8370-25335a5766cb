import { Ellip<PERSON>V<PERSON><PERSON>, Eye } from 'lucide-react';
import { toast } from 'react-toastify';

import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import Pagination from '@/components/reusable/Pagination/Pagination';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import {
  Menubar,
  MenubarContent,
  MenubarItem,
  MenubarMenu,
  MenubarTrigger,
} from '@/components/ui/menubar';
import { SinglePlanOfSubscription } from '@/redux/api/organizationApis/orgSubscriptionsApis';
import { useGetSubscriptionPlansPlansQuery } from '@/redux/api/superAdminApis/superAdminSubscriptionApis';

function SuperAdminPlansPage() {
  const { data, isLoading, isFetching } = useGetSubscriptionPlansPlansQuery({});

  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-2">
        <div className="flex items-center">
          <div className="search-title-and-btn flex items-center">
            <div className="relative">
              <div className="block xl:hidden">
                {/* <FilterButton
                handleClick={() => setIsFilterModalOpen(!isFilterModalOpen)}
              /> */}
              </div>
              {/* <div
              className={`${isFilterModalOpen ? 'block' : 'hidden'} xl:hidden`}
            >
              <StockListPageFilterModal
                handleClearAndClose={() => setIsFilterModalOpen(false)}
                handleFilter={handleFilter}
              />
            </div> */}
            </div>
          </div>
          <div className="hidden xl:block">
            {/* <div className="flex items-center gap-x-2">
            <StockListFilterOptions handleFilter={handleFilter} />
          </div> */}
            <h2>Filters Will be here</h2>
          </div>
        </div>
      </div>
      {!isLoading && !isFetching ? (
        <div>
          <div className="tableTop w-full">
            <p>Plans</p>
            <div className="flex items-center gap-2">
              <p>Total : {data?.pagination?.total}</p>
            </div>
          </div>
          <div className="full-table-container w-full">
            {data?.data.length ? (
              <div>
                <div className="full-table-box h-custom">
                  <table className="full-table">
                    <thead className="bg-gray-100">
                      <tr>
                        <th className="tableHead">#</th>
                        <th className="tableHead">Plan Name</th>
                        <th className="tableHead">Plan Type</th>
                        <th className="tableHead">Billing Type</th>
                        <th className="tableHead">Billing Cycle</th>
                        <th className="tableHead">Amount</th>
                        <th className="tableHead">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y">
                      {data?.data?.map(
                        (
                          singleSubscription: SinglePlanOfSubscription,
                          index: number,
                        ) => (
                          <tr key={singleSubscription?.id}>
                            <td className="tableData">{index + 1}</td>
                            <td className="tableData">
                              {singleSubscription.name}
                            </td>
                            <td className="tableData">
                              {singleSubscription?.planType}
                            </td>
                            <td className="tableData">Monthly</td>
                            <td className="tableData">--</td>
                            <td className="tableData">
                              {singleSubscription?.priceMonthly}
                            </td>

                            <td className="tableData">
                              <div className="flex items-center justify-center">
                                <Menubar
                                  style={{
                                    border: 'none',
                                    backgroundColor: 'transparent',
                                  }}
                                >
                                  <MenubarMenu>
                                    <MenubarTrigger className="cursor-pointer">
                                      <EllipsisVertical />
                                    </MenubarTrigger>
                                    <MenubarContent
                                      style={{
                                        marginRight: '25px',
                                        borderColor: 'black',
                                      }}
                                    >
                                      <MenubarItem
                                        onClick={() =>
                                          toast.success('View Details Clicked')
                                        }
                                        className="flex cursor-pointer items-center gap-1 bg-primary font-semibold text-white"
                                      >
                                        <Eye size={20} />
                                        <span>View Details</span>
                                      </MenubarItem>
                                    </MenubarContent>
                                  </MenubarMenu>
                                </Menubar>
                              </div>
                            </td>
                          </tr>
                        ),
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            ) : (
              <NoResultFound pageType="stock" />
            )}
          </div>
          <div className="pagination-box flex justify-end rounded bg-white p-3">
            <Pagination
              totalCount={data?.pagination?.total}
              totalPages={Math.ceil(
                Number(data?.pagination?.total) /
                  Number(data?.pagination?.limit),
              )}
            />
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={12} tableRow={6} />
      )}
    </div>
  );
}

export default SuperAdminPlansPage;
