export const generateFilterParams = (fieldName: string, value: string) => {
  // Get the current URL search parameters
  const currentParams = new URLSearchParams(window.location.search);
  const limit = currentParams.get('limit');

  // Update the specific field with the new value
  if (fieldName === 'page') {
    currentParams.set('page', value);
    if (limit) {
      currentParams.set('limit', limit.toString());
    }
  } else if (fieldName === 'limit') {
    currentParams.set('limit', value);
    currentParams.set('page', '1');
  } else if (!value) {
    currentParams.delete(fieldName);
  } else {
    currentParams.set(fieldName, value);
    currentParams.set('page', '1'); // Reset to page 1 whenever filter changes
  }

  // Construct the new query string
  const query = currentParams.toString();
  return query;
};
