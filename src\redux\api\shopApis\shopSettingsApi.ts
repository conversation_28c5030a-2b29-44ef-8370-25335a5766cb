import BaseApi from '../baseApi';

import { TagTypes } from '@/redux/tag-types';

const ShopSettingsApis = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getShopSettings: builder.query<GetShopSettingsResponse, any>({
      query: (id) => ({
        url: `/shop/settings/${id}`,
        method: 'GET',
      }),
      providesTags: [TagTypes.BRAND],
    }),
    updateShopSettings: builder.mutation({
      query: ({ data, id }) => ({
        url: `/shop/settings/${id}`,
        method: 'PATCH',
        data,
      }),
      invalidatesTags: [TagTypes.BRAND],
    }),
  }),
});

export const { useGetShopSettingsQuery, useUpdateShopSettingsMutation } =
  ShopSettingsApis;

export interface GetShopSettingsResponse {
  success: boolean;
  statusCode: number;
  message: string;
  data: ShopSettingsData;
}

export interface ShopSettingsData {
  id: string;
  createdAt: string;
  updatedAt: string;
  shopId: string;
  maximumOrderReturnPolicy: number;
  maximumPaymentMethodEditPolicy: number;
  maximumOrderEditPolicy: number;
  profitMarginPercentage: number;
  returnPolicyText: string;
}
