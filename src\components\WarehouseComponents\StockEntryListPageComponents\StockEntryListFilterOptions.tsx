import { useLocation } from 'react-router-dom';

import CustomDateFilterInput from '@/components/reusable/Inputs/CustomDateFilterInput';
import SearchInput from '@/components/reusable/Inputs/SearchInput';

interface Props {
  handleFilter: (fieldName: string, value: string) => void;
}

function StockEntryListFilterOptions({ handleFilter }: Props) {
  const router = new URLSearchParams(useLocation().search);
  const serialNo = router.get('serialNo');
  const supplierName = router.get('supplierName');
  const supplierInvoiceNo = router.get('supplierInvoiceNo');
  const startDate = router.get('startDate');
  const endDate = router.get('endDate');

  return (
    <div className="flex flex-col gap-3 md:flex-row md:gap-4">
      <SearchInput
        placeholder="Invoice no"
        handleSubmit={(value: string) => handleFilter('serialNo', value)}
        value={serialNo ?? ''}
      />
      <SearchInput
        placeholder="Supplier Name"
        handleSubmit={(value: string) => handleFilter('supplierName', value)}
        value={supplierName ?? ''}
      />
      <SearchInput
        placeholder="Supplier Invoice"
        handleSubmit={(value: string) =>
          handleFilter('supplierInvoiceNo', value)
        }
        value={supplierInvoiceNo ?? ''}
      />
      <CustomDateFilterInput
        value={startDate ?? ''}
        placeholder="Select Start Date"
        label="Start Date"
        handleChange={(value: string) => handleFilter('startDate', value)}
      />
      <CustomDateFilterInput
        value={endDate ?? ''}
        placeholder="Select Start Date"
        label="End Date"
        handleChange={(value: string) => handleFilter('endDate', value)}
        minimumDate={startDate ?? ''}
      />
    </div>
  );
}

export default StockEntryListFilterOptions;
