import { Trash2 } from 'lucide-react';
import { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

import StockDeleteModal from './StockDeleteModal';
import StockListFilterOptions from './StockListFilterOptions';
import StockListPageFilterModal from './StockListPageFilterModal';

import {
  BarcodeButton,
  GreenAcceptButton,
  TransparentDeleteButton,
  TransparentEditButton,
  TransparentEyeButton,
} from '@/components/reusable/Buttons/CommonButtons';
import FilterButton from '@/components/reusable/Buttons/FilterButton';
import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import Modal from '@/components/reusable/Modal/Modal';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import Pagination from '@/components/reusable/Pagination/Pagination';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import { useAcceptStockRestockMutation } from '@/redux/api/shopApis/shopStockApis';
import {
  useDeleteStocksMutation,
  useGetStockListQuery,
} from '@/redux/api/warehouseApis/stockApis';
import { ROUTES } from '@/Routes';
import { SingleStockDetails } from '@/types/warehouseTypes/stockTypes';
import { CalculateDiscountPrice } from '@/utils/CalculateDiscountPrice';
import { generateFilterParams } from '@/utils/generateFilterParams';
import StockName from '@/utils/StockName';

interface Props {
  warehouseId: string;
}
function StockListPageOverview({ warehouseId }: Props) {
  const navigate = useNavigate();
  const [acceptStockRestock] = useAcceptStockRestockMutation();
  const router = new URLSearchParams(useLocation().search);
  const page = router.get('page');
  const productName = router.get('productName');
  const categoryName = router.get('categoryName');
  const status = router.get('status');
  const limit = router.get('limit');
  const barcode = router.get('barcode');
  const productSerialNo = router.get('productSerialNo');
  const [isFilterModalOpen, setIsFilterModalOpen] = useState<boolean>(false);
  const [selectedStocks, setSelectedStocks] = useState<string[]>();
  const [isDeleteStocksModalOpen, setIsDeleteStocksModalOpen] =
    useState<boolean>(false);
  const startDate = router.get('startDate');
  const endDate = router.get('endDate');

  const { data, isLoading, refetch, isFetching } = useGetStockListQuery({
    warehouseId,
    page: page ?? '1',
    barcode: barcode ?? undefined,
    limit: limit ?? '10',
    productName: productName ?? undefined,
    categoryName: categoryName ?? undefined,
    productSerialNo: productSerialNo ?? undefined,
    status: status || undefined,
    type: startDate && endDate ? 'custom' : undefined,
    startDate: startDate ?? undefined,
    endDate: endDate ?? undefined,
  });

  const handleFilter = (fieldName: string, value: string) => {
    const query = generateFilterParams(fieldName, value);
    navigate(ROUTES.STOCK.LIST(warehouseId, query));
  };

  const encodedData = (stock: SingleStockDetails) => {
    return btoa(
      encodeURIComponent(
        JSON.stringify({
          barcode: stock.barcode,
          name: stock?.Product?.name.toUpperCase(),
          price: stock.retailPrice,
        }),
      ).replace(/%([0-9A-F]{2})/g, (_, p1) => {
        return String.fromCharCode(parseInt(p1, 16));
      }),
    );
  };

  const handleSelectAndUnselect = (stockId: string) => {
    if (selectedStocks?.length) {
      if (selectedStocks.includes(stockId)) {
        setSelectedStocks(selectedStocks.filter((item) => item !== stockId));
      } else {
        setSelectedStocks([...selectedStocks, stockId]);
      }
    } else {
      setSelectedStocks([stockId]);
    }
  };

  const [deleteStocks] = useDeleteStocksMutation();

  const handleDeleteStock = async (reason: string) => {
    const deleteData = {
      stockIds: selectedStocks,
      reason,
      warehouseId,
    };
    toast.promise(deleteStocks(deleteData).unwrap(), {
      pending: 'Deleting Stocks...',
      success: {
        render({ data: res }) {
          if (res?.statusCode === 200 || res?.statusCode === 201) {
            refetch();
            setSelectedStocks([]);
            setIsDeleteStocksModalOpen(false);
          }
          return 'Stocks Deleted Successfully';
        },
      },
      error: {
        render({ data: error }) {
          console.log(error);
          return 'Error on delete stocks';
        },
      },
    });
  };

  const handleAcceptReStock = (id: string) => {
    toast.promise(
      acceptStockRestock({
        id,
        data: {
          shouldApproved: true,
        },
      }).unwrap(),
      {
        pending: 'Restocking Stock...',
        success: {
          render({ data: res }) {
            if (res?.statusCode === 200 || res?.statusCode === 201) {
              refetch();
            }
            return 'Stock Restocked Successfully';
          },
        },
        error: {
          render({ data: error }) {
            console.log(error);
            return 'Error on restocking stock';
          },
        },
      },
    );
  };

  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-2">
        <div className="flex items-center">
          <div className="search-title-and-btn flex items-center">
            <div className="relative">
              <div className="block xl:hidden">
                <FilterButton
                  handleClick={() => setIsFilterModalOpen(!isFilterModalOpen)}
                />
              </div>
              <div
                className={`${isFilterModalOpen ? 'block' : 'hidden'} xl:hidden`}
              >
                <StockListPageFilterModal
                  handleClearAndClose={() => setIsFilterModalOpen(false)}
                  handleFilter={handleFilter}
                />
              </div>
            </div>
          </div>
          <div className="hidden xl:block">
            <div className="flex items-center gap-x-2">
              <StockListFilterOptions handleFilter={handleFilter} />
            </div>
          </div>
        </div>
      </div>
      {!isLoading && !isFetching ? (
        <div>
          <div className="tableTop w-full">
            <p>Stock List</p>
            <div className="flex items-center gap-2">
              {selectedStocks?.length ? (
                <button
                  className="text-red-600"
                  onClick={() => setIsDeleteStocksModalOpen(true)}
                  type="button"
                >
                  <Trash2 />
                </button>
              ) : (
                ''
              )}
              <p>Total : {data?.pagination?.total}</p>
            </div>
          </div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {data?.data?.length ? (
              <div>
                <div className="full-table-box h-custom">
                  <table className="full-table">
                    <thead className="bg-gray-100">
                      <tr>
                        <th className="tableHead">
                          <input
                            type="checkbox"
                            name=""
                            id=""
                            checked={
                              data?.data?.length === selectedStocks?.length
                            }
                            onClick={() => {
                              if (
                                data?.data?.length === selectedStocks?.length
                              ) {
                                setSelectedStocks([]);
                              } else {
                                const ids = data?.data?.map(
                                  (single: SingleStockDetails) => single?.id,
                                );
                                setSelectedStocks(ids);
                              }
                            }}
                          />
                        </th>

                        <th className="tableHead">Barcode</th>
                        <th className="tableHead">Product ID</th>
                        <th className="tableHead table-col-width">Name</th>
                        <th className="tableHead">Stock Location</th>
                        <th className="tableHead">TP</th>
                        <th className="tableHead">Wholesale Price</th>
                        <th className="tableHead">Regular Price</th>
                        <th className="tableHead">Discount Price</th>
                        <th className="tableHead">VAT</th>
                        <th className="tableHead">Status</th>
                        <th className="tableHead">Supplier</th>
                        <th className="tableHead">Created At</th>
                        <th className="tableHead">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y">
                      {data?.data?.map((stock: SingleStockDetails) => (
                        <tr
                          key={stock?.id}
                          className={
                            selectedStocks?.includes(stock?.id)
                              ? 'tableRowYellow'
                              : stock?.isSold
                                ? 'tableRowRed'
                                : stock?.isHold
                                  ? 'tableRowYellow'
                                  : stock?.status === 'ASSIGNED'
                                    ? 'tableRowBlue'
                                    : stock.status === 'RESTOCK_REQUESTED'
                                      ? 'tableRowPurple'
                                      : 'tableRowGreen'
                          }
                        >
                          <td className="tableData">
                            <input
                              type="checkbox"
                              name=""
                              id=""
                              onClick={() => handleSelectAndUnselect(stock.id)}
                              checked={selectedStocks?.includes(stock.id)}
                              disabled={
                                stock.isSold ||
                                stock.isHold ||
                                stock?.assignedShopId !== null
                              }
                            />
                          </td>
                          <td className="tableData">{stock?.barcode}</td>
                          <td className="tableData">
                            {stock?.Product?.serialNo ?? 0}
                          </td>
                          <td className="tableData table-col-width">
                            <StockName stock={stock} name={stock.name} />
                          </td>
                          <td className="tableData">
                            {stock?.AssignedShop?.name ?? 'Warehouse'}
                          </td>
                          <td className="tableData">{stock?.purchasePrice}</td>
                          <td className="tableData">{stock?.wholesalePrice}</td>
                          <td className="tableData">{stock?.retailPrice}</td>
                          <td className="tableData">
                            {CalculateDiscountPrice({
                              retailPrice: stock?.retailPrice,
                              discountType: stock.discountType,
                              discount: stock.discount,
                            })}
                          </td>
                          <td className="tableData">{stock?.vat}%</td>
                          <td className="tableData">
                            {/* {stock?.isHold
                              ? 'Hold'
                              : stock?.isSold
                                ? 'Sold'
                                : 'Available'} */}
                            {stock?.status}
                          </td>
                          <td className="tableData">
                            {stock?.Supplier?.User?.name}
                          </td>
                          <td className="tableData">
                            <DateAndTimeViewer date={stock?.createdAt} />
                          </td>
                          <td className="tableData">
                            <div className="flex items-center justify-center gap-2">
                              <TransparentEyeButton
                                handleClick={() =>
                                  navigate(
                                    ROUTES.STOCK.DETAILS(warehouseId, stock.id),
                                  )
                                }
                              />
                              {!stock?.isSold ? (
                                <BarcodeButton
                                  handleClick={() => {
                                    const encodedURL = `/single-stock-barcode/?data=${encodedData(stock)}`;
                                    window.open(encodedURL, '_blank'); // Opens the URL in a new tab
                                  }}
                                />
                              ) : (
                                ''
                              )}
                              {stock?.status === 'RESTOCK_REQUESTED' ? (
                                <GreenAcceptButton
                                  handleClick={() => {
                                    handleAcceptReStock(stock.id);
                                  }}
                                />
                              ) : (
                                ''
                              )}
                              <TransparentEditButton
                                handleClick={() => {
                                  toast.warning(
                                    'you clicked on edit button no action set',
                                  );
                                }}
                              />
                              {Number(selectedStocks?.length) !== 0 ? (
                                ''
                              ) : (
                                <TransparentDeleteButton
                                  handleClick={() => {
                                    handleSelectAndUnselect(stock.id);
                                    setIsDeleteStocksModalOpen(true);
                                  }}
                                />
                              )}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            ) : (
              <NoResultFound pageType="stock" />
            )}
          </div>
          <div className="pagination-box flex justify-end rounded bg-white p-3">
            <Pagination
              totalCount={data?.pagination?.total}
              totalPages={Math.ceil(
                Number(data?.pagination?.total) /
                  Number(data?.pagination?.limit),
              )}
            />
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={12} tableRow={6} />
      )}
      <Modal
        showModal={isDeleteStocksModalOpen}
        setShowModal={setIsDeleteStocksModalOpen}
      >
        <StockDeleteModal
          handleClose={() => setIsDeleteStocksModalOpen(false)}
          handleDelete={(reason: string) => handleDeleteStock(reason)}
          length={selectedStocks?.length ?? 1}
        />
      </Modal>
    </div>
  );
}

export default StockListPageOverview;
