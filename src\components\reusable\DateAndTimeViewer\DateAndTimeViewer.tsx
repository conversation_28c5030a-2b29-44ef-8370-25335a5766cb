interface Props {
  date: string;
}
function DateAndTimeViewer({ date }: Props) {
  return (
    <div>
      {new Intl.DateTimeFormat('en-US', {
        day: '2-digit',
        month: 'short',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      }).format(new Date(date))}
      {/* {new Intl.DateTimeFormat('en-US', {
        hour: '2-digit',
        minute: '2-digit',
      }).format(new Date(date))} */}
    </div>
  );
}

export default DateAndTimeViewer;
