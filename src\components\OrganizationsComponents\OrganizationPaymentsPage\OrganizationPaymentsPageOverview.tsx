import { Document, pdf } from '@react-pdf/renderer';
import Cookies from 'js-cookie';
import { EllipsisVertical, File } from 'lucide-react';

import OrganizationBillInvoicePdf from '../OrganizationPaymentStatusPageComponents/OrganizationBillInvoicePdf';

import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import DateViewer from '@/components/reusable/DateAndTimeViewer/DateViewer';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import OrderStatusViewer from '@/components/reusable/OrdersPagesReusableComponents/OrderStatusViewer';
import Pagination from '@/components/reusable/Pagination/Pagination';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import {
  Menubar,
  MenubarContent,
  MenubarItem,
  MenubarMenu,
  MenubarTrigger,
} from '@/components/ui/menubar';
import {
  SingleOrgBill,
  useGetOrgPaymentListQuery,
} from '@/redux/api/organizationApis/orgPaymentApis';

function OrganizationPaymentsPageOverview() {
  const { data, isLoading, isFetching } = useGetOrgPaymentListQuery({
    organizationId: Cookies.get('organizationId'),
  });

  const handleGenerateSingleInvoicePdf = async (
    singleBillDetails: SingleOrgBill,
  ) => {
    const blob = await pdf(
      <Document>
        <OrganizationBillInvoicePdf billDetails={singleBillDetails} />
      </Document>,
    ).toBlob();

    const url = URL.createObjectURL(blob);
    window.open(url);
  };

  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-2">
        <div className="flex items-center">
          <div className="search-title-and-btn flex items-center">
            <div className="relative">
              <div className="block xl:hidden">
                {/* <FilterButton
                  handleClick={() => setIsFilterModalOpen(!isFilterModalOpen)}
                /> */}
              </div>
              {/* <div
                className={`${isFilterModalOpen ? 'block' : 'hidden'} xl:hidden`}
              >
                <StockListPageFilterModal
                  handleClearAndClose={() => setIsFilterModalOpen(false)}
                  handleFilter={handleFilter}
                />
              </div> */}
            </div>
          </div>
          <div className="hidden xl:block">
            {/* <div className="flex items-center gap-x-2">
              <StockListFilterOptions handleFilter={handleFilter} />
            </div> */}
            <h2>Filters Will Be here</h2>
          </div>
        </div>
      </div>
      {!isLoading && !isFetching ? (
        <div>
          <div className="tableTop w-full">
            <p>Billing History</p>
            <div className="flex items-center gap-2">
              <p>Total : {data?.pagination?.total}</p>
            </div>
          </div>
          <div className="full-table-container w-full">
            {data?.data?.length ? (
              <div>
                <div className="full-table-box h-custom">
                  <table className="full-table">
                    <thead className="bg-gray-100">
                      <tr>
                        <th className="tableHead">#</th>
                        <th className="tableHead">Org</th>
                        <th className="tableHead">Purpose</th>
                        <th className="tableHead">Billing Cycle</th>
                        {/* <th className="tableHead">Payment Id</th> */}
                        <th className="tableHead">Amount</th>
                        <th className="tableHead">Trn Id</th>
                        <th className="tableHead">Payment Method</th>
                        <th className="tableHead">Status</th>
                        <th className="tableHead">Paid At</th>
                        <th className="tableHead">Updated At</th>
                        <th className="tableHead">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y">
                      {data?.data?.map((payment: SingleOrgBill, index) => (
                        <tr key={payment?.id}>
                          <td className="tableData">{index + 1}</td>
                          <td className="tableData">
                            {payment?.Organization?.name}
                          </td>
                          <td className="tableData">{payment?.for}</td>
                          <td className="tableData">
                            <div className="flex items-center justify-center gap-1">
                              <span>{payment?.Subscription.billingCycle}</span>(
                              <DateViewer
                                date={payment.Subscription.startDate}
                              />
                              -
                              <DateViewer date={payment.Subscription.endDate} />
                              )
                            </div>
                          </td>
                          {/* <td className="tableData">{payment?.paymentID}</td> */}
                          <td className="tableData">{payment?.amount}</td>
                          <td className="tableData">{payment?.referenceId}</td>
                          <td className="tableData">
                            {payment?.paymentMethod}
                          </td>
                          <td className="tableData">
                            <OrderStatusViewer status={payment?.status} />
                          </td>
                          <td className="tableData">
                            {payment?.paidAt ? (
                              <DateAndTimeViewer date={payment.paidAt} />
                            ) : (
                              ''
                            )}
                          </td>
                          <td className="tableData">
                            {payment?.updatedAt ? (
                              <DateAndTimeViewer date={payment.updatedAt} />
                            ) : (
                              ''
                            )}
                          </td>
                          <td className="tableData">
                            <div className="flex items-center justify-center">
                              <Menubar
                                style={{
                                  border: 'none',
                                  backgroundColor: 'transparent',
                                }}
                              >
                                <MenubarMenu>
                                  <MenubarTrigger className="cursor-pointer">
                                    <EllipsisVertical />
                                  </MenubarTrigger>
                                  <MenubarContent
                                    style={{
                                      marginRight: '25px',
                                      borderColor: 'black',
                                    }}
                                  >
                                    <MenubarItem
                                      onClick={() =>
                                        handleGenerateSingleInvoicePdf(payment)
                                      }
                                      className="flex cursor-pointer items-center gap-1 bg-primary font-semibold text-white"
                                    >
                                      <File size={20} />
                                      <span>Invoice</span>
                                    </MenubarItem>
                                  </MenubarContent>
                                </MenubarMenu>
                              </Menubar>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            ) : (
              <NoResultFound pageType="stock" />
            )}
          </div>
          <div className="pagination-box flex justify-end rounded bg-white p-3">
            <Pagination
              totalCount={data?.pagination?.total}
              totalPages={Math.ceil(
                Number(data?.pagination?.total) /
                  Number(data?.pagination?.limit),
              )}
            />
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={12} tableRow={6} />
      )}
    </div>
  );
}

export default OrganizationPaymentsPageOverview;
