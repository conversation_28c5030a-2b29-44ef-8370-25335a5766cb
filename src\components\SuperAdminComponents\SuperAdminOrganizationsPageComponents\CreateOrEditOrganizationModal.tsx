import { useFormik } from 'formik';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import * as Yup from 'yup';

import FilledSubmitButton from '@/components/reusable/Buttons/FilledSubmitButton';
import CustomInputField from '@/components/reusable/CustomInputField/CustomInputField';
import ImageSelector from '@/components/reusable/ImageSelector/ImageSelector';
import ModalTitle from '@/components/reusable/Modal/ModalTitle';
import { useUpdateShopLimitMutation } from '@/redux/api/superAdminOrganizationsApi';
import { useAddNewUserMutation } from '@/redux/api/userApi';
import { SuperAdminSingleOrganization } from '@/types/superAdminOrganizationsTypes';
import { UploadImageOnAws } from '@/utils/ImageUploadModule';

interface Props {
  type: string;
  handleClose: () => void;
  updateRefreshCounter: () => void;
  organizationDetails?: SuperAdminSingleOrganization;
}

const formikInitialValues = {
  name: '',
  username: '',
  password: '',
  email: '',
  mobileNumber: '',
  organizationName: '',
  warehouseName: '',
  warehouseAddress: '',
  shopLimit: 1,
};

const validation = Yup.object({
  name: Yup.string().required('Owner name is required'),
  username: Yup.string().required('username is required'),
  password: Yup.string().required('password is required'),
  email: Yup.string().required('email is required'),
  mobileNumber: Yup.string().required('phone is required'),
  organizationName: Yup.string().required('organization name is required'),
  warehouseName: Yup.string().required('warehouse name is required'),
  warehouseAddress: Yup.string().required('warehouse address is required'),
  shopLimit: Yup.number().required('shop Limit is required'),
});

function CreateOrEditOrganizationModal({
  handleClose,
  updateRefreshCounter,
  type,
  organizationDetails,
}: Props) {
  const [currentFile, setCurrentFile] = useState<any>();
  const [addNewUser, { isLoading }] = useAddNewUserMutation();
  const [updateOrganizationDetails, { isLoading: isUpdating }] =
    useUpdateShopLimitMutation();

  const formik = useFormik({
    initialValues: formikInitialValues,
    validationSchema: validation,

    onSubmit: async (values) => {
      if (type === 'new') {
        const imgUrl = currentFile ? await UploadImageOnAws(currentFile) : null;
        toast.promise(
          addNewUser({
            ...values,
            shopLimit: 1,
            imgUrl,
          }).unwrap(),
          {
            pending: 'Registering New Organization...',
            success: {
              render({ data: res }) {
                if (res?.statusCode === 200 || res?.statusCode === 201) {
                  updateRefreshCounter();
                  handleClose();
                }
                return 'Organization Registered Successfully';
              },
            },
            error: {
              render({ data: error }) {
                console.log(error);
                return 'Error on creating Organization';
              },
            },
          },
        );
      } else {
        const imgUrl = currentFile
          ? await UploadImageOnAws(currentFile)
          : organizationDetails?.imgUrl
            ? organizationDetails?.imgUrl
            : null;
        toast.promise(
          updateOrganizationDetails({
            data: { shopLimit: values.shopLimit, imgUrl },
            id: organizationDetails?.id,
          }),
          {
            pending: 'Updating Organization...',
            success: {
              render({ data: res }) {
                if (
                  res.data?.statusCode === 200 ||
                  res.data?.statusCode === 201
                ) {
                  updateRefreshCounter();
                  handleClose();
                }
                return 'Brand updated Successfully';
              },
            },
            error: {
              render({ data: error }) {
                console.log(error);
                return 'Error on update brand';
              },
            },
          },
        );
      }
    },
  });

  useEffect(() => {
    if (type === 'edit' && organizationDetails?.id) {
      /* formik.setFieldValue('name', organizationDetails?.name);
      formik.setFieldValue('email', organizationDetails?.email);
      formik.setFieldValue('mobileNumber', organizationDetails?.mobileNumber);
      formik.setFieldValue(
        'organizationName',
        organizationDetails?.organizationName,
      );
      formik.setFieldValue('warehouseName', organizationDetails?.warehouseName);
      formik.setFieldValue(
        'warehouseAddress',
        organizationDetails?.warehouseAddress,
      ); */
      formik.setFieldValue('shopLimit', organizationDetails?.shopLimit);
    }
  }, [type, organizationDetails]);

  return (
    <div className="flex w-[400px] flex-col gap-4 rounded-xl bg-white p-4 md:w-[600px]">
      <ModalTitle
        text={type === 'new' ? 'Create Organization' : 'Edit Organization'}
        handleClose={handleClose}
      />
      <form
        onSubmit={formik.handleSubmit}
        className="flex w-full flex-col gap-4"
      >
        <div className="flex gap-4">
          <div className="w-[100px]">
            <ImageSelector
              previousImage={organizationDetails?.imgUrl ?? ''}
              setNewImage={(e) => setCurrentFile(e)}
            />
          </div>
          <div className="flex w-full flex-col gap-4">
            <CustomInputField
              type="text"
              placeholder="Enter Owner Name"
              name="name"
              label="Owner Name"
              formik={formik}
            />
            <CustomInputField
              type="text"
              placeholder="Enter Organization Phone"
              name="mobileNumber"
              label="Phone Number"
              formik={formik}
            />
          </div>
        </div>
        <div className="flex items-center gap-2">
          <CustomInputField
            type="email"
            placeholder="Enter Organization email"
            name="email"
            label="Email"
            formik={formik}
          />
          <CustomInputField
            type="number"
            placeholder="Enter Shop Limit"
            name="shopLimit"
            label="Shop Limit"
            formik={formik}
          />
        </div>
        <div className="flex items-center gap-2">
          <CustomInputField
            type="text"
            placeholder="Enter Organization Name"
            name="organizationName"
            label="Organization Name"
            formik={formik}
          />
          <CustomInputField
            type="text"
            placeholder="Enter Warehouse Name"
            name="warehouseName"
            label="Warehouse Name"
            formik={formik}
          />
        </div>
        <CustomInputField
          type="text"
          placeholder="Enter Warehouse Address"
          name="warehouseAddress"
          label="Warehouse Address"
          formik={formik}
        />

        <div className="flex items-center gap-2">
          <CustomInputField
            type="text"
            placeholder="Enter Organization username"
            name="username"
            label="Username"
            formik={formik}
          />
          <CustomInputField
            type="text"
            placeholder="Enter password"
            name="password"
            label="Password"
            formik={formik}
          />
        </div>
        <div className="mt-[10px] flex w-full items-center justify-center">
          <FilledSubmitButton
            text={type === 'new' ? 'Create Organization' : 'Done Editing'}
            isLoading={isLoading || isUpdating}
          />
        </div>
      </form>
    </div>
  );
}

export default CreateOrEditOrganizationModal;
