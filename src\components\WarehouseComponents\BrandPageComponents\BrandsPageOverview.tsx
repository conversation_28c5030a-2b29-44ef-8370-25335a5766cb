import Cookies from 'js-cookie';
import { EllipsisVertical, PencilLine, Trash } from 'lucide-react';
import { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

import FilledButton from '../../reusable/Buttons/FilledButton';
import DeleteModal from '../../reusable/DeleteModal/DeleteModal';
import Modal from '../../reusable/Modal/Modal';

import AddOrEditBrandModal from './AddOrEditBrandModal';
import BrandPageFilterModal from './BrandPageFilterModal';

import FilterButton from '@/components/reusable/Buttons/FilterButton';
import ImageViewer from '@/components/reusable/ImageViewer/ImageViewer';
import SearchInput from '@/components/reusable/Inputs/SearchInput';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import Pagination from '@/components/reusable/Pagination/Pagination';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import {
  Menubar,
  MenubarContent,
  MenubarItem,
  MenubarMenu,
  MenubarSeparator,
  MenubarTrigger,
} from '@/components/ui/menubar';
import { useDeleteBrandMutation } from '@/redux/api/superAdminOrganizationsApi';
import { useGetBrandsQuery } from '@/redux/api/warehouseApis/brandsApi';
import { ROUTES } from '@/Routes';
import { SingleBrandDetails } from '@/types/warehouseTypes/brandsTypes';
import { generateFilterParams } from '@/utils/generateFilterParams';

interface Props {
  warehouseId: string;
}

function BrandsPageOverview({ warehouseId }: Props) {
  const navigate = useNavigate();
  const router = new URLSearchParams(useLocation().search);
  const name = router.get('name');
  const page = router.get('page');
  const limit = router.get('limit');

  const { data, isLoading, refetch, isFetching } = useGetBrandsQuery({
    organizationId: Cookies.get('organizationId'),
    warehouseId,
    name: name ?? undefined,
    page: page ?? '1',
    limit: limit ?? '10',
  });
  const [deleteBrand] = useDeleteBrandMutation();
  const [isCreateBrandModalOpen, setIsCreateBrandModalOpen] =
    useState<boolean>(false);
  const [isEditBrandModalOpen, setIsEditBrandModalOpen] = useState(false);
  const [selectedBrand, setSelectedBrand] = useState<SingleBrandDetails>();
  const [isDeleteBrandModalOpen, setIsDeleteBrandModalOpen] = useState(false);
  const [deleteBrandData, setDeleteBrandData] = useState<SingleBrandDetails>();
  const [isFilterModalOpen, setIsFilterModalOpen] = useState<boolean>(false);

  const handleDeleteBrand = async () => {
    toast.promise(deleteBrand(deleteBrandData?.id), {
      pending: 'Deleting Brand...',
      success: {
        render({ data: res }) {
          if (res.data?.statusCode === 200 || res.data?.statusCode === 201) {
            refetch();
            setIsDeleteBrandModalOpen(false);
          }
          return 'Brand Deleted Successfully';
        },
      },
      error: {
        render({ data: error }) {
          console.log(error);
          return 'Error on delete brand';
        },
      },
    });
  };

  const handleFilter = (fieldName: string, value: string) => {
    const query = generateFilterParams(fieldName, value);

    navigate(ROUTES.WAREHOUSE.BRANDS(warehouseId, query));
  };

  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 lg:py-1">
        <div className="flex items-center">
          <div className="search-title-and-btn flex items-center">
            <div className="relative">
              <div className="block lg:hidden">
                <FilterButton
                  handleClick={() => setIsFilterModalOpen(!isFilterModalOpen)}
                />
              </div>
              <div
                className={`${isFilterModalOpen ? 'block' : 'hidden'} xl:hidden`}
              >
                <BrandPageFilterModal />
              </div>
            </div>
          </div>
          <div className="hidden lg:block">
            <div className="flex items-center gap-x-2">
              <SearchInput
                placeholder="Search by Name"
                handleSubmit={(value: string) => handleFilter('name', value)}
                value={name ?? ''}
              />
            </div>
          </div>
        </div>
        <div>
          <FilledButton
            isLoading={false}
            text="Add New"
            handleClick={() => setIsCreateBrandModalOpen(true)}
            isDisabled={false}
          />
        </div>
      </div>
      {!isLoading && !isFetching ? (
        <div>
          <div className="tableTop w-full">
            <p>Brand List</p>
            <p>Total : {data?.pagination?.total}</p>
          </div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {data?.data?.length ? (
              <div className="full-table-box h-custom">
                <table className="full-table">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="tableHead">No</th>
                      <th className="tableHead">Image</th>
                      <th className="tableHead">Name</th>
                      <th className="tableHead">Created By</th>
                      {/* <th className="tableHead">Created At</th> */}
                      <th className="tableHead">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y bg-slate-200">
                    {data?.data?.map(
                      (brand: SingleBrandDetails, index: number) => (
                        <tr key={brand?.id}>
                          <td className="tableData">{index + 1}</td>
                          <td className="tableData">
                            <ImageViewer imageUrl={brand?.imgUrl} />
                          </td>
                          <td className="tableData">{brand?.name}</td>
                          <td className="tableData">
                            {brand?.CreatedBy?.name}
                          </td>
                          {/* <td className="tableData">
                            <DateAndTimeViewer date={brand?.createdAt} />
                          </td> */}
                          <td className="tableData">
                            <div className="flex items-center justify-center gap-2">
                              <Menubar
                                style={{
                                  border: 'none',
                                  backgroundColor: 'transparent',
                                }}
                              >
                                <MenubarMenu>
                                  <MenubarTrigger className="cursor-pointer">
                                    <EllipsisVertical />
                                  </MenubarTrigger>
                                  <MenubarContent
                                    style={{
                                      marginRight: '25px',
                                      borderColor: 'black',
                                    }}
                                  >
                                    <MenubarItem
                                      onClick={() => {
                                        setSelectedBrand(brand);
                                        setIsEditBrandModalOpen(true);
                                      }}
                                      className="flex cursor-pointer items-center gap-1 bg-primary font-semibold text-white"
                                    >
                                      <PencilLine size={20} />
                                      <span>Edit</span>
                                    </MenubarItem>

                                    <MenubarSeparator />
                                    <MenubarItem
                                      onClick={() => {
                                        setIsDeleteBrandModalOpen(true);
                                        setDeleteBrandData(brand);
                                      }}
                                      className="flex cursor-pointer items-center gap-1 bg-primary font-semibold text-white"
                                    >
                                      <Trash size={20} />
                                      <span>Delete</span>
                                    </MenubarItem>
                                  </MenubarContent>
                                </MenubarMenu>
                              </Menubar>
                            </div>
                          </td>
                        </tr>
                      ),
                    )}
                  </tbody>
                </table>
              </div>
            ) : (
              <NoResultFound pageType="brand" />
            )}
          </div>
          <div className="pagination-box flex justify-end rounded bg-white p-3">
            <Pagination
              currentPage={page ?? '1'}
              limit={Number(limit ?? 10)}
              handleFilter={(fieldName: string, value: any) =>
                handleFilter(fieldName, value)
              }
              totalCount={data?.pagination?.total}
              totalPages={Math.ceil(
                Number(data?.pagination?.total) /
                  Number(data?.pagination?.limit),
              )}
            />
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={5} tableRow={6} />
      )}
      <Modal
        setShowModal={setIsCreateBrandModalOpen}
        showModal={isCreateBrandModalOpen}
      >
        <AddOrEditBrandModal
          type="new"
          warehouseId={warehouseId}
          handleClose={() => setIsCreateBrandModalOpen(false)}
          updateRefreshCounter={refetch}
        />
      </Modal>
      <Modal
        setShowModal={setIsEditBrandModalOpen}
        showModal={isEditBrandModalOpen}
      >
        <AddOrEditBrandModal
          type="edit"
          warehouseId={warehouseId}
          handleClose={() => setIsEditBrandModalOpen(false)}
          brandData={selectedBrand}
          updateRefreshCounter={refetch}
        />
      </Modal>
      <Modal
        setShowModal={setIsDeleteBrandModalOpen}
        showModal={isDeleteBrandModalOpen}
      >
        <DeleteModal
          type="Brand"
          name={deleteBrandData?.name ?? ''}
          handleClose={() => setIsDeleteBrandModalOpen(false)}
          handleDelete={handleDeleteBrand}
        />
      </Modal>
    </div>
  );
}

export default BrandsPageOverview;
