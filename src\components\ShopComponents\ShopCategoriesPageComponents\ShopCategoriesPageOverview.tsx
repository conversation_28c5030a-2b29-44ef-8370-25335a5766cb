import { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import FilterButton from '@/components/reusable/Buttons/FilterButton';
import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import SearchInput from '@/components/reusable/Inputs/SearchInput';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import Pagination from '@/components/reusable/Pagination/Pagination';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import CategoryPageFilterModal from '@/components/WarehouseComponents/CategoryPageComponents/CategoryPageFilterModal';
import { useGetShopCategoriesQuery } from '@/redux/api/shopApis/shopCategoriesApis';
import { ROUTES } from '@/Routes';
import { SingleCategoryDetails } from '@/types/warehouseTypes/categoriesTypes';
import { generateFilterParams } from '@/utils/generateFilterParams';

interface Props {
  shopId: string;
  warehouseId: string;
}

function ShopCategoriesPageOverview({ shopId, warehouseId }: Props) {
  const navigate = useNavigate();
  const router = new URLSearchParams(useLocation().search);
  const name = router.get('name');
  const page = router.get('page');
  const limit = router.get('limit');
  const { data, isLoading, isFetching } = useGetShopCategoriesQuery(
    {
      warehouseId,
      shopId,
      name: name ?? undefined,
      page: page ?? '1',
      limit: limit ?? '10',
    },
    { skip: !(shopId && warehouseId) },
  );
  const [isFilterModalOpen, setIsFilterModalOpen] = useState<boolean>(false);

  const handleFilter = (fieldName: string, value: string) => {
    const query = generateFilterParams(fieldName, value);

    navigate(ROUTES.SHOP.CATEGORIES(shopId, query));
  };
  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 lg:py-1">
        <div className="flex items-center">
          <div className="search-title-and-btn flex items-center">
            <div className="relative">
              <div className="block lg:hidden">
                <FilterButton
                  handleClick={() => setIsFilterModalOpen(!isFilterModalOpen)}
                />
              </div>
              <div
                className={`${isFilterModalOpen ? 'block' : 'hidden'} xl:hidden`}
              >
                <CategoryPageFilterModal />
              </div>
            </div>
          </div>
          <div className="hidden lg:block">
            <div className="flex items-center gap-x-2">
              <SearchInput
                placeholder="Search by Name"
                handleSubmit={(value: string) => handleFilter('name', value)}
                value={name ?? ''}
              />
            </div>
          </div>
        </div>
      </div>
      {!isLoading && !isFetching ? (
        <div>
          <div className="tableTop w-full">
            <p>Category List</p>
            <p>Total : {data?.pagination?.total}</p>
          </div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {data?.data?.length ? (
              <div className="full-table-box h-custom">
                <table className="full-table">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="tableHead">No</th>
                      <th className="tableHead">Name</th>
                      <th className="tableHead">Created At</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y bg-slate-200">
                    {data?.data?.map(
                      (category: SingleCategoryDetails, index: number) => (
                        <tr key={category?.id}>
                          <td className="tableData">{index + 1}</td>
                          <td className="tableData">{category?.name}</td>
                          <td className="tableData">
                            <DateAndTimeViewer date={category.createdAt} />
                          </td>
                        </tr>
                      ),
                    )}
                  </tbody>
                </table>
              </div>
            ) : (
              <NoResultFound pageType="category" />
            )}
          </div>
          <div className="pagination-box flex justify-end rounded bg-white p-3">
            <Pagination
              currentPage={page ?? '1'}
              limit={Number(limit ?? 10)}
              handleFilter={(fieldName: string, value: any) =>
                handleFilter(fieldName, value)
              }
              totalCount={data?.pagination?.total}
              totalPages={Math.ceil(
                Number(data?.pagination?.total) /
                  Number(data?.pagination?.limit),
              )}
            />
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={3} tableRow={6} />
      )}
    </div>
  );
}

export default ShopCategoriesPageOverview;
