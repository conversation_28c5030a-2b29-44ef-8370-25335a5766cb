import { Text, View } from '@react-pdf/renderer';

import { reportPdfStyles } from './ReportPdfStyles';

import { WarehouseDetailsInRedux } from '@/redux/slice/warehouseSlice';
import { Warehouse } from '@/types/shopTypes/shopTransferAmountToOwnerTypes';

interface Props {
  warehouseDetails?: Warehouse | WarehouseDetailsInRedux;
}
function WarehousePdfHeader({ warehouseDetails }: Props) {
  return (
    <View style={reportPdfStyles.header}>
      <Text style={reportPdfStyles.userName}>{warehouseDetails?.name}</Text>
      <Text style={reportPdfStyles.address}>{warehouseDetails?.address}</Text>
      <Text style={reportPdfStyles.phone}>
        {warehouseDetails?.mobileNumber}
      </Text>
    </View>
  );
}

export default WarehousePdfHeader;
