import { useParams } from 'react-router-dom';

import BrandsPageOverview from '@/components/WarehouseComponents/BrandPageComponents/BrandsPageOverview';
import { ProtectedRoute } from '@/utils/ProtectedRoutes';

function BrandsPage() {
  const { warehouseId } = useParams();
  return (
    <ProtectedRoute>
      <BrandsPageOverview warehouseId={warehouseId ?? ''} />
    </ProtectedRoute>
  );
}

export default BrandsPage;
