import {
  ArrowLeft,
  Building,
  Calendar,
  Plus,
  ShoppingCart,
  Tag,
  User,
} from 'lucide-react';
import { Link, useNavigate, useParams } from 'react-router-dom';

import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import { useGetSingleStockDetailsQuery } from '@/redux/api/warehouseApis/stockApis';
import { useAppSelector } from '@/redux/hooks';
import { ROUTES } from '@/Routes';
import { CalculateDiscountPrice } from '@/utils/CalculateDiscountPrice';
import { ProtectedRoute } from '@/utils/ProtectedRoutes';

function SingleStockDetailsPage() {
  const { userDetails } = useAppSelector((state) => state);
  const { stockId, warehouseId } = useParams();
  const navigate = useNavigate();
  const { data, isLoading } = useGetSingleStockDetailsQuery(stockId ?? '');

  if (isLoading) {
    return (
      <ProtectedRoute>
        <div className="flex h-screen items-center justify-center">
          <p className="text-lg font-semibold">Loading stock details...</p>
        </div>
      </ProtectedRoute>
    );
  }

  if (!data) {
    return (
      <ProtectedRoute>
        <div className="flex h-screen items-center justify-center">
          <p className="text-lg font-semibold">No stock details found.</p>
        </div>
      </ProtectedRoute>
    );
  }

  const {
    name,
    purchasePrice,
    retailPrice,
    barcode,
    discount,
    vat,
    status,
    AssignedShop,
    Warehouse,
    Supplier,
    StockLog,
    OrderItem,
    Purchase,
    discountType,
  } = data.data;

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-100">
        {/* Sticky Header */}
        <div className="sticky top-0 z-10 bg-white shadow-md">
          <div className="flex items-center justify-between px-6 py-4">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate(-1)}
                type="button" // Added type attribute
                className="text-gray-500 hover:text-gray-700"
              >
                <ArrowLeft className="h-6 w-6" />
              </button>
              <h1 className="text-xl font-bold">{name}</h1>
            </div>
            <span
              className={`rounded-full px-3 py-1 text-sm font-semibold ${
                status === 'ORDERED'
                  ? 'bg-red-100 text-red-600'
                  : status === 'ASSIGNED'
                    ? 'bg-blue-600 text-white'
                    : status === 'AVAILABLE'
                      ? 'bg-green-100 text-green-600'
                      : status === 'RESTOCK_REQUESTED'
                        ? 'border border-purple-500 bg-purple-500 text-white'
                        : 'border border-yellow-500 bg-yellow-100 text-black'
              }`}
            >
              {status}
            </span>
          </div>
        </div>

        {/* Main Content */}
        <div className="mt-4 px-6 pb-6">
          <div>
            <div className="grid grid-cols-2 gap-4">
              {/* Stock Information */}
              <div className="rounded-lg bg-white p-6 shadow-md">
                <h2 className="mb-4 text-lg font-bold">Stock Information</h2>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="flex items-center text-sm text-gray-500">
                      <Tag className="mr-2 h-5 w-5 text-gray-400" />
                      Purchase Price
                    </p>
                    <p className="ml-7 text-lg font-semibold">
                      {userDetails?.type === 'CUSTOMER' ? purchasePrice : '***'}
                    </p>
                  </div>
                  <div>
                    <p className="flex items-center text-sm text-gray-500">
                      <Tag className="mr-2 h-5 w-5 text-gray-400" />
                      Retail Price
                    </p>
                    <p className="ml-7 text-lg font-semibold">{retailPrice}</p>
                  </div>
                  <div>
                    <p className="flex items-center text-sm text-gray-500">
                      <Tag className="mr-2 h-5 w-5 text-gray-400" />
                      Barcode
                    </p>
                    <p className="ml-7 text-lg font-semibold">{barcode}</p>
                  </div>
                  <div>
                    <p className="flex items-center text-sm text-gray-500">
                      <Tag className="mr-2 h-5 w-5 text-gray-400" />
                      Discount Price
                    </p>
                    <p className="ml-7 text-lg font-semibold">
                      {CalculateDiscountPrice({
                        retailPrice,
                        discount,
                        discountType,
                      })}
                    </p>
                  </div>
                  <div>
                    <p className="flex items-center text-sm text-gray-500">
                      <Tag className="mr-2 h-5 w-5 text-gray-400" />
                      VAT
                    </p>
                    <p className="ml-7 text-lg font-semibold">{vat}%</p>
                  </div>
                  <div>
                    <p className="flex items-center text-sm text-gray-500">
                      <ShoppingCart className="mr-2 h-5 w-5 text-gray-400" />
                      Order Serial
                    </p>
                    {OrderItem?.length ? (
                      <Link
                        to={ROUTES.WAREHOUSE.ORDER_DETAILS(
                          warehouseId ?? '',
                          OrderItem[OrderItem.length - 1]?.Order?.id,
                        )}
                      >
                        <p className="ml-7 text-lg font-semibold">
                          {OrderItem[OrderItem.length - 1]?.Order?.serialNo}
                        </p>
                      </Link>
                    ) : (
                      ''
                    )}
                  </div>
                </div>
              </div>

              {/* Associated Entities */}
              <div className="rounded-lg bg-white p-6 shadow-md">
                <h2 className="mb-4 text-lg font-bold">Associated Entities</h2>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="flex items-center text-sm text-gray-500">
                      <Building className="mr-2 h-5 w-5 text-gray-400" />
                      Assigned Shop
                    </p>
                    <p className="ml-7 text-lg font-semibold">
                      {AssignedShop?.name}
                    </p>
                  </div>
                  <div>
                    <p className="flex items-center text-sm text-gray-500">
                      <Building className="mr-2 h-5 w-5 text-gray-400" />
                      Warehouse
                    </p>
                    <p className="ml-7 text-lg font-semibold">
                      {Warehouse?.name}
                    </p>
                  </div>
                  <div>
                    <p className="flex items-center text-sm text-gray-500">
                      <User className="mr-2 h-5 w-5 text-gray-400" />
                      Supplier
                    </p>
                    <p className="ml-7 text-lg font-semibold">
                      {Supplier?.User?.name}
                    </p>
                  </div>
                  <div>
                    <p className="flex items-center text-sm text-gray-500">
                      <Plus className="mr-2 h-5 w-5 text-gray-400" />
                      Purchase Invoice
                    </p>
                    <Link
                      to={ROUTES.STOCK.ENTRY_DETAILS(
                        warehouseId ?? '',
                        Purchase?.id,
                      )}
                    >
                      <p className="ml-7 text-lg font-semibold">
                        {Purchase.serialNo}
                      </p>
                    </Link>
                  </div>
                  <div>
                    <p className="flex items-center text-sm text-gray-500">
                      <Calendar className="mr-2 h-5 w-5 text-gray-400" />
                      Purchase Date
                    </p>
                    <Link
                      to={ROUTES.STOCK.ENTRY_DETAILS(
                        warehouseId ?? '',
                        Purchase?.id,
                      )}
                    >
                      <p className="ml-7 text-sm font-semibold">
                        <DateAndTimeViewer date={data?.data?.createdAt} />
                      </p>
                    </Link>
                  </div>
                </div>
              </div>
            </div>

            {/* Stock Logs */}
            <div className="mt-4 rounded-lg bg-white p-6 shadow-md">
              <h2 className="mb-4 text-lg font-bold">Stock Logs</h2>
              <div className="space-y-4">
                {StockLog.map((log) => (
                  <div
                    key={log.id}
                    className="rounded-lg border bg-gray-50 p-4 shadow-sm transition-opacity duration-300"
                  >
                    <p className="text-sm text-gray-500">Type: {log.type}</p>
                    <p className="text-sm text-gray-500">
                      Message: {log.message || 'N/A'}
                    </p>
                    <p className="text-sm text-gray-500">
                      Created At: {new Date(log.createdAt).toLocaleString()}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}

export default SingleStockDetailsPage;
