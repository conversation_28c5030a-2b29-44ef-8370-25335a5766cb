import { useState } from 'react';
import { useLocation } from 'react-router-dom';

import { EyeButton } from '@/components/reusable/Buttons/CommonButtons';
import ExportButton from '@/components/reusable/Buttons/ExportButton';
import FilterButton from '@/components/reusable/Buttons/FilterButton';
import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import Modal from '@/components/reusable/Modal/Modal';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import StartDateEndDateWithSearch from '@/components/reusable/ReusableFilters/StartDateEndDateWithSearch';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import ViewStockEntryItemsModal from '@/components/ShopComponents/ShopStockEntryRequestPageComponents/ViewStockEntryItemsModal';
import { useGetWarehouseStockTransferReportQuery } from '@/redux/api/warehouseApis/warehouseReportsApis';
import { ShopStockTransferDetails } from '@/types/shopTypes/shopStockTransferReportTypes';
import {
  handleGenerateStockTransferReportCsv,
  handleGenerateStockTransferReportPdf,
} from '@/utils/ReportExport/ExportStockTransferReport';

interface Props {
  warehouseId: string;
}

function StockTransferReportPageOverview({ warehouseId }: Props) {
  const router = new URLSearchParams(useLocation().search);
  const startDate = router.get('startDate') || `${new Date().toISOString()}`;
  const endDate = router.get('endDate') || `${new Date().toISOString()}`;
  const [isViewItemsModalOpen, setIsViewItemsModalOpen] =
    useState<boolean>(false);
  const [selectedEntryId, setSelectedEntryId] = useState<string>('');
  const [selectedShopId, setSelectedShopId] = useState<string>('');
  const { data, isLoading, isFetching } =
    useGetWarehouseStockTransferReportQuery({
      warehouseId,
      type: 'custom',
      startDate,
      endDate,
    });

  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-1">
        <div className="flex items-center gap-x-2">
          <div className="search-title-and-btn flex items-center gap-x-3">
            {/* <p className="whitespace-nowrap">Search Filters</p> */}
            <div className="relative">
              <div className="block xl:hidden">
                <FilterButton handleClick={() => console.log('higbig')} />
              </div>
              <div className="block xl:hidden">
                {/* <ProductPageFilterModal /> */}
              </div>
            </div>
          </div>
          <div className="hidden xl:block">
            <StartDateEndDateWithSearch />
          </div>
        </div>
      </div>
      {!isLoading && !isFetching ? (
        <div>
          <div className="tableTop w-full">
            <p>Stock Transfer Report</p>
            <div className="flex items-center">
              <p>Total : {data?.data?.result?.length}</p>
              <div className="ml-4">
                <ExportButton
                  totalCount={data?.data?.result.length ?? 0}
                  handleExportCsv={() =>
                    handleGenerateStockTransferReportCsv({
                      data: data?.data?.result,
                    })
                  }
                  handleExportPdf={() =>
                    handleGenerateStockTransferReportPdf(
                      data?.data?.result,
                      data?.data?.warehouse as any,
                    )
                  }
                />
              </div>
            </div>
          </div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {data?.data?.result?.length ? (
              <div className="full-table-box h-customExc">
                <table className="full-table">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="tableHead">No</th>
                      <th className="tableHead">Transfer Date</th>
                      <th className="tableHead">Transfer From</th>
                      <th className="tableHead">Transfer To</th>
                      <th className="tableHead">Transfer By</th>
                      <th className="tableHead">Quantity</th>
                      <th className="tableHead">TP</th>
                      <th className="tableHead">Regular Price</th>
                      <th className="tableHead">Status</th>
                      <th className="tableHead">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y bg-slate-200">
                    {data?.data?.result?.map(
                      (single: ShopStockTransferDetails, index: number) => (
                        <tr key={single.id}>
                          <td className="tableData">{index + 1}</td>
                          <td className="tableData">
                            <DateAndTimeViewer date={single.createdAt} />
                          </td>
                          <td className="tableData">Warehouse</td>
                          <td className="tableData">{single?.Shop?.name}</td>
                          <td className="tableData">{single.CreatedBy.name}</td>
                          <td className="tableData">
                            {single?.stockIds?.length}
                          </td>
                          <td className="tableData">
                            {single?.totalRetailPrice}
                          </td>
                          <td className="tableData">
                            {single?.totalRetailPrice}
                          </td>
                          <td className="tableData">
                            {single?.onHold ? 'Hold' : 'Received'}
                          </td>
                          <td className="tableData">
                            <EyeButton
                              handleClick={() => {
                                setSelectedEntryId(single.id);
                                setSelectedShopId(single.shopId);
                                setIsViewItemsModalOpen(true);
                              }}
                            />
                          </td>
                        </tr>
                      ),
                    )}
                  </tbody>
                </table>
              </div>
            ) : (
              <NoResultFound pageType="transfer report" />
            )}
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={9} tableRow={6} />
      )}
      <Modal
        showModal={isViewItemsModalOpen}
        setShowModal={setIsViewItemsModalOpen}
      >
        <ViewStockEntryItemsModal
          entryId={selectedEntryId}
          handleClose={() => setIsViewItemsModalOpen(false)}
          shopId={selectedShopId}
          hideButtons
        />
      </Modal>
    </div>
  );
}

export default StockTransferReportPageOverview;
