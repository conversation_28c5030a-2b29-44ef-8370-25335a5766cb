import { Document, pdf } from '@react-pdf/renderer';
import { toast } from 'react-toastify';
import * as XLSX from 'xlsx';

import ExpensesReportPdf from '@/components/WarehouseComponents/ReportsPdf/ExpensesReportPdf';
import { WarehouseDetailsInRedux } from '@/redux/slice/warehouseSlice';
import { ShopExpensesReportResponse } from '@/types/shopTypes/shopStockTransferReportTypes';
import { SingleProductDetails } from '@/types/warehouseTypes/productTypes';

export const handleGenerateProductListPdf = async (
  expenseReportData?: ShopExpensesReportResponse,
  warehouseDetails?: WarehouseDetailsInRedux,
) => {
  const blob = await pdf(
    <Document>
      <ExpensesReportPdf
        expenseReportData={expenseReportData}
        warehouseDetails={warehouseDetails as WarehouseDetailsInRedux}
      />
    </Document>,
  ).toBlob();

  const url = URL.createObjectURL(blob);
  window.open(url);
};

export const handleGenerateProductListCsv = ({
  data,
}: {
  data?: SingleProductDetails[];
}) => {
  if (!data || data.length === 0) {
    toast.error('No data available to generate Excel');
    return;
  }

  // Define Excel headers
  const headers = [
    'No',
    'Product Serial',
    'Brand Name',
    'Category',
    'Variants',
    'Buying Price',
    'Wholesale Price',
    'Regular Price',
    'Available Quantity',
    'Total Sold',
  ];

  // Map data rows
  const rows = data.map((product: SingleProductDetails, index: number) => {
    return [
      index + 1,
      product.serialNo || 'N/A',
      product.Brand?.name || 'N/A',
      product.ProductCategory?.name || 'N/A',
      product.Variants?.map((variant) => variant.name).join(', ') || 'N/A',
      product.currentPurchasePrice || 0,
      product.currentWholesaleSellingPrice || 0,
      product.currentSellingPrice || 0,
      product.currentStockQuantity || 0,
      product.totalSold || 0,
    ];
  });

  // Combine headers and rows
  const sheetData = [headers, ...rows];

  // Create a worksheet
  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // Create a workbook and add the worksheet
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Product List');

  // Generate Excel file and download
  const excelFileName = `product_list.xlsx`;
  XLSX.writeFile(workbook, excelFileName);
};
