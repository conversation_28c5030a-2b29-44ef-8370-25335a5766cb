interface Props {
  currentPage: number;
  totalPage: number;
  pageType: string;
  handlePageChange: (type: string) => void;
  totalItemCount: number;
}

function CustomPagination({
  currentPage,
  totalPage,
  pageType,
  handlePageChange,
  totalItemCount,
}: Props) {
  return (
    <div className="flex items-center justify-between">
      <span>
        Showing 0-10 {pageType} out of {totalItemCount}
      </span>
      <div className="flex items-center gap-2">
        <div>
          Page : {currentPage} out of {totalPage}
        </div>
        <div className="flex items-center gap-2 rounded-xl border-2 border-primary">
          <button
            className="px-2 py-1"
            onClick={() => handlePageChange('previous')}
            type="button"
          >
            Previous
          </button>
          <button
            className="min-w-[80px] rounded-r-lg bg-primary px-2 py-1 text-white"
            onClick={() => handlePageChange('next')}
            type="button"
          >
            Next
          </button>
        </div>
      </div>
    </div>
  );
}

export default CustomPagination;
