import { useParams } from 'react-router-dom';

import StockReturnReportPageOverview from '@/components/WarehouseComponents/ReportsPages/StockReturnReportPageOverview';

function StockReturnReportPage({
  viewFrom,
}: {
  viewFrom: 'shop' | 'warehouse';
}) {
  const { warehouseId, shopId } = useParams();
  return (
    <div>
      <StockReturnReportPageOverview
        warehouseId={warehouseId ?? ''}
        shopId={shopId}
        viewFrom={viewFrom}
      />
    </div>
  );
}

export default StockReturnReportPage;
