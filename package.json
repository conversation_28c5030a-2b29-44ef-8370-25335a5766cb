{"name": "pos-client", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint", "lint:fix": "eslint --fix", "preview": "vite preview", "prepare": "husky", "type:check": "tsc --noEmit", "commitlint": "commitlint --edit", "deploy": "yarn run build && aws s3 sync ./dist s3://retailpluse.com/ && aws cloudfront create-invalidation --distribution-id E2S8Y1ASSJ0F1T --paths /*"}, "dependencies": {"@headlessui/react": "^2.0.4", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.1.2", "@react-pdf/renderer": "^3.4.4", "@reduxjs/toolkit": "^2.2.5", "@types/js-cookie": "^3.0.6", "@types/jsbarcode": "^3.11.4", "@types/react-lottie": "^1.2.10", "apexcharts": "^3.50.0", "axios": "^1.6.8", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "formik": "^2.4.6", "js-cookie": "^3.0.5", "jsbarcode": "^3.11.6", "lint-staged": "^15.2.2", "lucide-react": "^0.378.0", "react": "^18.2.0", "react-apexcharts": "^1.4.1", "react-dom": "^18.2.0", "react-fast-marquee": "^1.6.5", "react-lottie": "^1.2.10", "react-pdf": "^9.1.0", "react-redux": "^9.1.2", "react-router-dom": "^6.23.1", "react-switch": "^7.1.0", "react-toastify": "^10.0.5", "socket.io-client": "^4.8.1", "sweetalert2": "^11.12.4", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5", "yup": "^1.4.0"}, "devDependencies": {"@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@eslint/js": "^9.3.0", "@types/node": "^20.12.12", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "eslint": "8.2.0", "eslint-config-airbnb": "19.0.4", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "2.25.3", "eslint-plugin-jsx-a11y": "6.5.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "7.28.0", "eslint-plugin-react-hooks": "4.3.0", "eslint-plugin-react-refresh": "^0.4.6", "globals": "^15.2.0", "husky": "^9.0.11", "postcss": "^8.4.38", "prettier": "^3.3.2", "prettier-plugin-tailwindcss": "^0.6.5", "sass": "^1.77.8", "tailwindcss": "^3.4.3", "typescript": "^5.1.3", "typescript-eslint": "^7.9.0", "vite": "^5.2.0"}, "lint-staged": {"*.{js,jsx,tsx, ts, html, css}": "prettier --end-of-line lf --write --ignore-unknown", "*.{js,jsx,tsx, ts}": "eslint --fix", "*.{js,tsx,jsx, ts}": "eslint"}}