import { useState } from 'react';
import { useLocation } from 'react-router-dom';

import ViewStockEntryItemsModal from '../ShopStockEntryRequestPageComponents/ViewStockEntryItemsModal';

import {
  EyeButton,
  TransparentPrintButtonTop,
} from '@/components/reusable/Buttons/CommonButtons';
import FilterButton from '@/components/reusable/Buttons/FilterButton';
import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import Modal from '@/components/reusable/Modal/Modal';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import StartDateEndDateWithSearch from '@/components/reusable/ReusableFilters/StartDateEndDateWithSearch';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import { useGetShopStockTransferReportQuery } from '@/redux/api/shopApis/shopReportsApis';
import { ShopStockTransferDetails } from '@/types/shopTypes/shopStockTransferReportTypes';
import { handleShopStockTransferReportPdf } from '@/utils/GenerateReportPdf';

interface Props {
  shopId: string;
  warehouseId: string;
}

function ShopStockTransferReportPageOverview({ shopId, warehouseId }: Props) {
  const router = new URLSearchParams(useLocation().search);
  const startDate = router.get('startDate') || `${new Date().toISOString()}`;
  const endDate = router.get('endDate') || `${new Date().toISOString()}`;
  const [isViewItemsModalOpen, setIsViewItemsModalOpen] =
    useState<boolean>(false);
  const [selectedEntryId, setSelectedEntryId] = useState<string>('');
  const { data, isLoading, isFetching } = useGetShopStockTransferReportQuery(
    {
      warehouseId,
      shopId,
      type: 'custom',
      startDate,
      endDate,
    },
    { skip: !(shopId && warehouseId && startDate && endDate) },
  );

  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-1">
        <div className="flex items-center gap-x-2">
          <div className="search-title-and-btn flex items-center gap-x-3">
            {/* <p className="whitespace-nowrap">Search Filters</p> */}
            <div className="relative">
              <div className="block xl:hidden">
                <FilterButton handleClick={() => console.log('higbig')} />
              </div>
              <div className="block xl:hidden">
                {/* <ShopStockListPageFilterModal /> */}
              </div>
            </div>
          </div>
          <div className="hidden xl:block">
            <StartDateEndDateWithSearch />
          </div>
        </div>
      </div>
      <div>
        {!isLoading && !isFetching ? (
          <div>
            <div className="tableTop w-full">
              <p>Stock Transfer Report</p>
              <div className="flex items-center">
                <p>Total : {data?.data?.result?.length}</p>
                <div className="ml-4">
                  <TransparentPrintButtonTop
                    handleClick={() => {
                      handleShopStockTransferReportPdf(
                        data?.data?.result,
                        data?.data?.shop,
                      );
                    }}
                  />
                </div>
              </div>
            </div>
            <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
              {data?.data?.result?.length ? (
                <div className="full-table-box h-customExc">
                  <table className="full-table">
                    <thead className="bg-gray-100">
                      <tr>
                        <th className="tableHead">No</th>
                        <th className="tableHead">Date</th>
                        <th className="tableHead">Transferred By</th>
                        <th className="tableHead">Quantity</th>
                        <th className="tableHead">Retail Price</th>
                        <th className="tableHead">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y bg-slate-200">
                      {data?.data?.result?.map(
                        (transfer: ShopStockTransferDetails, index: number) => (
                          <tr key={transfer.id}>
                            <td className="tableData">{index + 1}</td>
                            <td className="tableData">
                              <DateAndTimeViewer date={transfer?.createdAt} />
                            </td>
                            <td className="tableData">
                              {transfer?.CreatedBy.name}
                            </td>
                            <td className="tableData">
                              {transfer?.stockIds?.length}
                            </td>
                            <td className="tableData">
                              {transfer?.totalRetailPrice}
                            </td>
                            <td className="tableData">
                              <EyeButton
                                handleClick={() => {
                                  setSelectedEntryId(transfer.id);
                                  setIsViewItemsModalOpen(true);
                                }}
                              />
                            </td>
                          </tr>
                        ),
                      )}
                    </tbody>
                  </table>
                </div>
              ) : (
                <NoResultFound pageType="stock transfer" />
              )}
            </div>
          </div>
        ) : (
          <TableSkeletonLoader tableColumn={6} tableRow={6} />
        )}
      </div>
      <Modal
        showModal={isViewItemsModalOpen}
        setShowModal={setIsViewItemsModalOpen}
      >
        <ViewStockEntryItemsModal
          entryId={selectedEntryId}
          handleClose={() => setIsViewItemsModalOpen(false)}
          shopId={shopId}
          hideButtons
        />
      </Modal>
    </div>
  );
}

export default ShopStockTransferReportPageOverview;
