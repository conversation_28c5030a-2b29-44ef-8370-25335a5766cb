import { useState } from 'react';
import { toast } from 'react-toastify';

import FilledButton from '@/components/reusable/Buttons/FilledButton';
import ModalTitle from '@/components/reusable/Modal/ModalTitle';
import { useCancelOrderMutation } from '@/redux/api/shopApis/shopOrdersApis';
import { ShopOrderDetails } from '@/types/shopTypes/shopOrderTypes';

interface Props {
  handleClose: () => void;
  orderDetails?: ShopOrderDetails;
}

function CancelOrderModal({ handleClose, orderDetails }: Props) {
  const [note, setNote] = useState<string>('');
  const [cancelOrder, { isLoading }] = useCancelOrderMutation();

  const handleSubmit = async () => {
    toast.promise(
      cancelOrder({
        id: orderDetails?.id,
        data: {
          reason: note,
        },
      }).unwrap(),
      {
        pending: 'Cancelling Items Of Order...',
        success: {
          render({ data: res }) {
            if (res?.statusCode === 200 || res?.statusCode === 201) {
              handleClose();
            }
            return 'Items Cancelled Successfully';
          },
        },
        error: {
          render({ data: error }) {
            console.log(error);
            return 'Error on cancel order';
          },
        },
      },
    );
  };

  return (
    <div className="flex w-[400px] flex-col gap-4 rounded-xl bg-white p-4 md:w-[500px]">
      <ModalTitle text="Cancel Order Modal" handleClose={handleClose} />
      <span className="blink-animation font-semibold text-red-600">
        Are you sure you want to cancel order with order id{' '}
        {orderDetails?.serialNo} ?
      </span>
      <div className="group relative z-0 mt-[-20px] w-full">
        <span className="relative left-3 top-2.5 w-auto bg-white px-1 font-mono text-[12px] font-bold text-gray-900 group-focus-within:text-red-600 dark:text-gray-300">
          Cancel Reason
        </span>
        <textarea
          aria-multiline
          className="text-10 py-55-rem block h-20 w-full rounded-lg border bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 disabled:cursor-not-allowed disabled:bg-gray-100 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
          value={note}
          onChange={(e) => setNote(e.target.value)}
          placeholder="Enter Cancel reason"
        />
      </div>
      <div className="mt-[10px] flex w-full items-center justify-center">
        <FilledButton
          isLoading={isLoading}
          text="Cancel Order"
          handleClick={() => handleSubmit()}
          isDisabled={isLoading || !note?.length}
        />
      </div>
    </div>
  );
}

export default CancelOrderModal;
