import { X } from 'lucide-react';

interface Props {
  text: string;
  handleClose: () => void;
}

function ModalTitle({ text, handleClose }: Props) {
  return (
    <div className="flex items-center justify-between">
      <span className="text-xl font-semibold text-[#28243D]">{text}</span>
      <button
        type="button"
        onClick={handleClose}
        className="hover:text-red-600"
      >
        <X />
      </button>
    </div>
  );
}

export default ModalTitle;
