import * as TooltipPrimitive from '@radix-ui/react-tooltip';
import { Search } from 'lucide-react';

import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface Props {
  handleSearch: () => void;
  isSearchEnabled: boolean;
}

function CustomSearchButton({ handleSearch, isSearchEnabled }: Props) {
  return (
    <TooltipProvider>
      <Tooltip delayDuration={200}>
        <TooltipTrigger>
          <button
            onClick={handleSearch}
            disabled={!isSearchEnabled}
            className={`rounded px-2 py-2 text-white ${
              isSearchEnabled
                ? 'bg-primary hover:bg-blue-700'
                : 'cursor-not-allowed bg-gray-400'
            }`}
            type="button"
          >
            <Search size={18} />
          </button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Search</p>
          <TooltipPrimitive.Arrow
            width={11}
            height={5}
            className="tooltipArrow"
          />
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

export default CustomSearchButton;
