import Cookies from 'js-cookie';
import { useLocation } from 'react-router-dom';

import ExportButton from '@/components/reusable/Buttons/ExportButton';
import FilterButton from '@/components/reusable/Buttons/FilterButton';
import StartDateEndDateWithSearch from '@/components/reusable/ReusableFilters/StartDateEndDateWithSearch';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import { useGetEmployeesReportQuery } from '@/redux/api/warehouseApis/warehouseReportsApis';
import { SingleSellerDetails } from '@/types/warehouseTypes/sellersTypes';
import {
  handleGenerateEmployeesReportCsv,
  handleGenerateEmployeesReportPdf,
} from '@/utils/ReportExport/ExportEmployeesSalesReport';

interface Props {
  warehouseId: string;
}

function EmployeesSalesReportPageOverview({ warehouseId }: Props) {
  const router = new URLSearchParams(useLocation().search);
  const startDate = router.get('startDate') || `${new Date().toISOString()}`;
  const endDate = router.get('endDate') || `${new Date().toISOString()}`;
  const { data, isLoading, isFetching } = useGetEmployeesReportQuery({
    warehouseId,
    organizationId: Cookies.get('organizationId'),
    type: 'custom',
    startDate,
    endDate,
  });

  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-1">
        <div className="flex items-center gap-x-2">
          <div className="search-title-and-btn flex items-center gap-x-3">
            {/* <p className="whitespace-nowrap">Search Filters</p> */}
            <div className="relative">
              <div className="block xl:hidden">
                <FilterButton handleClick={() => console.log('higbig')} />
              </div>
              <div className="block xl:hidden">
                {/* <ProductPageFilterModal /> */}
              </div>
            </div>
          </div>
          <div className="hidden xl:block">
            <StartDateEndDateWithSearch />
          </div>
        </div>
      </div>
      {!isLoading && !isFetching ? (
        <div>
          <div className="tableTop w-full">
            <p>Employees List</p>
            <div className="flex items-center">
              <p>Total : {data?.data?.result?.length}</p>
              <div className="ml-4">
                <ExportButton
                  totalCount={data?.data?.result.length ?? 0}
                  handleExportCsv={() =>
                    handleGenerateEmployeesReportCsv({
                      data: data?.data?.result,
                    })
                  }
                  handleExportPdf={() =>
                    handleGenerateEmployeesReportPdf({
                      employees: data?.data.result,
                      warehouseDetails: data?.data.warehouse,
                    })
                  }
                />
              </div>
            </div>
          </div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {data?.data?.result?.length ? (
              <div className="full-table-box h-customExc">
                <table className="full-table">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="tableHead">No</th>
                      <th className="tableHead table-col-width">Name</th>
                      <th className="tableHead">Phone Number</th>
                      {/* <th className="tableHead">Access</th> */}
                      <th className="tableHead">Order</th>
                      <th className="tableHead">Amount</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y bg-slate-200">
                    {data?.data?.result?.map(
                      (seller: SingleSellerDetails, index: number) => (
                        <tr key={seller?.id}>
                          <td className="tableData">{index + 1}</td>
                          <td className="tableData table-col-width">
                            {seller?.User?.name}
                          </td>
                          <td className="tableData">
                            {seller?.User?.mobileNumber}
                          </td>
                          {/* <td className="tableData">{seller?.User}</td> */}
                          <td className="tableData">{seller?.orderCount}</td>
                          <td className="tableData">{seller?.totalSales}</td>
                        </tr>
                      ),
                    )}
                  </tbody>
                </table>
              </div>
            ) : (
              <div>
                <h2>No seller found</h2>
              </div>
            )}
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={7} tableRow={6} />
      )}
    </div>
  );
}

export default EmployeesSalesReportPageOverview;
