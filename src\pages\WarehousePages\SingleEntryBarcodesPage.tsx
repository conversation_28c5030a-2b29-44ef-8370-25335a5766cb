import { useLocation } from 'react-router-dom';

import SpinnerLoader from '@/components/reusable/SpinnerLoader/SpinnerLoader';
import SingleStockBarcode from '@/components/WarehouseComponents/StockListPageComponents/SingleStockBarcode';
import { useGetSingleEntryBarcodeListQuery } from '@/redux/api/warehouseApis/purchaseApis';
import { SingleBarcodeDetails } from '@/types/warehouseTypes/purchaseTypes';

function SingleEntryBarcodesPage() {
  const searchParams = new URLSearchParams(useLocation().search);
  const entryId = searchParams.get('entryId') ?? '';
  const warehouseId = searchParams.get('warehouseId') ?? '';
  const { data, isLoading } = useGetSingleEntryBarcodeListQuery({
    id: entryId,
    warehouseId,
  });

  return (
    <div
      style={{
        padding: '0px',
        margin: '0px',
        maxWidth: '1.5in',
        unicodeBidi: 'isolate',
      }}
    >
      {!isLoading ? (
        data?.data?.data?.map((singleBarcode: SingleBarcodeDetails) => (
          <>
            {!singleBarcode?.isHold && !singleBarcode?.isSold ? (
              <SingleStockBarcode
                key={singleBarcode.barcode}
                barcode={singleBarcode?.barcode.toString()}
                name={singleBarcode?.productName}
                price={singleBarcode?.retailPrice.toString()}
              />
            ) : (
              ''
            )}
          </>
        ))
      ) : (
        <SpinnerLoader />
      )}
    </div>
  );
}

export default SingleEntryBarcodesPage;
