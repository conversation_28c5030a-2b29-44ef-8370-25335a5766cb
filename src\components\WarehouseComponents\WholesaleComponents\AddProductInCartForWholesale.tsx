import { useFormik } from 'formik';
import { useEffect } from 'react';
import { toast } from 'react-toastify';
import * as Yup from 'yup';

import FilledSubmitButton from '@/components/reusable/Buttons/FilledSubmitButton';
import CustomInputField from '@/components/reusable/CustomInputField/CustomInputField';
import ModalTitle from '@/components/reusable/Modal/ModalTitle';
import { SingleProductDetails } from '@/types/warehouseTypes/productTypes';

interface SelectedProductType {
  productId: 'string';
  productName: 'string';
  purchasePrice: number;
  quantity: number;
  price: number;
  availableQuantity: number;
  serialNo: string;
}

interface Props {
  handleClose: () => void;
  productData?: SingleProductDetails;
  handleSave: (data: any) => void;
  type: string;
  editData?: SelectedProductType;
}

const formikInitialValues = {
  purchasePrice: 0,
  quantity: 0,
  price: 0,
};

function AddProductInCartForWholesale({
  handleClose,
  productData,
  handleSave,
  type,
  editData,
}: Props) {
  const formik = useFormik({
    initialValues: formikInitialValues,
    validationSchema: Yup.object({
      purchasePrice: Yup.string().required('Purchase Price is required'),
      price: Yup.string().required('Retails Price is required'),
      quantity: Yup.number()
        .required('Quantity is required')
        .min(1)
        .max(
          Number(
            type === 'new'
              ? productData?.totalAvailable
              : editData?.availableQuantity,
          ),
        ),
    }),

    onSubmit: async (values) => {
      const addData = {
        ...values,
        productId: productData?.id,
        productName: productData?.name,
        serialNo: productData?.serialNo,
        availableQuantity: productData?.totalAvailable,
      };

      const updateData = {
        ...values,
        productId: editData?.productId,
        productName: editData?.productName,
        serialNo: editData?.serialNo,
        availableQuantity: editData?.availableQuantity,
      };
      handleSave(type === 'new' ? addData : updateData);
      handleClose();
      toast.success(
        type === 'new' ? 'Stock Added for entry' : 'Stock Edited Successfully',
      );
    },
  });
  useEffect(() => {
    if (productData && type === 'new') {
      formik.setFieldValue('purchasePrice', productData?.currentPurchasePrice);
      formik.setFieldValue('price', productData?.currentWholesaleSellingPrice);
    }
  }, [productData, type]);

  useEffect(() => {
    if (editData && type === 'edit') {
      formik.setFieldValue('purchasePrice', editData?.purchasePrice);
      // formik.setFieldValue('retailPrice', editData?.retailPrice);
      // formik.setFieldValue('discountType', editData?.discountType);
      // formik.setFieldValue('discount', editData?.discount);
      // formik.setFieldValue('vat', editData?.vat);
      formik.setFieldValue('quantity', editData?.quantity);
      formik.setFieldValue('price', editData?.price);
    }
  }, [productData, type]);

  return (
    <div className="flex w-[350px] flex-col gap-4 rounded-xl bg-white p-4 lg:w-[400px]">
      <ModalTitle text="Select Product" handleClose={handleClose} />
      <form
        onSubmit={formik.handleSubmit}
        className="flex w-full flex-col gap-4"
      >
        <div className="flex items-center gap-2">
          <CustomInputField
            type="number"
            placeholder="Purchase Price"
            name="purchasePrice"
            label="Buying Price"
            formik={formik}
            isDisabled
          />
          <div className="group relative z-0 mt-[-20px] w-full">
            <span className="relative left-3 top-2.5 w-auto bg-white px-1 font-mono text-[12px] font-bold text-gray-900 group-focus-within:text-red-600 dark:text-gray-300">
              Available Quantity
            </span>
            <input
              disabled
              type="text"
              className="text-10 py-55-rem block h-10 w-full cursor-not-allowed rounded-lg border bg-gray-100 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
              placeholder="Price After Discount"
              value={
                type === 'new'
                  ? productData?.totalAvailable
                  : editData?.availableQuantity
              }
            />
          </div>
        </div>
        <div className="flex items-center gap-2">
          <CustomInputField
            type="number"
            placeholder="Enter Retail Price"
            name="price"
            label="Wholesale Price"
            formik={formik}
            isDisabled
          />
          <CustomInputField
            type="number"
            placeholder="Enter Quantity"
            name="quantity"
            label="Quantity"
            formik={formik}
          />
        </div>
        {/* <div className="flex items-center gap-2">
          <CustomDropdown
            placeholder="Select Discount Type"
            name="discountType"
            label="Discount Type"
            formik={formik}
            options={[
              { label: 'Percentage', value: 'PERCENTAGE' },
              { label: 'Fixed', value: 'FIXED' },
            ]}
          />
          <CustomInputField
            type="number"
            placeholder="Discount"
            name="discount"
            label="Discount"
            formik={formik}
          />
        </div> */}
        <div className="flex items-center gap-2">
          {/* <div className="group relative z-0 mt-[-20px] w-full">
            <span className="relative left-3 top-2.5 w-auto bg-white px-1 font-mono text-[12px] font-bold text-gray-900 group-focus-within:text-red-600 dark:text-gray-300">
              Discount Price
            </span>
            <input
              disabled
              type="text"
              className="text-10 py-55-rem block h-10 w-full cursor-not-allowed rounded-lg border bg-gray-100 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
              placeholder="Price After Discount"
              value={
                formik.values.discountType === 'FIXED'
                  ? Number(formik.values.retailPrice) -
                    Number(formik.values.discount)
                  : Number(formik.values.retailPrice) -
                    (Number(formik.values.retailPrice) *
                      Number(formik.values.discount)) /
                      100
              }
            />
          </div>
          <CustomInputField
            type="number"
            placeholder="Enter Vat percentage"
            name="vat"
            label="Vat(%)"
            formik={formik}
            isDisabled
          /> */}
          <div className="group relative z-0 mt-[-20px] w-full">
            <span className="relative left-3 top-2.5 w-auto bg-white px-1 font-mono text-[12px] font-bold text-gray-900 group-focus-within:text-red-600 dark:text-gray-300">
              Total Price
            </span>
            <input
              disabled
              type="text"
              className="text-10 py-55-rem block h-10 w-full cursor-not-allowed rounded-lg border bg-gray-100 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
              placeholder="Price After Discount"
              value={
                Number(formik.values.price) * Number(formik.values.quantity)
              }
            />
          </div>
        </div>
        <div className="mt-[10px] flex w-full items-center justify-center">
          <FilledSubmitButton text="Add To Cart" isLoading={false} />
        </div>
      </form>
    </div>
  );
}

export default AddProductInCartForWholesale;
