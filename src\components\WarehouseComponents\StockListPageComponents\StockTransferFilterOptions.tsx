import { useLocation } from 'react-router-dom';

import SearchInput from '@/components/reusable/Inputs/SearchInput';

interface Props {
  handleFilter: (fieldName: string, value: string) => void;
}

function StockTransferFilterOptions({ handleFilter }: Props) {
  const router = new URLSearchParams(useLocation().search);
  const productName = router.get('productName');
  const barcode = router.get('barcode');
  const productSerialNo = router.get('productSerialNo');

  return (
    <div className="flex flex-col gap-3 md:flex-row md:gap-4">
      <SearchInput
        placeholder="Search by Name"
        handleSubmit={(value: string) => handleFilter('productName', value)}
        value={productName ?? ''}
      />
      <SearchInput
        placeholder="Search by Barcode"
        handleSubmit={(value: string) => handleFilter('barcode', value)}
        value={barcode ?? ''}
      />
      <SearchInput
        placeholder="Product Id"
        handleSubmit={(value: string) => handleFilter('productSerialNo', value)}
        value={productSerialNo ?? ''}
      />
    </div>
  );
}

export default StockTransferFilterOptions;
