import { Document, pdf } from '@react-pdf/renderer';

import StockAuditReportPdf from '@/components/ShopComponents/ShopStockAuditPageComponents/StockAuditReportPdf';
import CategoryWiseSalesSummeryPdf from '@/components/WarehouseComponents/ReportsPdf/CategoryWiseSalesSummeryPdf';
import ProductWiseSalesSummeryPdf from '@/components/WarehouseComponents/ReportsPdf/ProductWiseSaleSummaryPdf';
import SalesReportPdf from '@/components/WarehouseComponents/ReportsPdf/SalesReportPdf';
import SalesSummeryReportPdf from '@/components/WarehouseComponents/ReportsPdf/SalesSummeryReportPdf';
import ShopExpenseReportPdf from '@/components/WarehouseComponents/ReportsPdf/ShopExpenseReportPdf';
import ShopStockReportPdf from '@/components/WarehouseComponents/ReportsPdf/ShopStockReportPdf';
import ShopStockTransferReportPdf from '@/components/WarehouseComponents/ReportsPdf/ShopStockTransferReportPdf';
import StockAnalysisReportPdf from '@/components/WarehouseComponents/ReportsPdf/StockAnalysisReportPdf';
import StockReturnReportPdf from '@/components/WarehouseComponents/ReportsPdf/StockReturnReportPdf';
import { SingleAuditData } from '@/redux/api/shopApis/shopStockAuditApis';
import { ShopDetailsInRedux } from '@/redux/slice/storeSlice';
import { WarehouseDetailsInRedux } from '@/redux/slice/warehouseSlice';
import { StockReturnReportResponseType } from '@/types/reportTypes/stockReturnReportType';
import { SingleShopDetails } from '@/types/shopTypes';
import {
  ShopExpenseDetails,
  Warehouse,
} from '@/types/shopTypes/shopExpensesTypes';
import {
  GetShopAccountsSummeryResponse,
  GetShopCategoryWiseSummeryResponse,
  GetShopProductWiseSummeryResponse,
  ShopStockReportResponse,
  ShopStockTransferDetails,
} from '@/types/shopTypes/shopStockTransferReportTypes';
import { SingleProductDataOnDetails } from '@/types/warehouseTypes/productTypes';

export const handleGenerateSalesReportPdf = async (
  shopSalesReportData: any,
) => {
  const blob = await pdf(
    <Document>
      <SalesReportPdf shopSalesReportData={shopSalesReportData} />
    </Document>,
  ).toBlob();

  const url = URL.createObjectURL(blob);
  window.open(url);
};

export const handleGenerateStockReturnReportPdf = async ({
  warehouseDetails,
  data,
  shopDetails,
  viewFrom,
}: {
  warehouseDetails: WarehouseDetailsInRedux;
  data?: StockReturnReportResponseType;
  shopDetails?: ShopDetailsInRedux;
  viewFrom: 'shop' | 'warehouse';
}) => {
  const blob = await pdf(
    <Document>
      <StockReturnReportPdf
        data={data}
        warehouseDetails={warehouseDetails}
        shopDetails={shopDetails}
        viewFrom={viewFrom}
      />
    </Document>,
  ).toBlob();

  const url = URL.createObjectURL(blob);
  window.open(url);
};

export const handleGenerateSalesSummeryReportPdf = async (
  warehouseDetails: WarehouseDetailsInRedux,
  data?: GetShopAccountsSummeryResponse,
) => {
  const blob = await pdf(
    <Document>
      <SalesSummeryReportPdf data={data} warehouseDetails={warehouseDetails} />
    </Document>,
  ).toBlob();

  const url = URL.createObjectURL(blob);
  window.open(url);
};
export const handleCategoryWiseSalesSummeryPdf = async (
  categoryWiseSaleReportData?: GetShopCategoryWiseSummeryResponse,
  accountSummery?: GetShopAccountsSummeryResponse,
) => {
  const blob = await pdf(
    <Document>
      <CategoryWiseSalesSummeryPdf
        categoryWiseSaleReportData={categoryWiseSaleReportData}
        accountSummery={accountSummery}
      />
    </Document>,
  ).toBlob();

  const url = URL.createObjectURL(blob);
  window.open(url);
};

export const handleGenerateProductWiseSalesSummeryPdf = async (
  productWiseSaleReportData?: GetShopProductWiseSummeryResponse,
  accountSummery?: GetShopAccountsSummeryResponse,
) => {
  const blob = await pdf(
    <Document>
      <ProductWiseSalesSummeryPdf
        productWiseSaleReportData={productWiseSaleReportData}
        accountSummery={accountSummery}
      />
    </Document>,
  ).toBlob();

  const url = URL.createObjectURL(blob);
  window.open(url);
};
export const handleShopStockReportPdf = async (
  stockReportData?: ShopStockReportResponse,
  shopDetails?: ShopDetailsInRedux,
) => {
  const blob = await pdf(
    <Document>
      <ShopStockReportPdf
        stockReportData={stockReportData}
        shopDetails={shopDetails}
      />
    </Document>,
  ).toBlob();

  const url = URL.createObjectURL(blob);
  window.open(url);
};
export const handleShopExpensesReportPdf = async ({
  expenses,
  shopDetails,
}: {
  expenses: ShopExpenseDetails[];
  shopDetails?: SingleShopDetails;
}) => {
  const blob = await pdf(
    <Document>
      <ShopExpenseReportPdf expenses={expenses} shopDetails={shopDetails} />
    </Document>,
  ).toBlob();

  const url = URL.createObjectURL(blob);
  window.open(url);
};
export const handleShopStockTransferReportPdf = async (
  stockTransferList?: ShopStockTransferDetails[],
  shop?: SingleShopDetails,
) => {
  const blob = await pdf(
    <Document>
      <ShopStockTransferReportPdf
        stockTransferList={stockTransferList}
        shop={shop}
      />
    </Document>,
  ).toBlob();

  const url = URL.createObjectURL(blob);
  window.open(url);
};

export const handleGenerateStockAnalysisPdf = async (
  products: SingleProductDataOnDetails[],
  warehouse?: Warehouse,
) => {
  const blob = await pdf(
    <Document>
      <StockAnalysisReportPdf products={products} warehouse={warehouse} />
    </Document>,
  ).toBlob();

  const url = URL.createObjectURL(blob);
  window.open(url);
};

export const handleGenerateStockAuditReportPdf = async (
  auditDetails: SingleAuditData,
) => {
  const blob = await pdf(
    <Document>
      <StockAuditReportPdf auditDetails={auditDetails} />
    </Document>,
  ).toBlob();

  const url = URL.createObjectURL(blob);
  window.open(url);
};
