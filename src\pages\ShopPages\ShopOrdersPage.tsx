import { useParams } from 'react-router-dom';

import ShopOrdersPageOverview from '@/components/ShopComponents/ShopOrdersPageComponents/ShopOrdersPageOverview';
import { useAppSelector } from '@/redux/hooks';

function ShopOrdersPage() {
  const { shopId } = useParams();
  const { warehouseId } = useAppSelector((state) => state.shopDetails);
  return (
    <div className="h-full">
      <ShopOrdersPageOverview shopId={shopId ?? ''} warehouseId={warehouseId} />
    </div>
  );
}

export default ShopOrdersPage;
