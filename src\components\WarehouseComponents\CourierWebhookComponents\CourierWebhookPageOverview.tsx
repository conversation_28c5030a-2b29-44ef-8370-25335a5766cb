import { Clipboard } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'react-toastify';

import AddOrEditWebhookModal from './AddOrEditWebhookModal';

import FilledButton from '@/components/reusable/Buttons/FilledButton';
import Modal from '@/components/reusable/Modal/Modal';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import { useGetShopsQuery } from '@/redux/api/shopApi';
import {
  SingleWebhookDetails,
  useGetWarehouseWebhooksQuery,
} from '@/redux/api/warehouseApis/webhookApis';

interface Props {
  warehouseId: string;
}
function CourierWebhookPageOverview({ warehouseId }: Props) {
  const [isAddNewWebhookModalOpen, setIsAddNewWebhookModalOpen] =
    useState<boolean>(false);
  const { data, isLoading, refetch } = useGetWarehouseWebhooksQuery({
    warehouseId,
  });

  const { data: shopData } = useGetShopsQuery({
    warehouseId,
    i: false,
  });

  const handleCopyText = (text: string) => {
    if (navigator.clipboard) {
      navigator.clipboard
        .writeText(text)
        .then(() => {
          toast.success('Text copied to clipboard');
        })
        .catch(() => {
          toast.error('Failed to copy text: ');
        });
    }
  };

  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-end rounded bg-white px-3 py-3 lg:py-2">
        <div>
          <FilledButton
            isLoading={false}
            text="Add New Webhook"
            handleClick={() => setIsAddNewWebhookModalOpen(true)}
            isDisabled={false}
          />
        </div>
      </div>
      {!isLoading ? (
        <div>
          <div className="tableTop w-full">
            <p>Webhook List</p>
            <p>Total : {data?.pagination?.total}</p>
          </div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {data?.data?.length ? (
              <div className="full-table-box h-custom">
                <table className="full-table">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="tableHead">No</th>
                      <th className="tableHead">Type</th>
                      <th className="tableHead">Event</th>
                      <th className="tableHead">Nickname</th>
                      <th className="tableHead">Callback Url</th>
                      <th className="tableHead">Webhook Secret</th>
                      <th className="tableHead">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y bg-slate-200">
                    {data?.data?.map(
                      (webhook: SingleWebhookDetails, index: number) => (
                        <tr key={webhook?.id}>
                          <td className="tableData">{index + 1}</td>
                          <td className="tableData">{webhook.type}</td>
                          <td className="tableData">{webhook.event}</td>
                          <td className="tableData">
                            {webhook?.Courier?.nickName}
                          </td>
                          <td className="tableData">
                            <div className="flex items-center justify-between">
                              <button
                                type="button"
                                className="hover:cursor-pointer hover:text-blue-400 hover:underline"
                                onClick={() =>
                                  handleCopyText(webhook.callBackUrl)
                                }
                              >
                                {webhook?.callBackUrl.slice(0, 20)}...
                              </button>
                              <button
                                onClick={() =>
                                  handleCopyText(webhook.callBackUrl)
                                }
                                type="button"
                              >
                                <Clipboard size={16} />
                              </button>
                            </div>
                          </td>
                          <td className="tableData">
                            <div className="flex items-center justify-between">
                              <button
                                type="button"
                                className="hover:cursor-pointer hover:text-blue-400 hover:underline"
                                onClick={() => handleCopyText(webhook.secret)}
                              >
                                {webhook?.secret}
                              </button>
                              <button
                                onClick={() => handleCopyText(webhook?.secret)}
                                type="button"
                              >
                                <Clipboard size={16} />
                              </button>
                            </div>
                          </td>
                          <td className="tableData">...</td>
                        </tr>
                      ),
                    )}
                  </tbody>
                </table>
              </div>
            ) : (
              <NoResultFound pageType="webhook" />
            )}
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={6} tableRow={6} />
      )}
      <Modal
        setShowModal={setIsAddNewWebhookModalOpen}
        showModal={isAddNewWebhookModalOpen}
      >
        <AddOrEditWebhookModal
          type="new"
          handleClose={() => setIsAddNewWebhookModalOpen(false)}
          updateRefreshCounter={refetch}
          shopList={shopData?.data ?? []}
        />
      </Modal>
      {/* <Modal
        setShowModal={setIsEditCourierModalOpen}
        showModal={isEditCourierModalOpen}
      >
        <AddOrEditCourierSettingsModal
          type="edit"
          handleClose={() => setIsEditCourierModalOpen(false)}
          updateRefreshCounter={refetch}
          courierDetails={editCourierData}
        />
      </Modal> */}
    </div>
  );
}

export default CourierWebhookPageOverview;
