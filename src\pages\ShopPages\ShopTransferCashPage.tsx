import { useParams } from 'react-router-dom';

import ShopTransferCashPageOverview from '@/components/ShopComponents/ShopTransferCashPageComponents/ShopTransferCashPageOverview';
import { useAppSelector } from '@/redux/hooks';

function ShopTransferCashPage() {
  const { shopId } = useParams();
  const { warehouseId } = useAppSelector((state) => state.shopDetails);
  return (
    <div>
      <ShopTransferCashPageOverview
        shopId={shopId ?? ''}
        warehouseId={warehouseId}
      />
    </div>
  );
}

export default ShopTransferCashPage;
