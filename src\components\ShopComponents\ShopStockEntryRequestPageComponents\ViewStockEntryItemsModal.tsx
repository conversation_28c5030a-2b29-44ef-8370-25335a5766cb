import { useState } from 'react';
import { toast } from 'react-toastify';
import Swal from 'sweetalert2';

import CancelStockEntryRequestModal from './CancelStockEntryRequestModal';

import FilledButton from '@/components/reusable/Buttons/FilledButton';
import Modal from '@/components/reusable/Modal/Modal';
import ModalTitle from '@/components/reusable/Modal/ModalTitle';
import SpinnerLoader from '@/components/reusable/SpinnerLoader/SpinnerLoader';
import {
  useAcceptStockEntryMutation,
  useGetShopSingleStockEntryDetailsQuery,
} from '@/redux/api/shopApis/shopStockApis';
import {
  SingleEntryDetailsProduct,
  StockDetailsInEntryRequest,
} from '@/types/warehouseTypes/stockTypes';

interface Props {
  entryId: string;
  shopId: string;
  handleClose: () => void;
  hideButtons?: boolean;
}
function ViewStockEntryItemsModal({
  entryId,
  handleClose,
  shopId,
  hideButtons = false,
}: Props) {
  const [acceptStockEntry] = useAcceptStockEntryMutation();
  const { data, isLoading } = useGetShopSingleStockEntryDetailsQuery({
    id: entryId,
    shopId,
  });

  // const [expandedId, setExpandedId] = useState<string>('');
  const [isCancelEntryModalOpen, setIsCancelEntryModalOpen] =
    useState<boolean>(false);

  const handleAcceptStockEntry = () => {
    Swal.fire({
      title: `Are you sure? you want to entry items to shop?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, entry it!',
    }).then((result) => {
      if (result.isConfirmed) {
        toast.promise(
          acceptStockEntry({
            id: entryId,
            data: {
              shouldApproved: true,
              shopId,
            },
          }).unwrap(),
          {
            pending: 'Entering Stock...',
            success: {
              render({ data: res }) {
                if (res?.statusCode === 200 || res?.statusCode === 201) {
                  handleClose();
                }
                return 'Stock Accepted Successfully';
              },
            },
            error: {
              render({ data: error }) {
                console.log(error);
                return 'Error on stock entry';
              },
            },
          },
        );
      }
    });
  };

  return (
    <div className="flex w-[800px] flex-col gap-4 rounded-xl bg-white p-4">
      <ModalTitle text="Stock Entry List" handleClose={handleClose} />
      {!isLoading ? (
        <div className="w-full">
          {data?.data?.products ? (
            <div className="w-full">
              <table className="w-full">
                <thead className="bg-gray-100">
                  <tr>
                    <th className="tableHead">Serial</th>
                    <th className="tableHeadLeftAlign">Product Name</th>
                    <th className="tableHead">Quantity</th>
                    <th className="tableHeadLeftAlign">Barcodes</th>
                    {/* <th className="tableHead">Action</th> */}
                  </tr>
                </thead>
                <tbody className="divide-y bg-slate-200">
                  {data?.data?.products?.map(
                    (product: SingleEntryDetailsProduct, index) => (
                      <tr key={product?.productId}>
                        <td className="tableData">{index + 1}</td>
                        <td className="tableDataLeftAlign">
                          {product?.productName}
                        </td>
                        <td className="tableData">{product?.quantity}</td>
                        <td className="tableDataLeftAlign">
                          {product.stocks.map(
                            (stock: StockDetailsInEntryRequest) => (
                              <span key={stock.barcode}>{stock.barcode}, </span>
                            ),
                          )}
                        </td>
                        {/* <td className="tableData">
                          <Eye
                            onClick={() => setExpandedId(product.productId)}
                          />
                        </td> */}
                      </tr>
                    ),
                  )}
                </tbody>
              </table>
            </div>
          ) : (
            <div>
              <h2>No product found</h2>
            </div>
          )}
        </div>
      ) : (
        <SpinnerLoader />
      )}
      {!hideButtons && data?.data.status === 'PENDING' ? (
        <div className="flex items-center justify-center gap-8">
          <FilledButton
            text="Entry Products"
            isLoading={isLoading}
            handleClick={() => handleAcceptStockEntry()}
            isDisabled={isLoading}
          />
          <button
            type="button"
            className="cursor-pointer whitespace-nowrap rounded-lg border border-red-400 px-8 py-2 font-semibold text-red-400 disabled:cursor-not-allowed disabled:bg-slate-300"
            onClick={() => setIsCancelEntryModalOpen(true)}
          >
            Cancel Entry
          </button>
        </div>
      ) : (
        ''
      )}
      <Modal
        showModal={isCancelEntryModalOpen}
        setShowModal={setIsCancelEntryModalOpen}
      >
        <CancelStockEntryRequestModal
          entryId={entryId}
          handleClose={() => setIsCancelEntryModalOpen(false)}
          handleCloseParent={() => handleClose()}
          closeParent
          shopId={shopId}
        />
      </Modal>
    </div>
  );
}

export default ViewStockEntryItemsModal;
