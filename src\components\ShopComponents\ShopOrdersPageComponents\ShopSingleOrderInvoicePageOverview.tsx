import { Document, PDFViewer } from '@react-pdf/renderer';

import SingleOrderInvoicePdf from '@/components/reusable/SingeOrderInvoicePdf/SingleOrderInvoicePdf';
import { useGetShopSingleOrderDetailsQuery } from '@/redux/api/shopApis/shopOrdersApis';

interface Props {
  orderId: string;
}

function ShopSingleOrderInvoicePageOverview({ orderId }: Props) {
  const { data, isLoading } = useGetShopSingleOrderDetailsQuery(orderId);

  return (
    <div>
      {!isLoading ? (
        <PDFViewer style={{ width: '100%', height: '100vh' }}>
          <Document>
            <SingleOrderInvoicePdf orderDetails={data?.data} />
          </Document>
        </PDFViewer>
      ) : (
        'Loading Please wait'
      )}
    </div>
  );
}

export default ShopSingleOrderInvoicePageOverview;
