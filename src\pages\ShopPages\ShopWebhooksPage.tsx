import { Clipboard } from 'lucide-react';
import { useParams } from 'react-router-dom';
import { toast } from 'react-toastify';

import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import {
  SingleWebhookDetails,
  useGetWebhooksQuery,
} from '@/redux/api/warehouseApis/webhookApis';
import { ProtectedRoute } from '@/utils/ProtectedRoutes';

function ShopWebhooksPage() {
  const { shopId } = useParams();

  const { data, isLoading } = useGetWebhooksQuery({
    shopId,
  });
  const handleCopyText = (text: string) => {
    if (navigator.clipboard) {
      navigator.clipboard
        .writeText(text)
        .then(() => {
          toast.success('Text copied to clipboard');
        })
        .catch(() => {
          toast.error('Failed to copy text: ');
        });
    }
  };

  return (
    <ProtectedRoute>
      <div>
        {!isLoading ? (
          <div>
            <div className="tableTop w-full">
              <p>Webhook List</p>
              <p>Total : {data?.pagination?.total}</p>
            </div>
            <div className="full-table-container w-full">
              {data?.data?.length ? (
                <div className="full-table-box h-custom">
                  <table className="full-table">
                    <thead className="bg-gray-100">
                      <tr>
                        <th className="tableHead">No</th>
                        <th className="tableHead">Type</th>
                        <th className="tableHead">Event</th>
                        <th className="tableHead">Callback Url</th>
                        <th className="tableHead">Webhook Secret</th>
                        <th className="tableHead">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y bg-slate-200">
                      {data?.data?.map(
                        (webhook: SingleWebhookDetails, index: number) => (
                          <tr key={webhook?.id}>
                            <td className="tableData">{index + 1}</td>
                            <td className="tableData">{webhook.type}</td>
                            <td className="tableData">{webhook.event}</td>
                            <td className="tableData">
                              <div className="flex items-center justify-between">
                                <button
                                  type="button"
                                  className="hover:cursor-pointer hover:text-blue-400 hover:underline"
                                  onClick={() =>
                                    handleCopyText(webhook.callBackUrl)
                                  }
                                >
                                  {webhook?.callBackUrl.slice(0, 60)}...
                                </button>
                                <button
                                  onClick={() =>
                                    handleCopyText(webhook.callBackUrl)
                                  }
                                  type="button"
                                >
                                  <Clipboard size={16} />
                                </button>
                              </div>
                            </td>
                            <td className="tableData">
                              <div className="flex items-center justify-between">
                                <button
                                  type="button"
                                  className="hover:cursor-pointer hover:text-blue-400 hover:underline"
                                  onClick={() => handleCopyText(webhook.secret)}
                                >
                                  {webhook?.secret}
                                </button>
                                <button
                                  onClick={() =>
                                    handleCopyText(webhook?.secret)
                                  }
                                  type="button"
                                >
                                  <Clipboard size={16} />
                                </button>
                              </div>
                            </td>
                            <td className="tableData">...</td>
                          </tr>
                        ),
                      )}
                    </tbody>
                  </table>
                </div>
              ) : (
                <NoResultFound pageType="webhook" />
              )}
            </div>
          </div>
        ) : (
          <TableSkeletonLoader tableColumn={6} tableRow={6} />
        )}
      </div>
    </ProtectedRoute>
  );
}

export default ShopWebhooksPage;
