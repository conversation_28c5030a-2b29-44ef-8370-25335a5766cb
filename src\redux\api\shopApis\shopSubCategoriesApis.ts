import BaseApi from '../baseApi';

import { TagTypes } from '@/redux/tag-types';
import { GetSubCategoriesResponse } from '@/types/warehouseTypes/subCategoriesTypes';

interface GetSubCategoriesParams {
  shopId?: string;
  name?: string;
  page?: string;
  limit?: string;
}
const ShopSubCategoriesApi = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getShopSubCategories: builder.query<
      GetSubCategoriesResponse,
      GetSubCategoriesParams
    >({
      query: (params) => ({
        url: '/product-sub-category',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP_SUB_CATEGORIES],
    }),
  }),
});

export const { useGetShopSubCategoriesQuery } = ShopSubCategoriesApi;
