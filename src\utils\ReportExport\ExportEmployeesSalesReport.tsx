import { Document, pdf } from '@react-pdf/renderer';
import { toast } from 'react-toastify';
import * as XLSX from 'xlsx';

import EmployeeReportPdf from '@/components/WarehouseComponents/ReportsPdf/EmployeeReportPdf';
import { Warehouse } from '@/types/shopTypes/shopTransferAmountToOwnerTypes';
import { SingleSellerDetails } from '@/types/warehouseTypes/sellersTypes';

export const handleGenerateEmployeesReportPdf = async ({
  employees,
  warehouseDetails,
}: {
  employees?: SingleSellerDetails[];
  warehouseDetails?: Warehouse;
}) => {
  const blob = await pdf(
    <Document>
      <EmployeeReportPdf
        employees={employees}
        warehouseDetails={warehouseDetails}
      />
    </Document>,
  ).toBlob();

  const url = URL.createObjectURL(blob);
  window.open(url);
};

export const handleGenerateEmployeesReportCsv = ({
  data,
}: {
  data?: SingleSellerDetails[];
}) => {
  if (!data || data.length === 0) {
    toast.error('No data available to generate Excel');
    return;
  }

  // Define Excel headers
  const headers = ['No', 'Name', 'Phone', 'Order Count', 'Order Amount'];

  // Map data rows
  const rows = data.map((deleteData: SingleSellerDetails, index: number) => {
    return [
      index + 1,
      deleteData?.User?.name || 'N/A',
      deleteData?.User?.mobileNumber || 'N/A',
      deleteData?.orderCount || 0,
      deleteData?.totalSales || 0,
    ];
  });

  // Combine headers and rows
  const sheetData = [headers, ...rows];

  // Create a worksheet
  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // Create a workbook and add the worksheet
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Employee Sales Report');

  // Generate Excel file and download
  const excelFileName = `employee_sales_report.xlsx`;
  XLSX.writeFile(workbook, excelFileName);
};
