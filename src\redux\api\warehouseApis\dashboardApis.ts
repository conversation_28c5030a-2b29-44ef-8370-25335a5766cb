import BaseApi from '../baseApi';

import { TagTypes } from '@/redux/tag-types';
import {
  DashboardLastSevenDaysSalesSummeryResponse,
  GetDashboardCustomersSummeryResponse,
  GetDashboardPaidOrderListResponse,
  GetDashboardUpdatesResponse,
  TopTenCustomersResponse,
  TopTenSellingItemsResponse,
} from '@/types/warehouseTypes/dashboardTypes';

interface GetDashboardUpdatesParams {
  organizationId: string;
  warehouseId?: string;
  type?: string;
  shopId?: string | null;
  startDate?: string;
  endDate?: string;
}

const DashboardApi = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getDashboardUpdates: builder.query<
      GetDashboardUpdatesResponse,
      GetDashboardUpdatesParams
    >({
      query: (params) => ({
        url: '/dashboard',
        method: 'GET',
        params,
      }),
      // providesTags: [TagTypes.PRODUCTS],
    }),
    getDashboardCustomersData: builder.query<
      GetDashboardCustomersSummeryResponse,
      GetDashboardUpdatesParams
    >({
      query: (params) => ({
        url: '/dashboard/warehouse/customer',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.PRODUCTS],
    }),
    getDashboardPaidOrderList: builder.query<
      GetDashboardPaidOrderListResponse,
      GetDashboardUpdatesParams
    >({
      query: (params) => ({
        url: '/dashboard/warehouse/paid-order',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.PRODUCTS],
    }),
    getDashboardSalesSummery: builder.query<
      DashboardLastSevenDaysSalesSummeryResponse,
      GetDashboardUpdatesParams
    >({
      query: (params) => ({
        url: '/dashboard/sales/report',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.PRODUCTS],
    }),
    getTopTenProductList: builder.query<
      TopTenSellingItemsResponse,
      GetDashboardUpdatesParams
    >({
      query: (params) => ({
        url: '/dashboard/top-10-product',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.PRODUCTS],
    }),
    getTopTenCustomerList: builder.query<
      TopTenCustomersResponse,
      { metricType: 'count' | 'amount' } & GetDashboardUpdatesParams
    >({
      query: ({ metricType, ...params }) => ({
        url: `/dashboard/top-10-customer-based-on-order-${metricType}`,
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.PRODUCTS],
    }),
  }),
});

export const {
  useGetDashboardUpdatesQuery,
  useGetDashboardCustomersDataQuery,
  useGetDashboardPaidOrderListQuery,
  useGetDashboardSalesSummeryQuery,
  useGetTopTenProductListQuery,
  useGetTopTenCustomerListQuery,
} = DashboardApi;
