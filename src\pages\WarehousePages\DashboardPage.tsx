import { useParams } from 'react-router-dom';

import DashboardPageOverview from '@/components/WarehouseComponents/DashboardPageComponents/DashboardPageOverview';
import { ProtectedRoute } from '@/utils/ProtectedRoutes';

function DashboardPage() {
  const { warehouseId } = useParams();
  return (
    <ProtectedRoute>
      <DashboardPageOverview warehouseId={warehouseId ?? ''} />
    </ProtectedRoute>
  );
}

export default DashboardPage;
