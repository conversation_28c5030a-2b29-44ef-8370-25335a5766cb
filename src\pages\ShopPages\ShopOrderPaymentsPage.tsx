import { useParams } from 'react-router-dom';

import OrderPaymentsList from '@/components/reusable/OrderPaymentsComponents/OrderPaymentsList';
import { useAppSelector } from '@/redux/hooks';

function ShopOrderPaymentsPage() {
  const { shopId } = useParams();
  const { warehouseId } = useAppSelector((state) => state.shopDetails);
  return (
    <OrderPaymentsList
      shopId={shopId ?? ''}
      pageType="shop"
      warehouseId={warehouseId}
    />
  );
}

export default ShopOrderPaymentsPage;
