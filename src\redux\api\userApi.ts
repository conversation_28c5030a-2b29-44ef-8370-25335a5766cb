import { TagTypes } from '../tag-types';

import BaseApi from './baseApi';

import {
  GetUserProfileResponse,
  GetUserWarehouseAndShopPermissionsType,
} from '@/types/userTypes';

const UserApi = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    addNewUser: builder.mutation({
      query: (data) => ({
        url: '/orgs/new',
        method: 'POST',
        data,
      }),
    }),
    getUserProfile: builder.query<GetUserProfileResponse, any>({
      query: (params) => ({
        url: '/users/profile',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP],
    }),
    updatePassword: builder.mutation<any, any>({
      query: (data) => ({
        url: '/users/reset-password',
        method: 'PATCH',
        data,
      }),
    }),
    getUserPermissions: builder.query<
      GetUserWarehouseAndShopPermissionsType,
      any
    >({
      query: () => ({
        url: '/users/permissions',
        method: 'GET',
      }),
    }),
  }),
});

export const {
  useAddNewUserMutation,
  useGetUserProfileQuery,
  useUpdatePasswordMutation,
  useGetUserPermissionsQuery,
} = UserApi;
