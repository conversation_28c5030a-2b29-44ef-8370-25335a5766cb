import { Settings } from 'lucide-react';
import { useLocation, useNavigate } from 'react-router-dom';

import GeneralSettingsTab from './GeneralSettingsTab';

import { useGetShopSettingsQuery } from '@/redux/api/shopApis/shopSettingsApi';
import { ROUTES } from '@/Routes';

interface Props {
  shopId: string;
}

function ShopSettingsOverview({ shopId }: Props) {
  const navigate = useNavigate();
  const router = new URLSearchParams(useLocation().search);
  const currentTab = router.get('currentTab');
  const { data, refetch } = useGetShopSettingsQuery(shopId);

  const handleFilter = (value: string) => {
    navigate(ROUTES.SHOP.SETTINGS(shopId, value));
  };

  return (
    <div className="grid w-full grid-cols-12 gap-4">
      {/* Left-side Tabs */}
      <div className="col col-span-2 bg-gray-100 p-4">
        <div className="flex items-center gap-2">
          <Settings className="mb-4" size={20} />
          <p className="mb-4 text-lg font-bold">Settings</p>
        </div>
        <ul className="space-y-2">
          <li>
            <button
              type="button"
              className={`w-full cursor-pointer rounded p-2 text-left ${
                currentTab === 'general' ? 'bg-blue-500 text-white' : ''
              }`}
              onClick={() => handleFilter('general')}
            >
              General Settings
            </button>
          </li>
          {/* <li>
            <button
              type="button"
              className={`w-full cursor-pointer rounded p-2 text-left ${
                currentTab === 'advanced' ? 'bg-blue-500 text-white' : ''
              }`}
              onClick={() => handleFilter('advanced')}
            >
              Advanced Settings
            </button>
          </li> */}
          {/* Add more tabs as needed */}
        </ul>
      </div>

      {/* Right-side Form */}
      <div className="col col-span-10">
        {currentTab === 'general' && (
          <GeneralSettingsTab data={data} handleUpdateData={refetch} />
        )}
        {currentTab === 'advanced' && (
          <div>
            <p>Advanced settings form goes here...</p>
          </div>
        )}
      </div>
    </div>
  );
}

export default ShopSettingsOverview;
