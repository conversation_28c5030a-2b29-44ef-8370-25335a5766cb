import BaseApi from '../baseApi';

import { TagTypes } from '@/redux/tag-types';
import { StockReturnReportResponseType } from '@/types/reportTypes/stockReturnReportType';
import {
  GetShopAccountsSummeryResponse,
  GetShopOrderStatusWiseSummeryResponse,
  GetShopSalesReportResponse,
  GetShopStockDeleteReportResponse,
  GetShopStockTransferReportResponse,
  ShopExpensesReportResponse,
  ShopStockReportResponse,
} from '@/types/shopTypes/shopStockTransferReportTypes';
import {
  StockAnalysisReportResponse,
  StockEntryReportResponse,
} from '@/types/warehouseTypes/reportTypes';
import { ArektaNameDiyaDensResponse } from '@/types/warehouseTypes/sellersTypes';

interface GetStockLocationParams {
  warehouseId?: string;
  type?: string;
  isUnAssigned?: boolean;
  startDate?: string;
  endDate?: string;
  status?: string;
}

interface GetStockReportsParams {
  warehouseId?: string;
  organizationId?: string;
  type?: string;
  startDate?: string;
  endDate?: string;
  expenseCategoryId?: string | null;
  shopId?: string | null;
}

const WarehouseReportsApi = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    // stocks
    getWarehouseStockReport: builder.query<
      ShopStockReportResponse,
      GetStockLocationParams
    >({
      query: (params) => ({
        url: '/report/current-stock',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP_STOCK],
    }),
    getWarehouseStockAnalysisReport: builder.query<
      StockAnalysisReportResponse,
      GetStockLocationParams
    >({
      query: (params) => ({
        url: '/report/product-analysis',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP_STOCK],
    }),
    getWarehouseStockEntryReport: builder.query<
      StockEntryReportResponse,
      GetStockReportsParams
    >({
      query: (params) => ({
        url: '/report/stock-entry',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP_STOCK],
    }),
    getWarehouseStockTransferReport: builder.query<
      GetShopStockTransferReportResponse,
      GetStockReportsParams
    >({
      query: (params) => ({
        url: '/report/stock-transfer',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP_STOCK],
    }),
    getWarehouseStockDeleteReport: builder.query<
      GetShopStockDeleteReportResponse,
      GetStockReportsParams
    >({
      query: (params) => ({
        url: '/report/stock-delete',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP_STOCK],
    }),
    getWarehouseStockReturnReport: builder.query<
      StockReturnReportResponseType,
      GetStockReportsParams
    >({
      query: (params) => ({
        url: '/report/stock-return',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP_STOCK],
    }),
    // expenses
    getWarehouseExpensesReport: builder.query<
      ShopExpensesReportResponse,
      GetStockReportsParams
    >({
      query: (params) => ({
        url: '/report/expense',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP_STOCK],
    }),
    getShopAccountsSummeryReport: builder.query<
      GetShopAccountsSummeryResponse,
      GetStockReportsParams
    >({
      query: (params) => ({
        url: '/report/profit-loss',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP_STOCK],
    }),
    // employees
    getEmployeesReport: builder.query<
      ArektaNameDiyaDensResponse,
      GetStockReportsParams
    >({
      query: (params) => ({
        url: '/report/employee',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP_STOCK],
    }),
    // sales report
    getWarehouseSalesReport: builder.query<
      GetShopSalesReportResponse,
      GetStockReportsParams
    >({
      query: (params) => ({
        url: '/report/sales',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP_STOCK],
    }),
    // status wise report
    getWarehouseOrderStatusWiseReport: builder.query<
      GetShopOrderStatusWiseSummeryResponse,
      { type: 'warehouse' | 'shop' } & GetStockReportsParams
    >({
      query: ({ type, ...params }) => ({
        url: `/report/order-status`,
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.STATUS_WISE_REPORT],
    }),
  }),
});

export const {
  useGetWarehouseStockTransferReportQuery,
  useGetWarehouseStockAnalysisReportQuery,
  useGetWarehouseExpensesReportQuery,
  useGetWarehouseStockReportQuery,
  useGetWarehouseStockEntryReportQuery,
  useGetWarehouseStockDeleteReportQuery,
  useGetWarehouseStockReturnReportQuery,
  useGetShopAccountsSummeryReportQuery,
  useGetWarehouseSalesReportQuery,
  useGetEmployeesReportQuery,
  useGetWarehouseOrderStatusWiseReportQuery,
} = WarehouseReportsApi;
