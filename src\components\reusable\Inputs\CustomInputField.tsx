interface Props {
  placeholder: string;
}
export default function CustomInputField({ placeholder }: Props) {
  return (
    <div className="relative my-2 w-full">
      <input
        type="text"
        id="default-search"
        className="text-md block w-full rounded-lg border border-gray-400 px-4 py-[5px] ps-[15px] text-gray-900"
        placeholder={placeholder}
        style={{ outline: 'none' }}
      />
    </div>
  );
}
