import { useParams } from 'react-router-dom';

import ShopStockTransferReportPageOverview from '@/components/ShopComponents/ShopStockTransferReportPageComponents/ShopStockTransferReportPageOverview';
import { useAppSelector } from '@/redux/hooks';

function ShopStockTransferReportPage() {
  const { shopId } = useParams();
  const { warehouseId } = useAppSelector((state) => state.shopDetails);
  return (
    <div>
      <ShopStockTransferReportPageOverview
        shopId={shopId ?? ''}
        warehouseId={warehouseId}
      />
    </div>
  );
}

export default ShopStockTransferReportPage;
