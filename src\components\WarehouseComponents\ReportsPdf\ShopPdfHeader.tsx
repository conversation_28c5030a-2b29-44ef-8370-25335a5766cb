import { Text, View } from '@react-pdf/renderer';

import { reportPdfStyles } from './ReportPdfStyles';

import { ShopDetailsInRedux } from '@/redux/slice/storeSlice';
import { SingleShopDetails } from '@/types/shopTypes';

function ShopPdfHeader({
  shopDetails,
}: {
  shopDetails?: SingleShopDetails | ShopDetailsInRedux;
}) {
  return (
    <View style={reportPdfStyles.header}>
      <Text style={reportPdfStyles.userName}>
        {shopDetails?.name}({shopDetails?.nickName})
      </Text>
      <Text style={reportPdfStyles.address}>{shopDetails?.address}</Text>
      <Text style={reportPdfStyles.phone}>{shopDetails?.mobileNumber}</Text>
    </View>
  );
}

export default ShopPdfHeader;
