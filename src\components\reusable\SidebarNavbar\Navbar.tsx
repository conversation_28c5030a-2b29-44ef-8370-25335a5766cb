import { Store } from 'lucide-react';
import { useState } from 'react';

import logo from '../../../images/logo_4.png';
import NotificationDropdown from '../Dropdown/NotificationDropdown';
import UserDropdown from '../Dropdown/UserDropdown';
import Modal from '../Modal/Modal';

import AddOrEditCustomerModal from '@/components/ShopComponents/ShopCustomersPageComponents/AddOrEditCustomerModal';
import { useAppSelector } from '@/redux/hooks';

interface Props {
  isSidebarOpen: boolean;
  setIsSidebarOpen: (open: boolean) => void;
}

function Navbar({ setIsSidebarOpen, isSidebarOpen }: Props) {
  const shopType = window.location.href.split('/')[3];
  // const navigate = useNavigate();
  const toggleMenu = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };
  const [isCreateCustomerModalOpen, setIsCreateCustomerModalOpen] =
    useState<boolean>(false);
  const { shopDetails, warehouseDetails } = useAppSelector((state) => state);

  return (
    <nav className="flex h-[60px] items-center bg-[#28243D]">
      <div className="flex w-full items-center px-2 md:px-6 lg:px-8">
        <div className="min-w-[165px] sm:min-w-[227px]">
          <div className="flex items-center gap-x-2">
            <div className="block md:hidden">
              <button
                type="button"
                onClick={toggleMenu}
                className="inline-flex items-center justify-center rounded-md p-2 text-gray-400 hover:bg-gray-700 hover:text-white focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
                aria-expanded="false"
              >
                <span className="sr-only">Open main menu</span>
                {isSidebarOpen ? (
                  <svg
                    className="block h-6 w-6"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    aria-hidden="true"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                ) : (
                  <svg
                    className="block h-6 w-6"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    aria-hidden="true"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M4 6h16M4 12h16m-7 6h7"
                    />
                  </svg>
                )}
              </button>
            </div>
            <div>
              <img src={logo} alt="SOFTS.AI" className="h-12 object-contain" />
            </div>
          </div>
        </div>
        <div className="w-full">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Store className="text-white" size={18} />
              {shopType === 'shop' ? (
                <h3 className="text-xs text-white sm:text-base">
                  {shopDetails?.name}
                  {`${shopDetails?.nickName ? `(${shopDetails?.nickName})` : ''}`}
                </h3>
              ) : (
                <h3 className="text-xs text-white sm:text-base">
                  {warehouseDetails?.name ?? 'Warehouse'}
                </h3>
              )}
            </div>
            {/* <div className="hidden lg:block">
              <div className="flex">
                <div className="mr-2">
                  <SearchInput
                    placeholder="Search Product"
                    handleSubmit={(value: string) =>
                      navigate(
                        window.location.href.split('/')[3] === 'shop'
                          ? ROUTES.SHOP.PRODUCTS(
                              window.location.href.split('/')[4],
                              `page=1${value && `&name=${value}`}`,
                            )
                          : ROUTES.WAREHOUSE.PRODUCTS(
                              window.location.href.split('/')[4],
                              `page=1${value && `&name=${value}`}`,
                            ),
                      )
                    }
                  />
                </div>
                <div>
                  <SearchInput
                    placeholder="Search Customer"
                    handleSubmit={(value: string) =>
                      navigate(
                        shopType === 'shop'
                          ? ROUTES.SHOP.CUSTOMERS(
                              window.location.href.split('/')[4],
                              `page=1${value && `&mobileNumber=${value}`}`,
                            )
                          : ROUTES.WAREHOUSE.CUSTOMERS(
                              window.location.href.split('/')[4],
                              `page=1${value && `&mobileNumber=${value}`}`,
                            ),
                      )
                    }
                  />
                </div>
              </div>
            </div> */}
            <div className="flex items-center gap-x-3 lg:gap-x-5">
              {/* {shopType !== 'warehouse' ? (
                <div className="cart">
                  <button
                    type="button"
                    onClick={() => setIsCreateCustomerModalOpen(true)}
                  >
                    <ShoppingCart className="text-white" />
                  </button>
                </div>
              ) : (
                ''
              )} */}
              <div className="user flex cursor-pointer items-center justify-center gap-4">
                <NotificationDropdown shopId={shopDetails?.id} />
                <UserDropdown />
              </div>
            </div>
          </div>
        </div>
      </div>
      <Modal
        setShowModal={setIsCreateCustomerModalOpen}
        showModal={isCreateCustomerModalOpen}
      >
        <AddOrEditCustomerModal
          type="new"
          shopId={window.location.href.split('/')[4]}
          handleClose={() => setIsCreateCustomerModalOpen(false)}
          updateRefreshCounter={() => console.log('refetch')}
        />
      </Modal>
    </nav>
  );
}

export default Navbar;
