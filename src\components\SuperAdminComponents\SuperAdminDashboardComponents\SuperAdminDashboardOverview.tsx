import { Link } from 'react-router-dom';

import { useGetOrganizationsQuery } from '@/redux/api/superAdminOrganizationsApi';
import { ROUTES } from '@/Routes';

function SuperAdminDashboardOverview() {
  const { data, isLoading } = useGetOrganizationsQuery({});

  if (isLoading)
    return <p className="text-center text-lg font-medium">Loading...</p>;

  return (
    <div>
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4">
        {data?.data.map((organization) => (
          <Link
            to={ROUTES.SUPER_ADMIN.ORGANIZATION_DETAILS(organization.id)}
            key={organization.id}
          >
            <div className="flex w-full items-center gap-4 rounded-lg bg-white p-4 shadow-lg">
              <img
                src={
                  organization?.imgUrl
                    ? `https://retail-pluse-upload.s3.ap-southeast-1.amazonaws.com/${organization.imgUrl}`
                    : 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQMXYd5y7rsgpHBZgJYq-mpg_E-eNPJYvxbpw&s'
                }
                alt={organization.name}
                className="object-fit h-12 w-20"
              />
              <div>
                <h3 className="text-lg font-semibold text-gray-800">
                  {organization.name}
                </h3>
                <p className="text-sm text-gray-600">
                  <strong>Warehouse Limit:</strong>{' '}
                  {organization.warehouseLimit}
                </p>
                <p className="text-sm text-gray-600">
                  <strong>Shop Limit:</strong> {organization.shopLimit}
                </p>
              </div>
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
}

export default SuperAdminDashboardOverview;
