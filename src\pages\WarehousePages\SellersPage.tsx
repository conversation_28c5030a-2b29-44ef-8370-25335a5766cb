import { useParams } from 'react-router-dom';

import SellersPageOverview from '@/components/WarehouseComponents/SellersPageComponents/SellersPageOverview';
import { ProtectedRoute } from '@/utils/ProtectedRoutes';

function SellersPage() {
  const { warehouseId } = useParams();
  return (
    <ProtectedRoute>
      <SellersPageOverview warehouseId={warehouseId ?? ''} />
    </ProtectedRoute>
  );
}

export default SellersPage;
