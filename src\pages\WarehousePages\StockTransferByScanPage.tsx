import { useParams } from 'react-router-dom';

import StockTransferByScanPageOverview from '@/components/WarehouseComponents/StockListPageComponents/StockTransferByScanPageOverview';
import { ProtectedRoute } from '@/utils/ProtectedRoutes';

function StockTransferByScanPage() {
  const { warehouseId } = useParams();
  return (
    <ProtectedRoute>
      <StockTransferByScanPageOverview warehouseId={warehouseId ?? ''} />
    </ProtectedRoute>
  );
}

export default StockTransferByScanPage;
