import { Dot } from 'lucide-react';
import { useState } from 'react';
import { Link } from 'react-router-dom';

import {
  CourierButton,
  PrintA4Button,
  PrintButton,
} from '@/components/reusable/Buttons/CommonButtons';
import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import ImageViewer from '@/components/reusable/ImageViewer/ImageViewer';
import Modal from '@/components/reusable/Modal/Modal';
import SpinnerLoader from '@/components/reusable/SpinnerLoader/SpinnerLoader';
import ShopCourierBookingModal from '@/components/ShopComponents/ShopCourierBooking/ShopCourierBookingModal';
import { useGetOrderDetailsQuery } from '@/redux/api/warehouseApis/warehouseOrdersApis';
import { useAppSelector } from '@/redux/hooks';
import { ROUTES } from '@/Routes';
import {
  OrderSinglePaymentDetails,
  ShopOrderDetails,
  ShopSingleOrderItem,
  SingleWebhookLog,
} from '@/types/shopTypes/shopOrderTypes';
import {
  CalculateDiscountPrice,
  ProductTpCalculator,
  ProfitCalculator,
} from '@/utils/CalculateDiscountPrice';
import {
  handleGenerateSingleOrderPdf,
  handleGenerateSingleOrderPosPdf,
} from '@/utils/GenerateOrderPdf';
import StockName from '@/utils/StockName';

interface Props {
  orderId: string;
  warehouseId?: string;
}

function OrderDetailsPageOverview({ orderId, warehouseId }: Props) {
  const { userDetails } = useAppSelector((state) => state);
  const { data, isLoading } = useGetOrderDetailsQuery(orderId);
  const calculatedProfit =
    !isLoading && ProfitCalculator(data?.data as ShopOrderDetails);
  const [isCourierBookingModalOpen, setIsCourierBookingModalOpen] =
    useState<boolean>(false);

  return (
    <div className="mb-20">
      {!isLoading ? (
        <>
          {/* Order Info Section */}
          <div className="mb-4 grid grid-cols-1 gap-4 sm:grid-cols-3">
            <div className="rounded-lg bg-blue-50 p-2">
              <p className="text-sm font-medium text-gray-500">Order Serial</p>
              <p className="mt-1 text-xl font-bold text-blue-600">
                {data?.data?.serialNo}
              </p>
            </div>
            <div className="rounded-lg bg-purple-50 p-2">
              <p className="text-sm font-medium text-gray-500">Order Status</p>
              <p className="mt-1 text-xl font-bold text-purple-600">
                {data?.data?.orderStatus}
              </p>
            </div>
            <div className="rounded-lg bg-indigo-50 p-2">
              <p className="text-sm font-medium text-gray-500">Shop</p>
              <p className="mt-1 text-xl font-bold text-indigo-600">
                {data?.data?.Shop?.name} ({data?.data?.Shop?.nickName})
              </p>
            </div>
          </div>
          {/* Customer and Order Details Cards */}
          <div className="mb-6 grid grid-cols-1 gap-6 md:grid-cols-2">
            {/* Customer Details Card */}
            <div className="overflow-hidden rounded-xl bg-white shadow-lg transition-all duration-300 hover:shadow-xl">
              <div className="flex items-center justify-between border-b border-gray-100 bg-gray-50 px-6 py-4">
                <h3 className="text-lg font-semibold text-gray-800">
                  Customer Details
                </h3>
                <Link
                  to={
                    warehouseId
                      ? ROUTES.WAREHOUSE.CUSTOMER_DETAILS(
                          warehouseId as string,
                          data?.data?.Customer?.id as string,
                        )
                      : ROUTES.SHOP.CUSTOMER_DETAILS(
                          data?.data?.shopId as string,
                          data?.data?.Customer?.id as string,
                        )
                  }
                  className="rounded-lg bg-primary px-4 py-2 text-sm text-white"
                >
                  View Profile
                </Link>
              </div>
              <div className="p-6">
                <div className="flex flex-col gap-6 sm:flex-row">
                  <div className="flex-shrink-0">
                    <div className="flex h-24 w-24 items-center justify-center overflow-hidden rounded-full border-2 border-primary">
                      <ImageViewer imageUrl={data?.data?.Customer?.imgUrl} />
                    </div>
                  </div>
                  <div className="flex flex-col gap-2">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-gray-700">Name:</span>
                      <span className="text-gray-800">
                        {data?.data?.Customer?.name}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-gray-700">Phone:</span>
                      <span className="text-gray-800">
                        {data?.data?.Customer?.mobileNumber}
                      </span>
                    </div>
                    <div className="flex items-start gap-2">
                      <span className="font-medium text-gray-700">
                        Address:
                      </span>
                      <span className="text-gray-800">
                        {data?.data?.Customer?.address}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Order Details Card */}
            <div className="overflow-hidden rounded-xl bg-white shadow-lg transition-all duration-300 hover:shadow-xl">
              <div className="flex items-center justify-between border-b border-gray-100 bg-gray-50 px-6 py-4">
                <h3 className="text-lg font-semibold text-gray-800">
                  Order Details
                </h3>
                <div className="flex items-center gap-2">
                  <PrintButton
                    handleClick={() =>
                      handleGenerateSingleOrderPdf(
                        data?.data,
                        data?.data?.Shop?.ShopSettings?.returnPolicyText,
                      )
                    }
                  />
                  <PrintA4Button
                    handleClick={() =>
                      handleGenerateSingleOrderPosPdf(
                        data?.data as ShopOrderDetails,
                        data?.data?.Shop?.ShopSettings?.returnPolicyText,
                      )
                    }
                  />
                  {data?.data?.Shop?.type === 'ONLINE' &&
                  data.data.orderStatus === 'PENDING' ? (
                    <CourierButton
                      handleClick={() => setIsCourierBookingModalOpen(true)}
                    />
                  ) : (
                    ''
                  )}
                </div>
              </div>
              <div className="px-6 py-2">
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
                  <div className="rounded-lg bg-gray-50 p-2">
                    <p className="text-sm font-medium text-gray-500">Total</p>
                    <p className="mt-1 text-xl font-bold text-gray-900">
                      {Number(data?.data?.grandTotal) +
                        Number(data?.data?.deliveryCharge)}
                    </p>
                  </div>
                  <div className="rounded-lg bg-green-50 p-2">
                    <p className="text-sm font-medium text-gray-500">Paid</p>
                    <p className="mt-1 text-xl font-bold text-green-600">
                      {data?.data?.totalPaid}
                    </p>
                  </div>
                  <div className="rounded-lg bg-red-50 p-2">
                    <p className="text-sm font-medium text-gray-500">Due</p>
                    <p className="mt-1 text-xl font-bold text-red-600">
                      {data?.data?.totalDue}
                    </p>
                  </div>
                  <div className="rounded-lg bg-orange-50 p-2">
                    <p className="text-sm font-medium text-gray-500">TP</p>
                    <p className="mt-1 text-xl font-bold text-red-600">
                      {userDetails?.type === 'CUSTOMER'
                        ? ProductTpCalculator(
                            data?.data?.OrderItem as ShopSingleOrderItem[],
                          )
                        : '***'}
                    </p>
                  </div>
                  <div className="rounded-lg bg-red-50 p-2">
                    <p className="text-sm font-medium text-gray-500">
                      Other Cost
                    </p>
                    <p className="mt-1 text-xl font-bold text-red-600">
                      {data?.data?.deliveryCost === 0
                        ? data?.data?.deliveryCharge
                        : data?.data?.deliveryCost}
                    </p>
                  </div>
                  <div className="rounded-lg bg-green-100 p-2">
                    <p className="text-sm font-medium text-gray-500">Profit</p>
                    {userDetails?.type === 'CUSTOMER' ? (
                      <p className="mt-1 text-xl font-bold text-green-600">
                        {calculatedProfit ? calculatedProfit?.profit : 0}{' '}
                        <span className="text-sm">
                          (
                          {calculatedProfit
                            ? calculatedProfit?.profitPercentage
                            : 0}
                          %)
                        </span>
                      </p>
                    ) : (
                      <p className="mt-1 text-xl font-bold text-green-600">
                        ***
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Product List */}
          <div>
            <div>
              <div className="tableTop w-full">
                <p>Product List</p>
                <p>Total : {data?.data?.OrderItem?.length}</p>
              </div>
              <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
                {data?.data?.OrderItem?.length ? (
                  <div className="full-table-box">
                    <table className="full-table">
                      <thead className="bg-gray-100">
                        <tr>
                          <th className="tableHead">No</th>
                          <th className="tableHead">Barcode</th>
                          <th className="tableHead table-col-width">Name</th>
                          {userDetails?.type === 'CUSTOMER' ? (
                            <th className="tableHead">TP</th>
                          ) : (
                            ''
                          )}
                          <th className="tableHead">Regular Price</th>
                          <th className="tableHead">Discount Price</th>
                          <th className="tableHead">Status</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y bg-slate-200">
                        {data?.data?.OrderItem?.map(
                          (order: ShopSingleOrderItem, index: number) => (
                            <tr key={order?.id}>
                              <td className="tableData">{index + 1}</td>
                              <td className="tableData">
                                {order?.Stock?.barcode}
                              </td>
                              <td className="tableData table-col-width">
                                <StockName stock={order?.Stock} name="" />
                              </td>
                              {userDetails?.type === 'CUSTOMER' ? (
                                <td className="tableData">
                                  {order?.Stock?.purchasePrice}
                                </td>
                              ) : (
                                ''
                              )}

                              <td className="tableData">
                                {order?.Stock?.retailPrice}
                              </td>
                              <td className="tableData">
                                {CalculateDiscountPrice({
                                  retailPrice: order?.Stock?.retailPrice,
                                  discount: order?.Stock?.discount,
                                  discountType: order?.Stock?.discountType,
                                })}
                              </td>
                              <td className="tableData">{order?.status}</td>
                            </tr>
                          ),
                        )}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div>
                    <h2>No product found</h2>
                  </div>
                )}
              </div>
            </div>
          </div>
          {/* Payment List */}
          {data?.data?.Payment?.length ? (
            <div className="mt-4">
              <div className="tableTop w-full">
                <p>Payment List</p>
                <p>Total : {data?.data?.Payment?.length}</p>
              </div>
              <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
                {data?.data?.OrderItem?.length ? (
                  <div className="full-table-box">
                    <table className="full-table">
                      <thead className="bg-gray-100">
                        <tr>
                          <th className="tableHead">No</th>
                          <th className="tableHead">Paid Amount</th>
                          <th className="tableHead">Payment Method</th>
                          <th className="tableHead">Time</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y bg-slate-200">
                        {data?.data?.Payment?.map(
                          (
                            payment: OrderSinglePaymentDetails,
                            index: number,
                          ) => (
                            <tr key={payment?.id}>
                              <td className="tableData">{index + 1}</td>
                              <td className="tableData">{payment?.amount}</td>
                              <td className="tableData">
                                {payment?.paymentMethod}
                              </td>
                              <td className="tableData">
                                <DateAndTimeViewer date={payment?.createdAt} />
                              </td>
                            </tr>
                          ),
                        )}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div>
                    <h2>No product found</h2>
                  </div>
                )}
              </div>
            </div>
          ) : (
            ''
          )}

          {/* Tracking Timeline */}
          <div className="my-8 overflow-hidden rounded-xl bg-white shadow-lg">
            <div className="border-b border-gray-100 bg-gray-50 px-6 py-4">
              <h3 className="text-lg font-semibold text-gray-800">
                Order Tracking
              </h3>
            </div>
            <div className="p-6">
              <div className="relative">
                {/* Timeline line */}
                <div className="absolute left-[19px] top-0 h-full w-0.5 bg-green-200" />

                {/* Order Created */}
                <div className="relative mb-8 flex items-start">
                  <div className="absolute left-0 flex h-10 w-10 items-center justify-center rounded-full bg-green-500 text-white">
                    <Dot size={24} />
                  </div>
                  <div className="ml-16 flex-1">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                      <h4 className="text-lg font-medium text-gray-900">
                        Order Created
                      </h4>
                      <p className="mt-1 text-sm text-gray-500 sm:mt-0">
                        {new Intl.DateTimeFormat('en-US', {
                          day: '2-digit',
                          month: 'short',
                          year: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit',
                        }).format(new Date(data?.data.createdAt ?? ''))}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Order Booked on Courier */}
                {data?.data?.CourierBooking?.length ? (
                  <div className="relative mb-8 flex items-start">
                    <div className="absolute left-0 flex h-10 w-10 items-center justify-center rounded-full bg-green-500 text-white">
                      <Dot size={24} />
                    </div>
                    <div className="ml-16 flex-1">
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                        <h4 className="text-lg font-medium text-gray-900">
                          Order Booked on Courier
                        </h4>
                        <p className="mt-1 text-sm text-gray-500 sm:mt-0">
                          {new Intl.DateTimeFormat('en-US', {
                            day: '2-digit',
                            month: 'short',
                            year: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit',
                          }).format(
                            new Date(
                              data?.data.CourierBooking[0]?.createdAt ?? '',
                            ),
                          )}
                        </p>
                      </div>
                    </div>
                  </div>
                ) : null}

                {/* Webhook Logs */}
                {data?.data?.WebhookLog.map((single: SingleWebhookLog) => (
                  <div
                    key={single.id}
                    className="relative mb-8 flex items-start"
                  >
                    <div className="absolute left-0 flex h-10 w-10 items-center justify-center rounded-full bg-green-500 text-white">
                      <Dot size={24} />
                    </div>
                    <div className="ml-16 flex-1">
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                        <h4 className="text-lg font-medium text-gray-900">
                          {single.payload.order_status}
                        </h4>
                        <p className="mt-1 text-sm text-gray-500 sm:mt-0">
                          {new Intl.DateTimeFormat('en-US', {
                            day: '2-digit',
                            month: 'short',
                            year: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit',
                          }).format(new Date(single.createdAt))}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </>
      ) : (
        <div className="flex h-64 items-center justify-center">
          <SpinnerLoader />
        </div>
      )}
      <Modal
        showModal={isCourierBookingModalOpen}
        setShowModal={setIsCourierBookingModalOpen}
      >
        <ShopCourierBookingModal
          orderDetails={data?.data}
          handleClose={() => setIsCourierBookingModalOpen(false)}
        />
      </Modal>
    </div>
  );
}

export default OrderDetailsPageOverview;
