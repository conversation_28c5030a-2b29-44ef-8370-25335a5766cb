import { useState } from 'react';
import { toast } from 'react-toastify';

import FilledButton from '@/components/reusable/Buttons/FilledButton';
import ModalTitle from '@/components/reusable/Modal/ModalTitle';
import { useReturnOrderItemsMutation } from '@/redux/api/shopApis/shopOrdersApis';
import { useAppSelector } from '@/redux/hooks';
import {
  ShopOrderDetails,
  ShopSingleOrderItem,
} from '@/types/shopTypes/shopOrderTypes';
import { CalculateDiscountPrice } from '@/utils/CalculateDiscountPrice';
import StockName from '@/utils/StockName';

interface Props {
  handleClose: () => void;
  orderDetails?: ShopOrderDetails;
  shopId: string;
}

function ShopOrderProductReturnModal({
  handleClose,
  orderDetails,
  shopId,
}: Props) {
  const userDetailsFromState = useAppSelector((state) => state.userDetails);
  const [selectedProductsForReturn, setSelectedProductsForReturn] = useState<
    string[]
  >([]);
  const [note, setNote] = useState<string>('');
  const [returnOrderItems, { isLoading }] = useReturnOrderItemsMutation();
  const [fullReturn, setFullReturn] = useState<boolean>(true);
  const [isDeliveryChargePaid, setIsDeliveryChargePaid] =
    useState<boolean>(true);
  const [newAdminDiscount, setNewAdminDiscount] = useState<number>(0);

  const handleSelectUnselect = (id: string) => {
    if (selectedProductsForReturn?.length) {
      const isSelected = selectedProductsForReturn.some(
        (sin: any) => sin.id === id,
      );
      if (isSelected) {
        const remaining = selectedProductsForReturn.filter(
          (sin: any) => sin.id !== id,
        );
        setSelectedProductsForReturn(remaining);
      } else {
        setSelectedProductsForReturn([...selectedProductsForReturn, id]);
      }
    } else {
      setSelectedProductsForReturn([id]);
    }
  };

  const handleSubmit = async () => {
    const ids: string[] = [];
    if (fullReturn) {
      orderDetails?.OrderItem?.map((sin: ShopSingleOrderItem) => {
        ids.push(sin.id);
        return 0;
      });
    } else {
      selectedProductsForReturn?.map((single: string) => {
        ids.push(single);
        return 0;
      });
    }
    const data = {
      shopId,
      customerId: orderDetails?.Customer?.id,
      orderId: orderDetails?.id,
      note: note ?? null,
      // returnAmount: total,
      orderItemIds: ids,
      fullReturn,
      isDeliveryChargePaid,
      newAdminDiscount: fullReturn
        ? orderDetails?.adminDiscount
        : newAdminDiscount ?? 0,
    };

    toast.promise(returnOrderItems(data).unwrap(), {
      pending: 'Returning Items Of Order...',
      success: {
        render({ data: res }) {
          if (res?.statusCode === 200 || res?.statusCode === 201) {
            handleClose();
          }
          return 'Items Returned Successfully';
        },
      },
      error: {
        render({ data: error }) {
          console.log(error);
          return 'Error on return order items';
        },
      },
    });
  };

  return (
    <div className="flex w-[340px] flex-col gap-4 rounded-xl bg-white p-4 md:w-[500px] xl:w-[800px]">
      <ModalTitle text="Product Return Modal" handleClose={handleClose} />
      <div className="flex flex-col gap-2">
        <div className="flex items-center gap-4">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={fullReturn}
              onChange={() => setFullReturn(!fullReturn)}
              disabled={userDetailsFromState.shopType !== 'ONLINE'}
              className="form-radio h-4 w-4 cursor-pointer text-blue-600 transition duration-150 ease-in-out"
            />
            <button
              type="button"
              className="cursor-pointer select-none text-gray-700"
              onClick={() => setFullReturn(!fullReturn)}
              disabled={userDetailsFromState.shopType !== 'ONLINE'}
            >
              Full Return
            </button>
          </div>
          {userDetailsFromState.shopType === 'ONLINE' ? (
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={isDeliveryChargePaid}
                onChange={() => setIsDeliveryChargePaid(!isDeliveryChargePaid)}
                className="form-radio h-4 w-4 text-blue-600 transition duration-150 ease-in-out"
              />
              <button
                type="button"
                className="cursor-pointer select-none text-gray-700"
                onClick={() => setIsDeliveryChargePaid(!isDeliveryChargePaid)}
              >
                Delivery Charge Paid
              </button>
            </div>
          ) : (
            ''
          )}
        </div>
        <div className="flex items-center gap-4">
          <div className="group relative z-0 w-full">
            <span className="relative left-3 top-2.5 w-auto bg-white px-1 font-mono text-[12px] font-bold text-gray-900 group-focus-within:text-red-600 dark:text-gray-300">
              Previous Admin Discount
            </span>
            <input
              disabled
              className="text-10 py-55-rem block w-full rounded-lg border bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 disabled:cursor-not-allowed disabled:bg-gray-100 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
              value={orderDetails?.adminDiscount}
              placeholder="Enter Return Note"
            />
          </div>
          <div className="group relative z-0 w-full">
            <span className="relative left-3 top-2.5 w-auto bg-white px-1 font-mono text-[12px] font-bold text-gray-900 group-focus-within:text-red-600 dark:text-gray-300">
              New Admin Discount
            </span>
            <input
              type="number"
              disabled={fullReturn}
              max={orderDetails?.adminDiscount}
              min={0}
              aria-multiline
              className="text-10 py-55-rem block w-full rounded-lg border bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 disabled:cursor-not-allowed disabled:bg-gray-100 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
              value={
                fullReturn ? orderDetails?.adminDiscount : newAdminDiscount
              }
              onChange={(e) => setNewAdminDiscount(Number(e.target.value))}
              placeholder="Enter New Admin Discount"
            />
          </div>
        </div>
        {orderDetails?.OrderItem?.map((product: ShopSingleOrderItem) => (
          <div
            className="flex w-full items-center gap-2 rounded border px-4 py-1"
            key={product?.id}
          >
            <input
              type="checkbox"
              checked={
                fullReturn || selectedProductsForReturn?.includes(product.id)
              }
              onClick={() => handleSelectUnselect(product?.id)}
              disabled={
                fullReturn ||
                product?.status === 'RETURNED' ||
                product?.status === 'DELIVERED'
              }
            />
            <span>{product?.Stock?.barcode}</span>
            <span>
              <StockName stock={product?.Stock} name="" />
            </span>
            <span>
              {CalculateDiscountPrice({
                retailPrice: product?.Stock?.retailPrice,
                discountType: product?.Stock?.discountType,
                discount: product?.Stock?.discount,
              })}
            </span>
            <span>{product?.status}</span>
          </div>
        ))}
      </div>
      <div className="group relative z-0 mt-[-20px] w-full">
        <span className="relative left-3 top-2.5 w-auto bg-white px-1 font-mono text-[12px] font-bold text-gray-900 group-focus-within:text-red-600 dark:text-gray-300">
          Return Note
        </span>
        <textarea
          aria-multiline
          className="text-10 py-55-rem block h-20 w-full rounded-lg border bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 disabled:cursor-not-allowed disabled:bg-gray-100 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
          value={note}
          onChange={(e) => setNote(e.target.value)}
          placeholder="Enter Return Note"
        />
      </div>
      <div className="mt-[10px] flex w-full items-center justify-center">
        <FilledButton
          isLoading={isLoading}
          text="Return"
          handleClick={() => handleSubmit()}
          isDisabled={false}
        />
      </div>
    </div>
  );
}

export default ShopOrderProductReturnModal;
