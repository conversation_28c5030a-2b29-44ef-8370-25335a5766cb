import BaseApi from '../baseApi';

import { TagTypes } from '@/redux/tag-types';
import {
  GetSupplierDetailsResponse,
  GetSuppliersResponse,
  SupplierDetailsBillsListResponse,
  SupplierPaymentsListResponse,
} from '@/types/warehouseTypes/suppliersTypes';

interface GetBrandSuppliers {
  organizationId: string;
  warehouseId?: string;
  name?: string;
  mobileNumber?: string;
  page?: string;
  limit?: string;
}

const SuppliersApi = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getSuppliers: builder.query<GetSuppliersResponse, GetBrandSuppliers>({
      query: (params) => ({
        url: '/supplier',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SUPPLIERS],
    }),
    getSupplierDetails: builder.query<GetSupplierDetailsResponse, any>({
      query: (id) => ({
        url: `/supplier/${id}`,
        method: 'GET',
      }),
      providesTags: [TagTypes.SUPPLIERS],
    }),
    getSupplierPayments: builder.query<SupplierPaymentsListResponse, any>({
      query: (params) => ({
        url: `/supplier/payments`,
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SUPPLIERS],
    }),
    createSupplier: builder.mutation({
      query: (data) => ({
        url: '/supplier/new',
        method: 'POST',
        data,
      }),
      invalidatesTags: [TagTypes.SUPPLIERS],
    }),
    updateSupplier: builder.mutation({
      query: ({ data, id }) => ({
        url: `/supplier/${id}`,
        method: 'PATCH',
        data,
      }),
      invalidatesTags: [TagTypes.SUPPLIERS],
    }),
    deleteSupplier: builder.mutation({
      query: (id) => ({
        url: `/supplier/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: [TagTypes.SUPPLIERS],
    }),
    getSupplierBillsList: builder.query<SupplierDetailsBillsListResponse, any>({
      query: (id) => ({
        url: `/supplier-bill/supplier/${id}`,
        method: 'GET',
      }),
      providesTags: [TagTypes.SUPPLIERS],
    }),
    payToSupplier: builder.mutation({
      query: (data) => ({
        url: '/supplier/payment/new',
        method: 'POST',
        data,
      }),
      invalidatesTags: [TagTypes.SUPPLIERS],
    }),
  }),
});

export const {
  useGetSuppliersQuery,
  useGetSupplierDetailsQuery,
  useGetSupplierPaymentsQuery,
  useCreateSupplierMutation,
  useUpdateSupplierMutation,
  useDeleteSupplierMutation,
  useGetSupplierBillsListQuery,
  usePayToSupplierMutation,
} = SuppliersApi;
