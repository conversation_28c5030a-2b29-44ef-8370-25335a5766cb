import { useFormik } from 'formik';
import Cookies from 'js-cookie';
import { useEffect } from 'react';
import { toast } from 'react-toastify';
import * as Yup from 'yup';

import FilledSubmitButton from '../../reusable/Buttons/FilledSubmitButton';
import CustomInputField from '../../reusable/CustomInputField/CustomInputField';
import ModalTitle from '../../reusable/Modal/ModalTitle';

import CustomDropdown from '@/components/reusable/CustomInputField/CustomDropdown';
import { useGetOrgShopsQuery } from '@/redux/api/organizationApis/orgShopAndWarehouseApis';
import {
  useCreateCourierMutation,
  useUpdateCourierMutation,
} from '@/redux/api/warehouseApis/couriersApi';
import { SingleShopDetails } from '@/types/shopTypes';
import { SingleCourierDetails } from '@/types/warehouseTypes/settingsTypes';

interface Props {
  type: string;
  handleClose: () => void;
  updateRefreshCounter: () => void;
  courierDetails?: SingleCourierDetails;
}

const formikInitialValues = {
  type: '',
  secretKey: null,
  apiKey: null,
  clientId: null,
  clientSecret: null,
  userName: null,
  password: null,
  storeId: null,
  nickName: null,
  assignedShopId: null,
};

const steadFastValidation = Yup.object({
  // type: Yup.string().required('Courier Name is required'),
  nickName: Yup.string().required('Courier Nickname is required'),
  // secretKey: Yup.string().required('Secret Key is required'),
  // apiKey: Yup.string().required('Api Key is required'),
});

function AddOrEditCourierSettingsModal({
  type,
  handleClose,
  updateRefreshCounter,
  courierDetails,
}: Props) {
  const organizationId = Cookies.get('organizationId');
  const { data } = useGetOrgShopsQuery({
    organizationId,
  });

  const [createCourier, { isLoading }] = useCreateCourierMutation();
  const [updateCourier, { isLoading: isCourierUpdating }] =
    useUpdateCourierMutation();

  const formik = useFormik({
    initialValues: formikInitialValues,
    validationSchema: steadFastValidation,

    onSubmit: async (values) => {
      if (type === 'new') {
        toast.promise(
          createCourier({
            ...values,
            organizationId,
          }).unwrap(),
          {
            pending: 'Adding New Courier...',
            success: {
              render({ data: res }) {
                if (res?.statusCode === 200 || res?.statusCode === 201) {
                  updateRefreshCounter();
                  handleClose();
                }
                return 'Courier Added Successfully';
              },
            },
            error: {
              render({ data: error }) {
                console.log(error);
                return 'Error on creating courier';
              },
            },
          },
        );
      } else {
        const payload = values;
        // @ts-ignore
        delete payload.type;
        toast.promise(
          updateCourier({
            data: {
              ...payload,
              // organizationId: Cookies.get('organizationId'),
            },
            id: courierDetails?.id,
          }).unwrap(),
          {
            pending: 'Updating Courier Details...',
            success: {
              render({ data: res }) {
                if (res?.statusCode === 200 || res?.statusCode === 201) {
                  updateRefreshCounter();
                  handleClose();
                }
                return 'Courier Updated Successfully';
              },
            },
            error: {
              render({ data: error }) {
                console.log(error);
                return 'Error on updating courier';
              },
            },
          },
        );
      }
    },
  });

  useEffect(() => {
    if (courierDetails && type === 'edit') {
      formik.setFieldValue('type', courierDetails?.type);
      formik.setFieldValue('secretKey', courierDetails?.secretKey);
      formik.setFieldValue('apiKey', courierDetails?.apiKey);
      formik.setFieldValue('clientId', courierDetails?.clientId);
      formik.setFieldValue('clientSecret', courierDetails?.clientSecret);
      formik.setFieldValue('userName', courierDetails?.userName);
      formik.setFieldValue('password', courierDetails?.password);
      formik.setFieldValue('storeId', courierDetails?.storeId);
      formik.setFieldValue('nickName', courierDetails?.nickName);
    }
  }, [type, courierDetails]);

  return (
    <div className="flex w-[400px] flex-col gap-4 rounded-xl bg-white p-4">
      <ModalTitle
        text={type === 'new' ? 'Add New Courier' : 'Edit Courier'}
        handleClose={handleClose}
      />
      <form
        onSubmit={formik.handleSubmit}
        className="flex w-full flex-col gap-4"
      >
        <CustomDropdown
          placeholder="Select Shop"
          name="assignedShopId"
          label="Shop"
          formik={formik}
          options={data?.data
            ?.filter((sin) => sin.type === 'ONLINE')
            .map((shop: SingleShopDetails) => ({
              value: shop.id,
              label: shop.name,
            }))}
        />
        <CustomDropdown
          placeholder="Select Courier"
          name="type"
          label="Courier Type"
          formik={formik}
          options={[
            { value: 'PATHAO', label: 'Pathao' },
            { value: 'STEADFAST', label: 'Steadfast' },
            // { value: 'REDX', label: 'RedX' },
          ]}
        />
        <CustomInputField
          type="text"
          placeholder="Courier Nickname"
          name="nickName"
          label="Courier Nickname"
          formik={formik}
        />
        {formik.values.type === 'STEADFAST' ? (
          <>
            <CustomInputField
              type="text"
              placeholder="Enter Api Key"
              name="apiKey"
              label="Api Key"
              formik={formik}
            />
            <CustomInputField
              type="text"
              placeholder="Enter Secret Key"
              name="secretKey"
              label="Secret Key"
              formik={formik}
            />
          </>
        ) : formik.values.type === 'PATHAO' ? (
          <>
            <CustomInputField
              type="text"
              placeholder="Enter Client ID"
              name="clientId"
              label="Pathao Client Id"
              formik={formik}
            />
            <CustomInputField
              type="text"
              placeholder="Enter Client Secret"
              name="clientSecret"
              label="Pathao Client Secret"
              formik={formik}
            />
            <CustomInputField
              type="text"
              placeholder="Enter Pathao Username"
              name="userName"
              label="Pathao Merchent Username"
              formik={formik}
            />
            <CustomInputField
              type="text"
              placeholder="Enter Pathao Password"
              name="password"
              label="Pathao Merchent Password"
              formik={formik}
            />
            <CustomInputField
              type="text"
              placeholder="Enter Pathao Store ID"
              name="storeId"
              label="Pathao Store Id"
              formik={formik}
            />
          </>
        ) : (
          ''
        )}
        <div className="mt-[10px] flex w-full items-center justify-center">
          <FilledSubmitButton
            text={type === 'new' ? 'Add Courier' : 'Update Courier'}
            isLoading={isLoading || isCourierUpdating}
          />
        </div>
      </form>
    </div>
  );
}

export default AddOrEditCourierSettingsModal;
