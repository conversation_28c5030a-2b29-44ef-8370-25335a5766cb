import { useParams } from 'react-router-dom';

import ShopStockReportPageOverview from '@/components/ShopComponents/ShopStockReportPageComponents/ShopStockReportPageOverview';
import { useAppSelector } from '@/redux/hooks';

function ShopStockReportPage() {
  const { shopId } = useParams();
  const { warehouseId } = useAppSelector((state) => state.shopDetails);
  return (
    <div>
      <ShopStockReportPageOverview
        shopId={shopId ?? ''}
        warehouseId={warehouseId}
      />
    </div>
  );
}

export default ShopStockReportPage;
