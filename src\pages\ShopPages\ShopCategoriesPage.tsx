import { useParams } from 'react-router-dom';

import ShopCategoriesPageOverview from '@/components/ShopComponents/ShopCategoriesPageComponents/ShopCategoriesPageOverview';
import { useAppSelector } from '@/redux/hooks';

function ShopCategoriesPage() {
  const { shopId } = useParams();
  const { warehouseId } = useAppSelector((state) => state.shopDetails);
  return (
    <div>
      <ShopCategoriesPageOverview
        shopId={shopId ?? ''}
        warehouseId={warehouseId}
      />
    </div>
  );
}

export default ShopCategoriesPage;
