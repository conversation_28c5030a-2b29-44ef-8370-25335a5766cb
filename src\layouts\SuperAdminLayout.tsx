import { useState } from 'react';
import { Outlet } from 'react-router-dom';

import Navbar from '@/components/reusable/SidebarNavbar/Navbar';
import SuperAdminSidebar from '@/components/reusable/SidebarNavbar/SuperAdminSidebar';

function SuperAdminLayout() {
  const [isSidebarOpen, setIsSidebarOpen] = useState<boolean>(false);
  return (
    <div className="h-[100vh]">
      <div className="fixed top-0 w-full">
        <Navbar
          isSidebarOpen={isSidebarOpen}
          setIsSidebarOpen={setIsSidebarOpen}
        />
      </div>
      <div
        className="fixed top-[60px] w-full"
        style={{
          height: `calc(100vh - 60px)`,
        }}
      >
        <div className="flex h-full">
          <SuperAdminSidebar isSidebarOpen={isSidebarOpen} />
          <div className="w-full bg-slate-200 px-4 pt-4">
            <Outlet />
          </div>
        </div>
      </div>
    </div>
  );
}

export default SuperAdminLayout;
