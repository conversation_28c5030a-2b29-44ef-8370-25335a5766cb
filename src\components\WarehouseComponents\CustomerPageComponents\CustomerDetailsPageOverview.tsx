import { MapPin, Phone, User } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

import CustomerPurchaseItemsList from './CustomerPurchaseItemsList';

import FilledButton from '@/components/reusable/Buttons/FilledButton';
import OrderListViewer from '@/components/reusable/OrdersPagesReusableComponents/OrderListViewer';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import SpinnerLoader from '@/components/reusable/SpinnerLoader/SpinnerLoader';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useGetCustomersDetailsQuery } from '@/redux/api/warehouseApis/customersApi';
import { useGetWarehouseOrdersQuery } from '@/redux/api/warehouseApis/warehouseOrdersApis';
import { useAppSelector } from '@/redux/hooks';
import { ROUTES } from '@/Routes';

interface Props {
  customerId: string;
  shopId: string;
  warehouseId?: string;
}

function CustomerDetailsPageOverview({
  customerId,
  shopId,
  warehouseId: warehouseIdProp,
}: Props) {
  const { warehouseId } = useAppSelector((state) => state.shopDetails);
  const navigate = useNavigate();
  const { data, isLoading } = useGetCustomersDetailsQuery(customerId);

  const { data: orderData, isLoading: isOrderDataLoading } =
    useGetWarehouseOrdersQuery({
      warehouseId,
      customerId: customerId ?? undefined,
      shopId: shopId ?? undefined,
    });

  return (
    <div>
      {!isLoading ? (
        <div>
          <div className="mb-2 w-full rounded-2xl border border-gray-100 bg-white p-6 shadow-lg md:p-8">
            {/* Top: Customer Identity */}
            <div className="mb-8 flex flex-col gap-6 md:flex-row md:items-center md:justify-between">
              <div className="flex items-center gap-4">
                <div className="flex h-16 w-16 items-center justify-center rounded-full bg-green-100 text-green-600">
                  <User size={28} />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-gray-800">
                    {data?.data?.name}
                  </h2>
                  <p className="text-sm text-gray-500">
                    Customer ID: {data?.data?.serialNo}
                  </p>
                </div>
              </div>

              {shopId ? (
                <div>
                  <FilledButton
                    text="➕ Create New Order"
                    handleClick={() =>
                      navigate(ROUTES.SHOP.ADD_NEW_ORDER(shopId, customerId))
                    }
                    isDisabled={false}
                    isLoading={false}
                  />
                </div>
              ) : (
                ''
              )}
            </div>

            {/* Contact Info */}
            <div className="mb-6 grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div className="flex items-start gap-3">
                <Phone className="mt-1 text-gray-400" size={18} />
                <div>
                  <p className="text-sm text-gray-500">Phone Number</p>
                  <p className="font-medium text-gray-700">
                    {data?.data?.mobileNumber}
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <MapPin className="mt-1 text-gray-400" size={18} />
                <div>
                  <p className="text-sm text-gray-500">Address</p>
                  <p className="font-medium text-gray-700">
                    {data?.data?.address}
                  </p>
                </div>
              </div>
            </div>

            {/* Billing Summary */}
            <div className="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-3">
              <div className="rounded-xl bg-gray-50 p-4 text-center shadow-sm">
                <p className="text-sm text-gray-500">Total Order</p>
                <p className="text-xl font-semibold text-gray-700">
                  ৳ {data?.data?.totalAmount ?? 0}
                </p>
              </div>
              <div className="rounded-xl bg-green-50 p-4 text-center shadow-sm">
                <p className="text-sm text-gray-500">Total Paid</p>
                <p className="text-xl font-semibold text-green-600">
                  ৳ {data?.data?.totalPaid ?? 0}
                </p>
              </div>
              <div className="rounded-xl bg-red-50 p-4 text-center shadow-sm">
                <p className="text-sm text-gray-500">Due Amount</p>
                <p className="text-xl font-semibold text-red-600">
                  ৳ {data?.data?.totalDue ?? 0}
                </p>
              </div>
            </div>
          </div>

          <div className="mt-6">
            <Tabs defaultValue="account" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="account">Orders List</TabsTrigger>
                <TabsTrigger value="password">Purchase Products</TabsTrigger>
              </TabsList>
              <TabsContent value="account">
                <div className="mb-20">
                  {!isOrderDataLoading ? (
                    <OrderListViewer
                      orders={orderData?.data}
                      warehouseId=""
                      shopType={warehouseIdProp ? 'WAREHOUSE' : 'SHOP'}
                      total={orderData?.pagination.total}
                    />
                  ) : (
                    <TableSkeletonLoader tableColumn={10} tableRow={5} />
                  )}
                </div>
              </TabsContent>
              <TabsContent value="password">
                <CustomerPurchaseItemsList
                  customerId={customerId}
                  warehouseId={
                    warehouseId.length ? warehouseId : warehouseIdProp ?? ''
                  }
                  // shopId={shopId}
                />
              </TabsContent>
            </Tabs>
          </div>
        </div>
      ) : (
        <SpinnerLoader />
      )}
    </div>
  );
}

export default CustomerDetailsPageOverview;
