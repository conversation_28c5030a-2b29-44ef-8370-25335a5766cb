import { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import OrderPageFilterModal from './OrderPageFilterModal';
import OrderPageFilterOptions from './OrderPageFilterOptions';
import StatusWiseOrderCountView from './StatusWiseOrderCountView';

import FilterButton from '@/components/reusable/Buttons/FilterButton';
import OrderListViewer from '@/components/reusable/OrdersPagesReusableComponents/OrderListViewer';
import Pagination from '@/components/reusable/Pagination/Pagination';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import { useGetWarehouseOrdersQuery } from '@/redux/api/warehouseApis/warehouseOrdersApis';
import { useGetWarehouseOrderStatusWiseReportQuery } from '@/redux/api/warehouseApis/warehouseReportsApis';
import { ROUTES } from '@/Routes';
import { generateFilterParams } from '@/utils/generateFilterParams';

interface Props {
  warehouseId: string;
  orderType: 'Normal' | 'Wholesale';
}

function OrdersPageOverview({ warehouseId, orderType }: Props) {
  const navigate = useNavigate();
  const router = new URLSearchParams(useLocation().search);
  const customerId = router.get('customerId');
  const customerName = router.get('customerName');
  const mobileNumber = router.get('mobileNumber');
  const serialNo = router.get('serialNo');
  const page = router.get('page');
  const limit = router.get('limit');
  const startDate = router.get('startDate');
  const endDate = router.get('endDate');
  const orderStatus = router.get('orderStatus');
  const hasDue = router.get('hasDue');

  const [isFilterModalOpen, setIsFilterModalOpen] = useState<boolean>(false);

  const { data, isLoading, isFetching } = useGetWarehouseOrdersQuery({
    warehouseId,
    page: page ?? '1',
    limit: limit ?? '10',
    serialNo: serialNo ?? undefined,
    customerName: customerName ?? undefined,
    mobileNumber: mobileNumber ?? undefined,
    customerId: customerId ?? undefined,
    startDate: startDate ?? undefined,
    endDate: endDate ?? undefined,
    orderStatus: orderStatus ?? undefined,
    hasDue: hasDue ? hasDue === 'true' : undefined,
    orderType,
  });

  const { data: statusData } = useGetWarehouseOrderStatusWiseReportQuery({
    type: 'warehouse',
    warehouseId,
  });

  const handleFilter = (fieldName: string, value: string) => {
    const query = generateFilterParams(fieldName, value);
    if (orderType === 'Normal') {
      navigate(ROUTES.WAREHOUSE.ORDERS(warehouseId, query));
    } else {
      navigate(ROUTES.WAREHOUSE.WHOLESALE_ORDERS(warehouseId, query));
    }
  };

  return (
    <div>
      <div className="search-filters mb-1 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-2">
        <div className="flex items-center">
          <div className="search-title-and-btn flex items-center">
            <div className="relative">
              <div className="block xl:hidden">
                <FilterButton
                  handleClick={() => setIsFilterModalOpen(!isFilterModalOpen)}
                />
              </div>
              <div
                className={`${isFilterModalOpen ? 'block' : 'hidden'} xl:hidden`}
              >
                <OrderPageFilterModal
                  handleClearAndClose={() => setIsFilterModalOpen(false)}
                  handleFilter={handleFilter}
                />
              </div>
            </div>
          </div>
          <div className="hidden xl:block">
            <div className="flex items-center gap-x-2">
              <OrderPageFilterOptions handleFilter={handleFilter} />
            </div>
          </div>
        </div>
      </div>
      {!isLoading && !isFetching ? (
        <div>
          <div className="mb-1">
            <StatusWiseOrderCountView
              statusData={statusData}
              handleFilter={handleFilter}
              orderStatus={orderStatus ?? ''}
            />
          </div>
          <OrderListViewer
            orders={data?.data}
            total={data?.pagination.total}
            shopType="WAREHOUSE"
            warehouseId={warehouseId}
          />
          <div className="pagination-box flex justify-end rounded bg-white p-3">
            <Pagination
              currentPage={page ?? '1'}
              limit={Number(limit ?? 10)}
              handleFilter={(fieldName: string, value: any) =>
                handleFilter(fieldName, value)
              }
              totalCount={data?.pagination?.total}
              totalPages={Math.ceil(
                Number(data?.pagination?.total) /
                  Number(data?.pagination?.limit),
              )}
            />
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={14} tableRow={6} />
      )}
    </div>
  );
}

export default OrdersPageOverview;
