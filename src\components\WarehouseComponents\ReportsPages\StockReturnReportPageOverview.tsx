import { useLocation } from 'react-router-dom';

import ExportButton from '@/components/reusable/Buttons/ExportButton';
import FilterButton from '@/components/reusable/Buttons/FilterButton';
import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import OrderStatusViewer from '@/components/reusable/OrdersPagesReusableComponents/OrderStatusViewer';
import StartDateEndDateWithSearch from '@/components/reusable/ReusableFilters/StartDateEndDateWithSearch';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import { useGetWarehouseStockReturnReportQuery } from '@/redux/api/warehouseApis/warehouseReportsApis';
import { useAppSelector } from '@/redux/hooks';
import { SingleStockReturnReport } from '@/types/reportTypes/stockReturnReportType';
import { handleGenerateStockReturnReportPdf } from '@/utils/GenerateReportPdf';
import { handleGenerateStockReturnReportCsv } from '@/utils/ReportExport/ExportStockDeleteReport';

interface Props {
  warehouseId: string;
  shopId?: string;
  viewFrom: 'shop' | 'warehouse';
}

function StockReturnReportPageOverview({
  warehouseId,
  shopId,
  viewFrom,
}: Props) {
  const { warehouseDetails, shopDetails } = useAppSelector((state) => state);
  const router = new URLSearchParams(useLocation().search);
  const startDate = router.get('startDate') || `${new Date().toISOString()}`;
  const endDate = router.get('endDate') || `${new Date().toISOString()}`;
  const { data, isLoading } = useGetWarehouseStockReturnReportQuery({
    warehouseId: warehouseId ?? shopDetails?.warehouseId ?? undefined,
    type: 'custom',
    startDate,
    endDate,
    shopId: shopId ?? undefined,
  });

  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-1">
        <div className="flex items-center gap-x-2">
          <div className="search-title-and-btn flex items-center gap-x-3">
            <div className="relative">
              <div className="block xl:hidden">
                <FilterButton handleClick={() => console.log('higbig')} />
              </div>
              <div className="block xl:hidden">
                {/* <ProductPageFilterModal /> */}
              </div>
            </div>
          </div>
          <div className="hidden xl:block">
            <StartDateEndDateWithSearch />
          </div>
        </div>
      </div>
      {!isLoading ? (
        <div>
          <div className="tableTop w-full">
            <p>Return Stock List</p>
            <div className="flex items-center">
              <p>Total : {data?.data.length}</p>
              <div className="ml-4">
                <ExportButton
                  totalCount={data?.data.length ?? 0}
                  handleExportCsv={() =>
                    handleGenerateStockReturnReportCsv({
                      data: data?.data,
                    })
                  }
                  handleExportPdf={() =>
                    handleGenerateStockReturnReportPdf({
                      data,
                      warehouseDetails,
                      shopDetails,
                      viewFrom,
                    })
                  }
                />
              </div>
            </div>
          </div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {!isLoading ? (
              <div className="full-table-box h-customExc">
                <table className="full-table">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="tableHead">No</th>
                      <th className="tableHead">Name</th>
                      <th className="tableHead">Barcode</th>
                      <th className="tableHead">OrderId</th>
                      <th className="tableHead">Status</th>
                      <th className="tableHead">Regular Price</th>
                      <th className="tableHead">Return Date</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y bg-slate-200">
                    {data?.data?.map(
                      (single: SingleStockReturnReport, index) => (
                        <tr key={single.id}>
                          <td className="tableData">{index + 1}</td>
                          <td className="tableData">{single.name}</td>
                          <td className="tableData">{single.barcode}</td>
                          <td className="tableData">
                            {single.OrderItem[0]?.Order?.serialNo}
                          </td>
                          <td className="tableData">
                            <OrderStatusViewer status={single.status} />
                          </td>
                          <td className="tableData">{single.retailPrice}</td>
                          <td className="tableData">
                            <DateAndTimeViewer date={single.updatedAt} />
                          </td>
                        </tr>
                      ),
                    )}
                  </tbody>
                </table>
              </div>
            ) : (
              <div>
                <h2>No product found</h2>
              </div>
            )}
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={12} tableRow={6} />
      )}
    </div>
  );
}

export default StockReturnReportPageOverview;
