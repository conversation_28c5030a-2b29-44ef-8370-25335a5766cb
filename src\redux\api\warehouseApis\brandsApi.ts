import { GetBrandsResponse } from '@/types/warehouseTypes/brandsTypes';
import BaseApi from '../baseApi';
import { TagTypes } from '@/redux/tag-types';

interface GetBrandParams {
  warehouseId?: string;
  name?: string;
  page?: string;
  limit?: string;
  organizationId?: string;
}

const BrandsApi = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getBrands: builder.query<GetBrandsResponse, GetBrandParams>({
      query: (params) => ({
        url: '/brand',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.BRAND],
    }),
    createBrand: builder.mutation({
      query: (data) => ({
        url: '/brand/new',
        method: 'POST',
        data,
      }),
      invalidatesTags: [TagTypes.BRAND],
    }),
    updateBrand: builder.mutation({
      query: ({ data, id }) => ({
        url: `/brand/${id}`,
        method: 'PATCH',
        data,
      }),
      invalidatesTags: [TagTypes.BRAND],
    }),
    deleteBrand: builder.mutation({
      query: (id) => ({
        url: `/brand/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: [TagTypes.BRAND],
    }),
  }),
});

export const {
  useGetBrandsQuery,
  useCreateBrandMutation,
  useDeleteBrandMutation,
  useUpdateBrandMutation,
} = BrandsApi;
