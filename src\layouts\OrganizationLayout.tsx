import { useState } from 'react';
import { Outlet } from 'react-router-dom';

import OrganizationPageNavbar from '@/components/reusable/SidebarNavbar/OrganizationPageNavbar';
import OrganizationSidebar from '@/components/reusable/SidebarNavbar/OrganizationSidebar';

function OrganizationLayout() {
  const [isSidebarOpen, setIsSidebarOpen] = useState<boolean>(false);

  const handleKeyDown = (event: React.KeyboardEvent<HTMLDivElement>) => {
    if (event.key === 'Enter' || event.key === ' ') {
      setIsSidebarOpen(false);
    }
  };

  return (
    <div className="h-[100vh]">
      <div className="fixed top-0 w-full">
        <OrganizationPageNavbar />
      </div>
      <div
        className="fixed top-[60px] w-full"
        style={{ height: 'calc(100vh - 60px)' }}
      >
        <div className="flex h-full bg-slate-200">
          <OrganizationSidebar isSidebarOpen={isSidebarOpen} />
          {isSidebarOpen && (
            <div
              className="absolute z-40 h-full w-full cursor-pointer bg-[#28243d5e]"
              onClick={() => setIsSidebarOpen(false)}
              onKeyDown={handleKeyDown}
              role="button"
              tabIndex={0}
              aria-label="Close sidebar"
            />
          )}
          <div className="h-full w-full overflow-y-auto px-4 pt-4">
            <Outlet />
          </div>
        </div>
      </div>
    </div>
  );
}

export default OrganizationLayout;
