import Cookies from 'js-cookie';
import { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import FilterButton from '@/components/reusable/Buttons/FilterButton';
import ImageViewer from '@/components/reusable/ImageViewer/ImageViewer';
import SearchInput from '@/components/reusable/Inputs/SearchInput';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import Pagination from '@/components/reusable/Pagination/Pagination';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import BrandPageFilterModal from '@/components/WarehouseComponents/BrandPageComponents/BrandPageFilterModal';
import { useGetShopBrandsQuery } from '@/redux/api/shopApis/shopBrandsApi';
import { ROUTES } from '@/Routes';
import { SingleBrandDetails } from '@/types/warehouseTypes/brandsTypes';
import { generateFilterParams } from '@/utils/generateFilterParams';

interface Props {
  shopId: string;
  warehouseId: string;
}

function ShopBrandsPageOverview({ shopId, warehouseId }: Props) {
  const navigate = useNavigate();
  const router = new URLSearchParams(useLocation().search);
  const name = router.get('name');
  const page = router.get('page');
  const limit = router.get('limit');
  const organizationId = Cookies.get('organizationId') as string;
  const { data, isLoading, isFetching } = useGetShopBrandsQuery(
    {
      organizationId,
      warehouseId,
      shopId,
      name: name ?? undefined,
      page: page ?? '1',
      limit: limit ?? '10',
    },
    { skip: !(shopId && organizationId) },
  );
  const [isFilterModalOpen, setIsFilterModalOpen] = useState<boolean>(false);

  const handleFilter = (fieldName: string, value: string) => {
    const query = generateFilterParams(fieldName, value);

    navigate(ROUTES.SHOP.BRANDS(shopId, query));
  };

  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 lg:py-1">
        <div className="flex items-center">
          <div className="search-title-and-btn flex items-center">
            <div className="relative">
              <div className="block lg:hidden">
                <FilterButton
                  handleClick={() => setIsFilterModalOpen(!isFilterModalOpen)}
                />
              </div>
              <div
                className={`${isFilterModalOpen ? 'block' : 'hidden'} xl:hidden`}
              >
                <BrandPageFilterModal />
              </div>
            </div>
          </div>
          <div className="hidden lg:block">
            <div className="flex items-center gap-x-2">
              <SearchInput
                placeholder="Search by Name"
                handleSubmit={(value: string) => handleFilter('name', value)}
                value={name ?? ''}
              />
            </div>
          </div>
        </div>
      </div>
      {!isLoading && !isFetching ? (
        <div>
          <div className="tableTop w-full">
            <p>Brand List</p>
            <p>Total : {data?.pagination?.total}</p>
          </div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {data?.data?.length ? (
              <div className="full-table-box h-custom">
                <table className="full-table">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="tableHead">No</th>
                      <th className="tableHead">Image</th>
                      <th className="tableHead">Name</th>
                      {/* <th className="tableHead">Created At</th> */}
                    </tr>
                  </thead>
                  <tbody className="divide-y bg-slate-200">
                    {data?.data?.map(
                      (brand: SingleBrandDetails, index: number) => (
                        <tr key={brand?.id}>
                          <td className="tableData">{index + 1}</td>
                          <td className="tableData">
                            <ImageViewer imageUrl={brand?.imgUrl} />
                          </td>
                          <td className="tableData">{brand?.name}</td>
                          {/* <td className="tableData">
                            <DateAndTimeViewer date={brand.createdAt} />
                          </td> */}
                        </tr>
                      ),
                    )}
                  </tbody>
                </table>
              </div>
            ) : (
              <NoResultFound pageType="brands" />
            )}
          </div>
          <div className="pagination-box flex justify-end rounded bg-white p-3">
            <Pagination
              currentPage={page ?? '1'}
              limit={Number(limit ?? 10)}
              handleFilter={(fieldName: string, value: any) =>
                handleFilter(fieldName, value)
              }
              totalCount={data?.pagination?.total}
              totalPages={Math.ceil(
                Number(data?.pagination?.total) /
                  Number(data?.pagination?.limit),
              )}
            />
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={4} tableRow={6} />
      )}
    </div>
  );
}

export default ShopBrandsPageOverview;
