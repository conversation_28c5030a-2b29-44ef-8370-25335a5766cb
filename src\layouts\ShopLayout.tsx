import { useEffect, useState } from 'react';
import { Outlet } from 'react-router-dom';

import Navbar from '@/components/reusable/SidebarNavbar/Navbar';
import ShopSidebar from '@/components/reusable/SidebarNavbar/ShopSidebar';
import { useGetShopDetailsQuery } from '@/redux/api/shopApis/shopDetailsApi';
import { useAppDispatch } from '@/redux/hooks';
import { setShopSettings } from '@/redux/slice/shopSettingsSlice';
import { setShopDetails } from '@/redux/slice/storeSlice';

function ShopLayout() {
  const { pathname } = window.location;
  const shopId = pathname.split('/')[2];
  const dispatch = useAppDispatch();
  const [isSidebarOpen, setIsSidebarOpen] = useState<boolean>(false);

  const handleKeyDown = (event: React.KeyboardEvent<HTMLDivElement>) => {
    if (event.key === 'Enter' || event.key === ' ') {
      setIsSidebarOpen(false);
    }
  };
  const { data, isLoading } = useGetShopDetailsQuery(shopId);
  useEffect(() => {
    if (shopId) {
      dispatch({ type: 'socket/connect', payload: shopId }); // Connect WebSocket once shopId is available
    }
  }, [dispatch, shopId]);

  useEffect(() => {
    if (!isLoading && data?.data) {
      dispatch(setShopDetails(data?.data));
      if (data?.data.ShopSettings) {
        dispatch(setShopSettings(data?.data.ShopSettings));
      }
    }
  }, [data, dispatch]);

  return (
    <div className="h-[100vh]">
      <div className="fixed top-0 w-full">
        <Navbar
          isSidebarOpen={isSidebarOpen}
          setIsSidebarOpen={setIsSidebarOpen}
        />
      </div>
      <div
        className="fixed top-[60px] w-full"
        style={{ height: 'calc(100vh - 60px)' }}
      >
        <div className="flex h-full bg-slate-200">
          <ShopSidebar isSidebarOpen={isSidebarOpen} />
          {isSidebarOpen && (
            <div
              className="absolute z-40 h-full w-full cursor-pointer bg-[#28243d5e]"
              onClick={() => setIsSidebarOpen(false)}
              onKeyDown={handleKeyDown}
              role="button"
              tabIndex={0}
              aria-label="Close sidebar"
            />
          )}
          <div className="h-full w-full overflow-y-auto px-4 pt-4">
            <Outlet />
          </div>
        </div>
      </div>
    </div>
  );
}

export default ShopLayout;
