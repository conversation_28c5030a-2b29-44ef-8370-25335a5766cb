import { Page, Text, View } from '@react-pdf/renderer';

import PrintedTime from './PrintedTime';
import { reportPdfStyles } from './ReportPdfStyles';
import SoftwareMarketingOnPdf from './SoftwareMarketingOnPdf';

import { Warehouse } from '@/types/shopTypes/shopExpensesTypes';
import { SingleProductDataOnDetails } from '@/types/warehouseTypes/productTypes';
import { truncateText } from '@/utils/stringTruncate';

interface Props {
  products: SingleProductDataOnDetails[];
  warehouse?: Warehouse;
}

function StockAnalysisReportPdf({ products, warehouse }: Props) {
  return (
    <Page size="A4" style={reportPdfStyles.page}>
      <View>
        <View style={reportPdfStyles.header}>
          <Text style={reportPdfStyles.userName}>{warehouse?.name}</Text>
          {/* <Text style={reportPdfStyles.address}>
            Tangail, Dhaka, Bangladesh
          </Text>
          <Text style={reportPdfStyles.phone}>01739719796</Text> */}
        </View>
        <PrintedTime />

        <View style={reportPdfStyles.table}>
          <View style={reportPdfStyles.tableRow}>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.stockAnalysisCol,
                reportPdfStyles.stockAnalysisNameCol,
              ]}
            >
              Name
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.stockAnalysisCol,
                reportPdfStyles.stockAnalysisTotalCol,
              ]}
            >
              Total
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.stockAnalysisCol,
              ]}
            >
              Available
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.stockAnalysisCol,
              ]}
            >
              Previous
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.stockAnalysisCol,
                reportPdfStyles.stockAnalysisMonthEntryCol,
              ]}
            >
              This Month Entry
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.stockAnalysisCol,
                reportPdfStyles.stockAnalysisMonthSoldCol,
              ]}
            >
              This Month Sold
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.stockAnalysisCol,
                reportPdfStyles.tableColLast,
                reportPdfStyles.stockAnalysisTotalSoldCol,
              ]}
            >
              Total Sold
            </Text>
          </View>
          {products.map((product: SingleProductDataOnDetails, index) => (
            <View
              key={product.id}
              style={
                index === products.length - 1
                  ? [reportPdfStyles.tableRowLast]
                  : [reportPdfStyles.tableRow]
              }
            >
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.stockAnalysisCol,
                  reportPdfStyles.stockAnalysisNameCol,
                ]}
              >
                {truncateText(product?.name)}
              </Text>
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.stockAnalysisCol,
                  reportPdfStyles.stockAnalysisTotalCol,
                ]}
              >
                {product.totalStock}
              </Text>
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.stockAnalysisCol,
                ]}
              >
                {product.totalAvailableStock}
              </Text>
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.stockAnalysisCol,
                ]}
              >
                {product.previousUnsoldStockCount}
              </Text>
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.stockAnalysisCol,
                  reportPdfStyles.stockAnalysisMonthEntryCol,
                ]}
              >
                {product.currentMonthStockEntryCount}
              </Text>
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.stockAnalysisCol,
                  reportPdfStyles.stockAnalysisMonthSoldCol,
                ]}
              >
                {product.thisMonthStockSoldCount}
              </Text>
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.stockAnalysisCol,
                  reportPdfStyles.tableColLast,
                  reportPdfStyles.stockAnalysisTotalSoldCol,
                ]}
              >
                {product.totalSold}
              </Text>
            </View>
          ))}
        </View>

        <SoftwareMarketingOnPdf />
      </View>
    </Page>
  );
}

export default StockAnalysisReportPdf;
