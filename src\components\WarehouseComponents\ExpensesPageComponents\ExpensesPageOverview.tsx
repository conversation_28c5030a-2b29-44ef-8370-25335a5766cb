import { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import AddExpenseModal from './AddExpenseModal';
import ExpensesPageFilterModal from './ExpensesPageFilterModal';
import ExpensesPageFilterOptions from './ExpensesPageFilterOptions';

import FilledButton from '@/components/reusable/Buttons/FilledButton';
import FilterButton from '@/components/reusable/Buttons/FilterButton';
import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import Modal from '@/components/reusable/Modal/Modal';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import Pagination from '@/components/reusable/Pagination/Pagination';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import { useGetShopsQuery } from '@/redux/api/shopApi';
import {
  useGetExpenseCategoriesQuery,
  useGetExpensesQuery,
} from '@/redux/api/warehouseApis/expensesApis';
import { ROUTES } from '@/Routes';
import { SingleShopDetails } from '@/types/shopTypes';
import { ShopExpenseDetails } from '@/types/shopTypes/shopExpensesTypes';
import { generateFilterParams } from '@/utils/generateFilterParams';

interface Props {
  warehouseId: string;
}

function ExpensesPageOverview({ warehouseId }: Props) {
  const navigate = useNavigate();
  const router = new URLSearchParams(useLocation().search);
  const name = router.get('name');
  const shopName = router.get('shopName');
  const page = router.get('page');
  const limit = router.get('limit');
  const expenseCategoryId = router.get('expenseCategoryId');
  const shopId = router.get('shopId');

  const { data, isLoading, refetch } = useGetExpensesQuery({
    warehouseId,
    page: page ?? '1',
    name: name ?? undefined,
    shopName: shopName ?? undefined,
    limit: limit ?? '10',
    expenseCategoryId: expenseCategoryId ?? null,
    shopId: shopId ?? null,
  });
  const [isCreateBrandModalOpen, setIsCreateBrandModalOpen] =
    useState<boolean>(false);

  const { data: expenseCategories, isLoading: isExpenseCategoriesLoading } =
    useGetExpenseCategoriesQuery({
      warehouseId,
    });

  const { data: shopData, isLoading: isShopListLoading } = useGetShopsQuery({
    warehouseId,
    isFromStockTransfer: false,
  });
  const [isFilterModalOpen, setIsFilterModalOpen] = useState<boolean>(false);

  const handleFilter = (fieldName: string, value: string) => {
    const query = generateFilterParams(fieldName, value);
    navigate(ROUTES.WAREHOUSE.EXPENSES(warehouseId, query));
  };

  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 lg:py-2">
        <div className="flex items-center">
          <div className="search-title-and-btn flex items-center">
            <div className="relative">
              <div className="block lg:hidden">
                <FilterButton
                  handleClick={() => setIsFilterModalOpen(!isFilterModalOpen)}
                />
              </div>
              <div
                className={`${isFilterModalOpen ? 'block' : 'hidden'} xl:hidden`}
              >
                <ExpensesPageFilterModal
                  handleClearAndClose={() => setIsFilterModalOpen(false)}
                  handleFilter={handleFilter}
                  categories={
                    !isExpenseCategoriesLoading &&
                    expenseCategories?.data?.length
                      ? expenseCategories?.data?.map((single: any) => {
                          return { value: single.id, label: single.name };
                        })
                      : []
                  }
                  shopList={
                    !isShopListLoading && shopData?.data?.length
                      ? shopData?.data?.map((single: SingleShopDetails) => {
                          return {
                            value: single.id,
                            label: `${single.name} (${single.nickName})`,
                          };
                        })
                      : []
                  }
                />
              </div>
            </div>
          </div>
          <div className="hidden lg:block">
            <div className="flex items-center gap-x-2">
              <ExpensesPageFilterOptions
                handleFilter={handleFilter}
                categories={
                  !isExpenseCategoriesLoading && expenseCategories?.data?.length
                    ? expenseCategories?.data?.map((single: any) => {
                        return { value: single.id, label: single.name };
                      })
                    : []
                }
                shopList={
                  !isShopListLoading && shopData?.data?.length
                    ? shopData?.data?.map((single: SingleShopDetails) => {
                        return {
                          value: single.id,
                          label: `${single.name} (${single.nickName})`,
                        };
                      })
                    : []
                }
              />
            </div>
          </div>
        </div>
        <div>
          <FilledButton
            isLoading={false}
            text="Add New Expense"
            handleClick={() => setIsCreateBrandModalOpen(true)}
            isDisabled={false}
          />
        </div>
      </div>
      {!isLoading ? (
        <div>
          <div className="tableTop w-full">
            <p>Expenses List</p>
            <p>Total : {data?.pagination?.total}</p>
          </div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {data?.data?.length ? (
              <div className="full-table-box h-custom">
                <table className="full-table">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="tableHead">No</th>
                      <th className="tableHead table-col-width">Name</th>
                      <th className="tableHead">Amount</th>
                      <th className="tableHead">Expense Category</th>
                      <th className="tableHead">Expense Location</th>
                      <th className="tableHead">Created By</th>
                      <th className="tableHead">Created At</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y bg-slate-200">
                    {data?.data?.map(
                      (expense: ShopExpenseDetails, index: number) => (
                        <tr key={expense?.id}>
                          <td className="tableData">{index + 1}</td>
                          <td className="tableData table-col-width">
                            {expense?.name}
                          </td>
                          <td className="tableData">{expense?.amount}</td>
                          <td className="tableData">
                            {expense?.ExpenseCategory?.name}
                          </td>
                          <td className="tableData">
                            {expense?.Shop?.name ?? expense?.Warehouse?.name}
                          </td>
                          <td className="tableData">
                            {expense?.CreatedBy?.name}
                          </td>
                          <td className="tableData">
                            <DateAndTimeViewer date={expense?.createdAt} />
                          </td>
                        </tr>
                      ),
                    )}
                  </tbody>
                </table>
              </div>
            ) : (
              <NoResultFound pageType="expense" />
            )}
          </div>
          <div className="pagination-box flex justify-end rounded bg-white p-3">
            <Pagination
              currentPage={page ?? '1'}
              limit={Number(limit ?? 10)}
              handleFilter={(fieldName: string, value: any) =>
                handleFilter(fieldName, value)
              }
              totalCount={data?.pagination?.total}
              totalPages={Math.ceil(
                Number(data?.pagination?.total) /
                  Number(data?.pagination?.limit),
              )}
            />
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={4} tableRow={6} />
      )}
      <Modal
        setShowModal={setIsCreateBrandModalOpen}
        showModal={isCreateBrandModalOpen}
      >
        <AddExpenseModal
          warehouseId={warehouseId}
          handleClose={() => setIsCreateBrandModalOpen(false)}
          updateRefreshCounter={refetch}
          categories={
            !isExpenseCategoriesLoading && expenseCategories?.data?.length
              ? expenseCategories?.data?.map((single: any) => {
                  return { value: single.id, label: single.name };
                })
              : []
          }
        />
      </Modal>
    </div>
  );
}

export default ExpensesPageOverview;
