import { useState } from 'react';
import { toast } from 'react-toastify';

import FilledButton from '@/components/reusable/Buttons/FilledButton';
import ModalTitle from '@/components/reusable/Modal/ModalTitle';
import { useAddOrderNoteMutation } from '@/redux/api/shopApis/shopOrdersApis';
import { ShopOrderDetails } from '@/types/shopTypes/shopOrderTypes';

interface Props {
  handleClose: () => void;
  orderDetails?: ShopOrderDetails;
}

function AddOrderNoteModal({ handleClose, orderDetails }: Props) {
  const [note, setNote] = useState<string>('');
  const [addNoteToOrder, { isLoading }] = useAddOrderNoteMutation();

  const handleSubmit = async () => {
    toast.promise(
      addNoteToOrder({
        orderId: orderDetails?.id,
        notes: note,
      }).unwrap(),
      {
        pending: 'Adding new note to Order...',
        success: {
          render({ data: res }) {
            if (res?.statusCode === 200 || res?.statusCode === 201) {
              handleClose();
            }
            return 'Note added Successfully';
          },
        },
        error: {
          render({ data: error }) {
            console.log(error);
            return 'Error on adding note to order';
          },
        },
      },
    );
  };

  return (
    <div className="flex w-[400px] flex-col gap-4 rounded-xl bg-white p-4">
      <ModalTitle text="Add New Note Modal" handleClose={handleClose} />
      <div className="group relative z-0 mt-[-20px] w-full">
        <span className="relative left-3 top-2.5 w-auto bg-white px-1 font-mono text-[12px] font-bold text-gray-900 group-focus-within:text-red-600 dark:text-gray-300">
          Note
        </span>
        <textarea
          aria-multiline
          className="text-10 py-55-rem block h-20 w-full rounded-lg border bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 disabled:cursor-not-allowed disabled:bg-gray-100 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
          value={note}
          onChange={(e) => setNote(e.target.value)}
          placeholder="Enter Order Note"
        />
      </div>
      <div className="mt-[10px] flex w-full items-center justify-center">
        <FilledButton
          isLoading={isLoading}
          text="Add Note"
          handleClick={() => handleSubmit()}
          isDisabled={isLoading || !note?.length}
        />
      </div>
    </div>
  );
}

export default AddOrderNoteModal;
