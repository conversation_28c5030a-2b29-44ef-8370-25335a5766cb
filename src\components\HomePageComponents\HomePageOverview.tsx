import Cookies from 'js-cookie';
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

import SpinnerLoader from '../reusable/SpinnerLoader/SpinnerLoader';

import SingleShopCard from './SingleShopCard';

import { useGetUserPermissionsQuery } from '@/redux/api/userApi';
import { ROUTES } from '@/Routes';

function HomePageOverview() {
  const type = Cookies.get('type');
  const navigate = useNavigate();
  useEffect(() => {
    if (type === 'CUSTOMER') {
      navigate(ROUTES.ORGANIZATION.DASHBOARD);
      return;
    }
    if (type === 'SUPER_ADMIN') {
      navigate(ROUTES.SUPER_ADMIN.DASHBOARD);
    }
  }, [type]);

  const { data, isLoading } = useGetUserPermissionsQuery(undefined);

  useEffect(() => {
    if (data?.data.length === 1) {
      if (data?.data[0].permissionLevel === 'warehouse') {
        navigate(ROUTES.WAREHOUSE.DASHBOARD(data?.data[0].warehouseId ?? ''));
      } else {
        navigate(ROUTES.SHOP.DASHBOARD(data?.data[0].shopId ?? ''));
      }
    }
  }, [data]);

  return (
    <div>
      <div className="mt-4 px-6 sm:px-10 lg:px-20" />
      <div className="mt-4 px-6 sm:px-10 lg:px-20">
        {!isLoading ? (
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4">
            {data?.data ? (
              data?.data?.map((shop: any) => (
                <SingleShopCard
                  shop={shop}
                  key={shop.warehouseId || shop.shopId}
                />
              ))
            ) : (
              <p>No Shop Found</p>
            )}
          </div>
        ) : (
          <SpinnerLoader />
        )}
      </div>
    </div>
  );
}

export default HomePageOverview;
