import { Document, PDFViewer } from '@react-pdf/renderer';
import { useFormik } from 'formik';
import Cookies from 'js-cookie';
import { useEffect, useState } from 'react';
import Lottie from 'react-lottie';
import { useLocation, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import * as Yup from 'yup';

import animationData from '../../../utils/lottieFiles/success-lottie.json';
import ShopCourierBookingModal from '../ShopCourierBooking/ShopCourierBookingModal';

import MultiplePaymentMethodModal from './MultiplePaymentMethodModal';
import ShopCreateOrderPageStockTable from './ShopCreateOrderPageStockTable';
import ShopCustomerDetailsFormInOrderForm from './ShopCustomerDetailsFormInOrderForm';
import ShopCustomerLastOrderDetails from './ShopCustomerLastOrderDetails';
import ShopCustomerLatestOrderModal from './ShopCustomerLatestOrderModal';
import ShopOrderConfirmModal from './ShopOrderConfirmModal';
import ShopOrderSubmitForm from './ShopOrderSubmitForm';

import SearchInput from '@/components/reusable/Inputs/SearchInput';
import Modal from '@/components/reusable/Modal/Modal';
import ModalTitle from '@/components/reusable/Modal/ModalTitle';
import NoResultFoundForOthers from '@/components/reusable/NoResultFound/NoResultFoundForOthers';
import SingleOrderInvoicePdf from '@/components/reusable/SingeOrderInvoicePdf/SingleOrderInvoicePdf';
import TableSkeletonLoaderHalf from '@/components/reusable/SkeletonLoader/TableSkeletionLoaderHalf';
import {
  useGetShopCustomerDetailsByPhoneQuery,
  useGetShopCustomerDetailsQuery,
} from '@/redux/api/shopApis/shopCustomersApi';
import { useGetShopSellersQuery } from '@/redux/api/shopApis/shopEmployeesApis';
import { useCreateShopOrderMutation } from '@/redux/api/shopApis/shopOrdersApis';
import { useGetShopStockListQuery } from '@/redux/api/shopApis/shopStockApis';
import { usePathaoAddressParserMutation } from '@/redux/api/warehouseApis/couriersApi';
import { useAppSelector } from '@/redux/hooks';
import { ROUTES } from '@/Routes';
import { ShopOrderDetails } from '@/types/shopTypes/shopOrderTypes';
import { PathaoParsedAddressDetails } from '@/types/warehouseTypes/settingsTypes';
import { SingleStockDetails } from '@/types/warehouseTypes/stockTypes';
import {
  CalculateDiscountPrice,
  CalculateVat,
} from '@/utils/CalculateDiscountPrice';
import {
  handleGenerateSingleOrderPdf,
  handleGenerateSingleOrderPosPdf,
} from '@/utils/GenerateOrderPdf';
import { getDivisionByDistrict } from '@/utils/getDivisionName';

// import { getLoggedInUserId } from '@/utils/getLoggedinUserId';

interface Props {
  shopId: string;
}

const formikInitialValues = {
  name: '',
  mobileNumber: '',
  address: null,
  email: null,
  gender: null,
  totalProductPrice: 0,
  productDiscount: 0,
  totalDiscount: 0,
  vat: 0,
  deliveryCharge: 0,
  customerPaid: 0,
  returnAmount: 0,
  deliveryPartner: 'OTHER',
  paymentMethod: 'CASH',
  customerId: '',
  note: '',
  employeeId: '',
  orderSource: 'FACEBOOK',
};

/* const validation = Yup.object({
  name: Yup.string().required('Customer name is required'),
  mobileNumber: Yup.string().required('Mobile Number is required'),
  address: Yup.string().required('Address is required'),
}); */
function ShopAddNewOrderPageOverview({ shopId }: Props) {
  const { warehouseId } = useAppSelector((state) => state.shopDetails);
  // const dispatch = useAppDispatch();
  const organizationId = Cookies.get('organizationId') as string;
  const navigate = useNavigate();
  const router = new URLSearchParams(useLocation().search);
  const customerId = router.get('customerId');
  const { userDetails, shopSettings } = useAppSelector((state) => state);
  const [isCourierBookingModalOpen, setIsCourierBookingModalOpen] =
    useState<boolean>(false);
  const [courierBookingOrderDetails, setCourierBookingOrderDetails] =
    useState<ShopOrderDetails>();
  const [isOrderSuccessModalOpen, setIsOrderSuccessModalOpen] =
    useState<boolean>(false);
  const [selectedProducts, setSelectedProducts] =
    useState<SingleStockDetails[]>();
  const [discountPercentage, setDiscountPercentage] = useState<number>(0);
  const [isBarcodeScanned, setIsBarcodeScanned] = useState(false);
  const [barcode, setBarcode] = useState<string>();
  const [productName, setProductName] = useState<string>();
  const [isCustomerDetailsModalOpen, setIsCustomerDetailsModalOpen] =
    useState<boolean>(false);
  const [totalPaidAmount, setTotalPaidAmount] = useState(0);
  const [isMultiplePaymentModalOpen, setIsMultiplePaymentModalOpen] =
    useState(false);
  const [selectedPayments, setSelectedPayments] = useState<
    [{ method: string; amount: number }]
  >([
    {
      method: userDetails?.shopType === 'ONLINE' ? 'BKASH' : 'CASH',
      amount: 0,
    },
  ]);
  const [netTotal, setNetTotal] = useState(0);
  const [lastOrderDetails, setLastOrderDetails] = useState<ShopOrderDetails>();
  const [isConfirmOrderModalOpen, setIsConfirmOrderModalOpen] =
    useState<boolean>(false);

  // pathao address parser api
  const [pathaoAddressParser] = usePathaoAddressParserMutation();

  // get moderator list api
  const { data: employees, isLoading: isEmployeesDataLoading } =
    useGetShopSellersQuery({ shopId, role: 'MODERATOR' });
  const { data, isLoading, refetch, isFetching } = useGetShopStockListQuery(
    {
      organizationId,
      warehouseId: warehouseId ?? undefined,
      shopId,
      barcode,
      productName,
      status: 'IN_SHOP_AVAILABLE',
      page: '1',
      limit: '10',
    },
    { skip: !(shopId && organizationId) },
  );

  // get customer details api
  const { data: customerDetails, isLoading: isCustomerDetailsLoading } =
    useGetShopCustomerDetailsQuery(
      { id: customerId, organizationId },
      {
        skip: !(customerId && organizationId),
      },
    );

  // create order api
  const [createShopOrder, { isLoading: isOrderCreating }] =
    useCreateShopOrderMutation();

  const formik = useFormik({
    initialValues: formikInitialValues,
    validationSchema: Yup.object({
      name: Yup.string().required('Customer name is required'),
      mobileNumber: Yup.string()
        .required('Mobile Number is required')
        .min(11)
        .max(11),
    }),

    onSubmit: async () => {
      setIsConfirmOrderModalOpen(true);
    },
  });
  const handleSubmitOrder = async () => {
    let pathaoParsedAddress: PathaoParsedAddressDetails | null = null;
    if (
      formik.values.address &&
      Cookies.get('pathaoAccessToken') &&
      userDetails?.shopType === 'ONLINE'
    ) {
      const res = await pathaoAddressParser({
        pathaoToken: Cookies.get('pathaoAccessToken'),
        address: formik?.values?.address,
      });
      if (res.data?.data.data) {
        pathaoParsedAddress = res.data.data.data;
      }
    }
    const orderData = {
      name: formik.values.name,
      mobileNumber: formik.values.mobileNumber,
      // email: formik.values.email ?? null,
      email: null,
      note: formik.values.note ? formik.values.note : null,
      address: formik.values.address,
      adminDiscount:
        Number(formik.values.totalDiscount) -
        Number(formik.values.productDiscount),
      // vat: Number(values.vat),
      deliveryCharge: Number(formik.values.deliveryCharge),
      customerPaid:
        Number(totalPaidAmount) > netTotal ? netTotal : Number(totalPaidAmount),
      orderType: 'Normal',
      OrderItem: selectedProducts?.map((product: SingleStockDetails) => {
        return {
          itemId: product?.id,
          productId: null,
          quantity: 1,
        };
      }),
      warehouseId: null,
      shopId,
      paymentMethods:
        selectedPayments?.length > 1
          ? selectedPayments?.filter((sin) => sin.amount > 0)
          : [
              {
                method: selectedPayments[0]?.method,
                amount:
                  selectedPayments[0]?.amount > netTotal
                    ? netTotal
                    : selectedPayments[0]?.amount,
              },
            ],
      organizationId: Cookies.get('organizationId'),
      customerId: formik.values.customerId ? formik.values.customerId : null,
      employeeId: formik.values.employeeId ? formik.values.employeeId : null,
      orderSource: formik.values.orderSource ?? null,
      recipientCity: pathaoParsedAddress?.district_id?.toString() ?? null,
      recipientZone: pathaoParsedAddress?.zone_id?.toString() ?? null,
      recipientArea: pathaoParsedAddress?.area_id?.toString() ?? null,
      division:
        getDivisionByDistrict(pathaoParsedAddress?.district_name ?? '') ?? null,
      district: pathaoParsedAddress?.district_name ?? null,
      street: pathaoParsedAddress?.zone_name ?? null,
    };

    toast.promise(createShopOrder(orderData).unwrap(), {
      pending: 'Creating New Order...',
      success: {
        render({ data: res }) {
          if (res?.statusCode === 200 || res?.statusCode === 201) {
            setLastOrderDetails(undefined);
            setIsConfirmOrderModalOpen(false);
            formik.setFieldValue('name', '');
            formik.setFieldValue('mobileNumber', '');
            // handle after order process in online shop
            if (userDetails?.shopType === 'ONLINE') {
              setIsConfirmOrderModalOpen(false);
              // dispatch({ type: 'socket/subscribeOrder', payload: res.data.id });
              setIsOrderSuccessModalOpen(true);
              setCourierBookingOrderDetails(res?.data);
              navigate(ROUTES.SHOP.ADD_NEW_ORDER(shopId));
            } else {
              // dispatch({ type: 'socket/subscribeOrder', payload: res.data.id });
              setIsConfirmOrderModalOpen(false);
              handleGenerateSingleOrderPosPdf(
                res?.data,
                shopSettings.returnPolicyText,
              );
              navigate(ROUTES.SHOP.ADD_NEW_ORDER(shopId));
            }
            refetch();
            // navigate(ROUTES.SHOP.INVOICE(shopId, res?.data?.data?.id));
            formik.resetForm();
            setSelectedProducts([]);
            setSelectedPayments([
              {
                method: 'CASH',
                amount: 0,
              },
            ]);
          }
          return 'Order created Successfully';
        },
      },
      error: {
        render({ data: error }) {
          console.log(error);
          return 'Error on creating order';
        },
      },
    });
  };

  // select stock for order function
  const handleSelectProduct = (stock: SingleStockDetails) => {
    if (selectedProducts?.length) {
      const isProductExist = selectedProducts?.find(
        (product) => product?.id === stock?.id,
      );
      if (isProductExist) {
        const remaining = selectedProducts?.filter(
          (product) => product.id !== stock.id,
        );
        setSelectedProducts(remaining);
        toast.success(`${stock?.name} from Cart`);
      } else {
        setSelectedProducts([...selectedProducts, stock]);
        toast.success(`${stock?.name} Added To Cart`);
      }
    } else {
      setSelectedProducts([stock]);
      toast.success(`${stock?.name} Added To Cart`);
    }
    setBarcode('');
    setProductName('');
  };

  // select product automatically on scan
  useEffect(() => {
    if (
      data?.data[0] &&
      Number(data?.data[0]?.barcode) === Number(barcode) &&
      isBarcodeScanned
    ) {
      handleSelectProduct(data?.data[0]);
      setBarcode('');
      setIsBarcodeScanned(false);
    }
  }, [data]);

  // set user detail from api response
  useEffect(() => {
    if (customerDetails) {
      formik.setFieldValue('name', customerDetails?.data?.name);
      formik.setFieldValue('mobileNumber', customerDetails?.data?.mobileNumber);
      formik.setFieldValue('address', customerDetails?.data?.address);
      formik.setFieldValue('email', customerDetails?.data?.email);
      formik.setFieldValue('gender', customerDetails?.data?.gender);
      formik.setFieldValue('customerId', customerDetails?.data?.id);
    }
  }, [customerDetails]);

  // get customer details by phone number
  const { data: customerData, isLoading: isCustomerDataLoading } =
    useGetShopCustomerDetailsByPhoneQuery(
      {
        phone: formik.values.mobileNumber,
        organizationId: Cookies.get('organizationId'),
      },
      {
        skip: Boolean(customerId) || formik.values.mobileNumber?.length !== 11,
      },
    );

  // set user detail from api response
  useEffect(() => {
    if (customerData?.data) {
      formik.setFieldValue('name', customerData?.data?.name);
      formik.setFieldValue('mobileNumber', customerData?.data?.mobileNumber);
      formik.setFieldValue('address', customerData?.data?.address);
      formik.setFieldValue('email', customerData?.data?.email);
      formik.setFieldValue('gender', customerData?.data?.gender);
      formik.setFieldValue('customerId', customerData?.data?.id);
      setLastOrderDetails(customerData.data.Order[0]);
      // customerData?.data?.Order?.length && setIsCustomerDetailsModalOpen(true);
    }
  }, [customerData]);

  // calculate pricing
  useEffect(() => {
    let discountProductPrice = 0;
    let total = 0;
    // let productDiscount = 0;
    let vat = 0;
    selectedProducts?.map((product: SingleStockDetails) => {
      total += product.retailPrice;
      discountProductPrice += CalculateDiscountPrice({
        retailPrice: product?.retailPrice,
        discountType: product.discountType,
        discount: product.discount,
      });
      vat += CalculateVat({
        retailPrice: product?.retailPrice,
        discountType: product.discountType,
        discount: product.discount,
        vat: product?.vat,
      });
      return 0;
    });
    formik.setFieldValue('totalProductPrice', total);
    formik.setFieldValue('totalDiscount', total - discountProductPrice);
    formik.setFieldValue('productDiscount', total - discountProductPrice);
    formik.setFieldValue('vat', vat);
  }, [selectedProducts]);

  // calculate discount price
  useEffect(() => {
    if (formik.values?.totalProductPrice) {
      const discount =
        (Number(formik.values?.totalDiscount) * 100) /
        Number(formik.values?.totalProductPrice);
      setDiscountPercentage(Math.ceil(discount));
    }
  }, [formik.values.totalProductPrice]);

  // read barcode and make api call
  useEffect(() => {
    let scannedBarcode = '';
    let timeout: NodeJS.Timeout;

    const handleBarcodeInput = (e: KeyboardEvent) => {
      if (timeout) clearTimeout(timeout);

      if (e.key === 'Enter') {
        if (scannedBarcode) {
          setIsBarcodeScanned(true);
          setBarcode(scannedBarcode);
        }
        scannedBarcode = '';
      } else {
        scannedBarcode += e.key;
        timeout = setTimeout(() => {
          scannedBarcode = '';
        }, 200);
      }
    };

    window.addEventListener('keydown', handleBarcodeInput);
    return () => {
      window.removeEventListener('keydown', handleBarcodeInput);
    };
  }, []);

  // calculate total paid amount
  useEffect(() => {
    let tot = 0;
    if (selectedPayments?.length) {
      selectedPayments.forEach((payment) => {
        tot += Number(payment.amount);
      });
    }
    setTotalPaidAmount(tot);
  }, [selectedPayments]);

  // calculate net total
  useEffect(() => {
    setNetTotal(
      Number(formik.values.totalProductPrice) +
        Number(formik.values.deliveryCharge) +
        Number(formik.values.vat) -
        Number(formik.values.totalDiscount),
    );
  }, [
    formik.values.totalProductPrice,
    formik.values.deliveryCharge,
    formik.values.vat,
    formik.values.totalDiscount,
  ]);

  // lottie animation data
  const defaultOptions = {
    loop: false,
    autoplay: true,
    animationData,
    rendererSettings: {
      preserveAspectRatio: 'xMidYMid slice',
    },
  };

  return (
    <div>
      <div className="grid grid-cols-12 gap-4">
        <div className="col col-span-12 md:col-span-5">
          <div>
            <div className="tableTop-2 w-full">
              <div className="flex w-full items-center justify-between">
                <SearchInput
                  value={productName}
                  placeholder="Search by Name"
                  handleSubmit={(value: string) => setProductName(value)}
                />
                <SearchInput
                  value={barcode}
                  placeholder="Barcode"
                  handleSubmit={(value: string) => setBarcode(value)}
                />
              </div>
            </div>
            {!isLoading && !isFetching ? (
              <div>
                {data?.data?.length ? (
                  <div className="h-[80vh] overflow-auto">
                    <ShopCreateOrderPageStockTable
                      stocks={data?.data}
                      selectedProducts={selectedProducts ?? []}
                      handleSelectProduct={handleSelectProduct}
                    />
                  </div>
                ) : (
                  <NoResultFoundForOthers pageType="product" />
                )}
              </div>
            ) : (
              <TableSkeletonLoaderHalf tableColumn={3} tableRow={4} />
            )}
          </div>
        </div>
        <div className="col col-span-12 md:col-span-7">
          <form onSubmit={formik.handleSubmit} className="w-full">
            <div>
              <ShopCustomerDetailsFormInOrderForm
                formik={formik}
                isLoading={isCustomerDetailsLoading || isCustomerDataLoading}
              />
              {customerData?.data ? (
                <div className="mt-1">
                  {/* <div className="w-full">
                  <p>Latest Order</p>
                </div> */}
                  <ShopCustomerLastOrderDetails
                    shopId={shopId}
                    lastOrderDetails={lastOrderDetails}
                  />
                </div>
              ) : (
                ''
              )}
              <div className="my-3 h-[1px] w-full bg-black text-black" />
              <div>
                <ShopOrderSubmitForm
                  selectedProducts={selectedProducts}
                  handleSelectProduct={handleSelectProduct}
                  formik={formik}
                  setDiscountPercentage={setDiscountPercentage}
                  discountPercentage={discountPercentage}
                  netTotal={netTotal}
                  selectedPayments={selectedPayments}
                  setSelectedPayments={setSelectedPayments}
                  setIsMultiplePaymentModalOpen={setIsMultiplePaymentModalOpen}
                  totalPaidAmount={totalPaidAmount}
                  isOrderCreating={isOrderCreating}
                  employees={employees}
                  isEmployeesDataLoading={isEmployeesDataLoading}
                />
              </div>
            </div>
          </form>
        </div>
      </div>
      <Modal
        showModal={isCourierBookingModalOpen}
        setShowModal={setIsCourierBookingModalOpen}
      >
        <ShopCourierBookingModal
          orderDetails={courierBookingOrderDetails}
          handleClose={() => setIsCourierBookingModalOpen(false)}
        />
      </Modal>
      <Modal
        showModal={isCustomerDetailsModalOpen}
        setShowModal={setIsCustomerDetailsModalOpen}
      >
        <ShopCustomerLatestOrderModal
          customerDetails={customerData?.data}
          handleClose={() => setIsCustomerDetailsModalOpen(false)}
        />
      </Modal>
      <Modal
        showModal={isMultiplePaymentModalOpen}
        setShowModal={setIsMultiplePaymentModalOpen}
      >
        <MultiplePaymentMethodModal
          handleClose={() => setIsMultiplePaymentModalOpen(false)}
          setSelectedPayments={setSelectedPayments}
          selectedPayments={selectedPayments}
          netTotal={netTotal}
        />
      </Modal>
      <Modal
        showModal={isConfirmOrderModalOpen}
        setShowModal={setIsConfirmOrderModalOpen}
      >
        <ShopOrderConfirmModal
          selectedProducts={selectedProducts}
          formik={formik}
          netTotal={netTotal}
          totalPaidAmount={totalPaidAmount}
          selectedPayments={selectedPayments}
          shopType={userDetails?.shopType}
          handleClose={() => setIsConfirmOrderModalOpen(false)}
          handleSubmit={handleSubmitOrder}
          isOrderCreating={isOrderCreating}
        />
      </Modal>
      <Modal
        showModal={isOrderSuccessModalOpen}
        setShowModal={setIsOrderSuccessModalOpen}
      >
        <div className="flex h-[90vh] flex-col gap-4 rounded-xl bg-white p-4">
          <ModalTitle
            text="Order Success"
            handleClose={() => setIsOrderSuccessModalOpen(false)}
          />
          <div className="flex items-start gap-8">
            <div className="mt-2 flex w-[200px] flex-col gap-4">
              <p>
                Order Created Successfully. Now Choose what next you want to do?
              </p>
              <Lottie options={defaultOptions} height={150} width={150} />
              <button
                type="button"
                className="cursor-pointer whitespace-nowrap rounded-lg bg-[#28243D] px-8 py-2 font-semibold text-white hover:bg-[#28243dd4] disabled:cursor-not-allowed disabled:bg-slate-300"
                onClick={() => {
                  handleGenerateSingleOrderPdf(
                    courierBookingOrderDetails,
                    shopSettings.returnPolicyText,
                  );
                  setIsOrderSuccessModalOpen(false);
                }}
              >
                Print Invoice
              </button>
              <button
                type="button"
                className="cursor-pointer whitespace-nowrap rounded-lg bg-[#28243D] px-8 py-2 font-semibold text-white hover:bg-[#28243dd4] disabled:cursor-not-allowed disabled:bg-slate-300"
                onClick={() => {
                  setIsCourierBookingModalOpen(true);
                  setIsOrderSuccessModalOpen(false);
                }}
              >
                Book on Courier
              </button>
              <button
                type="button"
                className="cursor-pointer whitespace-nowrap rounded-lg bg-red-600 px-8 py-2 font-semibold text-white hover:bg-[#28243dd4] disabled:cursor-not-allowed disabled:bg-slate-300"
                onClick={() => setIsOrderSuccessModalOpen(false)}
              >
                Close
              </button>
            </div>
            <div>
              <PDFViewer height={550} width={600}>
                <Document>
                  <SingleOrderInvoicePdf
                    orderDetails={courierBookingOrderDetails}
                  />
                </Document>
              </PDFViewer>
            </div>
          </div>
        </div>
      </Modal>
    </div>
  );
}

export default ShopAddNewOrderPageOverview;
