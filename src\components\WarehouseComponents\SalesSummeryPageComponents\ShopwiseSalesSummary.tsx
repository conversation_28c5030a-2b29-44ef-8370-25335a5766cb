import {
  GetShopAccountsSummeryResponse,
  SingleReceivedMethod,
  SingleShopSalesSummary,
} from '@/types/shopTypes/shopStockTransferReportTypes';
import { formatNumberWithComma } from '@/utils/formatNumberWithComma';
import ProtectedDataViewer from '@/utils/ProtectedDataViewer';

function ShopWiseSalesSummary({
  shopWiseData,
}: {
  shopWiseData?: GetShopAccountsSummeryResponse;
}) {
  return (
    <div className="mb-4">
      <div className="tableTop w-full">
        <p>Sales Report</p>
        {/* <TransparentPrintButtonTop
          handleClick={() => {
            handleGenerateSalesSummeryReportPdf(shopWiseData);
          }}
        /> */}
      </div>
      <div className="w-full">
        <div className="full-table-box">
          <table className="full-table">
            <thead className="bg-gray-100">
              <tr>
                <th className="tableHeadLeftAlign">Shop</th>
                <th className="tableHead">Invoice Qty</th>
                <th className="tableHead">Product Qty</th>
                <th className="tableHeadRightAlign">MRP</th>
                <th className="tableHeadRightAlign">Delivery Charge</th>
                <th className="tableHeadRightAlign">Discount</th>
                <th className="tableHeadRightAlign">VAT</th>
                <th className="tableHeadRightAlign">Payable</th>
                <th className="tableHeadRightAlign">Received</th>
                <th className="tableHeadRightAlign">Due</th>
                <th className="tableHeadRightAlign">TP</th>
                <th className="tableHeadRightAlign">Est. Profit</th>
              </tr>
            </thead>
            <tbody className="divide-y bg-slate-200">
              {shopWiseData?.data?.shops?.map(
                (singleShop: SingleShopSalesSummary) => (
                  <tr key={singleShop?.shop?.id}>
                    <td className="tableDataLeftAlign">
                      {singleShop?.shop?.name} (
                      {singleShop?.shop?.nickName ?? 'Warehouse'})
                    </td>
                    <td className="tableData">{singleShop?.totalOrderCount}</td>
                    <td className="tableData">{singleShop?.totalStockSold}</td>
                    <td className="tableDataRightAlign">
                      {formatNumberWithComma(singleShop?.totalSubtotal)}
                    </td>
                    <td className="tableDataRightAlign">
                      {formatNumberWithComma(singleShop?.totalDeliveryCharge)}
                    </td>
                    <td className="tableDataRightAlign">
                      {formatNumberWithComma(singleShop?.totalDiscount)}
                    </td>
                    <td className="tableDataRightAlign">
                      {formatNumberWithComma(singleShop?.totalVat)}
                    </td>
                    <td className="tableDataRightAlign">
                      {' '}
                      {formatNumberWithComma(
                        Number(singleShop?.totalPayable) +
                          Number(singleShop?.totalDeliveryCharge),
                      )}
                    </td>
                    <td className="tableDataRightAlign">
                      {formatNumberWithComma(singleShop?.cashReceive)}
                    </td>
                    <td className="tableDataRightAlign">
                      {formatNumberWithComma(singleShop?.totalDue)}
                    </td>
                    <td className="tableDataRightAlign">
                      <ProtectedDataViewer>
                        {formatNumberWithComma(singleShop?.totalPurchasePrice)}
                      </ProtectedDataViewer>
                    </td>
                    <td className="tableDataRightAlign">
                      <ProtectedDataViewer>
                        {formatNumberWithComma(
                          Number(singleShop?.totalPayable) -
                            Number(singleShop?.totalPurchasePrice),
                        )}
                      </ProtectedDataViewer>
                    </td>
                  </tr>
                ),
              )}
              <tr>
                <td className="tableDataRightAlign font-bold">Total</td>
                <td className="tableData font-bold">
                  {formatNumberWithComma(shopWiseData?.data?.totalOrderCount)}
                </td>
                <td className="tableData font-bold">
                  {formatNumberWithComma(shopWiseData?.data?.totalStockSold)}
                </td>
                <td className="tableDataRightAlign font-bold">
                  {formatNumberWithComma(shopWiseData?.data?.totalSubtotal)}
                </td>
                <td className="tableDataRightAlign font-bold">
                  {formatNumberWithComma(
                    shopWiseData?.data?.totalDeliveryCharge,
                  )}
                </td>
                <td className="tableDataRightAlign font-bold">
                  {formatNumberWithComma(shopWiseData?.data?.totalDiscount)}
                </td>
                <td className="tableDataRightAlign font-bold">
                  {formatNumberWithComma(shopWiseData?.data?.totalVat)}
                </td>
                <td className="tableDataRightAlign font-bold">
                  {' '}
                  {formatNumberWithComma(shopWiseData?.data?.totalPayable)}
                </td>
                <td className="tableDataRightAlign font-bold">
                  {formatNumberWithComma(shopWiseData?.data?.cashReceive)}
                </td>
                <td className="tableDataRightAlign font-bold">
                  {formatNumberWithComma(shopWiseData?.data?.totalDue)}
                </td>
                <td className="tableDataRightAlign font-bold">
                  <ProtectedDataViewer>
                    {formatNumberWithComma(
                      shopWiseData?.data?.totalPurchasePrice,
                    )}
                  </ProtectedDataViewer>
                </td>
                <td className="tableDataRightAlign font-bold">
                  <ProtectedDataViewer>
                    {formatNumberWithComma(
                      Number(shopWiseData?.data?.totalPayable) -
                        Number(shopWiseData?.data?.totalPurchasePrice),
                    )}
                  </ProtectedDataViewer>
                </td>
              </tr>
              {shopWiseData?.data?.methodBasedPaymentList?.length ? (
                <tr>
                  <td className="tableDataRightAlign" colSpan={9}>
                    <div className="flex items-center justify-end gap-2">
                      {shopWiseData?.data?.methodBasedPaymentList?.map(
                        (singleMethod: SingleReceivedMethod) => (
                          <div key={singleMethod?.paymentMethod}>
                            <span>{singleMethod?.paymentMethod} - </span>
                            <span>{singleMethod?.totalAmount?.toFixed(2)}</span>
                          </div>
                        ),
                      )}
                    </div>
                  </td>
                  <td className="tableData" colSpan={3} />
                </tr>
              ) : (
                ''
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

export default ShopWiseSalesSummary;
