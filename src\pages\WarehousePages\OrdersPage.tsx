import { useParams } from 'react-router-dom';

import OrdersPageOverview from '@/components/WarehouseComponents/OrdersPageComponents/OrdersPageOverview';
import { ProtectedRoute } from '@/utils/ProtectedRoutes';

function OrdersPage() {
  const { warehouseId } = useParams();
  return (
    <ProtectedRoute>
      <OrdersPageOverview warehouseId={warehouseId ?? ''} orderType="Normal" />
    </ProtectedRoute>
  );
}

export default OrdersPage;
