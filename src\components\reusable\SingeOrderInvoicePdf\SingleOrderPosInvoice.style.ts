import { StyleSheet } from '@react-pdf/renderer';

export const posInvoiceStyles = StyleSheet.create({
  page: {
    backgroundColor: 'white',
    padding: 10,
    fontSize: 10,
  },
  companyLogo: {
    width: 100,
    maxHeight: 50,
    marginBottom: 2,
    objectFit: 'cover',
  },

  title: {
    textAlign: 'center',
    fontSize: '15px',
    marginBottom: 20,
    borderTop: '1px solid black',
    borderBottom: '1px solid black',
    paddingVertical: 4,
  },
  header: {
    textAlign: 'center',
    marginBottom: 5,
    paddingBottom: 4,
  },
  borderLine: {
    borderBottomWidth: 1,
    borderBottomColor: 'black',
    borderBottomStyle: 'solid',
    marginBottom: 2,
  },
  twoSideContent: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 5,
    marginTop: 5,
  },
  dashedBorderLine: {
    borderBottomWidth: 1,
    borderBottomColor: 'black',
    borderBottomStyle: 'dashed',
    marginVertical: 1,
    dashArray: [3, 3],
  },
  dashedBorderLinePX: {
    marginLeft: 40,
    marginRight: 40,
  },
  to: {
    marginBottom: 2,
  },
  userName: {
    fontSize: '13px',
  },
  addressText: {
    marginHorizontal: 30,
  },
  note: {
    marginTop: 3,
  },
  userDetails: {
    marginTop: 5,
    marginBottom: 5,
  },
  displayFlex: {
    display: 'flex',
    alignItems: 'center',
  },

  leftSideContent: {
    flexGrow: 1,
  },

  rightSideContent: {
    flexGrow: 1,
    textAlign: 'right',
  },
  boldText: {
    fontWeight: 'bold',
    fontFamily: 'Open Sans',
  },
  container: {
    display: 'flex',
    justifyContent: 'space-between',
    flexDirection: 'row',
  },

  flexAndGap: {
    display: 'flex',
    alignItems: 'flex-start',
    flexDirection: 'row',
    gap: '2px',
  },

  text: {
    fontSize: '10px',
  },

  barcode: {
    width: 60,
    height: 30,
    padding: 0,
    marginLeft: '-2px',
  },

  flexBetween: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    flexDirection: 'row',
  },
  table: {
    width: '100%',
    marginTop: 10,
  },
  tableRow: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
  },
  tableRowBorderY: {
    backgroundColor: '#ECEFEF',
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: 'black',
    borderStyle: 'dashed',
  },
  tableCol: {
    width: '15%',
    padding: 3,
  },
  tabeName: {
    width: '70%',
  },
  headerCol: {
    fontWeight: 'bold', // Bold for headers
    backgroundColor: '#f0f0f0', // Light background for header (optional)
    paddingVertical: 4,
  },
  amount: {
    display: 'flex',
    flexDirection: 'row', // Row direction for flex layout
    justifyContent: 'space-between', // Space between the left and right texts
    alignItems: 'center', // Center align the items vertically
    marginVertical: 4, // Optional margin between the rows
  },
  mainTotal: {
    fontSize: '12px',
  },
  leftText: {
    textAlign: 'left',
  },
  middleText: {
    textAlign: 'center',
  },
  rightText: {
    textAlign: 'right', // Right align the text
  },
  textRight: {
    textAlign: 'right', // Right align the text
    width: '40%', // Optional: Control the width of the right text
  },
  moneyInWords: {
    paddingVertical: 5,
    textAlign: 'center',
  },
  paidDetailsWrapper: {
    display: 'flex',
    justifyContent: 'center',
    flexDirection: 'row', // Ensure the border wraps only the text
  },
  paidDetails: {
    fontSize: '13px',
    borderBottomWidth: 1, // Adds a bottom border
    borderBottomColor: 'black', // Color of the bottom border
    borderBottomStyle: 'solid', // Style of the bottom border
    paddingBottom: 2, // Adds space between text and border
  },
  payable: {
    textAlign: 'left',
    marginTop: 5,
  },
  barcodeDesign: {
    display: 'flex',
    justifyContent: 'center',
    flexDirection: 'row',
    paddingVertical: 5,
  },
  tnxMessage: {
    textAlign: 'center',
  },
  DateTime: {
    paddingVertical: 5,
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
});
