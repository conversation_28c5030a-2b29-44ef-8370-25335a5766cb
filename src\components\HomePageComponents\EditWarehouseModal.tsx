import { useFormik } from 'formik';
import { useEffect, useState } from 'react';
import * as Yup from 'yup';

import FilledSubmitButton from '../reusable/Buttons/FilledSubmitButton';
import CustomInputField from '../reusable/CustomInputField/CustomInputField';
import ImageSelector from '../reusable/ImageSelector/ImageSelector';
import ModalTitle from '../reusable/Modal/ModalTitle';

import { useUpdateWarehouseMutation } from '@/redux/api/shopApi';
import { SingleShopDetails } from '@/types/shopTypes';
import { UploadImageOnAws } from '@/utils/ImageUploadModule';

interface Props {
  shopData?: SingleShopDetails;
  handleClose: () => void;
  updateRefreshCounter: () => void;
}

const formikInitialValues = {
  name: '',
  district: '',
  division: '',
  street: '',
  zipCode: '',
  country: '',
  mobileNumber: '',
  websiteUrl: '',
  fbUrl: '',
  type: '',
  nickName: '',
  address: '',
};

const validation = Yup.object({
  name: Yup.string().required('Warehouse name is required'),
  address: Yup.string().required('Warehouse address is required'),
  mobileNumber: Yup.string().required('Warehouse mobileNumber is required'),
});

function EditWarehouseModal({
  shopData,
  handleClose,
  updateRefreshCounter,
}: Props) {
  const [updateShop, { isLoading: isUpdating }] = useUpdateWarehouseMutation();
  const [currentFile, setCurrentFile] = useState<any>();

  const formik = useFormik({
    initialValues: formikInitialValues,
    validationSchema: validation,

    onSubmit: async (values) => {
      try {
        const imgUrl = currentFile ? await UploadImageOnAws(currentFile) : null;
        await updateShop({
          data: {
            name: values.name,
            address: values.address,
            district: values.district,
            zipCode: values.zipCode,
            mobileNumber: values.mobileNumber,
            nickName: values.nickName,
            websiteUrl: values.websiteUrl,
            fbUrl: values.fbUrl,
            country: 'Bangladesh',
            imgUrl,
          },
          id: shopData?.id,
        });
        updateRefreshCounter();
        handleClose();
      } catch (error) {
        console.log(error);
      }
    },
  });

  useEffect(() => {
    formik.setFieldValue('name', shopData?.name);
    formik.setFieldValue('street', shopData?.street);
    formik.setFieldValue('address', shopData?.address);
    formik.setFieldValue('district', shopData?.district);
    formik.setFieldValue('zipCode', shopData?.zipCode);
    formik.setFieldValue('mobileNumber', shopData?.mobileNumber);
    formik.setFieldValue('websiteUrl', shopData?.websiteUrl);
    formik.setFieldValue('fbUrl', shopData?.fbUrl);
    formik.setFieldValue('type', shopData?.type);
    formik.setFieldValue('nickName', shopData?.nickName);
  }, [shopData]);

  return (
    <div className="min-w-[450px] rounded-xl bg-white p-4">
      <ModalTitle text="Edit Warehouse" handleClose={handleClose} />
      <form onSubmit={formik.handleSubmit} className="mt-4 flex flex-col gap-4">
        <div className="flex gap-4">
          <div className="w-[100px]">
            <ImageSelector
              previousImage=""
              setNewImage={(e) => setCurrentFile(e)}
            />
          </div>
          <div className="flex w-full flex-col gap-4">
            <CustomInputField
              type="text"
              placeholder="Enter Warehouse Name"
              name="name"
              label="Warehouse Name"
              formik={formik}
            />
            <div className="flex w-full gap-4">
              <CustomInputField
                type="text"
                placeholder="Enter Warehouse Phone Number"
                name="mobileNumber"
                label="Warehouse Phone"
                formik={formik}
              />
            </div>
          </div>
        </div>

        <CustomInputField
          type="text"
          placeholder="Enter Warehouse Address"
          name="address"
          label="Address"
          formik={formik}
        />
        <CustomInputField
          type="text"
          placeholder="Enter Warehouse Website Url"
          name="websiteUrl"
          label="Website Link"
          formik={formik}
        />
        <CustomInputField
          type="text"
          placeholder="Enter Warehouse Facebook Url"
          name="fbUrl"
          label="Facebook Link"
          formik={formik}
        />
        <div className="mt-2">
          <FilledSubmitButton text="Update" isLoading={isUpdating} />
        </div>
      </form>
    </div>
  );
}

export default EditWarehouseModal;
