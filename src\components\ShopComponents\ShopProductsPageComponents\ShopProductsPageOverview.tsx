import Cookies from 'js-cookie';
import {
  Barcode,
  EllipsisVertical,
  Eye,
  LocateFixed,
  ShieldCheck,
} from 'lucide-react';
import { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import FilterButton from '@/components/reusable/Buttons/FilterButton';
import ImageViewer from '@/components/reusable/ImageViewer/ImageViewer';
import SearchInput from '@/components/reusable/Inputs/SearchInput';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import Pagination from '@/components/reusable/Pagination/Pagination';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import {
  Menubar,
  MenubarContent,
  MenubarItem,
  MenubarMenu,
  MenubarSeparator,
  MenubarTrigger,
} from '@/components/ui/menubar';
import { useGetShopProductsQuery } from '@/redux/api/shopApis/shopProductsApi';
import { ROUTES } from '@/Routes';
import { SingleProductDetails } from '@/types/warehouseTypes/productTypes';
import { CalculateDiscountPrice } from '@/utils/CalculateDiscountPrice';
import { generateFilterParams } from '@/utils/generateFilterParams';
import ProtectedDataViewer from '@/utils/ProtectedDataViewer';

interface Props {
  shopId: string;
}
function ShopProductsPageOverview({ shopId }: Props) {
  const navigate = useNavigate();
  const [isFilterModalOpen, setIsFilterModalOpen] = useState<boolean>(false);
  const router = new URLSearchParams(useLocation().search);
  const serialNo = router.get('serialNo');
  const name = router.get('name');
  const page = router.get('page');
  const limit = router.get('limit');
  const { data, isLoading, isFetching } = useGetShopProductsQuery(
    {
      organizationId: Cookies.get('organizationId') as string,
      shopId,
      page: page ?? '1',
      serialNo: serialNo ?? undefined,
      name: name ?? undefined,
      limit: limit ?? '10',
    },
    { skip: !shopId },
  );

  const handleFilter = (fieldName: string, value: string) => {
    const query = generateFilterParams(fieldName, value);
    navigate(ROUTES.SHOP.PRODUCTS(shopId, query));
  };
  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-1">
        <div className="flex items-center">
          <div className="search-title-and-btn flex items-center">
            <div className="relative">
              <div className="block xl:hidden">
                <FilterButton
                  handleClick={() => setIsFilterModalOpen(!isFilterModalOpen)}
                />
              </div>
              <div
                className={`${isFilterModalOpen ? 'block' : 'hidden'} xl:hidden`}
              >
                {/* <ProductPageFilterModal /> */}
              </div>
            </div>
          </div>
          <div className="hidden xl:block">
            <div className="flex items-center gap-x-2">
              <SearchInput
                placeholder="Search by Name"
                handleSubmit={(value: string) => handleFilter('name', value)}
              />
              <SearchInput
                placeholder="Search by Id"
                handleSubmit={(value: string) =>
                  handleFilter('serialNo', value)
                }
              />
              <SearchInput
                placeholder="Search by Category"
                handleSubmit={(value: string) => console.log(value)}
              />
              {/* <SearchInput
                placeholder="Search by Sub Category"
                handleSubmit={(value: string) => console.log(value)}
              /> */}
            </div>
          </div>
        </div>
      </div>
      {!isLoading && !isFetching ? (
        <div>
          <div className="tableTop w-full">
            <p>Products List</p>
            <p>Total : {data?.pagination?.total}</p>
          </div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {data?.data?.length ? (
              <div className="full-table-box h-custom">
                <table className="full-table">
                  <thead className="bg-gray-100">
                    <tr>
                      {/* <th className="tableHead">#</th> */}
                      <th className="tableHead">ID</th>
                      <th className="tableHead">Image</th>
                      <th className="tableHead table-col-width">Name</th>
                      <th className="tableHead">Brand</th>
                      <th className="tableHead">Category</th>
                      {/* <th className="tableHead">Sub Category</th> */}
                      {/* {userDetails?.permissionInShop === 'ADMIN' ? (
                        <th className="tableHead">TP</th>
                      ) : (
                        ''
                      )} */}
                      <ProtectedDataViewer>
                        <th className="tableHead">TP</th>
                      </ProtectedDataViewer>
                      <th className="tableHead">Regular</th>
                      <th className="tableHead">Discount Price</th>
                      <th className="tableHead">Available</th>
                      {/* <th className="tableHead">Total Sold</th> */}
                      {/* <th className="tableHead">Created At</th> */}
                      <th className="tableHead">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y bg-slate-200">
                    {data?.data?.map((product: SingleProductDetails) => (
                      <tr key={product?.id}>
                        {/* <td className="tableData">{index + 1}</td> */}
                        <td className="tableData">{product?.serialNo ?? 0}</td>
                        <td className="tableData">
                          <ImageViewer imageUrl={product?.imgUrl} />
                        </td>
                        <td className="tableData table-col-width">
                          {product?.name}
                        </td>
                        <td className="tableData">{product?.Brand?.name}</td>
                        <td className="tableData">{product?.Category?.name}</td>
                        {/* <td className="tableData">
                            {product?.ProductSubCategory?.name}
                          </td> */}
                        <ProtectedDataViewer>
                          <td className="tableData">
                            {product?.currentPurchasePrice}
                          </td>
                        </ProtectedDataViewer>

                        <td className="tableData">
                          {product?.currentSellingPrice}
                        </td>
                        <td className="tableData">
                          {CalculateDiscountPrice({
                            retailPrice: product?.currentSellingPrice,
                            discount: product?.discount,
                            discountType: product?.discountType,
                          })}
                        </td>
                        <td className="tableData">
                          {product?.ProductStockCount.length
                            ? product?.ProductStockCount[0]?.availableQty
                            : 0}
                        </td>
                        {/* <td className="tableData">{product?.totalSold ?? 0}</td> */}
                        {/* <td className="tableData">
                          <DateAndTimeViewer date={product.createdAt} />
                        </td> */}
                        <td className="tableData">
                          <div className="flex items-center justify-center">
                            <Menubar
                              style={{
                                border: 'none',
                                backgroundColor: 'transparent',
                              }}
                            >
                              <MenubarMenu>
                                <MenubarTrigger className="cursor-pointer">
                                  <EllipsisVertical />
                                </MenubarTrigger>

                                <MenubarContent
                                  style={{
                                    marginRight: '25px',
                                    borderColor: 'black',
                                  }}
                                >
                                  <MenubarItem
                                    onClick={() =>
                                      navigate(
                                        ROUTES.SHOP.PRODUCT_DETAILS(
                                          shopId,
                                          product?.id,
                                        ),
                                      )
                                    }
                                    className="flex cursor-pointer items-center gap-1 bg-primary font-semibold text-white"
                                  >
                                    <Eye size={20} />
                                    <span>View Details</span>
                                  </MenubarItem>
                                  <MenubarSeparator />
                                  <MenubarItem
                                    onClick={() =>
                                      navigate(
                                        ROUTES.SHOP.STOCK_LIST(
                                          shopId,
                                          `productSerialNo=${product.serialNo}&limit=${product.totalAvailable}&available=true`,
                                        ),
                                      )
                                    }
                                    className="flex cursor-pointer items-center gap-1 bg-primary font-semibold text-white"
                                  >
                                    <Barcode size={20} />
                                    <span>Available Barcodes</span>
                                  </MenubarItem>
                                  <MenubarSeparator />
                                  <MenubarItem
                                    onClick={() =>
                                      navigate(
                                        ROUTES.SHOP.STOCK_SEARCH(
                                          shopId,
                                          product?.id,
                                        ),
                                      )
                                    }
                                    className="flex cursor-pointer items-center gap-1 bg-primary font-semibold text-white"
                                  >
                                    <LocateFixed size={20} />
                                    <span>Available Locations</span>
                                  </MenubarItem>
                                  <MenubarSeparator />
                                  <MenubarItem
                                    onClick={() =>
                                      navigate(
                                        ROUTES.SHOP.STOCK_AUDIT(
                                          shopId,
                                          product?.id,
                                          `limit=${product.totalAvailable}&productSerialNo=${product.serialNo}`,
                                        ),
                                      )
                                    }
                                    className="flex cursor-pointer items-center gap-1 bg-primary font-semibold text-white"
                                  >
                                    <ShieldCheck size={20} />
                                    <span>Stock Audit</span>
                                  </MenubarItem>
                                </MenubarContent>
                              </MenubarMenu>
                            </Menubar>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <NoResultFound pageType="products" />
            )}
          </div>
          <div className="pagination-box flex justify-end rounded bg-white p-3">
            <Pagination
              currentPage={page ?? '1'}
              limit={Number(limit ?? 10)}
              handleFilter={(fieldName: string, value: any) =>
                handleFilter(fieldName, value)
              }
              totalCount={data?.pagination?.total}
              totalPages={Math.ceil(
                Number(data?.pagination?.total) /
                  Number(data?.pagination?.limit),
              )}
            />
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={13} tableRow={6} />
      )}
    </div>
  );
}

export default ShopProductsPageOverview;
