import { TransparentPrintButtonTop } from '@/components/reusable/Buttons/CommonButtons';
import {
  GetShopAccountsSummeryResponse,
  GetShopCategoryWiseSummeryResponse,
  SingleCategorySale,
  SingleReceivedMethod,
} from '@/types/shopTypes/shopStockTransferReportTypes';
import { formatNumberWithComma } from '@/utils/formatNumberWithComma';
import { handleCategoryWiseSalesSummeryPdf } from '@/utils/GenerateReportPdf';

interface Props {
  categorySummery?: GetShopCategoryWiseSummeryResponse;
  data?: GetShopAccountsSummeryResponse;
}

function CategoryWiseSalesSummary({ categorySummery, data }: Props) {
  return (
    <div>
      <div className="tableTop w-full">
        <p>Category Wise Sales Summery</p>
        <TransparentPrintButtonTop
          handleClick={() => {
            handleCategoryWiseSalesSummeryPdf(categorySummery, data);
          }}
        />
      </div>
      <div className="w-full">
        <div className="full-table-box h-customExc">
          <table className="full-table">
            <thead className="bg-gray-100">
              <tr>
                <th className="tableHeadLeftAlign">Category Name</th>
                <th className="tableHeadLeftAlign">Subcategory Name</th>
                <th className="tableHead">Quantity</th>
                <th className="tableHeadRightAlign">Total</th>
              </tr>
            </thead>
            <tbody className="divide-y bg-slate-200">
              {categorySummery?.data?.result?.length
                ? categorySummery?.data?.result?.map(
                    (singleCategory: SingleCategorySale) => (
                      <tr key={singleCategory?.categoryName}>
                        <td className="tableDataLeftAlign">
                          {singleCategory?.categoryName}
                        </td>
                        <td className="tableDataLeftAlign">
                          {singleCategory?.subCategoryName}
                        </td>
                        <td className="tableData">
                          {formatNumberWithComma(singleCategory?.quantity)}
                        </td>
                        <td className="tableDataRightAlign">
                          {formatNumberWithComma(
                            singleCategory?.totalRetailPrice,
                          )}
                        </td>
                      </tr>
                    ),
                  )
                : ''}
              <tr>
                <td className="tableDataRightAlign" colSpan={2} />
                <td className="tableDataRightAlign">MRP</td>
                <td className="tableDataRightAlign">
                  {formatNumberWithComma(data?.data?.totalSubtotal)}
                </td>
              </tr>
              <tr>
                <td className="tableDataRightAlign" colSpan={2} />
                <td className="tableDataRightAlign">Delivery Charge</td>
                <td className="tableDataRightAlign">
                  {formatNumberWithComma(data?.data?.totalDeliveryCharge)}
                </td>
              </tr>
              <tr>
                <td className="tableDataRightAlign" colSpan={2} />
                <td className="tableDataRightAlign">Discount</td>
                <td className="tableDataRightAlign">
                  {formatNumberWithComma(data?.data?.totalDiscount)}
                </td>
              </tr>
              <tr>
                <td className="tableDataRightAlign" colSpan={2} />
                <td className="tableDataRightAlign">Vat</td>
                <td className="tableDataRightAlign">
                  {formatNumberWithComma(data?.data?.totalVat)}
                </td>
              </tr>
              <tr>
                <td className="tableDataRightAlign" colSpan={2} />
                <td className="tableDataRightAlign">Payable</td>
                <td className="tableDataRightAlign">
                  {' '}
                  {formatNumberWithComma(
                    Number(data?.data?.totalPayable) +
                      Number(data?.data?.totalDeliveryCharge),
                  )}
                </td>
              </tr>
              <tr>
                <td className="tableDataRightAlign" colSpan={2}>
                  <div className="flex items-center justify-end gap-2">
                    {data?.data?.methodBasedPaymentList?.map(
                      (singleMethod: SingleReceivedMethod) => (
                        <div>
                          <span>{singleMethod?.paymentMethod} - </span>
                          <span>
                            {formatNumberWithComma(singleMethod?.totalAmount)}
                          </span>
                        </div>
                      ),
                    )}
                  </div>
                </td>
                <td className="tableDataRightAlign">Received</td>
                <td className="tableDataRightAlign">
                  {formatNumberWithComma(data?.data?.cashReceive)}
                </td>
              </tr>
              <tr>
                <td className="tableDataRightAlign" colSpan={2} />
                <td className="tableDataRightAlign">Due</td>
                <td className="tableDataRightAlign">
                  {formatNumberWithComma(data?.data?.totalDue)}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

export default CategoryWiseSalesSummary;
