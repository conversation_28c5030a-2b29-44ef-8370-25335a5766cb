import Cookies from 'js-cookie';
import { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import { TransparentEyeButton } from '@/components/reusable/Buttons/CommonButtons';
import FilterButton from '@/components/reusable/Buttons/FilterButton';
import CustomSelectForFilter from '@/components/reusable/Inputs/CustomSelectForFilter';
import SearchInput from '@/components/reusable/Inputs/SearchInput';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import Pagination from '@/components/reusable/Pagination/Pagination';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import { useGetShopStockListQuery } from '@/redux/api/shopApis/shopStockApis';
import { useAppSelector } from '@/redux/hooks';
import { ROUTES } from '@/Routes';
import { SingleStockDetails } from '@/types/warehouseTypes/stockTypes';
import { CalculateDiscountPrice } from '@/utils/CalculateDiscountPrice';
import { generateFilterParams } from '@/utils/generateFilterParams';
import StockName from '@/utils/StockName';

interface Props {
  shopId: string;
  warehouseId: string;
}
function ShopStockListPageOverview({ shopId, warehouseId }: Props) {
  const userDetails = useAppSelector((state) => state.userDetails);
  const navigate = useNavigate();
  const router = new URLSearchParams(useLocation().search);
  const page = router.get('page');
  const productName = router.get('productName');
  const limit = router.get('limit');
  const barcode = router.get('barcode');
  const productSerialNo = router.get('productSerialNo');
  const status = router.get('status');
  const [isFilterModalOpen, setIsFilterModalOpen] = useState<boolean>(false);

  const stockStatusFilter = [
    { label: 'AVAILABLE', value: 'IN_SHOP_AVAILABLE' },
    { label: 'SOLD', value: 'ORDERED' },
    { label: 'RESTOCK REQUESTED', value: 'RESTOCK_REQUESTED' },
  ];
  const organizationId = Cookies.get('organizationId') as string;
  const { data, isLoading, isFetching } = useGetShopStockListQuery(
    {
      organizationId,
      warehouseId: warehouseId ?? undefined,
      shopId,
      page: page ?? '1',
      barcode: barcode ?? undefined,
      limit: limit ?? '10',
      productName: productName ?? undefined,
      productSerialNo: productSerialNo ?? undefined,
      status: status || undefined,
    },
    { skip: !(organizationId && shopId) },
  );

  const handleFilter = (fieldName: string, value: string) => {
    const query = generateFilterParams(fieldName, value);
    navigate(ROUTES.SHOP.STOCK_LIST(shopId, query));
  };
  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-1">
        <div className="flex items-center">
          <div className="search-title-and-btn flex items-center">
            <div className="relative">
              <div className="block xl:hidden">
                <FilterButton
                  handleClick={() => setIsFilterModalOpen(!isFilterModalOpen)}
                />
              </div>
              <div
                className={`${isFilterModalOpen ? 'block' : 'hidden'} xl:hidden`}
              >
                {/* <StockListPageFilterModal /> */}
              </div>
            </div>
          </div>
          <div className="hidden xl:block">
            <div className="flex items-center gap-x-2">
              <SearchInput
                placeholder="Search by Name"
                handleSubmit={(value: string) =>
                  handleFilter('productName', value)
                }
              />
              <SearchInput
                placeholder="Search by Barcode"
                handleSubmit={(value: string) => handleFilter('barcode', value)}
              />
              <SearchInput
                placeholder="Search by Category"
                handleSubmit={(value: string) => handleFilter('page', value)}
              />
              <SearchInput
                placeholder="Product Id"
                handleSubmit={(value: string) =>
                  handleFilter('productSerialNo', value)
                }
              />
              <CustomSelectForFilter
                options={stockStatusFilter}
                selectedValue={status ?? ''}
                handleSelect={(e) => handleFilter('status', e)}
                placeHolder="Status"
              />
            </div>
          </div>
        </div>
      </div>
      {!isLoading && !isFetching ? (
        <div>
          <div className="tableTop w-full">
            <p>Stock List</p>
            <p>Total : {data?.pagination?.total}</p>
          </div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {data?.data?.length ? (
              <div className="full-table-box h-custom">
                <table className="full-table">
                  <thead className="bg-gray-100">
                    <tr>
                      {/* <th className="tableHead">No</th> */}
                      <th className="tableHead">Barcode</th>
                      <th className="tableHead">Product Id</th>
                      <th className="tableHead table-col-width">Name</th>
                      {userDetails?.permissionInShop === 'ADMIN' ? (
                        <th className="tableHead">TP</th>
                      ) : (
                        ''
                      )}
                      <th className="tableHead">Regular Price</th>
                      <th className="tableHead">Discount Price</th>
                      <th className="tableHead">Vat</th>
                      <th className="tableHead">Warehouse</th>
                      <th className="tableHead">Status</th>
                      {/* <th className="tableHead">Created At</th> */}
                      <th className="tableHead">Action</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y bg-slate-200">
                    {data?.data?.map((stock: SingleStockDetails) => (
                      <tr
                        key={stock?.id}
                        // className={
                        //   stock.status === 'ASSIGNED'
                        //     ? 'tableRowGreen'
                        //     : stock.status === 'RESTOCK_REQUESTED'
                        //       ? 'tableRowPurple'
                        //       : 'tableRowRed'
                        // }
                      >
                        {/* <td className="tableData">{index + 1}</td> */}
                        <td className="tableData">{stock?.barcode}</td>
                        <td className="tableData">
                          {stock?.Product?.serialNo}
                        </td>
                        <td className="tableData table-col-width">
                          <StockName stock={stock} name={stock.name} />
                        </td>
                        {userDetails?.permissionInShop === 'ADMIN' ? (
                          <td className="tableData">{stock?.purchasePrice}</td>
                        ) : (
                          ''
                        )}
                        <td className="tableData">{stock?.retailPrice}</td>
                        <td className="tableData">
                          {CalculateDiscountPrice({
                            retailPrice: stock?.retailPrice,
                            discountType: stock.discountType,
                            discount: stock.discount,
                          })}
                        </td>
                        <td className="tableData">
                          {stock?.Product?.vat || 0}
                        </td>
                        <td className="tableData">{stock?.Warehouse?.name}</td>
                        <td className="tableData">
                          {/* {stock?.isSold ? 'Sold' : 'Available'} */}
                          {stock?.status === 'ASSIGNED'
                            ? 'AVAILABLE'
                            : stock?.status}
                        </td>
                        {/* <td className="tableData">{stock?.Supplier?.name}</td> */}
                        {/* <td className="tableData">
                          <DateAndTimeViewer date={stock.createdAt} />
                        </td> */}
                        <td className="tableData">
                          <div className="flex items-center justify-center gap-2">
                            <TransparentEyeButton
                              handleClick={() =>
                                navigate(
                                  ROUTES.SHOP.STOCK_DETAILS(shopId, stock.id),
                                )
                              }
                            />
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <NoResultFound pageType="stock" />
            )}
          </div>
          <div className="pagination-box flex justify-end rounded bg-white p-3">
            <Pagination
              currentPage={page ?? '1'}
              limit={Number(limit ?? 10)}
              handleFilter={(fieldName: string, value: any) =>
                handleFilter(fieldName, value)
              }
              totalCount={data?.pagination?.total}
              totalPages={Math.ceil(
                Number(data?.pagination?.total) /
                  Number(data?.pagination?.limit),
              )}
            />
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={10} tableRow={6} />
      )}
    </div>
  );
}

export default ShopStockListPageOverview;
