import { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import Swal from 'sweetalert2';

import CancelStockEntryRequestModal from './CancelStockEntryRequestModal';
import ShopStockEntryRequestPageFilterModal from './ShopStockEntryRequestPageFilterModal';
import SingleStockTransferDetailsPdf from './SingleStockTransferDetailsPdf';
import ViewStockEntryItemsModal from './ViewStockEntryItemsModal';

import {
  EyeButton,
  GreenAcceptButton,
  PrintButton,
  RedCloseButton,
} from '@/components/reusable/Buttons/CommonButtons';
import ExportButton from '@/components/reusable/Buttons/ExportButton';
import FilterButton from '@/components/reusable/Buttons/FilterButton';
import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import CustomDateFilterInput from '@/components/reusable/Inputs/CustomDateFilterInput';
import CustomSelectForFilter from '@/components/reusable/Inputs/CustomSelectForFilter';
import Modal from '@/components/reusable/Modal/Modal';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import Pagination from '@/components/reusable/Pagination/Pagination';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import { useGetShopsQuery } from '@/redux/api/shopApi';
import {
  useAcceptStockEntryMutation,
  useGetShopStockHoldListQuery,
} from '@/redux/api/shopApis/shopStockApis';
import { useAppSelector } from '@/redux/hooks';
import { ROUTES } from '@/Routes';
import { SingleShopDetails } from '@/types/shopTypes';
import { SingleStockEntryRequestType } from '@/types/warehouseTypes/stockTypes';
import { generateFilterParams } from '@/utils/generateFilterParams';
import {
  handleGenerateStockTransferReportCsv,
  handleGenerateStockTransferReportPdf,
} from '@/utils/ReportExport/ExportStockTransferReport';

interface Props {
  shopId?: string;
  warehouseId?: string;
  isShop?: boolean;
  isWarehouse?: boolean;
}
function ShopStockEntryRequestPageOverview({
  shopId,
  warehouseId,
  isShop,
  isWarehouse,
}: Props) {
  const { warehouseDetails } = useAppSelector((state) => state);
  const navigate = useNavigate();
  const router = new URLSearchParams(useLocation().search);
  const page = router.get('page');
  const limit = router.get('limit');
  const status = router.get('status');
  const startDate = router.get('startDate');
  const routerShopId = router.get('shopId') ?? undefined;
  const endDate = router.get('endDate');
  const { data, isLoading, refetch, isFetching } = useGetShopStockHoldListQuery(
    {
      shopId: isShop ? shopId : isWarehouse ? routerShopId : undefined,
      warehouseId: isWarehouse ? warehouseId : undefined,
      page: page ?? '1',
      limit: limit ?? '10',
      status: status ?? undefined,
      type: startDate && endDate ? 'custom' : undefined,
      startDate: startDate ?? undefined,
      endDate: endDate ?? undefined,
    },
  );

  const { data: shopData, isLoading: isShopListLoading } = useGetShopsQuery({
    warehouseId,
    isFromStockTransfer: false,
  });

  const [acceptStockEntry] = useAcceptStockEntryMutation();
  const [isFilterModalOpen, setIsFilterModalOpen] = useState<boolean>(false);
  const [isViewItemsModalOpen, setIsViewItemsModalOpen] =
    useState<boolean>(false);
  const [isSingleEntryPdfModalOpen, setIsSingleEntryPdfModalOpen] =
    useState<boolean>(false);
  const [isCancelEntryModalOpen, setIsCancelEntryModalOpen] =
    useState<boolean>(false);
  const [selectedEntry, setSelectedEntry] =
    useState<SingleStockEntryRequestType>();

  const handleAcceptStockEntry = (id: string, quantity: number) => {
    Swal.fire({
      title: `Are you sure? you want to entry ${quantity} items to shop?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, entry it!',
    }).then((result) => {
      if (result.isConfirmed) {
        toast.promise(
          acceptStockEntry({
            id,
            data: {
              shouldApproved: true,
              shopId,
            },
          }).unwrap(),
          {
            pending: 'Entering Stock...',
            success: {
              render({ data: res }) {
                if (res?.statusCode === 200 || res?.statusCode === 201) {
                  refetch();
                }
                return 'Stock Accepted Successfully';
              },
            },
            error: {
              render({ data: error }) {
                console.log(error);
                return 'Error on stock entry';
              },
            },
          },
        );
      }
    });
  };

  const handleFilter = (fieldName: string, value: string) => {
    const query = generateFilterParams(fieldName, value);
    navigate(
      isShop
        ? ROUTES.SHOP.STOCK_ENTRY_REQUEST(shopId ?? '', query)
        : ROUTES.STOCK.TRANSFER_HISTORY(warehouseId ?? '', query),
    );
  };

  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-1">
        <div className="flex items-center">
          <div className="search-title-and-btn flex items-center">
            <div className="relative">
              <div className="block xl:hidden">
                <FilterButton
                  handleClick={() => setIsFilterModalOpen(!isFilterModalOpen)}
                />
              </div>
              <div
                className={`${isFilterModalOpen ? 'block' : 'hidden'} xl:hidden`}
              >
                <ShopStockEntryRequestPageFilterModal />
              </div>
            </div>
          </div>
          <div className="hidden xl:block">
            <div className="flex items-center gap-x-2">
              <CustomSelectForFilter
                options={[
                  { label: 'PENDING', value: 'PENDING' },
                  { label: 'ACCEPTED', value: 'ACCEPTED' },
                  { label: 'REJECTED', value: 'REJECTED' },
                ]}
                selectedValue={status ?? ''}
                handleSelect={(e) => handleFilter('status', e)}
                placeHolder="Status"
              />
              {isWarehouse ? (
                <CustomSelectForFilter
                  options={
                    !isShopListLoading && shopData?.data?.length
                      ? shopData?.data?.map((single: SingleShopDetails) => {
                          return {
                            value: single.id,
                            label: `${single.name} (${single.nickName})`,
                          };
                        })
                      : []
                  }
                  selectedValue={shopId ?? ''}
                  handleSelect={(e) => handleFilter('shopId', e)}
                  placeHolder="Select Shop"
                />
              ) : (
                ''
              )}
              <CustomDateFilterInput
                value={startDate ?? ''}
                placeholder="Select Start Date"
                label="Start Date"
                handleChange={(value: string) =>
                  handleFilter('startDate', value)
                }
              />
              <CustomDateFilterInput
                value={endDate ?? ''}
                placeholder="Select Start Date"
                label="End Date"
                handleChange={(value: string) => handleFilter('endDate', value)}
                minimumDate={startDate ?? ''}
              />
            </div>
          </div>
        </div>
      </div>
      {!isLoading && !isFetching ? (
        <div>
          <div className="tableTop w-full">
            <p>Stock Transfer History</p>

            <div className="flex items-center gap-2">
              <p>Total : {data?.pagination?.total}</p>
              <div className="ml-4">
                <ExportButton
                  totalCount={data?.data.length ?? 0}
                  handleExportCsv={() =>
                    handleGenerateStockTransferReportCsv({
                      data: data?.data as any,
                    })
                  }
                  handleExportPdf={() =>
                    handleGenerateStockTransferReportPdf(
                      data?.data as any,
                      warehouseDetails,
                    )
                  }
                />
              </div>
            </div>
          </div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {data?.data?.length ? (
              <div className="full-table-box h-custom">
                <table className="full-table">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="tableHead">No</th>
                      <th className="tableHead">From</th>
                      <th className="tableHead">To</th>
                      <th className="tableHead">Transferred By</th>
                      <th className="tableHead">Received By</th>
                      <th className="tableHead">Quantity</th>
                      <th className="tableHead">Status</th>
                      <th className="tableHead">Created At</th>
                      <th className="tableHead">Received At</th>
                      <th className="tableHead">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y bg-slate-200">
                    {data?.data?.map(
                      (stock: SingleStockEntryRequestType, index: number) => (
                        <tr key={stock?.id}>
                          <td className="tableData">{index + 1}</td>
                          <td className="tableData">Warehouse</td>
                          <td className="tableData">
                            {stock.Shop.name}({stock?.Shop?.nickName})
                          </td>
                          <td className="tableData">
                            {stock?.CreatedBy?.name}
                          </td>
                          <td className="tableData">
                            {stock?.ReceivedBy?.name}
                          </td>
                          <td className="tableData">
                            {stock?.stockIds?.length}
                          </td>
                          <td className="tableData">
                            <span
                              className={`${
                                stock.status === 'PENDING'
                                  ? 'blink-animation text-orange-500'
                                  : stock.status === 'REJECTED'
                                    ? 'text-red-600'
                                    : 'text-green-600'
                              } font-semibold`}
                            >
                              {stock.status}
                            </span>
                          </td>
                          <td className="tableData">
                            <DateAndTimeViewer date={stock.createdAt} />
                          </td>
                          <td className="tableData">
                            {stock?.status !== 'PENDING' ? (
                              <DateAndTimeViewer date={stock.updatedAt} />
                            ) : (
                              ''
                            )}
                          </td>
                          <td className="tableData">
                            <div className="flex items-center justify-center gap-4">
                              {stock.status === 'PENDING' && (
                                <>
                                  {isShop ? (
                                    <GreenAcceptButton
                                      handleClick={() => {
                                        handleAcceptStockEntry(
                                          stock.id,
                                          stock.stockIds?.length,
                                        );
                                      }}
                                    />
                                  ) : (
                                    ''
                                  )}
                                  <RedCloseButton
                                    handleClick={() => {
                                      setSelectedEntry(stock);
                                      setIsCancelEntryModalOpen(true);
                                    }}
                                  />
                                </>
                              )}
                              <EyeButton
                                handleClick={() => {
                                  setSelectedEntry(stock);
                                  setIsViewItemsModalOpen(true);
                                }}
                              />
                              <PrintButton
                                handleClick={() => {
                                  setSelectedEntry(stock);
                                  setIsSingleEntryPdfModalOpen(true);
                                }}
                              />
                            </div>
                          </td>
                        </tr>
                      ),
                    )}
                  </tbody>
                </table>
              </div>
            ) : (
              <NoResultFound pageType="stock entry" />
            )}
          </div>
          <div className="pagination-box flex justify-end rounded bg-white p-3">
            <Pagination
              currentPage={page ?? '1'}
              limit={Number(limit ?? 10)}
              handleFilter={(fieldName: string, value: any) =>
                handleFilter(fieldName, value)
              }
              totalCount={data?.pagination?.total}
              totalPages={Math.ceil(
                Number(data?.pagination?.total) /
                  Number(data?.pagination?.limit),
              )}
            />
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={7} tableRow={6} />
      )}
      <Modal
        showModal={isViewItemsModalOpen}
        setShowModal={setIsViewItemsModalOpen}
      >
        <ViewStockEntryItemsModal
          entryId={selectedEntry?.id ?? ''}
          handleClose={() => setIsViewItemsModalOpen(false)}
          shopId={selectedEntry?.shopId ?? ''}
        />
      </Modal>
      <Modal
        showModal={isCancelEntryModalOpen}
        setShowModal={setIsCancelEntryModalOpen}
      >
        <CancelStockEntryRequestModal
          entryId={selectedEntry?.id ?? ''}
          handleClose={() => setIsCancelEntryModalOpen(false)}
          shopId={selectedEntry?.shopId ?? ''}
        />
      </Modal>
      <Modal
        showModal={isSingleEntryPdfModalOpen}
        setShowModal={setIsSingleEntryPdfModalOpen}
      >
        <SingleStockTransferDetailsPdf
          entryId={selectedEntry?.id ?? ''}
          handleClose={() => setIsSingleEntryPdfModalOpen(false)}
          shopId={selectedEntry?.shopId ?? ''}
        />
      </Modal>
    </div>
  );
}

export default ShopStockEntryRequestPageOverview;
