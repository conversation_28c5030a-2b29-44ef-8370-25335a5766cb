import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface User {
  id: string;
  name: string;
  email: string;
  username: string;
  mobileNumber: string;
  imgUrl: string;
  type: string;
  permissionInShop: string;
  shopName: string;
  shopNickname: string;
  shopType: string;
}

const initialStoreStates: User = {
  id: '',
  name: '',
  email: '',
  username: '',
  mobileNumber: '',
  imgUrl: '',
  type: '',
  permissionInShop: '',
  shopName: '',
  shopNickname: '',
  shopType: '',
};

const userSlice = createSlice({
  name: 'userDetails',
  initialState: initialStoreStates,
  reducers: {
    setUserDetails: (state, action: PayloadAction<User>) => {
      state.id = action.payload.id;
      state.name = action.payload.name;
      state.email = action.payload.email;
      state.username = action.payload.username;
      state.mobileNumber = action.payload.mobileNumber;
      state.imgUrl = action.payload.imgUrl;
      state.type = action.payload.type;
      state.permissionInShop = action.payload.permissionInShop;
      state.shopName = action.payload.shopName;
      state.shopNickname = action.payload.shopNickname;
      state.shopType = action.payload.shopType;
    },

    reset: () => initialStoreStates,
  },
});

// Action creators are generated for each case reducer function
export const { reset, setUserDetails } = userSlice.actions;

export default userSlice.reducer;

/* import { SingleShopDetails } from '@/types/shopTypes';
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface User {
  id: string;
  name: string;
  email: any;
  username: string;
  mobileNumber: string;
  imgUrl: any;
  organizationLimit: number;
  warehouseLimit: number;
  shopLimit: number;
  type: string;
  Employee: Employee;
}

export interface Employee {
  id: string;
  createdAt: string;
  updatedAt: string;
  lastSalary: number;
  currentSalary: number;
  userId: string;
  organizationId: string;
  serialNo: number;
  isActive: boolean;
  EmployeePermission: EmployeePermission[];
}

export interface EmployeePermission {
  id: string;
  createdAt: string;
  updatedAt: string;
  employeeId: string;
  assignedShopId: string;
  warehouseId: string;
  isActive: boolean;
  role: string;
  createdById: string;
  AssignedShop: SingleShopDetails;
}

const initialStoreStates: User = {
  id: '',
  name: '',
  email: '',
  username: '',
  mobileNumber: '',
  imgUrl: '',
  organizationLimit: 0,
  warehouseLimit: 0,
  shopLimit: 0,
  type: '',
  Employee: {
    id: '',
    createdAt: '',
    updatedAt: '',
    lastSalary: 0,
    currentSalary: 0,
    userId: '',
    organizationId: '',
    serialNo: 0,
    isActive: false,
    EmployeePermission: [],
  },
};

const userSlice = createSlice({
  name: 'userDetails',
  initialState: initialStoreStates,
  reducers: {
    setUserDetails: (state, action: PayloadAction<User>) => {
      state.id = action.payload.id;
      state.name = action.payload.name;
      state.email = action.payload.email;
      state.username = action.payload.username;
      state.mobileNumber = action.payload.mobileNumber;
      state.imgUrl = action.payload.imgUrl;
      state.organizationLimit = action.payload.organizationLimit;
      state.warehouseLimit = action.payload.warehouseLimit;
      state.shopLimit = action.payload.shopLimit;
      state.type = action.payload.type;
      state.Employee = action.payload.Employee;
    },

    reset: () => initialStoreStates,
  },
});

// Action creators are generated for each case reducer function
export const { reset, setUserDetails } = userSlice.actions;

export default userSlice.reducer;
 */
