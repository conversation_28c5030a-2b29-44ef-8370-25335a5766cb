import { EllipsisVertical, Eye, File, Play, StopCircle } from 'lucide-react';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

import StockAuditReportGenerateModal from './StockAuditReportGenerateModal';

import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import Modal from '@/components/reusable/Modal/Modal';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import OrderStatusViewer from '@/components/reusable/OrdersPagesReusableComponents/OrderStatusViewer';
import {
  Menubar,
  MenubarContent,
  MenubarItem,
  MenubarMenu,
  MenubarSeparator,
  MenubarTrigger,
} from '@/components/ui/menubar';
import {
  GetStockAuditListResponse,
  SingleAuditData,
} from '@/redux/api/shopApis/shopStockAuditApis';
import { ROUTES } from '@/Routes';

interface Props {
  data?: GetStockAuditListResponse;
}

function StockAuditListViewer({ data }: Props) {
  const navigate = useNavigate();
  const [auditId, setAuditId] = useState('');
  const [isAuditReportGenerateModalOpen, setIsAuditReportGenerateModalOpen] =
    useState(false);
  return (
    <>
      {data?.data?.length ? (
        <div className="full-table-box h-custom">
          <table className="full-table">
            <thead className="bg-gray-100">
              <tr>
                <th className="tableHead">#</th>
                <th className="tableHead">Created By</th>
                {/* <th className="tableHead">Created At</th>
                <th className="tableHead">Updated At</th> */}
                <th className="tableHead">Started At</th>
                <th className="tableHead">Completed At</th>
                <th className="tableHead">Reviewed By</th>
                <th className="tableHead">Approved By</th>
                <th className="tableHead">Submitted By</th>
                <th className="tableHead">Status</th>
                <th className="tableHead">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y bg-slate-200">
              {data?.data?.map((product: SingleAuditData, index: number) => (
                <tr key={product?.id}>
                  <td className="tableData">{index + 1}</td>
                  <td className="tableData">{product?.CreatedBy.name}</td>
                  {/* <td className="tableData">
                    <DateAndTimeViewer date={product?.createdAt} />
                    </td>
                    <td className="tableData">
                    <DateAndTimeViewer date={product?.updatedAt} />
                    </td> */}
                  <td className="tableData">
                    {product.startedAt ? (
                      <DateAndTimeViewer date={product?.startedAt} />
                    ) : (
                      ''
                    )}
                  </td>
                  <td className="tableData">
                    {product.completedAt ? (
                      <DateAndTimeViewer date={product?.completedAt} />
                    ) : (
                      ''
                    )}
                  </td>
                  <td className="tableData">{product?.ReviewedBy?.name}</td>
                  <td className="tableData">{product?.ApprovedBy?.name}</td>
                  <td className="tableData">{product?.SubmittedBy.name}</td>
                  <td className="tableData">
                    <OrderStatusViewer status={product?.status} />
                  </td>
                  <td className="tableData">
                    <div className="flex items-center justify-center gap-2">
                      <Menubar
                        style={{
                          border: 'none',
                          backgroundColor: 'transparent',
                        }}
                      >
                        <MenubarMenu>
                          <MenubarTrigger className="cursor-pointer">
                            <EllipsisVertical />
                          </MenubarTrigger>
                          <MenubarContent
                            style={{
                              marginRight: '25px',
                              borderColor: 'black',
                            }}
                          >
                            <MenubarItem
                              onClick={() =>
                                navigate(
                                  ROUTES.SHOP.STOCK_AUDIT_DETAILS(
                                    product.shopId ?? '',
                                    product?.id,
                                  ),
                                )
                              }
                              className="flex cursor-pointer items-center gap-1 bg-primary font-semibold text-white"
                            >
                              <Eye size={20} />
                              <span>View Details</span>
                            </MenubarItem>
                            {product?.status === 'DRAFT' && (
                              <>
                                <MenubarSeparator />
                                <MenubarItem
                                  onClick={() =>
                                    navigate(
                                      ROUTES.SHOP.STOCK_AUDIT_DETAILS(
                                        product.shopId ?? '',
                                        product?.id,
                                      ),
                                    )
                                  }
                                  className="flex cursor-pointer items-center gap-1 bg-primary font-semibold text-white"
                                >
                                  <Play size={20} />
                                  <span>Start Audit</span>
                                </MenubarItem>

                                <MenubarSeparator />
                                <MenubarItem
                                  onClick={() => console.log('end')}
                                  className="flex cursor-pointer items-center gap-1 bg-primary font-semibold text-white"
                                >
                                  <StopCircle size={20} />
                                  <span>End Audit</span>
                                </MenubarItem>
                              </>
                            )}
                            <MenubarSeparator />
                            <MenubarItem
                              onClick={() => {
                                setAuditId(product?.id);
                                setIsAuditReportGenerateModalOpen(true);
                              }}
                              className="flex cursor-pointer items-center gap-1 bg-primary font-semibold text-white"
                            >
                              <File size={20} />
                              <span>Audit Report</span>
                            </MenubarItem>
                          </MenubarContent>
                        </MenubarMenu>
                      </Menubar>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <NoResultFound pageType="product" />
      )}
      <Modal
        showModal={isAuditReportGenerateModalOpen}
        setShowModal={setIsAuditReportGenerateModalOpen}
      >
        <StockAuditReportGenerateModal
          auditId={auditId}
          handleClose={() => {
            setIsAuditReportGenerateModalOpen(false);
            setAuditId('');
          }}
        />
      </Modal>
    </>
  );
}

export default StockAuditListViewer;
