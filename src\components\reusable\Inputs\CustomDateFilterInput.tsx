interface Props {
  label: string;
  placeholder: string;
  isDisabled?: boolean;
  value?: string;
  handleChange?: (date: string) => void;
  minimumDate?: string;
  maximumDate?: string;
}

function CustomDateFilterInput({
  label,
  placeholder,
  isDisabled,
  value,
  handleChange,
  minimumDate,
  maximumDate,
}: Props) {
  const getLocalISODate = (date: Date) => {
    const localDate = new Date(
      date.getTime() - date.getTimezoneOffset() * 60000,
    );
    return localDate.toISOString().substring(0, 10);
  };

  return (
    <div className="group relative z-0 mt-[-20px] w-full">
      <label
        htmlFor={label}
        className="relative left-3 top-2.5 w-auto bg-white px-1 font-mono text-[12px] font-bold text-gray-900 group-focus-within:text-red-600 dark:text-gray-300"
      >
        {label}
      </label>
      <input
        disabled={isDisabled}
        type="date"
        id={label}
        className="text-10 py-55-rem block h-10 w-full rounded-lg border bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 disabled:cursor-not-allowed disabled:bg-gray-100 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
        placeholder={placeholder}
        onChange={(e) => handleChange && handleChange(e.target.value)}
        value={getLocalISODate(new Date(value || new Date()))}
        min={minimumDate ? getLocalISODate(new Date(minimumDate)) : undefined}
        max={
          maximumDate
            ? getLocalISODate(new Date(maximumDate))
            : getLocalISODate(new Date())
        }
      />
    </div>
  );
}

export default CustomDateFilterInput;
