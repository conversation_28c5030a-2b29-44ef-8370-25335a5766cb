import { CreatedBy, Pagination } from '@/redux/commonTypes';
import BaseApi from '../baseApi';

import { TagTypes } from '@/redux/tag-types';
import { ShopOrderDetails } from '@/types/shopTypes/shopOrderTypes';

interface GetShopBrandParams {
  warehouseId?: string;
  shopId?: string;
  page?: string;
  limit?: string;
  phone?: string;
  status?: string;
  startDate?: string;
  endDate?: string;
  orderNumber?: string;
  name?: string;
}

const ShopExternalOrdersApi = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getShopExternalOrders: builder.query<
      ExternalOrdersListGetResponse,
      GetShopBrandParams
    >({
      query: (params) => ({
        url: '/order/external',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP_ORDERS],
    }),
    getShopSingleExternalOrderDetails: builder.query<
      GetSingleExternalOrderDetailsResponse,
      any
    >({
      query: (id) => ({
        url: `/order/external/${id}`,
        method: 'GET',
      }),
      providesTags: [TagTypes.SHOP_ORDERS],
    }),
    updateSingleExternalOrder: builder.mutation({
      query: ({ data, id }) => ({
        url: `/order/external/${id}`,
        method: 'PUT',
        data,
      }),
      invalidatesTags: [TagTypes.SHOP_ORDERS],
    }),
  }),
});

export const {
  useGetShopExternalOrdersQuery,
  useGetShopSingleExternalOrderDetailsQuery,
  useUpdateSingleExternalOrderMutation,
} = ShopExternalOrdersApi;

export interface ExternalOrdersListGetResponse {
  success: boolean;
  message: string;
  statusCode: number;
  pagination: Pagination;
  data: SingleExternalOrderDetails[];
}

export interface GetSingleExternalOrderDetailsResponse {
  success: boolean;
  message: string;
  statusCode: number;
  data: SingleExternalOrderDetails;
}

export interface SingleExternalOrderDetails {
  id: string;
  createdAt: string;
  updatedAt: string;
  from: string;
  customOrderId: string;
  customCustomerId: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  orderNumber: string;
  orderName: string;
  financialStatus: any;
  fulfillmentStatus: any;
  orderDate: string;
  cancelledAt: any;
  cancelReason: any;
  currency: any;
  totalPrice: number;
  subtotalPrice: number;
  totalDiscount: number;
  totalShippingCost: number;
  totalOutstanding: number;
  sourceName: any;
  tags: any[];
  locale: any;
  note: string;
  status: string;
  billingAddressId: any;
  shippingAddressId: string;
  shopId: string;
  organizationId: any;
  lineItems: SingleProductOfExternalOrder[];
  billingAddress: any;
  shippingAddress: ShippingAddress;
  paymentMethods: ExternalPaymentMethod[];
  ProcessedBy: CreatedBy;
  Order?: ShopOrderDetails;
}

export interface ExternalPaymentMethod {
  amount: number;
  method: string;
  serialNo: string;
}

export interface SingleProductOfExternalOrder {
  id: string;
  createdAt: string;
  updatedAt: string;
  customLineItemId: string;
  name: string;
  sku: any;
  quantity: number;
  price: number;
  discount: number;
  orderId: string;
}

export interface ShippingAddress {
  id: string;
  createdAt: string;
  updatedAt: string;
  address1: string;
  address2: any;
  city: string;
  province: string;
  zip: string;
  country: string;
  phone: any;
}
