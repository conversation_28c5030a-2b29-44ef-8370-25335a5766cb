import BaseApi from './baseApi';

import { TagTypes } from '@/redux/tag-types';
import {
  GetFeatureRequestsResponse,
  SingleFeatureRequestDetails,
  CreateFeatureRequestRequest,
  FeatureRequestStatus,
} from '@/types/warehouseTypes/featureRequestTypes';

interface GetFeatureRequestsParams {
  createdById?: string;
  status?: FeatureRequestStatus;
  page?: string;
  limit?: string;
}

const FeatureRequestsApi = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getFeatureRequests: builder.query<
      GetFeatureRequestsResponse,
      GetFeatureRequestsParams
    >({
      query: (params) => ({
        url: '/feature-requests',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.FEATURE_REQUESTS],
    }),
    getFeatureRequestDetails: builder.query<
      SingleFeatureRequestDetails,
      string
    >({
      query: (id) => ({
        url: `/feature-requests/${id}`,
        method: 'GET',
      }),
      providesTags: [TagTypes.FEATURE_REQUESTS],
    }),
    createFeatureRequest: builder.mutation<any, CreateFeatureRequestRequest>({
      query: (data) => ({
        url: '/feature-requests/new',
        method: 'POST',
        data,
      }),
      invalidatesTags: [TagTypes.FEATURE_REQUESTS],
    }),
    deleteFeatureRequest: builder.mutation<any, string>({
      query: (id) => ({
        url: `/feature-requests/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: [TagTypes.FEATURE_REQUESTS],
    }),
  }),
});

export const {
  useGetFeatureRequestsQuery,
  useGetFeatureRequestDetailsQuery,
  useCreateFeatureRequestMutation,
  useDeleteFeatureRequestMutation,
} = FeatureRequestsApi;
