import { Font, Image, Page, Text, View } from '@react-pdf/renderer';
import JsBarcode from 'jsbarcode';
import { useEffect, useState } from 'react';

import { posInvoiceStyles } from './SingleOrderPosInvoice.style';

import {
  ShopOrderDetails,
  ShopSingleOrderItem,
} from '@/types/shopTypes/shopOrderTypes';
import { numberToWords } from '@/utils/NumberToText';

Font.register({
  family: 'Open Sans',
  fonts: [
    {
      src: 'https://cdn.jsdelivr.net/npm/open-sans-all@0.1.3/fonts/open-sans-regular.ttf',
    },
    {
      src: 'https://cdn.jsdelivr.net/npm/open-sans-all@0.1.3/fonts/open-sans-600.ttf',
      fontWeight: 800,
    },
  ],
});

interface Props {
  orderDetails?: ShopOrderDetails;
}

function SingleOrderPosInvoiceTwo({ orderDetails }: Props) {
  const [barcode, setBarcode] = useState<string>('');

  useEffect(() => {
    if (orderDetails?.serialNo) {
      const canvas = document.createElement('canvas');
      JsBarcode(canvas, orderDetails.serialNo.toString(), {
        format: 'CODE128',
        displayValue: false,
      });
      setBarcode(canvas.toDataURL('image/png'));
    }
  }, [orderDetails]);
  return (
    <Page size={[227.76]} style={posInvoiceStyles.page}>
      <View>
        <View style={posInvoiceStyles.header}>
          {orderDetails?.Shop?.imgUrl ? (
            <View
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Image
                src={{
                  uri: `https://retail-pluse-upload.s3.ap-southeast-1.amazonaws.com/${orderDetails?.Shop?.imgUrl}`,
                  method: 'GET',
                  headers: { 'Cache-Control': 'no-cache' },
                  body: '',
                }}
                style={posInvoiceStyles.companyLogo}
                // cache={false}
              />
            </View>
          ) : (
            <Text style={posInvoiceStyles.userName}>
              {orderDetails?.Shop?.name}
            </Text>
          )}
          <Text style={posInvoiceStyles.addressText}>
            {orderDetails?.Shop?.address}
          </Text>
          <Text>Phone:{orderDetails?.Shop?.mobileNumber}</Text>
        </View>
        <View>
          <View style={posInvoiceStyles.DateTime}>
            <Text>Bill No: {orderDetails?.serialNo}</Text>
            <Text>
              Date:{' '}
              {new Intl.DateTimeFormat('en-US', {
                day: '2-digit',
                month: 'short',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
              }).format(new Date(orderDetails?.createdAt ?? ''))}
            </Text>
          </View>
        </View>
        <View style={posInvoiceStyles.dashedBorderLine} />
        <View style={posInvoiceStyles.userDetails}>
          <View>
            <Text style={posInvoiceStyles.to}>Bill To:</Text>
            <Text style={posInvoiceStyles.userName}>
              {orderDetails?.Customer?.name}:(ID)
            </Text>
            <Text>Phone: {orderDetails?.Customer?.mobileNumber}</Text>
          </View>
        </View>
        <View style={posInvoiceStyles.table}>
          <View
            style={[
              posInvoiceStyles.tableRow,
              posInvoiceStyles.tableRowBorderY,
            ]}
          >
            <Text
              style={[
                posInvoiceStyles.tableCol,
                posInvoiceStyles.headerCol,
                posInvoiceStyles.tabeName,
              ]}
            >
              Item
            </Text>
            <Text
              style={[posInvoiceStyles.tableCol, posInvoiceStyles.headerCol]}
            >
              Qty
            </Text>
            <Text
              style={[
                posInvoiceStyles.tableCol,
                posInvoiceStyles.headerCol,
                posInvoiceStyles.rightText,
              ]}
            >
              Price
            </Text>
          </View>

          {orderDetails?.OrderItem?.map((product: ShopSingleOrderItem) => (
            <View style={posInvoiceStyles.tableRow} key={product?.id}>
              <Text
                style={[posInvoiceStyles.tableCol, posInvoiceStyles.tabeName]}
              >
                {product?.Stock?.barcode}-{product?.Stock?.Product?.name}
              </Text>
              <Text style={posInvoiceStyles.tableCol}>1</Text>
              <Text
                style={[posInvoiceStyles.tableCol, posInvoiceStyles.rightText]}
              >
                {product?.Stock?.retailPrice}
              </Text>
            </View>
          ))}
        </View>
        <View style={posInvoiceStyles.borderLine} />
        <View>
          <View style={posInvoiceStyles.amount}>
            <Text style={posInvoiceStyles.leftText}>SubTotal:</Text>
            <Text style={posInvoiceStyles.rightText}>
              {Number(orderDetails?.grandTotal) +
                Number(orderDetails?.productDiscount) +
                Number(orderDetails?.adminDiscount)}
            </Text>
          </View>
          <View style={posInvoiceStyles.amount}>
            <Text style={posInvoiceStyles.leftText}>Discount:</Text>
            <Text style={posInvoiceStyles.rightText}>
              {Number(orderDetails?.productDiscount) +
                Number(orderDetails?.adminDiscount)}
            </Text>
          </View>
          <View style={posInvoiceStyles.amount}>
            <Text style={posInvoiceStyles.leftText}>Vat:</Text>
            <Text style={posInvoiceStyles.rightText}>{orderDetails?.vat}</Text>
          </View>
          <View style={posInvoiceStyles.amount}>
            <Text
              style={[posInvoiceStyles.leftText, posInvoiceStyles.boldText]}
            >
              Total:
            </Text>
            <Text style={posInvoiceStyles.rightText}>
              {Number(orderDetails?.grandTotal) +
                Number(orderDetails?.deliveryCharge)}
            </Text>
          </View>
          <View style={posInvoiceStyles.amount}>
            <Text style={posInvoiceStyles.leftText}>Paid Amount:</Text>
            <Text style={posInvoiceStyles.rightText}>
              {orderDetails?.totalPaid}
            </Text>
          </View>
          <View style={posInvoiceStyles.amount}>
            <Text style={posInvoiceStyles.leftText}>Due Amount:</Text>
            <Text style={posInvoiceStyles.rightText}>
              {orderDetails?.totalDue}
            </Text>
          </View>
        </View>
        <View style={posInvoiceStyles.borderLine} />
        <View>
          <Text
            style={[
              posInvoiceStyles.leftText,
              posInvoiceStyles.mainTotal,
              posInvoiceStyles.boldText,
            ]}
          >
            Total:
          </Text>
        </View>
        <View style={posInvoiceStyles.moneyInWords}>
          <Text>
            {numberToWords(
              Number(orderDetails?.grandTotal),
            ).toLocaleUpperCase()}
          </Text>
        </View>
        <View
          style={[
            posInvoiceStyles.dashedBorderLine,
            posInvoiceStyles.dashedBorderLinePX,
          ]}
        />
        <View style={posInvoiceStyles.barcodeDesign}>
          <Image src={barcode} style={posInvoiceStyles.barcode} />
        </View>
        <Text style={posInvoiceStyles.tnxMessage}>T H A N K S Y O U !</Text>
        <Text style={[posInvoiceStyles.tnxMessage, posInvoiceStyles.boldText]}>
          Software Made By SOFTS.AI
        </Text>
        <Text style={[posInvoiceStyles.tnxMessage, posInvoiceStyles.boldText]}>
          Call : 01723-714141
        </Text>
      </View>
    </Page>
  );
}

export default SingleOrderPosInvoiceTwo;
