import { useFormik } from 'formik';
import { useEffect } from 'react';
import { toast } from 'react-toastify';
import * as Yup from 'yup';

import FilledSubmitButton from '@/components/reusable/Buttons/FilledSubmitButton';
import CustomInputField from '@/components/reusable/CustomInputField/CustomInputField';
import CustomTextArea from '@/components/reusable/CustomInputField/CustomTextArea';
import {
  GetShopSettingsResponse,
  useUpdateShopSettingsMutation,
} from '@/redux/api/shopApis/shopSettingsApi';

const formikInitialValues = {
  maximumOrderReturnPolicy: 3,
  maximumOrderEditPolicy: 1,
  maximumPaymentMethodEditPolicy: 1,
  profitMarginPercentage: 30,
  returnPolicyText: '',
};

const validationSchema = Yup.object({
  maximumOrderReturnPolicy: Yup.number()
    .required('Maximum Order Return Policy is required')
    .positive('Value must be positive')
    .integer('Value must be an integer'),
  maximumOrderEditPolicy: Yup.number()
    .required('Maximum Order Edit Policy is required')
    .positive('Value must be positive')
    .integer('Value must be an integer'),
  maximumPaymentMethodEditPolicy: Yup.number()
    .required('Maximum Order Edit payment Policy is required')
    .positive('Value must be positive')
    .integer('Value must be an integer'),
  profitMarginPercentage: Yup.number()
    .required('Profit Margin Percentage is required')
    .min(0, 'Value cannot be negative')
    .max(100, 'Value cannot exceed 100'),
  returnPolicyText: Yup.string()
    .required('Return Policy Text is required')
    .min(10, 'Return Policy Text must be at least 10 characters'),
});

interface Props {
  data?: GetShopSettingsResponse;
  handleUpdateData: () => void;
}
function GeneralSettingsTab({ data, handleUpdateData }: Props) {
  const [updateShopSettings, { isLoading: isUpdatingShopSettings }] =
    useUpdateShopSettingsMutation();

  const formik = useFormik({
    initialValues: formikInitialValues,
    validationSchema,
    onSubmit: (values) => {
      toast.promise(
        updateShopSettings({
          data: { ...values },
          id: data?.data.shopId,
        }).unwrap(),
        {
          pending: 'Updating Settings...',
          success: {
            render({ data: res }) {
              if (res?.statusCode === 200 || res?.statusCode === 201) {
                handleUpdateData();
              }
              return 'Settings updated Successfully';
            },
          },
          error: {
            render() {
              return 'Error on update Settings';
            },
          },
        },
      );
    },
  });

  useEffect(() => {
    if (data?.data) {
      formik.setFieldValue(
        'maximumOrderReturnPolicy',
        data.data.maximumOrderReturnPolicy,
      );
      formik.setFieldValue(
        'maximumOrderEditPolicy',
        data.data.maximumOrderEditPolicy,
      );
      formik.setFieldValue(
        'profitMarginPercentage',
        data.data.profitMarginPercentage,
      );
      formik.setFieldValue('returnPolicyText', data.data.returnPolicyText);
      formik.setFieldValue(
        'maximumPaymentMethodEditPolicy',
        data.data.maximumPaymentMethodEditPolicy,
      );
    }
  }, [data]);
  return (
    <div>
      <form
        onSubmit={formik.handleSubmit}
        className="flex flex-col items-center gap-6 rounded-lg bg-white p-4"
      >
        <div className="grid w-full grid-cols-2 gap-4">
          <CustomInputField
            type="number"
            placeholder="Enter Maximum Order Return Policy"
            name="maximumOrderReturnPolicy"
            label="Order Return Date (Days)"
            formik={formik}
          />
          <CustomInputField
            type="number"
            placeholder="Enter Maximum Order Edit Policy"
            name="maximumOrderEditPolicy"
            label="Order Edit Date (Days)"
            formik={formik}
          />
          <CustomInputField
            type="number"
            placeholder="Enter Maximum Order Edit Policy"
            name="maximumPaymentMethodEditPolicy"
            label="Payment Method Edit (Days)"
            formik={formik}
          />
          <CustomInputField
            type="number"
            placeholder="Enter Profit Margin Percentage"
            name="profitMarginPercentage"
            label="Expected Profit Margin(%)"
            formik={formik}
          />
        </div>
        <CustomTextArea
          placeholder="Enter Return Policy Text"
          name="returnPolicyText"
          label="Return Policy Text"
          formik={formik}
        />
        <div className="mt-[10px] flex items-center justify-center">
          <FilledSubmitButton
            text="Update Settings"
            isLoading={isUpdatingShopSettings}
          />
        </div>
      </form>
    </div>
  );
}

export default GeneralSettingsTab;
