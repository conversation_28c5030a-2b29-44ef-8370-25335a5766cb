import { Font, Page, StyleSheet, Text, View } from '@react-pdf/renderer';

import { Payment } from '@/redux/api/organizationApis/orgPaymentApis';

// Register a custom font (optional)

interface Props {
  billDetails?: Payment;
}
Font.register({
  family: 'Open Sans',
  fonts: [
    {
      src: 'https://cdn.jsdelivr.net/npm/open-sans-all@0.1.3/fonts/open-sans-regular.ttf',
    },
    {
      src: 'https://cdn.jsdelivr.net/npm/open-sans-all@0.1.3/fonts/open-sans-600.ttf',
      fontWeight: 800,
    },
  ],
});
const styles = StyleSheet.create({
  page: {
    padding: 30,
    fontFamily: 'Open Sans',
    fontSize: 12,
    color: '#333',
  },
  header: {
    marginBottom: 20,
    textAlign: 'center',
  },
  companyDetails: {
    marginBottom: 20,
  },
  section: {
    marginBottom: 10,
  },
  table: {
    display: 'flex',
    width: 'auto',
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: '#ccc',
    marginBottom: 20,
  },
  tableRow: {
    flexDirection: 'row',
  },
  tableCell: {
    padding: 5,
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: '#ccc',
    flexGrow: 1,
  },
  bold: {
    fontWeight: 'bold',
  },
  footer: {
    marginTop: 20,
    textAlign: 'center',
    fontSize: 10,
    color: '#666',
  },
});

function OrganizationBillInvoicePdf({ billDetails }: Props) {
  return (
    <Page size="A4" style={styles.page}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={{ fontSize: 18, fontWeight: 'bold' }}>SOFTS.AI</Text>
        <Text>20/D Tenary More, Jhigatola, Dhaka-1209</Text>
        <Text>Email: <EMAIL> | Phone: 01723-714141</Text>
      </View>

      {/* Bill To Section */}
      <View style={styles.companyDetails}>
        <Text style={styles.bold}>Bill To:</Text>
        <Text>{billDetails?.Organization?.name}</Text>
        <Text>address</Text>
        <Text>Email: email</Text>
      </View>

      {/* Invoice Details */}
      <View style={styles.section}>
        <Text style={styles.bold}>Invoice Details:</Text>
        <Text>Invoice ID: {billDetails?.serialNo}</Text>
        <Text>
          Billing Period:{' '}
          {new Date(billDetails?.Subscription.startDate ?? '')
            .toDateString()
            .toUpperCase()}
          -
          {new Date(billDetails?.Subscription.endDate ?? '')
            .toDateString()
            .toUpperCase()}
        </Text>
        <Text>
          Issue Date:{' '}
          {new Date(billDetails?.paidAt ?? '').toDateString().toUpperCase()}
        </Text>
      </View>

      {/* Table Section */}
      <View style={styles.table}>
        <View style={[styles.tableRow, { backgroundColor: '#f0f0f0' }]}>
          <Text style={[styles.tableCell, styles.bold]}>Description</Text>
          <Text style={[styles.tableCell, styles.bold]}>Amount</Text>
        </View>
        {/* {billDetails?.items?.map((item, index) => (
            <View key={index} style={styles.tableRow}>
              <Text style={styles.tableCell}>{item.description}</Text>
              <Text style={styles.tableCell}>৳ {item.amount}</Text>
            </View>
          ))} */}
        <View style={[styles.tableRow, { backgroundColor: '#f9f9f9' }]}>
          <Text style={[styles.tableCell, styles.bold]}>Total</Text>
          {/* <Text style={[styles.tableCell, styles.bold]}>
            {billDetails?.Subscription?.Plan?.priceMonthly ?? 0}
          </Text> */}
        </View>
      </View>

      {/* Footer */}
      <View style={styles.footer}>
        <Text>Best Wish for your business!</Text>
        <Text>
          If you have any questions about this invoice, please contact us at
          <EMAIL>
        </Text>
      </View>
    </Page>
  );
}

export default OrganizationBillInvoicePdf;
