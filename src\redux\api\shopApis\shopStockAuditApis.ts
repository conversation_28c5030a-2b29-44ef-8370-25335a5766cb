import { CreatedBy, Pagination } from '@/redux/commonTypes';
import BaseApi from '../baseApi';

import { TagTypes } from '@/redux/tag-types';
import { SingleShopDetails } from '@/types/shopTypes';

interface GetStockListParams {
  warehouseId?: string;
  shopId?: string;
  page?: string;
  limit?: string;
  status?: string;
  organizationId?: string;
}

const ShopStocksApi = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getShopStockAuditList: builder.query<
      GetStockAuditListResponse,
      GetStockListParams
    >({
      query: (params) => ({
        url: '/stock-audit',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP_STOCK],
    }),
    getShopStockAuditDetailsList: builder.query<
      GetStockAuditDetailsResponse,
      any
    >({
      query: (id) => ({
        url: `/stock-audit/${id}`,
        method: 'GET',
      }),
      providesTags: [TagTypes.STOCK_AUDIT],
    }),

    addNewAudit: builder.mutation({
      query: (data) => ({
        url: '/stock-audit/new',
        method: 'POST',
        data,
      }),
      invalidatesTags: [TagTypes.STOCK_AUDIT],
    }),
    addItemOnAudit: builder.mutation({
      query: (data) => ({
        url: '/stock-audit/item/add',
        method: 'POST',
        data,
      }),
      invalidatesTags: [TagTypes.STOCK_AUDIT],
    }),
    updateSingleAudit: builder.mutation({
      query: ({ id, data }) => ({
        url: `/stock-audit/${id}`,
        method: 'PATCH',
        data,
      }),
      invalidatesTags: [TagTypes.STOCK_AUDIT],
    }),
    updateDiscrepancyReason: builder.mutation({
      query: (data) => ({
        url: `/stock-audit/product/${data.stockAuditReportProductId}`,
        method: 'PUT',
        data,
      }),
      invalidatesTags: [TagTypes.STOCK_AUDIT],
    }),
    submitAuditReport: builder.mutation({
      query: (data) => ({
        url: `/stock-audit/submit`,
        method: 'PUT',
        data,
      }),
      invalidatesTags: [TagTypes.STOCK_AUDIT],
    }),
  }),
});

export const {
  useGetShopStockAuditListQuery,
  useGetShopStockAuditDetailsListQuery,
  useAddNewAuditMutation,
  useAddItemOnAuditMutation,
  useUpdateSingleAuditMutation,
  useSubmitAuditReportMutation,
  useUpdateDiscrepancyReasonMutation,
} = ShopStocksApi;

export interface GetStockAuditListResponse {
  success: boolean;
  message: string;
  statusCode: number;
  pagination: Pagination;
  data: SingleAuditData[];
}
export interface GetStockAuditDetailsResponse {
  success: boolean;
  message: string;
  statusCode: number;
  pagination: Pagination;
  data: SingleAuditData;
}

export interface SingleAuditData {
  id: string;
  createdAt: string;
  updatedAt: string;
  status: string;
  finalVerdict: any;
  startedAt: any;
  completedAt: any;
  reviewedAt: any;
  reviewedById: any;
  createdById: string;
  organizationId: string;
  shopId: string;
  submittedById: string;
  CreatedBy: CreatedBy;
  SubmittedBy: CreatedBy;
  ReviewedBy: CreatedBy;
  ApprovedBy: CreatedBy;
  Shop: SingleShopDetails;
  Products: SingleProductOfAudit[];
}

export interface SingleProductOfAudit {
  id: string;
  createdAt: string;
  updatedAt: string;
  stockAuditReportId: string;
  productId: string;
  count: number;
  expectedCount: number;
  discrepancy: number;
  discrepancyReason: any;
  isResolved: boolean;
  Product: Product;
  Variants: Variant[];
}

export interface Product {
  id: string;
  name: string;
  imgUrl: any;
}

export interface Variant {
  id: string;
  Stocks: Stock[];
}

export interface Stock {
  Stock: {
    id: string;
    barcode: number;
  };
}
