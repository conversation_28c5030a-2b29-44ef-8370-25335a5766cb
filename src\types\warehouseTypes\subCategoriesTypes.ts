import { CreatedBy } from './../shopTypes/shopExpensesTypes';
import { Pagination } from '@/redux/commonTypes';
import { SingleCategoryDetails } from './categoriesTypes';

export interface GetSubCategoriesResponse {
  success: boolean;
  statusCode: number;
  message: string;
  data: SingleSubCategory[];
  pagination: Pagination;
}

export interface SingleSubCategory {
  id: string;
  createdAt: string;
  updatedAt: string;
  productCategoryId: string;
  name: string;
  imgUrl: any;
  warehouseId: string;
  ProductCategory: SingleCategoryDetails;
  CreatedBy: CreatedBy;
}
