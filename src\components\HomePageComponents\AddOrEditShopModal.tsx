import { useFormik } from 'formik';
import Cookies from 'js-cookie';
import { useEffect, useState } from 'react';
import * as Yup from 'yup';

import FilledSubmitButton from '../reusable/Buttons/FilledSubmitButton';
import CustomDropdown from '../reusable/CustomInputField/CustomDropdown';
import CustomInputField from '../reusable/CustomInputField/CustomInputField';
import ImageSelector from '../reusable/ImageSelector/ImageSelector';
import ModalTitle from '../reusable/Modal/ModalTitle';

import { useGetOrgWarehousesQuery } from '@/redux/api/organizationApis/orgShopAndWarehouseApis';
import {
  useCreateShopMutation,
  useUpdateShopMutation,
} from '@/redux/api/shopApi';
import { SingleShopDetails } from '@/types/shopTypes';
import { UploadImageOnAws } from '@/utils/ImageUploadModule';
import { divisionsWithDistrict, divisionTypes } from '@/utils/staticData';

interface Props {
  type: string;
  shopData?: SingleShopDetails;
  handleClose: () => void;
  updateRefreshCounter: () => void;
}

const formikInitialValues = {
  name: '',
  district: '',
  division: '',
  street: '',
  zipCode: '',
  country: '',
  mobileNumber: '',
  websiteUrl: '',
  fbUrl: '',
  type: '',
  nickName: '',
  address: '',
  warehouseId: '',
  // courierId: null,
};

const validation = Yup.object({
  name: Yup.string().required('Shop name is required'),
  address: Yup.string().required('Shop address is required'),
  mobileNumber: Yup.string().required('Shop mobileNumber is required'),
  type: Yup.string().required('Shop type is required'),
  nickName: Yup.string().required('Shop nickName is required'),
  warehouseId: Yup.string().required('Warehouse is required'),
});

function AddOrEditShopModal({
  type,
  shopData,
  handleClose,
  updateRefreshCounter,
}: Props) {
  const { data } = useGetOrgWarehousesQuery({
    organizationId: Cookies.get('organizationId'),
  });

  const [createShop, { isLoading }] = useCreateShopMutation();
  const [updateShop, { isLoading: isUpdating }] = useUpdateShopMutation();
  const [districts, setDistricts] = useState<string[]>();
  const [currentFile, setCurrentFile] = useState<any>();

  const formik = useFormik({
    initialValues: formikInitialValues,
    validationSchema: validation,

    onSubmit: async (values) => {
      if (type === 'new') {
        const imgUrl = currentFile ? await UploadImageOnAws(currentFile) : null;
        try {
          await createShop({
            name: values.name,
            address: values.address,
            district: values.district,
            zipCode: values.zipCode,
            mobileNumber: values.mobileNumber,
            websiteUrl: values.websiteUrl,
            fbUrl: values.fbUrl,
            nickName: values.nickName,
            country: 'Bangladesh',
            type: values.type,
            imgUrl,
            warehouseId: values.warehouseId,
            // courierId: values.courierId,
          });
          updateRefreshCounter();
          handleClose();
        } catch (error) {
          console.log(error);
        }
      } else {
        try {
          const imgUrl = currentFile
            ? await UploadImageOnAws(currentFile)
            : shopData?.imgUrl
              ? shopData?.imgUrl
              : null;
          await updateShop({
            data: {
              name: values.name,
              address: values.address,
              district: values.district,
              zipCode: values.zipCode,
              mobileNumber: values.mobileNumber,
              nickName: values.nickName,
              websiteUrl: values.websiteUrl,
              fbUrl: values.fbUrl,
              country: 'Bangladesh',
              imgUrl,
              // courierId: values.courierId,
            },
            id: shopData?.id,
          });
          updateRefreshCounter();
          handleClose();
        } catch (error) {
          console.log(error);
        }
      }
    },
  });

  useEffect(() => {
    if (type === 'edit') {
      formik.setFieldValue('name', shopData?.name);
      formik.setFieldValue('street', shopData?.street);
      formik.setFieldValue('address', shopData?.address);
      formik.setFieldValue('district', shopData?.district);
      formik.setFieldValue('zipCode', shopData?.zipCode);
      formik.setFieldValue('mobileNumber', shopData?.mobileNumber);
      formik.setFieldValue('websiteUrl', shopData?.websiteUrl);
      formik.setFieldValue('fbUrl', shopData?.fbUrl);
      formik.setFieldValue('type', shopData?.type);
      formik.setFieldValue('nickName', shopData?.nickName);
      formik.setFieldValue('warehouseId', shopData?.warehouseId);
      // formik.setFieldValue('courierId', shopData?.Courier[0]?.id);
    }
  }, [type, shopData]);

  useEffect(() => {
    if (formik.values.division) {
      const selected = divisionsWithDistrict?.find(
        (sin: divisionTypes) => sin.division === formik.values.division,
      );
      setDistricts(selected?.districts);
    }
  }, [formik.values.division]);

  return (
    <div className="min-w-[450px] rounded-xl bg-white p-4">
      <ModalTitle
        text={type === 'new' ? 'Create New Shop' : 'Edit Shop'}
        handleClose={handleClose}
      />
      <form onSubmit={formik.handleSubmit} className="mt-4 flex flex-col gap-4">
        <div className="flex gap-4">
          <div className="w-[100px]">
            <ImageSelector
              previousImage={shopData?.imgUrl ?? ''}
              setNewImage={(e) => setCurrentFile(e)}
            />
          </div>
          <div className="flex w-full flex-col gap-4">
            <CustomInputField
              type="text"
              placeholder="Enter Shop Name"
              name="name"
              label="Shop Name"
              formik={formik}
            />
            <div className="flex w-full gap-4">
              <CustomInputField
                type="text"
                placeholder="Enter Shop Phone Number"
                name="mobileNumber"
                label="Shop Phone"
                formik={formik}
              />
              <CustomInputField
                type="text"
                placeholder="Enter Shop Nickname"
                name="nickName"
                label="Shop Nickname"
                formik={formik}
              />
            </div>
          </div>
        </div>

        <CustomInputField
          type="text"
          placeholder="Enter Shop Address"
          name="address"
          label="Address"
          formik={formik}
        />
        <div className="flex items-center gap-2">
          <CustomDropdown
            placeholder="Select Division"
            name="division"
            label="Division"
            formik={formik}
            options={divisionsWithDistrict?.map((single: divisionTypes) => {
              return {
                value: single.division,
                label: single.division,
              };
            })}
          />
          <CustomDropdown
            placeholder="Select District"
            name="district"
            label="District"
            formik={formik}
            options={districts?.map((district: string) => {
              return {
                value: district,
                label: district,
              };
            })}
          />
          <CustomInputField
            type="text"
            placeholder="Post Code"
            name="zipCode"
            label="Post Code"
            formik={formik}
          />
        </div>
        <div className="flex items-center gap-2">
          <CustomDropdown
            placeholder="Select Shop Type"
            name="type"
            label="Shop Type"
            formik={formik}
            options={[
              { value: 'ONLINE', label: 'Online' },
              { value: 'PHYSICAL', label: 'Physical' },
            ]}
          />
          <CustomDropdown
            placeholder="Select Warehouse"
            name="warehouseId"
            label="Warehouse"
            formik={formik}
            options={data?.data?.map((warehouse: SingleShopDetails) => {
              return {
                value: warehouse.id,
                label: `${warehouse.name}`,
              };
            })}
          />
          {/* <CustomDropdown
            placeholder="Select Courier"
            name="courierId"
            label="Courier"
            formik={formik}
            options={data?.data?.map((district: SingleCourierDetails) => {
              return {
                value: district.id,
                label: district.nickName,
              };
            })}
          /> */}
        </div>
        <CustomInputField
          type="text"
          placeholder="Enter Shop Website Url"
          name="websiteUrl"
          label="Website Link"
          formik={formik}
        />
        <CustomInputField
          type="text"
          placeholder="Enter Shop Facebook Url"
          name="fbUrl"
          label="Facebook Link"
          formik={formik}
        />
        <div className="mt-2">
          <FilledSubmitButton
            text="Submit"
            isLoading={isLoading || isUpdating}
          />
        </div>
      </form>
    </div>
  );
}

export default AddOrEditShopModal;
