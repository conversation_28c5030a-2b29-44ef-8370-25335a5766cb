import Cookies from 'js-cookie';
import { useState } from 'react';
import { useLocation } from 'react-router-dom';

import CategoryWiseSalesSummary from './CategoryWiseSalesSummary';
import ProductWiseSalesSummery from './ProductWiseSalesSummery';
import SalesSummeryPageFilterModal from './SalesSummeryPageFilterModal';
import ShopWiseSalesSummary from './ShopwiseSalesSummary';

import FilterButton from '@/components/reusable/Buttons/FilterButton';
import StartDateEndDateWithSearch from '@/components/reusable/ReusableFilters/StartDateEndDateWithSearch';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import {
  useGetCategoryWiseSaleSummeryQuery,
  useGetProductWiseSaleSummeryQuery,
  useGetShopSummeryReportQuery,
} from '@/redux/api/warehouseApis/reportsApis';

interface Props {
  warehouseId: string;
}

function SalesSummeryPageOverview({ warehouseId }: Props) {
  const [isFilterModalOpen, setIsFilterModalOpen] = useState<boolean>(false);
  const router = new URLSearchParams(useLocation().search);
  const startDate = router.get('startDate') || `${new Date().toISOString()}`;
  const endDate = router.get('endDate') || `${new Date().toISOString()}`;

  const {
    data: shopWiseData,
    isLoading,
    isFetching,
  } = useGetShopSummeryReportQuery({
    organizationId: Cookies.get('organizationId') ?? '',
    warehouseId,
    type: 'custom',
    startDate,
    endDate,
  });

  const { data: categorySummery } = useGetCategoryWiseSaleSummeryQuery({
    organizationId: Cookies.get('organizationId') ?? '',
    warehouseId,
    type: 'custom',
    startDate,
    endDate,
  });

  const { data: productSummery } = useGetProductWiseSaleSummeryQuery({
    organizationId: Cookies.get('organizationId') ?? '',
    warehouseId,
    type: 'custom',
    startDate,
    endDate,
    sortBy: 'alphabetical_asc',
  });

  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-1">
        <div className="flex items-center">
          <div className="search-title-and-btn flex items-center">
            <div className="relative">
              <div className="block xl:hidden">
                <FilterButton
                  handleClick={() => setIsFilterModalOpen(!isFilterModalOpen)}
                />
              </div>
              <div
                className={`${isFilterModalOpen ? 'block' : 'hidden'} xl:hidden`}
              >
                <SalesSummeryPageFilterModal />
              </div>
            </div>
          </div>
          <div className="hidden xl:block">
            <StartDateEndDateWithSearch />
          </div>
        </div>
      </div>
      <div>
        {!isLoading && !isFetching ? (
          <ShopWiseSalesSummary shopWiseData={shopWiseData} />
        ) : (
          <TableSkeletonLoader tableColumn={9} tableRow={2} />
        )}
      </div>
      <div className="mb-8">
        {!isLoading && !isFetching ? (
          <ProductWiseSalesSummery
            productSummery={productSummery}
            data={shopWiseData}
          />
        ) : (
          <TableSkeletonLoader tableColumn={4} tableRow={2} />
        )}
      </div>
      <div className="mb-20">
        {!isLoading && !isFetching ? (
          <CategoryWiseSalesSummary
            categorySummery={categorySummery}
            data={shopWiseData}
          />
        ) : (
          <TableSkeletonLoader tableColumn={4} tableRow={2} />
        )}
      </div>
    </div>
  );
}

export default SalesSummeryPageOverview;
