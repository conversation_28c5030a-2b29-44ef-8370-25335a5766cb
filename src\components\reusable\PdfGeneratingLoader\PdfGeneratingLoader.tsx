import Lottie from 'react-lottie';

import animationData from './pdf-loader.json';

function PdfGeneratingLoader() {
  const defaultOptions = {
    loop: true,
    autoplay: true,
    animationData,
    rendererSettings: {
      preserveAspectRatio: 'xMidYMid slice',
    },
  };
  return (
    <div className="flex flex-col items-center justify-center gap-4">
      <Lottie options={defaultOptions} />
      <h2 className="blink-animation text-2xl font-bold text-white">
        Generating PDF. Please wait...
      </h2>
    </div>
  );
}

export default PdfGeneratingLoader;
