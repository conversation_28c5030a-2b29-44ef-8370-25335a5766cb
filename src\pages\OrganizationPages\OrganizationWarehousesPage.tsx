import Cookies from 'js-cookie';

import OrganizationSingleShopCard from '@/components/OrganizationsComponents/OrganizationShopAndWarehouseComponents/OrganizationSingleShopCard';
import SpinnerLoader from '@/components/reusable/SpinnerLoader/SpinnerLoader';
import { useGetOrgWarehousesQuery } from '@/redux/api/organizationApis/orgShopAndWarehouseApis';
import { ProtectedRoute } from '@/utils/ProtectedRoutes';

function OrganizationWarehousesPage() {
  const organizationId = Cookies.get('organizationId');
  const { data, isLoading } = useGetOrgWarehousesQuery({
    organizationId,
  });

  return (
    <ProtectedRoute>
      <div>
        <div className="mb-4 flex items-center justify-between">
          <span className="text-2xl font-bold text-[#28243D]">Warehouse</span>
        </div>
        {!isLoading ? (
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4">
            {data?.data ? (
              data?.data?.map((shop: any) => (
                <OrganizationSingleShopCard
                  shop={shop}
                  key={shop.warehouseId || shop.shopId}
                  type="warehouse"
                />
              ))
            ) : (
              <p>No Shop Found</p>
            )}
          </div>
        ) : (
          <SpinnerLoader />
        )}
      </div>
    </ProtectedRoute>
  );
}

export default OrganizationWarehousesPage;
