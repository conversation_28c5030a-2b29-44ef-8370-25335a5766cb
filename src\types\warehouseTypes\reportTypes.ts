import { Warehouse } from '../shopTypes/shopTransferAmountToOwnerTypes';
import { SingleProductDataOnDetails } from './productTypes';

export interface StockEntryReportResponse {
  success: boolean;
  statusCode: number;
  message: string;
  data: {
    result: SingleStockEntryDetails[];
    warehouse: Warehouse;
  };
}

export interface StockAnalysisReportResponse {
  success: boolean;
  message: string;
  statusCode: number;
  data: {
    result: SingleProductDataOnDetails[];
    warehouse: Warehouse;
  };
}

export interface SingleStockEntryDetails {
  id: string;
  createdAt: string;
  updatedAt: string;
  supplierId: string;
  supplierInvoiceNo: string;
  note: string;
  proofUrl: any;
  totalPrice: number;
  totalPaid: number;
  advancePayment: number;
  totalDue: number;
  purchaseDate: string;
  warehouseId: string;
  Supplier: {
    User: { name: string };
  };
  Purchase: Purchase[];
}

export interface Purchase {
  Product: Product;
  retailPrice: number;
  purchasePrice: number;
  quantity: number;
}

export interface Product {
  name: string;
}
