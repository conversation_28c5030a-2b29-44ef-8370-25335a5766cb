import Cookies from 'js-cookie';
import { Sheet, Upload } from 'lucide-react';
import { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

import CustomerPageFilterModal from './CustomerPageFilterModal';
import CustomerPageFilterOptions from './CustomerPageFilterOptions';

import {
  DeleteButton,
  EditButton,
  EyeButton,
} from '@/components/reusable/Buttons/CommonButtons';
import FilterButton from '@/components/reusable/Buttons/FilterButton';
import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import Pagination from '@/components/reusable/Pagination/Pagination';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import {
  <PERSON><PERSON><PERSON>,
  MenubarContent,
  MenubarItem,
  MenubarMenu,
  MenubarTrigger,
} from '@/components/ui/menubar';
import { useGetCustomersQuery } from '@/redux/api/warehouseApis/customersApi';
import { ROUTES } from '@/Routes';
import { ShopCustomerDetails } from '@/types/shopTypes/shopCustomersTypes';
import { handleGenerateCustomerCsv } from '@/utils/GenerateCsv';
import { generateFilterParams } from '@/utils/generateFilterParams';

interface Props {
  warehouseId: string;
}

function CustomersPageOverview({ warehouseId }: Props) {
  const navigate = useNavigate();
  const router = new URLSearchParams(useLocation().search);
  const serialNo = router.get('serialNo');
  const name = router.get('name');
  const mobileNumber = router.get('mobileNumber');
  const page = router.get('page');
  const limit = router.get('limit');

  const { data, isLoading, isFetching } = useGetCustomersQuery({
    organizationId: Cookies.get('organizationId'),
    page: page ?? '1',
    serialNo: serialNo ?? undefined,
    name: name ?? undefined,
    mobileNumber: mobileNumber ?? undefined,
    limit: limit ?? '10',
  });
  const [isFilterModalOpen, setIsFilterModalOpen] = useState<boolean>(false);

  const handleFilter = (fieldName: string, value: string) => {
    const query = generateFilterParams(fieldName, value);
    navigate(ROUTES.WAREHOUSE.CUSTOMERS(warehouseId, query));
  };

  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-2">
        <div className="flex items-center">
          <div className="search-title-and-btn flex items-center">
            <div className="relative">
              <div className="block xl:hidden">
                <FilterButton
                  handleClick={() => setIsFilterModalOpen(!isFilterModalOpen)}
                />
              </div>
              <div
                className={`${isFilterModalOpen ? 'block' : 'hidden'} xl:hidden`}
              >
                <CustomerPageFilterModal
                  handleClearAndClose={() => setIsFilterModalOpen(false)}
                  handleFilter={handleFilter}
                />
              </div>
            </div>
          </div>
          <div className="hidden xl:block">
            <div className="flex items-center gap-x-2">
              <CustomerPageFilterOptions handleFilter={handleFilter} />
            </div>
          </div>
        </div>
      </div>
      <div>
        {!isLoading && !isFetching ? (
          <div>
            <div className="tableTop w-full">
              <p>Customers List</p>
              <div className="flex items-center gap-4">
                <Menubar
                  style={{
                    border: 'none',
                    backgroundColor: 'transparent',
                  }}
                >
                  <MenubarMenu>
                    <MenubarTrigger
                      className="cursor-pointer"
                      disabled={!data?.data.length}
                    >
                      <button
                        className="flex items-center gap-2 rounded bg-white px-4 py-1 text-black"
                        type="button"
                      >
                        <Upload size={20} />
                        <span>Export ({data?.data?.length})</span>
                      </button>
                    </MenubarTrigger>
                    <MenubarContent
                      style={{
                        marginRight: '25px',
                        borderColor: 'black',
                      }}
                    >
                      <MenubarItem
                        onClick={() => handleGenerateCustomerCsv(data?.data)}
                        className="flex cursor-pointer items-center gap-2 bg-primary font-semibold text-white"
                      >
                        <Sheet size={20} />
                        <span>Download CSV</span>
                      </MenubarItem>

                      {/* <MenubarSeparator /> */}
                    </MenubarContent>
                  </MenubarMenu>
                </Menubar>
                <p>Total : {data?.pagination?.total}</p>
              </div>
            </div>
            <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
              {data?.data?.length ? (
                <div className="full-table-box h-custom">
                  <table className="full-table">
                    <thead className="bg-gray-100">
                      <tr>
                        {/* <th className="tableHead">No</th> */}
                        <th className="tableHead">Customer No</th>
                        <th className="tableHead table-col-width">Name</th>
                        <th className="tableHead">Phone</th>
                        <th className="tableHead table-col-width">Address</th>
                        <th className="tableHead">Order Count</th>
                        <th className="tableHead">Created At</th>
                        <th className="tableHead">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y bg-slate-200">
                      {data?.data?.map((customer: ShopCustomerDetails) => (
                        <tr key={customer?.id}>
                          {/* <td className="tableData">{index + 1}</td> */}

                          <td className="tableData">{customer?.serialNo}</td>
                          <td className="tableData table-col-width">
                            {customer?.name}
                          </td>
                          <td className="tableData">
                            {customer?.mobileNumber}
                          </td>
                          <td className="tableData table-col-width">
                            {customer?.address}
                          </td>
                          <td className="tableData">{customer?.orderCount}</td>
                          <td className="tableData">
                            <DateAndTimeViewer date={customer.createdAt} />
                          </td>
                          <td className="tableData">
                            <div className="flex items-center justify-center gap-2">
                              <EyeButton
                                handleClick={() =>
                                  navigate(
                                    ROUTES.WAREHOUSE.CUSTOMER_DETAILS(
                                      warehouseId,
                                      customer.id,
                                    ),
                                  )
                                }
                              />
                              <EditButton
                                handleClick={() => {
                                  toast.warning('clicked but no action set');
                                }}
                              />
                              <DeleteButton
                                handleClick={() => {
                                  toast.warning('clicked but no action set');
                                }}
                              />
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <NoResultFound pageType="customers" />
              )}
            </div>
            <div className="pagination-box flex justify-end rounded bg-white p-3">
              <Pagination
                currentPage={page ?? '1'}
                limit={Number(limit ?? 10)}
                handleFilter={(fieldName: string, value: any) =>
                  handleFilter(fieldName, value)
                }
                totalCount={data?.pagination?.total}
                totalPages={Math.ceil(
                  Number(data?.pagination?.total) /
                    Number(data?.pagination?.limit),
                )}
              />
            </div>
          </div>
        ) : (
          <TableSkeletonLoader tableColumn={7} tableRow={6} />
        )}
      </div>
    </div>
  );
}

export default CustomersPageOverview;
