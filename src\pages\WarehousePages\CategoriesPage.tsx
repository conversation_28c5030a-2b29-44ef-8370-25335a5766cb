import { useParams } from 'react-router-dom';

import CategoryPageOverview from '@/components/WarehouseComponents/CategoryPageComponents/CategoryPageOverview';
import { ProtectedRoute } from '@/utils/ProtectedRoutes';

function CategoriesPage() {
  const { warehouseId } = useParams();
  return (
    <ProtectedRoute>
      <CategoryPageOverview warehouseId={warehouseId ?? ''} />
    </ProtectedRoute>
  );
}

export default CategoriesPage;
