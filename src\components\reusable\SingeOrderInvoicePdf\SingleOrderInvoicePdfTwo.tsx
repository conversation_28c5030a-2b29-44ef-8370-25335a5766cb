import { Font, Image, Page, Text, View } from '@react-pdf/renderer';

import { styles } from './SingleOrderInvoiceStyle';

import {
  ShopOrderDetails,
  ShopSingleOrderItem,
} from '@/types/shopTypes/shopOrderTypes';
import { numberToWords } from '@/utils/NumberToText';

Font.register({
  family: 'Open Sans',
  fonts: [
    {
      src: 'https://cdn.jsdelivr.net/npm/open-sans-all@0.1.3/fonts/open-sans-regular.ttf',
    },
    {
      src: 'https://cdn.jsdelivr.net/npm/open-sans-all@0.1.3/fonts/open-sans-600.ttf',
      fontWeight: 800,
    },
  ],
});

// Register Noto Sans Bengali fonts
Font.register({
  family: 'Noto Sans Bengali',
  fonts: [
    {
      src: 'https://fonts.gstatic.com/ea/notosansbengali/v3/NotoSansBengali-Regular.ttf',
    },
    {
      src: 'https://fonts.gstatic.com/ea/notosansbengali/v3/NotoSansBengali-Bold.ttf',
      fontWeight: 600,
    },
  ],
});

interface Props {
  orderDetails?: ShopOrderDetails;
}

function SingleOrderInvoicePdfTwo({ orderDetails }: Props) {
  // const [barcode, setBarcode] = useState<string>('');

  const isBengaliText = (text: string) => {
    const bengaliRegex = /[\u0980-\u09FF]/; // Unicode range for Bengali script
    return bengaliRegex.test(text);
  };

  /* useEffect(() => {
    if (orderDetails?.serialNo) {
      const canvas = document.createElement('canvas');
      JsBarcode(canvas, orderDetails.serialNo.toString(), {
        format: 'CODE128',
        displayValue: false,
      });
      setBarcode(canvas.toDataURL('image/png'));
    }
  }, [orderDetails]); */
  return (
    <Page size="A4">
      <View style={styles.billTop}>
        <View style={[styles.container, styles.topContent]}>
          <View style={[styles.sectionLeft]}>
            <Text style={styles.invoiceText}>INVOICE</Text>
            <Text style={styles.billbyto}>Billed to</Text>
            <Text
              style={
                isBengaliText(
                  orderDetails?.customerName ??
                    orderDetails?.Customer?.name ??
                    '',
                )
                  ? styles.customerNameBangla
                  : styles.customerName
              }
            >
              {orderDetails?.customerName ?? orderDetails?.Customer?.name}
            </Text>
            <Text style={styles.OrderAddress}>
              Address: {orderDetails?.address}
            </Text>
            <Text style={styles.OrderAddress}>
              Phone:{' '}
              {orderDetails?.customerMobileNumber ??
                orderDetails?.Customer?.mobileNumber}
            </Text>
          </View>
          <View style={[styles.sectionLeft, styles.sectionMiddleTop]}>
            {orderDetails?.trackingNumber ? (
              <View style={styles.displayContent}>
                <Text style={styles.customerName}>
                  {orderDetails?.deliveryPartner} ID :
                </Text>
                <Text style={styles.customerName2}>
                  {orderDetails?.trackingNumber}
                </Text>
              </View>
            ) : (
              ''
            )}
            <View style={styles.displayContent}>
              <Text style={styles.customerName}>Invoice No :</Text>
              <Text style={styles.customerName2}>{orderDetails?.serialNo}</Text>
            </View>
            <View style={styles.displayContent}>
              <Text style={styles.customerName}>Date of issue : </Text>
              <Text style={styles.customerName2}>
                {new Intl.DateTimeFormat('en-US', {
                  day: '2-digit',
                  month: 'short',
                  year: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit',
                }).format(new Date(orderDetails?.createdAt ?? ''))}
              </Text>
            </View>
          </View>
          <View style={styles.sectionRight}>
            {orderDetails?.Shop?.imgUrl ? (
              <Image
                src={{
                  uri: `https://retail-pluse-upload.s3.ap-southeast-1.amazonaws.com/${orderDetails?.Shop?.imgUrl}`,
                  method: 'GET',
                  headers: { 'Cache-Control': 'no-cache' },
                  body: '',
                }}
                style={styles.companyLogo}
              />
            ) : (
              <Text style={styles.billbyto}>{orderDetails?.Shop?.name}</Text>
            )}
            {/* <Text style={styles.billbyto}>Bill by</Text> */}
            {/* <Text style={styles.customerName}>Admin</Text> */}
            <Text style={styles.OrderAddress}>
              {orderDetails?.Shop?.address}
            </Text>
            <Text style={styles.OrderAddress}>
              {orderDetails?.Shop?.mobileNumber}
            </Text>
            <Text style={styles.OrderAddress}>
              {orderDetails?.Shop?.websiteUrl}
            </Text>
            <Text style={styles.OrderAddress}>{orderDetails?.Shop?.fbUrl}</Text>
          </View>
        </View>
      </View>
      <View
        style={{
          padding: '0px 20px',
        }}
      >
        <View style={styles.fullTable}>
          <View style={styles.tableHead}>
            <Text style={[styles.tableHeadOne, styles.textLeft]}>
              Item Detail
            </Text>
            <Text style={(styles.tableHeadTwo, styles.textCenter)}>Qty</Text>
            <Text style={[styles.tableHeadThree, styles.textCenter]}>Rate</Text>
            <Text style={[styles.tableHeadFour, styles.textRight]}>Amount</Text>
          </View>
          {orderDetails?.OrderItem?.map((item: ShopSingleOrderItem) => (
            <View style={styles.tableBody}>
              <Text style={[styles.tableHeadOne, styles.textLeft]}>
                {item?.Stock?.barcode}-{item?.Stock?.Product?.name}
              </Text>
              <Text style={(styles.tableHeadTwo, styles.textCenter)}>2</Text>
              <Text style={[styles.tableHeadThree, styles.textCenter]}>
                400.00
              </Text>
              <Text style={[styles.tableHeadFour, styles.textRight]}>
                800.00
              </Text>
            </View>
          ))}
        </View>

        <View style={styles.tableFooter}>
          <View style={styles.tableFooterLeft}>
            <Text style={styles.tableNote}>Notes:</Text>
            <Text style={styles.tableNoteDes}>{orderDetails?.note}</Text>
            <Text style={styles.payableAmount}>
              PAYABLE AMOUNT :{' '}
              {numberToWords(
                Number(orderDetails?.totalDue),
              ).toLocaleUpperCase()}{' '}
              TAKA ONLY
            </Text>
          </View>
          <View style={styles.tableFooterRight}>
            <View style={styles.tableFooterRightFull}>
              <View style={styles.calculation}>
                <Text style={styles.calculationText}>Subtotal:</Text>
                <Text style={styles.calculationText}>2400.00</Text>
              </View>
              <View style={styles.calculation}>
                <Text style={styles.calculationText}>Discount:</Text>
                <Text style={styles.calculationText}>200.00</Text>
              </View>
              <View style={styles.calculation}>
                <Text style={styles.calculationText}>Tax:</Text>
                <Text style={styles.calculationText}>20.00</Text>
              </View>
              <View style={[styles.calculation, styles.calculationBg]}>
                <Text style={styles.calculationText}>Total:</Text>
                <Text style={styles.calculationText}>2180.00</Text>
              </View>
              <View style={styles.calculation}>
                <Text style={styles.calculationText}>Paid Amount:</Text>
                <Text style={styles.calculationText}>1000.00</Text>
              </View>
              <View style={styles.calculation}>
                <Text style={styles.calculationText}>Due Amount:</Text>
                <Text style={styles.calculationText}>1180.00</Text>
              </View>
            </View>
          </View>
        </View>

        <View style={styles.copyright}>
          <Text style={styles.copyrightText}>
            Software Made By SOFTS.AI | Call : +880 1627712571
          </Text>
        </View>
      </View>
    </Page>
  );
}

export default SingleOrderInvoicePdfTwo;
