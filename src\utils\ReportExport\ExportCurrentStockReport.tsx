import { Document, pdf } from '@react-pdf/renderer';
import { toast } from 'react-toastify';
import * as XLSX from 'xlsx';

import CurrentStockPdf from '@/components/WarehouseComponents/ReportsPdf/CurrentStockPdf';
import {
  ShopStockReportResponse,
  SingleProductStockReport,
} from '@/types/shopTypes/shopStockTransferReportTypes';
import { Warehouse } from '@/types/shopTypes/shopTransferAmountToOwnerTypes';

export const handleGenerateCurrentStockPdf = async (
  products: SingleProductStockReport[],
  warehouse?: Warehouse,
  date?: string,
  totalPurchasePrice?: number,
  totalQuantity?: number,
  totalRetailPrice?: number,
) => {
  const blob = await pdf(
    <Document>
      <CurrentStockPdf
        products={products}
        warehouse={warehouse}
        date={date}
        totalPurchasePrice={totalPurchasePrice}
        totalQuantity={totalQuantity}
        totalRetailPrice={totalRetailPrice}
      />
    </Document>,
  ).toBlob();

  const url = URL.createObjectURL(blob);
  window.open(url);
};

export const handleGenerateCurrentStockCsv = ({
  data,
  userRole,
}: {
  data?: ShopStockReportResponse;
  userRole: string;
}) => {
  if (!data || data.data.result.length === 0) {
    toast.error('No data available to generate Excel');
    return;
  }

  // Define Excel headers
  const headers = ['No', 'Name', 'Quantity', 'Purchase Price', 'Retail Price'];

  // Map data rows
  const rows = data.data.result.map(
    (order: SingleProductStockReport, index: number) => {
      return [
        index + 1,
        order.name,
        order.quantity,
        userRole === 'admin' ? order.purchasePrice : '***',
        order.retailPrice,
      ];
    },
  );

  // Combine headers and rows
  const sheetData = [headers, ...rows];

  // Create a worksheet
  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // Create a workbook and add the worksheet
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Sales Report');

  // Generate Excel file and download
  const excelFileName = `current_stock_report.xlsx`;
  XLSX.writeFile(workbook, excelFileName);
};
