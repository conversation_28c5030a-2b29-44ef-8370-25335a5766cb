import Cookies from 'js-cookie';
import React from 'react';

import { useAppSelector } from '@/redux/hooks';

function ProtectedDataViewer({ children }: { children: React.ReactNode }) {
  const { permissionInShop } = useAppSelector((state) => state.userDetails);
  return (
    <>
      {permissionInShop === 'ADMIN' || Cookies.get('type') === 'CUSTOMER'
        ? children
        : null}
    </>
  );
}

export default ProtectedDataViewer;
