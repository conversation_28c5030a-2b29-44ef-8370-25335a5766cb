import BaseApi from '../baseApi';

import { SingleSubscription } from './orgSubscriptionsApis';

import { Pagination } from '@/redux/commonTypes';
import { TagTypes } from '@/redux/tag-types';

interface GetNotificationsReqestParams {
  organizationId?: string;
  warehouseId?: string;
  shopId?: string;
  limit?: number;
  page?: number;
}
const OrgNotificationApis = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getAllNotifications: builder.query<
      GetNotificationListResponse,
      GetNotificationsReqestParams
    >({
      query: (params) => ({
        url: `/notification`,
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.ORG_PAYMENTS],
    }),
    getSingleOrgPaymentDetails: builder.query<
      GetOrgSinglePaymentDetailsResponse,
      string
    >({
      query: (id) => ({
        url: `/payment/${id}`,
        method: 'GET',
        // params,
      }),
      providesTags: [TagTypes.ORG_PAYMENTS],
    }),
  }),
});

export const {
  useGetAllNotificationsQuery,
  useGetSingleOrgPaymentDetailsQuery,
} = OrgNotificationApis;

export interface GetOrgPaymentListResponse {
  success: boolean;
  message: string;
  statusCode: number;
  data: SingleOrgBill[];
  pagination: Pagination;
}
export interface GetOrgSinglePaymentDetailsResponse {
  success: boolean;
  message: string;
  statusCode: number;
  data: SingleOrgBill;
}

export interface SingleOrgBill {
  id: string;
  createdAt: string;
  updatedAt: string;
  organizationId: string;
  subscriptionId: string;
  amount: number;
  dueDate: string;
  paidAt: any;
  isPaid: boolean;
  paymentMethod: string;
  paymentID: string;
  status: string;
  payUrl: string;
  referenceId: any;
  for: string;
  Subscription: SingleSubscription;
  serialNo: string;
  Organization: {
    id: string;
    name: string;
  };
}

export interface GetNotificationListResponse {
  success: boolean;
  message: string;
  statusCode: number;
  pagination: Pagination;
  data: Notification[];
}

export interface Notification {
  id: string;
  createdAt: string;
  updatedAt: string;
  expiresAt: any;
  title: string;
  message: string;
  event: string;
  source: string;
  isActionRequired: boolean;
  actionUrl: any;
  metadata: any;
  orderId: any;
  receivers: Receiver[];
}

export interface Receiver {
  id: string;
  createdAt: string;
  updatedAt: string;
  status: string;
  readAt: any;
  notificationId: string;
  receiverType: string;
  seenById: any;
  userId: any;
  shopId: string;
  warehouseId: any;
  organizationId: any;
}
