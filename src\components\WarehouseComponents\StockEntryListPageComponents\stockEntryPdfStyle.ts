import { StyleSheet } from '@react-pdf/renderer';

const stockEntryInvoicePdfStyle = StyleSheet.create({
  page: {
    padding: 30,
    backgroundColor: '#ffffff',
  },
  header: {
    marginBottom: 20,
    borderBottom: 1,
    borderColor: '#e5e7eb',
    paddingBottom: 20,
  },
  companyName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 5,
    textAlign: 'center',
  },
  companyAddress: {
    fontSize: 10,
    color: '#6b7280',
    marginBottom: 3,
    textAlign: 'center',
  },
  invoiceTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginTop: 10,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 10,
    backgroundColor: '#f3f4f6',
    padding: 5,
  },
  row: {
    flexDirection: 'row',
    marginBottom: 5,
  },
  label: {
    width: '40%',
    fontSize: 10,
    color: '#6b7280',
  },
  value: {
    width: '60%',
    fontSize: 10,
    color: '#1f2937',
  },
  table: {
    width: '100%',
    marginBottom: 20,
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#f3f4f6',
    paddingVertical: 5,
    borderBottom: 1,
    borderColor: '#e5e7eb',
  },
  tableRow: {
    flexDirection: 'row',
    paddingVertical: 5,
    borderBottom: 1,
    borderColor: '#e5e7eb',
  },
  tableCell: {
    fontSize: 10,
    paddingHorizontal: 5,
  },
  tableCellHeader: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#1f2937',
    paddingHorizontal: 5,
  },
  col1: { width: '5%' },
  col2: { width: '50%' },
  col3: { width: '10%' },
  col4: { width: '10%' },
  col6: { width: '25%' },
  amountRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5,
    paddingVertical: 3,
  },
  amountLabel: {
    fontSize: 10,
    color: '#6b7280',
  },
  amountValue: {
    fontSize: 10,
    color: '#1f2937',
    fontWeight: 'bold',
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
    paddingTop: 10,
    borderTop: 1,
    borderColor: '#e5e7eb',
  },
  totalLabel: {
    fontSize: 12,
    color: '#1f2937',
    fontWeight: 'bold',
  },
  totalValue: {
    fontSize: 12,
    color: '#1f2937',
    fontWeight: 'bold',
  },
  noteSection: {
    marginTop: 20,
    padding: 10,
    backgroundColor: '#f3f4f6',
    borderRadius: 4,
  },
  noteLabel: {
    fontSize: 10,
    color: '#6b7280',
    marginBottom: 5,
  },
  noteText: {
    fontSize: 10,
    color: '#1f2937',
  },
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 30,
    right: 30,
    textAlign: 'center',
    fontSize: 8,
    color: '#6b7280',
  },
});

export default stockEntryInvoicePdfStyle;
