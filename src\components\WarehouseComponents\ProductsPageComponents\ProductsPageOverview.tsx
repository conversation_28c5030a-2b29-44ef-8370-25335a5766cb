import Cookies from 'js-cookie';
import { EllipsisVertical, Eye, PencilLine, Trash } from 'lucide-react';
import { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

import FilledButton from '../../reusable/Buttons/FilledButton';
import DeleteModal from '../../reusable/DeleteModal/DeleteModal';
import Modal from '../../reusable/Modal/Modal';

import AddOrEditProductModal from './AddOrEditProductModal';
import ProductFilterOptions from './ProductFilterOptions';
import ProductPageFilterModal from './ProductPageFilterModal';

import ExportButton from '@/components/reusable/Buttons/ExportButton';
import FilterButton from '@/components/reusable/Buttons/FilterButton';
import ImageViewer from '@/components/reusable/ImageViewer/ImageViewer';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import Pagination from '@/components/reusable/Pagination/Pagination';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import {
  Menubar,
  MenubarContent,
  MenubarItem,
  MenubarMenu,
  MenubarSeparator,
  MenubarTrigger,
} from '@/components/ui/menubar';
import { useGetBrandsQuery } from '@/redux/api/warehouseApis/brandsApi';
import { useGetCategoriesQuery } from '@/redux/api/warehouseApis/categoriesApi';
import {
  useDeleteProductMutation,
  useGetProductsQuery,
} from '@/redux/api/warehouseApis/productsApi';
import { useAppSelector } from '@/redux/hooks';
import { ROUTES } from '@/Routes';
import { ViewFromType } from '@/types/commonTypes';
import { SingleBrandDetails } from '@/types/warehouseTypes/brandsTypes';
import { SingleProductDetails } from '@/types/warehouseTypes/productTypes';
import { CalculateDiscountPrice } from '@/utils/CalculateDiscountPrice';
import { generateFilterParams } from '@/utils/generateFilterParams';
import { handleGenerateProductListCsv } from '@/utils/ReportExport/ExportProductList';

interface Props {
  warehouseId?: string;
  shopId?: string;
  viewFrom?: ViewFromType;
}

function ProductsPageOverview({
  warehouseId = '',
  shopId = '',
  viewFrom,
}: Props) {
  const userDetailsFromState = useAppSelector((state) => state.userDetails);
  const navigate = useNavigate();
  const router = new URLSearchParams(useLocation().search);
  const serialNo = router.get('serialNo');
  const name = router.get('name');
  const brandName = router.get('brandName');
  const categoryName = router.get('categoryName');
  const page = router.get('page');
  const limit = router.get('limit');

  const [isCreateProductModalOpen, setIsCreateProductModalOpen] =
    useState<boolean>(false);
  const { data, isLoading, refetch, isFetching } = useGetProductsQuery({
    organizationId: Cookies.get('organizationId') as string,
    warehouseId:
      viewFrom === 'ORGANIZATION'
        ? undefined
        : viewFrom === 'WAREHOUSE'
          ? warehouseId
          : shopId,
    page: page ?? '1',
    serialNo: serialNo ?? undefined,
    name: name ?? undefined,
    brandName: brandName ?? undefined,
    categoryName: categoryName ?? undefined,
    limit: limit ?? '10',
  });
  const { data: brands } = useGetBrandsQuery({
    warehouseId,
    organizationId: Cookies.get('organizationId'),
  });
  const { data: categories } = useGetCategoriesQuery({
    warehouseId,
    organizationId: Cookies.get('organizationId'),
  });

  const [deleteProduct] = useDeleteProductMutation();
  const [isEditProductModalOpen, setIsEditProductModalOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] =
    useState<SingleProductDetails>();
  const [isDeleteProductModalOpen, setIsDeleteProductModalOpen] =
    useState(false);
  const [deleteProductData, setDeleteProductData] =
    useState<SingleProductDetails>();
  const [isFilterModalOpen, setIsFilterModalOpen] = useState<boolean>(false);

  const handleDeleteProduct = async () => {
    toast.promise(deleteProduct(deleteProductData?.id).unwrap(), {
      pending: 'Deleting Product...',
      success: {
        render({ data: res }) {
          if (res?.statusCode === 200 || res?.statusCode === 201) {
            refetch();
            setIsDeleteProductModalOpen(false);
          }
          return 'Product Deleted Successfully';
        },
      },
      error: {
        render({ data: error }) {
          console.log(error);
          return 'Error on delete Product';
        },
      },
    });
  };

  const handleFilter = (fieldName: string, value: string) => {
    const query = generateFilterParams(fieldName, value);
    navigate(ROUTES.WAREHOUSE.PRODUCTS(warehouseId ?? '', query));
  };
  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-1">
        <div className="flex items-center">
          <div className="search-title-and-btn flex items-center">
            {/* <p className="whitespace-nowrap">Search Filters</p> */}
            <div className="relative">
              <div className="block xl:hidden">
                <FilterButton
                  handleClick={() => setIsFilterModalOpen(!isFilterModalOpen)}
                />
              </div>
              <div
                className={`${isFilterModalOpen ? 'block' : 'hidden'} xl:hidden`}
              >
                <ProductPageFilterModal
                  handleClearAndClose={() => setIsFilterModalOpen(false)}
                  handleFilter={handleFilter}
                  brands={brands?.data ?? []}
                  categories={categories?.data ?? []}
                />
              </div>
            </div>
          </div>
          <div className="hidden xl:block">
            <div className="flex items-center gap-x-2">
              <ProductFilterOptions
                handleFilter={handleFilter}
                brands={brands?.data ?? []}
                categories={categories?.data ?? []}
              />
            </div>
          </div>
        </div>
        <div>
          <FilledButton
            isLoading={false}
            text="Add New"
            handleClick={() => setIsCreateProductModalOpen(true)}
            isDisabled={false}
          />
        </div>
      </div>
      {!isLoading && !isFetching ? (
        <div>
          <div className="tableTop w-full">
            <p>Products List</p>
            <div className="flex items-center">
              <p>Total : {data?.pagination?.total}</p>
              <div className="ml-4">
                <ExportButton
                  totalCount={data?.data?.length ?? 0}
                  handleExportCsv={() =>
                    handleGenerateProductListCsv({
                      data: data?.data,
                    })
                  }
                  // handleExportPdf={() => handleGenerateSalesReportPdf(data?.data)}
                />
              </div>
            </div>
          </div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {data?.data?.length ? (
              <div className="full-table-box h-custom">
                <table className="full-table">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="tableHead">Image</th>
                      <th className="tableHead">ID</th>
                      <th className="tableHead table-col-width">Name</th>
                      <th className="tableHead">Brand</th>
                      <th className="tableHead">Category</th>
                      {/* <th className="tableHead">Variants</th> */}
                      <th className="tableHead">Purchase Price</th>
                      {/* <th className="tableHead">Wholesale Price</th> */}
                      <th className="tableHead">Regular Price</th>
                      <th className="tableHead">Discount Price</th>
                      <th className="tableHead">Available</th>
                      {/* <th className="tableHead">Total Sold</th> */}
                      <th className="tableHead">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y bg-slate-200">
                    {data?.data?.map((product: SingleProductDetails) => (
                      <tr key={product?.id}>
                        <td className="tableData">
                          <ImageViewer imageUrl={product?.imgUrl} />
                        </td>
                        <td className="tableData">{product?.serialNo ?? 0}</td>
                        <td className="tableData table-col-width">
                          {product?.name}
                        </td>
                        <td className="tableData">{product?.Brand?.name}</td>
                        <td className="tableData">{product?.Category?.name}</td>
                        {/* <td className="tableData">
                          <div>
                            {product?.Options?.map(
                              (option: ProductVariantOrSubVariant) => (
                                <div
                                  key={option.id}
                                  className="flex items-center gap-1"
                                >
                                  <span>{option?.name}:</span>
                                  <span className="flex items-center gap-2">
                                    {option?.values?.map((value: string) => (
                                      <span key={value}>{value},</span>
                                    ))}
                                  </span>
                                </div>
                              ),
                            )}
                          </div>
                        </td> */}
                        <td className="tableData">
                          {product?.currentPurchasePrice}
                        </td>
                        {/* <td className="tableData">
                          {product?.currentWholesaleSellingPrice}
                        </td> */}
                        <td className="tableData">
                          {product?.currentSellingPrice}
                        </td>
                        <td className="tableData">
                          {CalculateDiscountPrice({
                            retailPrice: product?.currentSellingPrice,
                            discountType: product?.discountType,
                            discount: product?.discount,
                          })}
                        </td>
                        <td className="tableData">
                          <span
                          // className={`${Number(product?.totalAvailable) < 10 && 'blink text-orange-600'}`}
                          >
                            {product?.ProductStockCount?.length
                              ? product?.ProductStockCount[0]?.availableQty
                              : 0}
                          </span>
                        </td>
                        {/* <td className="tableData">{product?.totalSold ?? 0}</td> */}
                        <td className="tableData">
                          <Menubar
                            style={{
                              border: 'none',
                              backgroundColor: 'transparent',
                            }}
                          >
                            <MenubarMenu>
                              <MenubarTrigger className="cursor-pointer">
                                <EllipsisVertical />
                              </MenubarTrigger>
                              <MenubarContent
                                style={{
                                  marginRight: '25px',
                                  borderColor: 'black',
                                }}
                              >
                                {/* <MenubarItem
                                  onClick={() =>
                                    navigate(
                                      ROUTES.WAREHOUSE.STOCK_SEARCH(
                                        warehouseId,
                                        product?.id,
                                      ),
                                    )
                                  }
                                  className="flex cursor-pointer items-center gap-1 bg-primary font-semibold text-white"
                                >
                                  <LocateFixed size={20} />
                                  <span>Locations</span>
                                </MenubarItem> */}

                                <MenubarSeparator />
                                <MenubarItem
                                  onClick={() =>
                                    navigate(
                                      ROUTES.WAREHOUSE.PRODUCT_DETAILS(
                                        warehouseId,
                                        product?.id,
                                      ),
                                    )
                                  }
                                  className="flex cursor-pointer items-center gap-1 bg-primary font-semibold text-white"
                                >
                                  <Eye size={20} />
                                  <span>View Details</span>
                                </MenubarItem>

                                <MenubarSeparator />
                                <MenubarItem
                                  onClick={() => {
                                    setSelectedProduct(product);
                                    setIsEditProductModalOpen(true);
                                  }}
                                  className="flex cursor-pointer items-center gap-1 bg-primary font-semibold text-white"
                                >
                                  <PencilLine size={20} />
                                  <span>Edit</span>
                                </MenubarItem>

                                <MenubarSeparator />
                                {userDetailsFromState?.permissionInShop ===
                                'ADMIN' ? (
                                  <MenubarItem
                                    onClick={() => {
                                      setIsDeleteProductModalOpen(true);
                                      setDeleteProductData(product);
                                    }}
                                    className="flex cursor-pointer items-center gap-1 bg-primary font-semibold text-white"
                                  >
                                    <Trash size={20} />
                                    <span>Delete</span>
                                  </MenubarItem>
                                ) : (
                                  ''
                                )}
                              </MenubarContent>
                            </MenubarMenu>
                          </Menubar>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <NoResultFound pageType="product" />
            )}
          </div>
          <div className="pagination-box flex justify-end rounded bg-white p-3">
            <Pagination
              currentPage={page ?? '1'}
              limit={Number(limit ?? 10)}
              handleFilter={(fieldName: string, value: any) =>
                handleFilter(fieldName, value)
              }
              totalCount={data?.pagination?.total}
              totalPages={Math.ceil(
                Number(data?.pagination?.total) /
                  Number(data?.pagination?.limit),
              )}
            />
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={14} tableRow={6} />
      )}
      <Modal
        setShowModal={setIsCreateProductModalOpen}
        showModal={isCreateProductModalOpen}
      >
        <AddOrEditProductModal
          type="new"
          warehouseId={warehouseId ?? ''}
          handleClose={() => setIsCreateProductModalOpen(false)}
          brands={brands?.data?.map((single: SingleBrandDetails) => {
            return {
              value: single.id,
              label: single.name,
            };
          })}
          categories={categories?.data}
        />
      </Modal>
      <Modal
        setShowModal={setIsEditProductModalOpen}
        showModal={isEditProductModalOpen}
      >
        <AddOrEditProductModal
          type="edit"
          warehouseId={warehouseId ?? ''}
          handleClose={() => setIsEditProductModalOpen(false)}
          brands={brands?.data?.map((single: SingleBrandDetails) => {
            return {
              value: single.id,
              label: single.name,
            };
          })}
          categories={categories?.data}
          productData={selectedProduct}
        />
      </Modal>
      <Modal
        setShowModal={setIsDeleteProductModalOpen}
        showModal={isDeleteProductModalOpen}
      >
        <DeleteModal
          type="Product"
          name={deleteProductData?.name ?? ''}
          handleClose={() => setIsDeleteProductModalOpen(false)}
          handleDelete={handleDeleteProduct}
        />
      </Modal>
    </div>
  );
}

export default ProductsPageOverview;
