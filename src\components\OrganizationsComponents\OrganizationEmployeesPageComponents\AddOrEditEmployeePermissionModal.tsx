import { useFormik } from 'formik';
import Cookies from 'js-cookie';
import { useEffect } from 'react';
import { toast } from 'react-toastify';
import * as Yup from 'yup';

import FilledSubmitButton from '@/components/reusable/Buttons/FilledSubmitButton';
import CustomDropdown from '@/components/reusable/CustomInputField/CustomDropdown';
import ModalTitle from '@/components/reusable/Modal/ModalTitle';
import {
  useAddNewPermissionToEmployeeMutation,
  useUpdateEmployeePermissionMutation,
} from '@/redux/api/organizationApis/orgEmployeeApis';
import {
  useGetOrgShopsQuery,
  useGetOrgWarehousesQuery,
} from '@/redux/api/organizationApis/orgShopAndWarehouseApis';
import { SingleShopDetails } from '@/types/shopTypes';
import {
  EmployeePermissionDetails,
  SingleSellerDetails,
} from '@/types/warehouseTypes/sellersTypes';

const formikInitialValues = {
  role: '',
  assignedShopId: null,
  type: 'SHOP',
  warehouseId: null,
};

interface Props {
  employee?: SingleSellerDetails;
  handleClose: () => void;
  updateRefreshCounter: () => void;
  type: 'new' | 'edit';
  permission?: EmployeePermissionDetails;
}

function AddOrEditEmployeePermissionModal({
  handleClose,
  employee,
  type,
  updateRefreshCounter,
  permission,
}: Props) {
  const organizationId = Cookies.get('organizationId');
  const { data: warehouseData, isLoading: isWarehousesLoading } =
    useGetOrgWarehousesQuery({
      organizationId,
    });
  const { data: shopData, isLoading: isShopsLoading } = useGetOrgShopsQuery({
    organizationId,
  });

  const [assignEmployeeToShop, { isLoading: isSellerUpdating }] =
    useAddNewPermissionToEmployeeMutation();
  const [updateEmployeePermission, { isLoading: isPermissionUpdating }] =
    useUpdateEmployeePermissionMutation();

  const formik = useFormik({
    initialValues: formikInitialValues,
    validationSchema: Yup.object({
      // shopId: Yup.string().required('Shop is required'),
      role: Yup.string().required('Role is required'),
    }),

    onSubmit: async (values) => {
      const userAssignData = {
        role: values.role,
        warehouseId: values.type === 'WAREHOUSE' ? values.warehouseId : null,
        assignedShopId: values.type === 'SHOP' ? values.assignedShopId : null,
        employeeId: employee?.id,
      };
      if (type === 'new') {
        toast.promise(assignEmployeeToShop(userAssignData).unwrap(), {
          pending: 'Assigning Employee To Shop...',
          success: {
            render({ data: res }) {
              if (res.statusCode === 200 || res.statusCode === 201) {
                updateRefreshCounter();
                handleClose();
              }
              return 'Employee Assigned Successfully';
            },
          },
          error: {
            render({ data: error }) {
              console.log(error);
              return 'Error on assign employee';
            },
          },
        });
      } else {
        toast.promise(
          updateEmployeePermission({
            id: permission?.id,
            data: userAssignData,
          }).unwrap(),
          {
            pending: 'Updating Employee Permission...',
            success: {
              render({ data: res }) {
                if (res.statusCode === 200 || res.statusCode === 201) {
                  updateRefreshCounter();
                  handleClose();
                }
                return 'Permission Updated Successfully';
              },
            },
            error: {
              render({ data: error }) {
                console.log(error);
                return 'Error on updating permission';
              },
            },
          },
        );
      }
    },
  });

  useEffect(() => {
    if (type === 'edit' && permission) {
      formik.setFieldValue('role', permission?.role);
      formik.setFieldValue('assignedShopId', permission?.AssignedShop?.id);
      formik.setFieldValue('warehouseId', permission?.Warehouse?.id);
      formik.setFieldValue(
        'type',
        permission?.AssignedShop?.id ? 'SHOP' : 'WAREHOUSE',
      );
    }
  }, [type, permission]);

  return (
    <div className="min-w-[400px] rounded-xl bg-white p-4">
      <ModalTitle text={`Assign User To ${type}`} handleClose={handleClose} />
      <form onSubmit={formik.handleSubmit} className="mt-4 flex flex-col gap-4">
        <CustomDropdown
          placeholder="Select Location"
          name="type"
          label="Permission Location"
          formik={formik}
          options={[
            { value: 'WAREHOUSE', label: 'Warehouse' },
            { value: 'SHOP', label: 'Shop' },
          ]}
        />
        {formik.values.type === 'SHOP' ? (
          <CustomDropdown
            placeholder="Select Shop"
            name="assignedShopId"
            label="Shop"
            formik={formik}
            options={
              !isShopsLoading
                ? shopData?.data?.map((singleShop: SingleShopDetails) => {
                    return {
                      value: singleShop?.id,
                      label: singleShop?.name,
                    };
                  })
                : []
            }
          />
        ) : (
          <CustomDropdown
            placeholder="Select Warehouse"
            name="warehouseId"
            label="Warehouse"
            formik={formik}
            options={
              !isWarehousesLoading
                ? warehouseData?.data?.map(
                    (singleEmployee: SingleShopDetails) => {
                      return {
                        value: singleEmployee?.id,
                        label: singleEmployee?.name,
                      };
                    },
                  )
                : []
            }
          />
        )}
        <CustomDropdown
          placeholder="Select Role"
          name="role"
          label="Role"
          formik={formik}
          options={
            formik.values.type === 'SHOP'
              ? [
                  // { value: 'ADMIN', label: 'ADMIN' },
                  { value: 'MANAGER', label: 'MANAGER' },
                  // { value: 'ACCOUNTANT', label: 'ACCOUNTANT' },
                  { value: 'MODERATOR', label: 'MODERATOR' },
                  { value: 'SHOP_BILLER', label: 'BILLER' },
                  { value: 'AUDITOR', label: 'AUDIT OFFICER' },
                ]
              : [
                  // { value: 'ADMIN', label: 'ADMIN' },
                  { value: 'MANAGER', label: 'MANAGER' },
                  { value: 'STOCK_CLERK', label: 'STOCK MANAGER' },
                  // { value: 'ACCOUNTANT', label: 'ACCOUNTANT' },
                ]
          }
        />
        <div className="mt-2">
          <FilledSubmitButton
            text="Submit"
            isLoading={isSellerUpdating || isPermissionUpdating}
          />
        </div>
      </form>
    </div>
  );
}

export default AddOrEditEmployeePermissionModal;
