import { useState } from 'react';
import { useParams } from 'react-router-dom';

import CustomWebsiteApiDocumentation from '@/components/reusable/ApiDocumentationPageComponents/CustomWebsiteApiDocumentation';
import ShopifyDocumentation from '@/components/reusable/ApiDocumentationPageComponents/ShopifyDocumentation';
import WoocommerceDocumentation from '@/components/reusable/ApiDocumentationPageComponents/WoocommerceDocumentation';

function ShopApiDocumentationPage() {
  const { shopId } = useParams();
  const [selectedDocumentation, setSelectedDocumentation] = useState('custom');

  const buttons = [
    { label: 'Custom Website', value: 'custom' },
    { label: 'Shopify Website', value: 'shopify' },
    { label: 'Woocommerce Website', value: 'woocommerce' },
  ];

  return (
    <div className="grid grid-cols-12 gap-2 bg-gray-100 px-2 py-2">
      {/* Sidebar */}
      <div className="col-span-2">
        {buttons.map((btn) => (
          <button
            key={btn.value}
            type="button"
            onClick={() => setSelectedDocumentation(btn.value)}
            className={`mb-2 w-full rounded-lg px-4 py-2 text-left focus:outline-none focus:ring-2 focus:ring-opacity-50 ${
              selectedDocumentation === btn.value
                ? 'bg-primary text-white'
                : 'bg-white hover:bg-blue-600 hover:text-white focus:ring-blue-500'
            }`}
          >
            {btn.label}
          </button>
        ))}
      </div>

      {/* Content Area */}
      <div className="col-span-10 h-full overflow-auto rounded-lg bg-white p-4 shadow">
        {selectedDocumentation === 'custom' && (
          <CustomWebsiteApiDocumentation apiKey={shopId ?? ''} />
        )}
        {selectedDocumentation === 'shopify' && <ShopifyDocumentation />}
        {selectedDocumentation === 'woocommerce' && (
          <WoocommerceDocumentation />
        )}
      </div>
    </div>
  );
}

export default ShopApiDocumentationPage;
