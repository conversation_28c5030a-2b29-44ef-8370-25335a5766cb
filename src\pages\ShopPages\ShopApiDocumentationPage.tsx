function ShopApiDocumentationPage() {
  return (
    <div className="min-h-screen bg-gray-100 px-6 py-10">
      <div className="mx-auto max-w-4xl rounded-lg bg-white p-8 shadow-lg">
        <h1 className="mb-6 text-3xl font-bold text-gray-800">
          Shop API Documentation
        </h1>
        <p className="mb-8 text-gray-600">
          This documentation will guide you on how to integrate your website API
          with our POS system. Follow the steps below to get started.
        </p>

        {/* Section: API Key */}
        <div className="mb-8">
          <h2 className="mb-4 text-xl font-semibold text-gray-700">
            1. Generate Your API Key
          </h2>
          <p className="mb-4 text-gray-600">
            To connect your website with our POS system, you need an API key.
            Follow these steps to generate your API key:
          </p>
          <ul className="list-inside list-disc text-gray-600">
            <li>Log in to your account on the POS system.</li>
            <li>
              Navigate to the <span className="font-medium">API Settings</span>{' '}
              page.
            </li>
            <li>
              Click on <span className="font-medium">Generate API Key</span>.
            </li>
            <li>Copy the generated API key and keep it secure.</li>
          </ul>
        </div>

        {/* Section: API Endpoints */}
        <div className="mb-8">
          <h2 className="mb-4 text-xl font-semibold text-gray-700">
            2. API Endpoints
          </h2>
          <p className="mb-4 text-gray-600">
            Below are the available API endpoints for integration:
          </p>
          <table className="w-full border-collapse border border-gray-300">
            <thead>
              <tr className="bg-gray-100">
                <th className="border border-gray-300 px-4 py-2 text-left">
                  Endpoint
                </th>
                <th className="border border-gray-300 px-4 py-2 text-left">
                  Method
                </th>
                <th className="border border-gray-300 px-4 py-2 text-left">
                  Description
                </th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="border border-gray-300 px-4 py-2">
                  /api/products
                </td>
                <td className="border border-gray-300 px-4 py-2">GET</td>
                <td className="border border-gray-300 px-4 py-2">
                  Fetch all products from the POS system.
                </td>
              </tr>
              <tr>
                <td className="border border-gray-300 px-4 py-2">
                  /api/orders
                </td>
                <td className="border border-gray-300 px-4 py-2">POST</td>
                <td className="border border-gray-300 px-4 py-2">
                  Create a new order in the POS system.
                </td>
              </tr>
              <tr>
                <td className="border border-gray-300 px-4 py-2">
                  /api/inventory
                </td>
                <td className="border border-gray-300 px-4 py-2">GET</td>
                <td className="border border-gray-300 px-4 py-2">
                  Retrieve inventory details.
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        {/* Section: Authentication */}
        <div className="mb-8">
          <h2 className="mb-4 text-xl font-semibold text-gray-700">
            3. Authentication
          </h2>
          <p className="mb-4 text-gray-600">
            All API requests must include the API key in the request headers for
            authentication:
          </p>
          <pre className="overflow-auto rounded-lg bg-gray-100 p-4 text-sm text-gray-800">
            Authorization: Bearer YOUR_API_KEY
          </pre>
        </div>

        {/* Section: Example Request */}
        <div className="mb-8">
          <h2 className="mb-4 text-xl font-semibold text-gray-700">
            4. Example Request
          </h2>
          <p className="mb-4 text-gray-600">
            Here is an example of how to fetch products using the API:
          </p>
          <pre className="overflow-auto rounded-lg bg-gray-100 p-4 text-sm text-gray-800">
            {`curl -X GET https://your-pos-system.com/api/products \\
  -H "Authorization: Bearer YOUR_API_KEY"`}
          </pre>
        </div>

        {/* Section: Support */}
        <div>
          <h2 className="mb-4 text-xl font-semibold text-gray-700">
            5. Support
          </h2>
          <p className="text-gray-600">
            If you encounter any issues or have questions, please contact our
            support team at{' '}
            <a
              href="mailto:<EMAIL>"
              className="text-blue-500 underline"
            >
              <EMAIL>
            </a>
            .
          </p>
        </div>
      </div>
    </div>
  );
}

export default ShopApiDocumentationPage;
