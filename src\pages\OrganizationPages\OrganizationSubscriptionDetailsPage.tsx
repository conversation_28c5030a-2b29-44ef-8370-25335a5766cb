import { Calendar, CreditCard, Package, Users } from 'lucide-react';
import { useState } from 'react';
import { useParams } from 'react-router-dom';

import SubscriptionPayNowModal from '@/components/OrganizationsComponents/OrganizationSubscriptionsPageComponents/SubscriptionPayNowModal';
import Modal from '@/components/reusable/Modal/Modal';
import OrderStatusViewer from '@/components/reusable/OrdersPagesReusableComponents/OrderStatusViewer';
import SpinnerLoader from '@/components/reusable/SpinnerLoader/SpinnerLoader';
import { useGetSingleOrgSubscriptionDetailsQuery } from '@/redux/api/organizationApis/orgSubscriptionsApis';

function OrganizationSubscriptionDetailsPage() {
  const { subscriptionId } = useParams();
  const [isPayNowModalOpen, setIsPayNowModalOpen] = useState<boolean>(false);
  const { data, isLoading } = useGetSingleOrgSubscriptionDetailsQuery(
    subscriptionId ?? '',
  );

  if (isLoading) {
    return (
      <div className="flex h-[calc(100vh-60px)] items-center justify-center">
        <SpinnerLoader />
      </div>
    );
  }

  const subscription = data?.data;

  return (
    <div className="pt-4">
      {/* Header Section */}
      <div className="mb-4 rounded-lg bg-white px-4 py-2">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-800">
            Subscription Details
          </h1>
          {!subscription?.isPaid ? (
            <button
              className="cursor-pointer rounded-lg bg-[#28243D] px-8 py-2 font-semibold text-white hover:bg-[#28243dd4] disabled:bg-slate-400"
              type="button"
              onClick={() => setIsPayNowModalOpen(true)}
            >
              Pay Now
            </button>
          ) : (
            <div className="w-[150px] text-center">
              <OrderStatusViewer status="PAID" />
            </div>
          )}
        </div>
        <p className="mt-2 text-sm text-gray-600">
          View and manage your organizations subscription information
        </p>
      </div>

      {/* Main Content */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {/* Plan Details Card */}
        <div className="rounded-lg bg-white p-6 shadow-md">
          <div className="mb-4 flex items-center">
            <Package className="mr-3 h-6 w-6 text-blue-500" />
            <h2 className="text-lg font-semibold text-gray-800">
              Plan Details
            </h2>
          </div>
          <div className="space-y-3">
            <div>
              <p className="text-sm text-gray-500">Plan Name</p>
              <p className="font-medium text-gray-800">
                {subscription?.Plan.name || 'N/A'}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Plan Type</p>
              <p className="font-medium text-gray-800">
                {subscription?.Plan.name || 'N/A'}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Status</p>
              <span
                className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                  subscription?.isActive
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}
              >
                {subscription?.isActive ? 'Active' : 'Inactive'}
              </span>
            </div>
          </div>
        </div>

        {/* Billing Information Card */}
        <div className="rounded-lg bg-white p-6 shadow-md">
          <div className="mb-4 flex items-center">
            <CreditCard className="mr-3 h-6 w-6 text-blue-500" />
            <h2 className="text-lg font-semibold text-gray-800">
              Billing Information
            </h2>
          </div>
          <div className="space-y-3">
            <div>
              <p className="text-sm text-gray-500">Amount</p>
              <p className="font-medium text-gray-800">
                ৳{subscription?.Plan?.priceMonthly?.toFixed(2) || '0.00'}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Billing Cycle</p>
              <p className="font-medium text-gray-800">
                {subscription?.billingCycle || 'N/A'}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Payment Method</p>
              <p className="font-medium text-gray-800">BKASH</p>
            </div>
          </div>
        </div>

        {/* Usage Information Card */}
        <div className="rounded-lg bg-white p-6 shadow-md">
          <div className="mb-4 flex items-center">
            <Users className="mr-3 h-6 w-6 text-blue-500" />
            <h2 className="text-lg font-semibold text-gray-800">
              Usage Information
            </h2>
          </div>
          <div className="space-y-3">
            <div>
              <p className="text-sm text-gray-500">Users Limit</p>
              <p className="font-medium text-gray-800">
                {/* {subscription?.userLimit || 'N/A'} */}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Active Users</p>
              <p className="font-medium text-gray-800">
                {/* {subscription?.activeUsers || '0'} */}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Storage Used</p>
              <p className="font-medium text-gray-800">
                {/* {subscription?.storageUsed || '0 GB'} */}
              </p>
            </div>
          </div>
        </div>

        {/* Subscription Period Card */}
        <div className="rounded-lg bg-white p-6 shadow-md">
          <div className="mb-4 flex items-center">
            <Calendar className="mr-3 h-6 w-6 text-blue-500" />
            <h2 className="text-lg font-semibold text-gray-800">
              Subscription Period
            </h2>
          </div>
          <div className="space-y-3">
            <div>
              <p className="text-sm text-gray-500">Start Date</p>
              <p className="font-medium text-gray-800">
                {subscription?.startDate
                  ? new Date(subscription.startDate).toLocaleDateString()
                  : 'N/A'}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">End Date</p>
              <p className="font-medium text-gray-800">
                {subscription?.endDate
                  ? new Date(subscription.endDate).toLocaleDateString()
                  : 'N/A'}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Next Billing Date</p>
              <p className="font-medium text-gray-800">
                {subscription?.nextBillingDate
                  ? new Date(subscription.nextBillingDate).toLocaleDateString()
                  : 'N/A'}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Additional Information Section */}
      <div className="mt-8 rounded-lg bg-white p-6 shadow-md">
        <h2 className="mb-4 text-lg font-semibold text-gray-800">
          Additional Information
        </h2>
        <div className="grid gap-6 md:grid-cols-2">
          <div>
            <p className="text-sm text-gray-500">Description</p>
            <p className="mt-1 text-gray-800">
              {/* {subscription?.description || 'No description available'} */}
            </p>
          </div>
          {/* <div>
            <p className="text-sm text-gray-500">Features</p>
            <ul className="mt-1 list-inside list-disc text-gray-800">
              {subscription?.features?.map((feature: string, index: number) => (
                <li key={index}>{feature}</li>
              )) || <li>No features listed</li>}
            </ul>
          </div> */}
        </div>
      </div>
      <Modal showModal={isPayNowModalOpen} setShowModal={setIsPayNowModalOpen}>
        <SubscriptionPayNowModal
          handleClose={() => setIsPayNowModalOpen(false)}
          paymentDetails={data?.data}
        />
      </Modal>
    </div>
  );
}

export default OrganizationSubscriptionDetailsPage;
