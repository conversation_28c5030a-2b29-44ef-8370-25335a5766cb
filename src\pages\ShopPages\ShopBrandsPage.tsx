import { useParams } from 'react-router-dom';

import ShopBrandsPageOverview from '@/components/ShopComponents/ShopBrandsPageComponents/ShopBrandsPageOverview';
import { useAppSelector } from '@/redux/hooks';

function ShopBrandsPage() {
  const { shopId } = useParams();
  const { warehouseId } = useAppSelector((state) => state.shopDetails);
  return (
    <div>
      <ShopBrandsPageOverview shopId={shopId ?? ''} warehouseId={warehouseId} />
    </div>
  );
}

export default ShopBrandsPage;
