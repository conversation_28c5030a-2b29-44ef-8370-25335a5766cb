import { BarcodeButtonv2 } from '@/components/reusable/Buttons/CommonButtons';
import FilterButton from '@/components/reusable/Buttons/FilterButton';
import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import SearchInput from '@/components/reusable/Inputs/SearchInput';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import { useGetSingleEntryDetailsQuery } from '@/redux/api/warehouseApis/purchaseApis';
import { ROUTES } from '@/Routes';
import { SingleProductOnEntryDetails } from '@/types/warehouseTypes/purchaseTypes';

interface Props {
  entryId: string;
  warehouseId: string;
}

function StockEntryDetailsPageOverview({ entryId, warehouseId }: Props) {
  const { data, isLoading } = useGetSingleEntryDetailsQuery({
    id: entryId,
    warehouseId,
  });
  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-1">
        <div className="flex items-center gap-x-2">
          <div className="search-title-and-btn flex items-center gap-x-3">
            <p className="whitespace-nowrap">Search Filters</p>
            <div className="relative">
              <div className="block xl:hidden">
                <FilterButton handleClick={() => console.log('higbig')} />
              </div>
              <div className="block xl:hidden">
                {/* <ProductPageFilterModal /> */}
              </div>
            </div>
          </div>
          <div className="hidden xl:block">
            <div className="flex items-center gap-x-2">
              <SearchInput
                placeholder="Search by Brand"
                handleSubmit={(value: string) => console.log(value)}
              />
              <SearchInput
                placeholder="Search by Category"
                handleSubmit={(value: string) => console.log(value)}
              />
              <SearchInput
                placeholder="Search by Sub Category"
                handleSubmit={(value: string) => console.log(value)}
              />
            </div>
          </div>
        </div>
      </div>
      {!isLoading ? (
        <div>
          <div className="tableTop w-full">
            <p>Purchase List</p>
            {/* <p>Total : {data?.pagination?.total}</p> */}
          </div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {data?.data?.Purchase?.length ? (
              <div className="full-table-box h-custom">
                <table className="full-table">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="tableHead">No</th>
                      <th className="tableHead">Invoice No</th>
                      <th className="tableHead">Product Id</th>
                      <th className="tableHead table-col-width">
                        Product Name
                      </th>
                      <th className="tableHead">Supplier</th>
                      <th className="tableHead">QTY</th>
                      <th className="tableHead">TP</th>
                      <th className="tableHead">MRP</th>
                      <th className="tableHead">Created At</th>
                      <th className="tableHead">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y bg-slate-200">
                    {data?.data?.Purchase?.map(
                      (
                        singleProduct: SingleProductOnEntryDetails,
                        index: number,
                      ) => (
                        <tr key={singleProduct?.id}>
                          <td className="tableData">{index + 1}</td>
                          <td className="tableData">
                            {singleProduct?.serialNo ?? 0}
                          </td>
                          <td className="tableData">
                            {singleProduct?.serialNo ?? 0}
                          </td>
                          <td className="tableData table-col-width">
                            {singleProduct?.Product?.name}
                          </td>
                          <td className="tableData">
                            {data?.data?.Supplier?.User?.name ?? ''}
                          </td>
                          <td className="tableData">
                            {singleProduct?.quantity}
                          </td>
                          <td className="tableData">
                            {singleProduct?.purchasePrice}
                          </td>
                          <td className="tableData">
                            {singleProduct?.retailPrice}
                          </td>

                          <td className="tableData">
                            <DateAndTimeViewer
                              date={singleProduct?.createdAt}
                            />
                          </td>
                          <td className="tableData">
                            <button type="button">
                              {/* <Link
                                to={ROUTES.STOCK.SINGLE_PRODUCT_BARCODE(
                                  singleProduct?.id,
                                )}
                                target="_blank"
                              >
                                <Barcode />
                              </Link> */}
                              <BarcodeButtonv2
                                handleClick={() => {
                                  const url =
                                    ROUTES.STOCK.SINGLE_PRODUCT_BARCODE(
                                      singleProduct?.id,
                                      warehouseId,
                                    );
                                  window.open(url, '_blank');
                                }}
                              />
                            </button>
                          </td>
                        </tr>
                      ),
                    )}
                  </tbody>
                </table>
              </div>
            ) : (
              <div>
                <h2>No product found</h2>
              </div>
            )}
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={10} tableRow={6} />
      )}
    </div>
  );
}

export default StockEntryDetailsPageOverview;
