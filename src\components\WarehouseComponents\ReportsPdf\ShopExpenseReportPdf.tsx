import { Page, Text, View } from '@react-pdf/renderer';

import PrintedTime from './PrintedTime';
import { reportPdfStyles } from './ReportPdfStyles';
import ShopPdfHeader from './ShopPdfHeader';
import SoftwareMarketingOnPdf from './SoftwareMarketingOnPdf';

import { SingleShopDetails } from '@/types/shopTypes';
import { ShopExpenseDetails } from '@/types/shopTypes/shopExpensesTypes';

interface Props {
  expenses: ShopExpenseDetails[];
  shopDetails?: SingleShopDetails;
}

// need to fix
function ShopExpenseReportPdf({ expenses, shopDetails }: Props) {
  return (
    <Page size="A4" style={reportPdfStyles.page}>
      <View>
        <ShopPdfHeader shopDetails={shopDetails} />
        <PrintedTime />

        <View style={reportPdfStyles.table}>
          <View style={reportPdfStyles.tableRow}>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.shopExpensesReportCol,
              ]}
            >
              No
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.shopExpensesReportCol,
                reportPdfStyles.shopExpensesReportNameCol,
              ]}
            >
              Name
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.shopExpensesReportCol,
              ]}
            >
              Amount
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.tableColLast,
                reportPdfStyles.shopExpensesReportCol,
              ]}
            >
              Created At
            </Text>
          </View>
          {expenses.map((expense, index) => (
            <View
              key={expense.id}
              style={
                index === expenses.length - 1
                  ? [reportPdfStyles.tableRowLast]
                  : [reportPdfStyles.tableRow]
              }
            >
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.shopExpensesReportCol,
                ]}
              >
                {index + 1}
              </Text>
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.shopExpensesReportCol,
                  reportPdfStyles.shopExpensesReportNameCol,
                ]}
              >
                {expense.name}
              </Text>
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.shopExpensesReportCol,
                ]}
              >
                {expense.amount}
              </Text>
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.shopExpensesReportCol,
                  reportPdfStyles.tableColLast,
                ]}
              >
                {expense.createdAt}
              </Text>
            </View>
          ))}
        </View>
        <SoftwareMarketingOnPdf />
      </View>
    </Page>
  );
}

export default ShopExpenseReportPdf;
