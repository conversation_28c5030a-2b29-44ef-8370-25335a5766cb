import { Truck } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'react-toastify';
import Swal from 'sweetalert2';

import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import OrderStatusViewer from '@/components/reusable/OrdersPagesReusableComponents/OrderStatusViewer';
import { useBookBulkOrderOnSteadfastMutation } from '@/redux/api/shopApis/shopOrdersApis';
import { ShopOrderDetails } from '@/types/shopTypes/shopOrderTypes';
import { SingleCourierDetails } from '@/types/warehouseTypes/settingsTypes';

interface Props {
  orders?: ShopOrderDetails[];
  total?: number;
  shopId: string;
  courierDetails?: SingleCourierDetails;
}

function OrderListForSteadfastBulkEntry({
  orders,
  total,
  shopId,
  courierDetails,
}: Props) {
  const [selectedOrderList, setSelectedOrderList] =
    useState<ShopOrderDetails[]>();
  const [bookBulkOrderOnSteadfast, { isLoading: isBulkOrderBooking }] =
    useBookBulkOrderOnSteadfastMutation();

  const handleSelectOrder = (order: ShopOrderDetails) => {
    const isExist = selectedOrderList?.some(
      (sin: ShopOrderDetails) => sin.id === order.id,
    );
    if (!isExist) {
      setSelectedOrderList([...(selectedOrderList ?? []), order]);
    } else {
      setSelectedOrderList(
        selectedOrderList?.filter(
          (sin: ShopOrderDetails) => sin.id !== order.id,
        ),
      );
    }
  };

  const handleSelectOrUnselectAll = () => {
    if (selectedOrderList?.length === orders?.length) {
      setSelectedOrderList([]);
    } else {
      setSelectedOrderList(orders);
    }
  };

  const handleSubmitBooking = async () => {
    if (!selectedOrderList || selectedOrderList.length === 0) {
      toast.error('No orders selected.');
      return;
    }

    const bookData = selectedOrderList.map((order: ShopOrderDetails) => {
      return {
        invoice: order.serialNo,
        recipient_name: order.customerName ?? order.Customer?.name,
        recipient_address: order.address ?? order.Customer?.address,
        recipient_phone:
          order.customerMobileNumber ?? order.Customer?.mobileNumber,
        cod_amount: order.totalDue,
        note: order.orderNotes[0]?.notes ?? order.note ?? '',
      };
    });
    Swal.fire({
      title: `Are you sure? you want to entry ${bookData?.length} orders to Steadfast`,
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: `Yes, Book ${bookData?.length} order`,
    }).then((result) => {
      if (result.isConfirmed) {
        toast.promise(
          bookBulkOrderOnSteadfast({
            shopId,
            courierId: courierDetails?.id,
            orders: bookData,
          }).unwrap(),
          {
            pending: 'Bulk Order Booking on Courier...',
            success: {
              render({ data: res }) {
                if (res?.statusCode === 200 || res?.statusCode === 201) {
                  console.log('success');
                }
                return 'Order booked Successfully';
              },
            },
            error: {
              render({ data: error }) {
                console.log(error);
                return 'Error on booking order';
              },
            },
          },
        );
      }
    });
  };

  return (
    <div>
      <div className="tableTop w-full">
        <p>Pending Order List</p>
        <div className="flex items-center gap-4">
          {selectedOrderList?.length ? (
            <button
              type="button"
              onClick={() => handleSubmitBooking()}
              className="flex items-center gap-1 rounded border-2 border-slate-200 px-2 py-1 text-xs font-bold text-white"
              disabled={isBulkOrderBooking}
            >
              <Truck size={16} />
              <span>Entry</span>
              <span>{selectedOrderList?.length}</span>
            </button>
          ) : (
            ''
          )}
          <p>Total : {total}</p>
        </div>
      </div>
      <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
        {orders?.length ? (
          <div className="full-table-box h-orderTable">
            <table className="full-table">
              <thead className="bg-gray-100">
                <tr>
                  <th className="tableHead">
                    <input
                      type="checkbox"
                      onClick={handleSelectOrUnselectAll}
                      onChange={() => {}}
                      checked={selectedOrderList?.length === orders?.length}
                    />
                  </th>
                  <th className="tableHead">#</th>
                  <th className="tableHead">Order No</th>

                  <th className="tableHead table-col-width">Name</th>
                  <th className="tableHead">Phone</th>
                  <th className="tableHead table-col-width">Address</th>
                  <th className="tableHead">Total</th>
                  <th className="tableHead">Paid</th>
                  <th className="tableHead">Due</th>
                  <th className="tableHead">Biller</th>
                  <th className="tableHead">Status</th>
                  <th className="tableHead">Created At</th>
                </tr>
              </thead>
              <tbody className="divide-y bg-slate-200">
                {orders?.map((order: ShopOrderDetails, index: number) => {
                  return (
                    <tr key={order?.id}>
                      <td className="tableData">
                        <input
                          type="checkbox"
                          onClick={() => handleSelectOrder(order)}
                          onChange={() => {}}
                          checked={selectedOrderList?.some(
                            (ord: ShopOrderDetails) => ord.id === order.id,
                          )}
                        />
                      </td>
                      <td className="tableData">{index + 1}</td>
                      <td className="tableData">{order?.serialNo}</td>
                      <td className="tableData table-col-width">
                        {order?.customerName ?? order?.Customer.name}
                      </td>
                      <td className="tableData">
                        {order?.customerMobileNumber ??
                          order?.Customer.mobileNumber}
                      </td>
                      <td className="tableData table-col-width">
                        {order?.address ?? order?.Customer.address}
                      </td>
                      <td className="tableData">
                        {Number(order?.grandTotal) +
                          Number(order?.deliveryCharge)}
                      </td>
                      <td className="tableData">{order?.totalPaid}</td>
                      <td className="tableData">{order?.totalDue}</td>
                      <td className="tableData">
                        {order?.CreatedBy?.name ?? '--'}
                      </td>
                      <td className="tableData">
                        <div>
                          <OrderStatusViewer status={order.orderStatus} />
                        </div>
                      </td>
                      <td className="tableData">
                        <DateAndTimeViewer date={order?.createdAt} />
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        ) : (
          <NoResultFound pageType="order" />
        )}
      </div>
    </div>
  );
}

export default OrderListForSteadfastBulkEntry;
