import { Page, Text, View } from '@react-pdf/renderer';

import PrintedTime from './PrintedTime';
import { reportPdfStyles } from './ReportPdfStyles';
import SoftwareMarketingOnPdf from './SoftwareMarketingOnPdf';

import { Warehouse } from '@/types/shopTypes/shopExpensesTypes';
import { SingleStockEntryDetails } from '@/types/warehouseTypes/reportTypes';

const products = [
  {
    id: 1,
    purchaseDate: 'Sep 17, 2024, 06:00 AM',
    supplierName: 'Hasib',
    invoice: 222,
    total: 23000,
    paid: 2300,
    due: 20700,
  },
];

interface Props {
  entryList?: SingleStockEntryDetails[];
  warehouse?: Warehouse;
}

function StockEntryReportPdf({ entryList, warehouse }: Props) {
  return (
    <Page size="A4" style={reportPdfStyles.page}>
      <View>
        <View style={reportPdfStyles.header}>
          <Text style={reportPdfStyles.userName}>{warehouse?.name}</Text>
          {/* <Text style={reportPdfStyles.address}>
            Tangail, Dhaka, Bangladesh
          </Text>
          <Text style={reportPdfStyles.phone}>01739719796</Text> */}
        </View>
        <PrintedTime />

        <View style={reportPdfStyles.table}>
          <View style={reportPdfStyles.tableRow}>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.stockEntryCol,
              ]}
            >
              No
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.stockEntryCol,
                reportPdfStyles.purchaseDateTableCol,
              ]}
            >
              Purchase Date
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.stockEntryCol,
              ]}
            >
              Supplier Name
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.stockEntryCol,
              ]}
            >
              Invoice
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.stockEntryCol,
              ]}
            >
              Total
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.stockEntryCol,
              ]}
            >
              Paid
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.tableColLast,
                reportPdfStyles.stockEntryCol,
              ]}
            >
              Due
            </Text>
          </View>
          {entryList?.map((product: SingleStockEntryDetails, index) => (
            <View
              key={product.id}
              style={
                index === products.length - 1
                  ? [reportPdfStyles.tableRowLast]
                  : [reportPdfStyles.tableRow]
              }
            >
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.stockEntryCol,
                ]}
              >
                {index + 1}
              </Text>
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.stockEntryCol,
                  reportPdfStyles.purchaseDateTableCol,
                ]}
              >
                {new Intl.DateTimeFormat('en-US', {
                  day: '2-digit',
                  month: 'short',
                  year: 'numeric',
                }).format(new Date(product.purchaseDate))}
              </Text>
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.stockEntryCol,
                ]}
              >
                {product.Supplier?.User?.name}
              </Text>
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.stockEntryCol,
                ]}
              >
                {product.supplierInvoiceNo}
              </Text>
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.stockEntryCol,
                ]}
              >
                {product.totalPrice}
              </Text>
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.stockEntryCol,
                ]}
              >
                {product.totalPaid}
              </Text>
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.stockEntryCol,
                  reportPdfStyles.tableColLast,
                ]}
              >
                {product.totalDue}
              </Text>
            </View>
          ))}
        </View>

        <SoftwareMarketingOnPdf />
      </View>
    </Page>
  );
}

export default StockEntryReportPdf;
