interface Option {
  value: number | string;
  label: string;
}
interface Props {
  label: string;
  name: string;
  placeholder: string;
  formik?: any;
  options?: Option[];
}

function CustomDropdown({ name, label, options, formik, placeholder }: Props) {
  return (
    <div className="group relative z-0 mt-[-20px] w-full">
      <label
        htmlFor={name}
        className="relative left-3 top-2.5 w-auto bg-white px-1 font-mono text-[12px] font-bold text-gray-900 group-focus-within:text-red-600 dark:text-gray-300"
      >
        {label}
      </label>
      <select
        name={name}
        id={name}
        className={`block h-10 w-full rounded-lg border ${
          formik.touched[name] && formik.errors[name]
            ? 'border-red-500'
            : 'border-gray-300'
        } bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500`}
        onChange={formik.handleChange}
        onBlur={formik.handleBlur}
        value={formik.values[name] || ''}
      >
        <option value="" label={placeholder || 'Select Option'} />
        {options?.map((option: Option) => (
          <option key={option.value} value={option.value} label={option.label}>
            {option.label}
          </option>
        ))}
      </select>
      {formik.touched[name] && formik.errors[name] ? (
        <p className="m-0 text-xs text-red-400">{formik.errors[name]}</p>
      ) : null}
    </div>
  );
}

export default CustomDropdown;
