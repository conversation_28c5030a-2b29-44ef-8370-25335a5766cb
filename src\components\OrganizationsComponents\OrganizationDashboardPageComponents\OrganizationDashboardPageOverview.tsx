import Cookies from 'js-cookie';

import SpinnerLoader from '@/components/reusable/SpinnerLoader/SpinnerLoader';
import DashboardPageOverview from '@/components/WarehouseComponents/DashboardPageComponents/DashboardPageOverview';
import { useGetOrgWarehousesQuery } from '@/redux/api/organizationApis/orgShopAndWarehouseApis';

function OrganizationDashboardPageOverview() {
  const organizationId = Cookies.get('organizationId');

  const { data: warehouseData, isLoading: isWarehouseDataLoading } =
    useGetOrgWarehousesQuery(
      {
        organizationId,
      },
      {
        skip: !organizationId,
      },
    );
  return (
    <div className="pb-4">
      {!isWarehouseDataLoading ? (
        <DashboardPageOverview
          warehouseId={warehouseData?.data[0]?.id ?? ''}
          viewFrom="org"
        />
      ) : (
        <SpinnerLoader />
      )}
    </div>
  );
}

export default OrganizationDashboardPageOverview;
