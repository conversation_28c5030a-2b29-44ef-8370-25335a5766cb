import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface WarehouseDetailsInRedux {
  id: string;
  name: string;
  warehouseId: string;
  imgUrl?: string | null;
  mobileNumber?: string | null;
  address?: string | null;
  websiteUrl?: string | null;
  fbUrl?: string | null;
  nickName?: string | null;
}

const initialWarehouseStates: WarehouseDetailsInRedux = {
  id: '',
  name: '',
  imgUrl: '',
  mobileNumber: '',
  address: '',
  websiteUrl: '',
  fbUrl: '',
  warehouseId: '',
  nickName: '',
};

const warehouseSlice = createSlice({
  name: 'warehouseDetails',
  initialState: initialWarehouseStates,
  reducers: {
    setWarehouseDetails: (
      state,
      action: PayloadAction<WarehouseDetailsInRedux>,
    ) => {
      state.id = action.payload.id;
      state.name = action.payload.name;
      state.imgUrl = action.payload.imgUrl;
      state.mobileNumber = action.payload.mobileNumber;
      state.address = action.payload.address;
      state.websiteUrl = action.payload.websiteUrl;
      state.fbUrl = action.payload.fbUrl;
      state.warehouseId = action.payload.warehouseId;
      state.nickName = action.payload.nickName;
    },

    reset: () => initialWarehouseStates,
  },
});

// Action creators are generated for each case reducer function
export const { reset, setWarehouseDetails } = warehouseSlice.actions;

export default warehouseSlice.reducer;
