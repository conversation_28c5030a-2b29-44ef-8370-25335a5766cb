import { useFormik } from 'formik';
import { useEffect } from 'react';
import { toast } from 'react-toastify';
import * as Yup from 'yup';

import FilledSubmitButton from '@/components/reusable/Buttons/FilledSubmitButton';
import CustomInputField from '@/components/reusable/CustomInputField/CustomInputField';
import DateViewer from '@/components/reusable/DateAndTimeViewer/DateViewer';
import ModalTitle from '@/components/reusable/Modal/ModalTitle';
import {
  SingleSubscription,
  useInitiateSubscriptionPaymentMutation,
} from '@/redux/api/organizationApis/orgSubscriptionsApis';

interface Props {
  paymentDetails?: SingleSubscription;
  handleClose: () => void;
}

const formikInitialValues = {
  paymentMethod: 'BKASH',
  subscriptionId: '',
  mobileNumber: '',
  amount: '',
};

const validation = Yup.object({
  mobileNumber: Yup.string()
    .required('Bkash Number is required')
    .min(11)
    .max(11),
});

function SubscriptionPayNowModal({ handleClose, paymentDetails }: Props) {
  const [initiateSubscriptionPayment, { isLoading }] =
    useInitiateSubscriptionPaymentMutation();
  const formik = useFormik({
    initialValues: formikInitialValues,
    validationSchema: validation,

    onSubmit: async (values) => {
      try {
        toast.promise(initiateSubscriptionPayment(values).unwrap(), {
          pending: 'Initiating Payment...',
          success: {
            render({ data: res }) {
              if (res.statusCode === 200 || res.statusCode === 201) {
                window.location.href = res.data.redirectUrl;
              }
              return 'Payment Initiated Successfully';
            },
          },
          error: {
            render({ data: error }) {
              console.log(error);
              return 'Error on payment initiation';
            },
          },
        });
      } catch (error) {
        console.log(error);
      }
    },
  });

  useEffect(() => {
    if (paymentDetails) {
      formik.setFieldValue('subscriptionId', paymentDetails?.id);
      formik.setFieldValue('amount', paymentDetails?.Plan.priceMonthly);
    }
  }, [paymentDetails]);

  return (
    <div className="min-w-[450px] rounded-xl bg-white p-6 shadow-lg">
      <ModalTitle text="Pay Subscription Bill" handleClose={handleClose} />
      <div className="mt-4">
        {/* Payment Details Section */}
        <div className="mb-4 rounded-lg bg-gray-100 p-4">
          <h3 className="text-lg font-semibold text-gray-700">
            Payment Details
          </h3>
          <div className="mt-2 text-sm text-gray-600">
            <p className="flex items-center gap-2">
              <span className="font-medium">Billing Cycle:</span>{' '}
              <DateViewer date={paymentDetails?.startDate ?? ''} /> -{' '}
              <DateViewer date={paymentDetails?.endDate ?? ''} />
            </p>
            <p>
              <span className="font-medium">Amount:</span> ৳
              {paymentDetails?.Plan.priceMonthly}
            </p>
            <p>
              <span className="font-medium">Payment Method:</span> Bkash
            </p>
          </div>
        </div>

        {/* Form Section */}
        <form onSubmit={formik.handleSubmit} className="flex flex-col gap-4">
          <CustomInputField
            type="text"
            placeholder="Enter your Bkash Number"
            name="mobileNumber"
            label="Bkash Number"
            formik={formik}
          />
          <div className="mt-2">
            <FilledSubmitButton
              text="Submit"
              isLoading={isLoading}
              isDisabled={!formik.values.mobileNumber}
            />
          </div>
        </form>
      </div>
    </div>
  );
}

export default SubscriptionPayNowModal;
