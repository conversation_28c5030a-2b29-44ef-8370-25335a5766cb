import Cookies from 'js-cookie';
import {
  Activity,
  AudioWaveform,
  Cable,
  ChevronDown,
  CircleDollarSign,
  CircleParking,
  CirclePlus,
  FileClock,
  FileText,
  FolderSync,
  GitGraph,
  HandCoins,
  Handshake,
  History,
  LayoutDashboard,
  ListChecks,
  ListTodo,
  MapPin,
  Move3D,
  NotepadText,
  PiggyBank,
  ScanBarcode,
  ScrollText,
  ShoppingBasket,
  TicketPlus,
  Trash2,
  Undo2,
  Users,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';

import { useGetUserProfileQuery } from '@/redux/api/userApi';
import { useAppDispatch } from '@/redux/hooks';
import { setUserDetails } from '@/redux/slice/userSlice';
import { ROUTES } from '@/Routes';
import { SidebarItem, SingleSubmenu } from '@/types/sidebarTypes';
import { EmployeePermission } from '@/types/userTypes';

interface Props {
  isSidebarOpen: boolean;
}

function WarehouseSidebar({ isSidebarOpen }: Props) {
  const navigate = useNavigate();
  const [expanded, setExpanded] = useState<string>('');
  const [selectedPath, setSelectedPath] = useState('');
  const [employeeRole, setEmployeeRole] = useState<string>('');
  const location = useLocation();
  const { pathname } = location;
  const warehouseId = pathname.split('/')[2];
  // const pathname2 = useLocation();
  // console.log(pathname2.pathname);

  useEffect(() => {
    setSelectedPath(pathname);
  }, [pathname]);

  const { data: userDetails, isLoading: isUserDetailsLoading } =
    useGetUserProfileQuery({});
  const dispatch = useAppDispatch();
  useEffect(() => {
    if (!isUserDetailsLoading && userDetails?.data) {
      const permission = userDetails?.data?.Employee?.EmployeePermission?.find(
        (single: EmployeePermission) =>
          single?.warehouseId === warehouseId && single?.AssignedShop === null,
      );
      setEmployeeRole(permission?.role ?? '');
      if (permission?.role === 'STOCK_CLERK') {
        setExpanded('Stock');
        navigate(ROUTES.STOCK.LIST(warehouseId));
      }
      dispatch(
        setUserDetails({
          id: userDetails?.data?.id,
          name: userDetails?.data?.name,
          email: userDetails?.data?.email,
          username: userDetails?.data?.username,
          mobileNumber: userDetails?.data?.mobileNumber,
          imgUrl: userDetails?.data?.imgUrl,
          type: userDetails?.data?.type,
          permissionInShop: permission?.role ?? '',
          shopName: 'Warehouse',
          shopNickname: '',
          shopType: '',
        }),
      );
    }
  }, [userDetails, isUserDetailsLoading]);

  // const userDetailsFromState = useAppSelector((state) => state.userDetails);
  // console.log(userDetailsFromState);
  // console.log(employeeRole);

  const link =
    'flex items-center px-4 py-2 text-[12px] lg:text-[14px] xl:text-[16px] hover:bg-gray-700 rounded-r-2xl';
  const selectedLink =
    'flex items-center px-4 py-2 text-[12px] lg:text-[14px] xl:text-[16px] hover:bg-gray-700 bg-gray-400 rounded-r-2xl';

  const subLink = `dropdown-link flex items-center py-2 pr-4 text-[12px] lg:text-[14px] xl:text-[16px] before:mr-2 before:inline-block before:h-[1px] before:w-5 before:bg-gray-400 before:pl-[-10px] before:content-[''] hover:bg-gray-700 rounded-r-2xl`;
  const selectedSubLink = `dropdown-link flex items-center py-2 pr-4 text-[12px] lg:text-[14px] xl:text-[16px] before:mr-2 before:inline-block before:h-[1px] before:w-5 before:bg-gray-400 before:pl-[-10px] before:content-[''] hover:bg-gray-700 bg-gray-700 before:bg-white rounded-r-2xl`;

  const isVisible =
    employeeRole === 'ADMIN' || Cookies.get('type') === 'SUPER_ADMIN';
  const startAndEndDate = `startDate=${new Date().toISOString().split('T')[0]}&endDate=${new Date().toISOString().split('T')[0]}`;

  const warehouseSidebarItems: SidebarItem[] = [
    {
      label: 'Home',
      icon: <Undo2 className="w-4" />,
      path:
        Cookies.get('type') === 'SUPER_ADMIN'
          ? ROUTES.SUPER_ADMIN.DASHBOARD
          : Cookies.get('type') === 'EMPLOYEE'
            ? ROUTES.HOME
            : ROUTES.ORGANIZATION.DASHBOARD,
      slug: `/warehouse/${warehouseId}/home`,
      visible: true,
    },
    {
      label: 'Dashboard',
      icon: <LayoutDashboard className="w-4" />,
      path: ROUTES.WAREHOUSE.DASHBOARD(warehouseId),
      slug: `/warehouse/${warehouseId}/dashboard`,
      visible: isVisible,
    },
    {
      label: 'Products',
      icon: <ShoppingBasket className="w-4" />,
      slug: `/warehouse/${warehouseId}/products`,
      visible: true,
      submenu: [
        {
          label: 'Products List',
          icon: <CircleParking className="w-3" />,
          path: ROUTES.WAREHOUSE.PRODUCTS(warehouseId),
          slug: `/warehouse/${warehouseId}/products`,
          visible: true,
        },
        {
          label: 'Brands',
          icon: <Handshake className="w-3" />,
          path: ROUTES.WAREHOUSE.BRANDS(warehouseId),
          slug: `/warehouse/${warehouseId}/brands`,
          visible: true,
        },
        {
          label: 'Categories',
          icon: <ListTodo className="w-3" />,
          path: ROUTES.WAREHOUSE.CATEGORIES(warehouseId),
          slug: `/warehouse/${warehouseId}/categories`,
          visible: true,
        },
        // {
        //   label: 'Sub Categories',
        //   icon: <Replace className="w-3" />,
        //   path: ROUTES.WAREHOUSE.SUB_CATEGORIES(warehouseId),
        //   slug: `/warehouse/${warehouseId}/sub-categories`,
        //   visible: true,
        // },
      ],
    },
    {
      label: 'Inventory',
      icon: <AudioWaveform className="w-4" />,
      slug: `/warehouse/${warehouseId}/stock`,
      visible: true,
      submenu: [
        {
          label: 'Stock Entry',
          icon: <CirclePlus className="w-3" />,
          path: ROUTES.STOCK.ENTRY(warehouseId),
          slug: `/warehouse/${warehouseId}/stock-entry`,
          visible: true,
        },
        {
          label: 'Stock List',
          icon: <ScrollText className="w-3" />,
          path: ROUTES.STOCK.LIST(warehouseId),
          slug: `/warehouse/${warehouseId}/stock-list`,
          visible: true,
        },
        // {
        //   label: 'Stock Location',
        //   icon: <LocateFixed className="w-3" />,
        //   path: ROUTES.WAREHOUSE.STOCK_SEARCH(warehouseId),
        //   slug: `/warehouse/${warehouseId}/stock-location`,
        //   visible: true,
        // },
        {
          label: 'Stock Transfer',
          icon: <Move3D className="w-3" />,
          path: ROUTES.STOCK.TRANSFER(warehouseId),
          slug: `/warehouse/${warehouseId}/stock-transfer`,
          visible: true,
        },
        {
          label: 'Stock Transfer (Scan)',
          icon: <ScanBarcode className="w-3" />,
          path: ROUTES.STOCK.TRANSFER_SCAN(warehouseId),
          slug: `/warehouse/${warehouseId}/scan-stock-transfer`,
          visible: true,
        },
        {
          label: 'Stock Pull',
          icon: <Undo2 className="w-3" />,
          path: ROUTES.STOCK.PULL(warehouseId),
          slug: `/warehouse/${warehouseId}/stock-pull`,
          visible: isVisible,
        },
        {
          label: 'Transfer History',
          icon: <History className="w-3" />,
          path: ROUTES.STOCK.TRANSFER_HISTORY(warehouseId),
          slug: `/warehouse/${warehouseId}/stocks-transfer-history`,
          visible: true,
        },
        {
          label: 'Purchase Invoices',
          icon: <FileText className="w-3" />,
          path: ROUTES.STOCK.ENTRY_LIST(warehouseId),
          slug: `/warehouse/${warehouseId}/purchase-invoices`,
          visible: true,
        },
      ],
    },
    /* {
      label: 'Returns',
      icon: <CircleArrowOutDownLeft className="w-4" />,
      slug: `/warehouse/${warehouseId}/pending-returns`,
      path: ROUTES.WAREHOUSE.PENDING_RETURNS(warehouseId),
      visible: true,
    }, */
    {
      label: 'Suppliers',
      icon: <Cable className="w-4" />,
      path: ROUTES.WAREHOUSE.SUPPLIERS(warehouseId),
      slug: `/warehouse/${warehouseId}/suppliers`,
      visible: isVisible,
    },
    {
      label: 'Customers',
      icon: <Users className="w-4" />,
      path: ROUTES.WAREHOUSE.CUSTOMERS(warehouseId),
      slug: `/warehouse/${warehouseId}/customers`,
      visible: isVisible,
    },
    {
      label: 'Billing',
      icon: <ListChecks className="w-4" />,
      slug: `/warehouse/${warehouseId}/billing`,
      visible: isVisible,
      submenu: [
        {
          label: 'Order List',
          icon: <CircleParking className="w-3" />,
          path: ROUTES.WAREHOUSE.ORDERS(warehouseId),
          slug: `/warehouse/${warehouseId}/orders`,
          visible: true,
        },
        // {
        //   label: 'Sellers',
        //   icon: <Handshake className="w-3" />,
        //   path: ROUTES.WAREHOUSE.SELLERS(warehouseId),
        //   slug: `/warehouse/${warehouseId}/sellers`,
        //   visible: true,
        // },
        {
          label: 'Sales Summery',
          icon: <History className="w-3" />,
          path: ROUTES.WAREHOUSE.SALES_SUMMERY(warehouseId, startAndEndDate),
          slug: `/warehouse/${warehouseId}/sales-summery`,
          visible: true,
        },
        {
          label: 'Profit & Loss',
          icon: <PiggyBank className="w-3" />,
          path: ROUTES.WAREHOUSE.ACCOUNT_CLOSE(warehouseId, startAndEndDate),
          slug: `/warehouse/${warehouseId}/account-close`,
          visible: true,
        },
      ],
    },
    // {
    //   label: 'Wholesale',
    //   icon: <Container className="w-4" />,
    //   slug: `/warehouse/${warehouseId}/wholesale`,
    //   visible: isVisible,
    //   submenu: [
    //     {
    //       label: 'Add New Order',
    //       icon: <CircleParking className="w-3" />,
    //       path: ROUTES.WAREHOUSE.WHOLESALE_NEW_ORDER(warehouseId),
    //       slug: `/warehouse/${warehouseId}/add-new-wholesale-order`,
    //       visible: true,
    //     },
    //     {
    //       label: 'Wholesale Orders',
    //       icon: <Handshake className="w-3" />,
    //       path: ROUTES.WAREHOUSE.WHOLESALE_ORDERS(warehouseId),
    //       slug: `/warehouse/${warehouseId}/wholesale-orders`,
    //       visible: true,
    //     },
    //     {
    //       label: 'Sales Summary',
    //       icon: <History className="w-3" />,
    //       path: ROUTES.WAREHOUSE.SALES_SUMMERY(warehouseId),
    //       slug: `/warehouse/${warehouseId}/sales-summary`,
    //       visible: true,
    //     },
    //     {
    //       label: 'Account Close',
    //       icon: <PiggyBank className="w-3" />,
    //       path: ROUTES.WAREHOUSE.ACCOUNT_CLOSE(warehouseId),
    //       slug: `/warehouse/${warehouseId}/account-close`,
    //       visible: true,
    //     },
    //   ],
    // },
    {
      label: 'Expenses',
      icon: <CircleDollarSign className="w-4" />,
      slug: `/warehouse/${warehouseId}/expenses`,
      visible: isVisible,
      submenu: [
        {
          label: 'Expenses',
          icon: <HandCoins className="w-3" />,
          path: ROUTES.WAREHOUSE.EXPENSES(warehouseId),
          slug: `/warehouse/${warehouseId}/expenses`,
          visible: true,
        },
        {
          label: 'Categories',
          icon: <CircleParking className="w-3" />,
          path: ROUTES.WAREHOUSE.EXPENSES_CATEGORIES(warehouseId),
          slug: `/warehouse/${warehouseId}/expense-categories`,
          visible: true,
        },
      ],
    },
    /* {
      label: 'Courier',
      icon: <Truck className="w-4" />,
      slug: `/warehouse/${warehouseId}/courier`,
      visible: isVisible,
      submenu: [
        {
          label: 'Bookings',
          icon: <Package className="w-3" />,
          path: ROUTES.WAREHOUSE.COURIER_BOOKINGS(warehouseId),
          slug: `/warehouse/${warehouseId}/courier-bookings`,
          visible: true,
        },
        {
          label: 'Configure',
          icon: <Settings className="w-3" />,
          path: ROUTES.WAREHOUSE.COURIER_SETTINGS(warehouseId),
          slug: `/warehouse/${warehouseId}/courier-settings`,
          visible: true,
        },
        {
          label: 'Webhook',
          icon: <Webhook className="w-3" />,
          path: ROUTES.WAREHOUSE.COURIER_WEBHOOK(warehouseId),
          slug: `/warehouse/${warehouseId}/courier-webhook`,
          visible: true,
        },
      ],
    }, */
    /* {
      label: 'Webhook',
      icon: <Webhook className="w-3" />,
      path: ROUTES.WAREHOUSE.COURIER_WEBHOOK(warehouseId),
      slug: `/warehouse/${warehouseId}/courier-webhook`,
      visible: isVisible,
    }, */
    // {
    //   label: 'SMS Panel',
    //   icon: <MessagesSquare className="w-4" />,
    //   slug: `/warehouse/${warehouseId}/sms-panel`,
    //   visible: isVisible,
    //   submenu: [
    //     {
    //       label: 'Balance & History',
    //       icon: <BadgeDollarSign className="w-3" />,
    //       path: ROUTES.WAREHOUSE.SMS_BALANCE_AND_HISTORY(warehouseId),
    //       slug: `/warehouse/${warehouseId}/sms-balance-history`,
    //       visible: true,
    //     },
    //     {
    //       label: 'Send Message',
    //       icon: <Send className="w-3" />,
    //       path: ROUTES.WAREHOUSE.SEND_SMS(warehouseId),
    //       slug: `/warehouse/${warehouseId}/send-sms`,
    //       visible: true,
    //     },
    //     {
    //       label: 'Configure',
    //       icon: <Settings className="w-3" />,
    //       path: ROUTES.WAREHOUSE.SMS_SETTINGS(warehouseId),
    //       slug: `/warehouse/${warehouseId}/sms-settings`,
    //       visible: true,
    //     },
    //   ],
    // },
    {
      label: 'Reports',
      icon: <FileText className="w-4" />,
      slug: `/warehouse/${warehouseId}/reports`,
      visible: true, // Always visible for now, you can modify it based on conditions
      submenu: [
        {
          label: 'Current Stock',
          icon: <Activity className="w-3" />,
          path: ROUTES.WAREHOUSE.CURRENT_STOCK_REPORT(
            warehouseId,
            startAndEndDate,
          ),
          slug: `/warehouse/${warehouseId}/current-stock-report`,
          visible: true,
        },
        {
          label: 'Inventory',
          icon: <FileClock className="w-3" />,
          path: ROUTES.WAREHOUSE.DATE_WISE_STOCK_REPORT(
            warehouseId,
            `date=${new Date().toISOString().split('T')[0]}`,
          ),
          slug: `/warehouse/${warehouseId}/date-wise-stock-report`,
          visible: true,
        },
        {
          label: 'Stock Handover',
          icon: <FileClock className="w-3" />,
          path: ROUTES.WAREHOUSE.DATE_WISE_STOCK_HANDOVER_REPORT(
            warehouseId,
            `date=${new Date().toISOString().split('T')[0]}`,
          ),
          slug: `/warehouse/${warehouseId}/date-wise-stock-handover-report`,
          visible: true,
        },
        {
          label: 'Sales Report',
          icon: <NotepadText className="w-3" />,
          path: ROUTES.WAREHOUSE.SALES_REPORT(warehouseId, startAndEndDate),
          slug: `/warehouse/${warehouseId}/sales-report`,
          visible: isVisible,
        },
        {
          label: 'Stock Analysis',
          icon: <GitGraph className="w-3" />,
          path: ROUTES.WAREHOUSE.STOCK_ANALYSIS_REPORT(warehouseId),
          slug: `/warehouse/${warehouseId}/stock-analysis-report`,
          visible: true,
        },
        {
          label: 'Location Analysis',
          icon: <MapPin className="w-3" />,
          path: ROUTES.WAREHOUSE.LOCATION_ANALYSIS_REPORT(warehouseId),
          slug: `/warehouse/${warehouseId}/location-analysis-report`,
          visible: true,
        },
        {
          label: 'Stock Entry',
          icon: <TicketPlus className="w-3" />,
          path: ROUTES.WAREHOUSE.STOCK_ENTRY_REPORT(
            warehouseId,
            startAndEndDate,
          ),
          slug: `/warehouse/${warehouseId}/stock-entry-report`,
          visible: true,
        },
        {
          label: 'Stock Transfer',
          icon: <FolderSync className="w-3" />,
          path: ROUTES.WAREHOUSE.STOCK_TRANSFER_REPORT(
            warehouseId,
            startAndEndDate,
          ),
          slug: `/warehouse/${warehouseId}/stock-transfer-report`,
          visible: true,
        },
        {
          label: 'Stock Delete',
          icon: <Trash2 className="w-3" />,
          path: ROUTES.WAREHOUSE.STOCK_DELETE_REPORT(
            warehouseId,
            startAndEndDate,
          ),
          slug: `/warehouse/${warehouseId}/stock-delete-report`,
          visible: isVisible,
        },

        // {
        //   label: 'Stock Return Report',
        //   icon: <Undo2 className="w-3" />,
        //   path: ROUTES.WAREHOUSE.STOCK_RETURN_REPORT(
        //     warehouseId,
        //     startAndEndDate,
        //   ),
        //   slug: `/warehouse/${warehouseId}/stock-return-report`,
        //   visible: true,
        // },
        {
          label: 'Expenses Report',
          icon: <CircleDollarSign className="w-3" />,
          path: ROUTES.WAREHOUSE.EXPENSES_REPORT(warehouseId, startAndEndDate),
          slug: `/warehouse/${warehouseId}/expenses-report`,
          visible: isVisible,
        },
        {
          label: 'Employees Report',
          icon: <Users className="w-3" />,
          path: ROUTES.WAREHOUSE.SELLERS_REPORT(warehouseId, startAndEndDate),
          slug: `/warehouse/${warehouseId}/employees-report`,
          visible: isVisible,
        },
      ],
    },
    // {
    //   label: 'Settings',
    //   icon: <Settings className="w-4" />,
    //   slug: `/warehouse/${warehouseId}/settings`,
    //   visible: isVisible,
    //   submenu: [
    //     {
    //       label: 'Courier Settings',
    //       icon: <Truck className="w-3" />,
    //       path: ROUTES.WAREHOUSE.COURIER_SETTINGS(warehouseId),
    //       slug: `/warehouse/${warehouseId}/courier-settings`,
    //       visible: true,
    //     },
    //     {
    //       label: 'SMS Settings',
    //       icon: <Mail className="w-3" />,
    //       path: ROUTES.WAREHOUSE.SMS_SETTINGS(warehouseId),
    //       slug: `/warehouse/${warehouseId}/sms-settings`,
    //       visible: true,
    //     },
    //   ],
    // },
  ];

  return (
    <div className="relative z-50 flex h-full bg-gray-100">
      <div
        className={`fixed inset-y-0 top-[60px] z-30 h-sidebar w-[200px] transform overflow-y-auto bg-[#28243D] text-white transition-transform delay-200 duration-200 md:relative md:top-0 md:translate-x-0 lg:w-[220px] xl:w-[260px] ${isSidebarOpen ? 'md:translate-x-0' : '-translate-x-full'}`}
      >
        <div className="flex h-full flex-col overflow-y-auto">
          {warehouseSidebarItems?.map((item: SidebarItem) => {
            return item?.visible ? (
              item?.submenu?.length ? (
                <div key={item?.slug}>
                  {item?.visible ? (
                    <button
                      type="button"
                      onClick={() =>
                        expanded === item.label
                          ? setExpanded('')
                          : setExpanded(item.label)
                      }
                      className="flex w-full items-center justify-between px-4 py-2 text-[12px] hover:bg-gray-700 lg:text-[14px] xl:text-[16px]"
                    >
                      <span className="flex items-center">
                        <span className="mr-4">{item?.icon}</span> {item.label}
                      </span>
                      <ChevronDown
                        className={`${expanded === item.label ? 'rotate-180' : ''} w-4`}
                      />
                    </button>
                  ) : (
                    ''
                  )}
                  <div
                    className={`${expanded === item.label ? 'opacity-500 block transition-opacity delay-500 duration-500 ease-in-out' : 'hidden opacity-0'} ml-[23px] border-l border-gray-400 ease-in-out`}
                  >
                    {item?.submenu?.map((subMenu: SingleSubmenu) => {
                      return subMenu?.visible ? (
                        <Link
                          to={subMenu.path ?? ''}
                          className={
                            selectedPath.includes(subMenu.slug ?? '')
                              ? selectedSubLink
                              : subLink
                          }
                          key={subMenu.path}
                        >
                          <span className="mr-[5px] translate-y-[.5px]">
                            {subMenu.icon}
                          </span>{' '}
                          {subMenu.label}
                        </Link>
                      ) : (
                        ''
                      );
                    })}
                  </div>
                </div>
              ) : (
                <Link
                  key={item.slug}
                  to={item.path ?? ''}
                  className={
                    selectedPath.includes(item?.slug ?? '')
                      ? selectedLink
                      : link
                  }
                  onClick={() => setExpanded(item.slug ?? '--')}
                >
                  <span className="mr-4">{item.icon}</span> {item.label}
                </Link>
              )
            ) : (
              ''
            );
          })}
        </div>
      </div>
      <div
        className={`fixed bottom-0 z-50 w-[200px] transition-transform delay-200 duration-200 md:translate-x-0 lg:w-[220px] xl:w-[260px] ${isSidebarOpen ? 'md:translate-x-0' : '-translate-x-full'}`}
      >
        <div className="bg-[#221e33] py-2">
          <h2 className="text-center text-[10px] text-gray-300">
            {`Softs.ai © ${new Date().getFullYear()}`}
          </h2>
        </div>
      </div>
    </div>
  );
}

export default WarehouseSidebar;
