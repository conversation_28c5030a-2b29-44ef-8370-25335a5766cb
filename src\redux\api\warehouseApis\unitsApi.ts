import { GetUnitsResponse } from '@/types/warehouseTypes/unitTypes';
import BaseApi from '../baseApi';
import { TagTypes } from '@/redux/tag-types';

interface GetUnitsParams {
  warehouseId?: string;
}

const UnitsApi = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getUnits: builder.query<GetUnitsResponse, GetUnitsParams>({
      query: (params) => ({
        url: '/unit',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.UNIT],
    }),
    createUnit: builder.mutation({
      query: (data) => ({
        url: '/unit/new',
        method: 'POST',
        data,
      }),
      invalidatesTags: [TagTypes.UNIT],
    }),
    updateUnit: builder.mutation({
      query: ({ data, id }) => ({
        url: `/unit/${id}`,
        method: 'PATCH',
        data,
      }),
      invalidatesTags: [TagTypes.UNIT],
    }),
    deleteUnit: builder.mutation({
      query: (id) => ({
        url: `/unit/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: [TagTypes.UNIT],
    }),
  }),
});

export const {
  useGetUnitsQuery,
  useCreateUnitMutation,
  useDeleteUnitMutation,
  useUpdateUnitMutation,
} = UnitsApi;
