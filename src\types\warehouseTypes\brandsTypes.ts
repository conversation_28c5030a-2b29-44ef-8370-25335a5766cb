import { CreatedBy } from './../shopTypes/shopExpensesTypes';
import { Pagination } from '@/redux/commonTypes';

export interface GetBrandsResponse {
  success: boolean;
  statusCode: number;
  message: string;
  data: SingleBrandDetails[];
  pagination: Pagination;
}

export interface SingleBrandDetails {
  id: string;
  createdAt: string;
  updatedAt: string;
  shopId: string;
  name: string;
  imgUrl: string;
  serialNo: number;
  CreatedBy: CreatedBy;
}
