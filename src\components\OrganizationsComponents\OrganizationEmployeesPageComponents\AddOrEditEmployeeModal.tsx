import { useFormik } from 'formik';
import Cookies from 'js-cookie';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import * as Yup from 'yup';

import FilledSubmitButton from '@/components/reusable/Buttons/FilledSubmitButton';
import CustomInputField from '@/components/reusable/CustomInputField/CustomInputField';
import ImageSelector from '@/components/reusable/ImageSelector/ImageSelector';
import ModalTitle from '@/components/reusable/Modal/ModalTitle';
import {
  useCreateSellerMutation,
  useUpdateSellerMutation,
} from '@/redux/api/warehouseApis/sellersApis';
import { SingleSellerDetails } from '@/types/warehouseTypes/sellersTypes';
import { UploadImageOnAws } from '@/utils/ImageUploadModule';

interface Props {
  type: string;
  handleClose: () => void;
  sellerData?: SingleSellerDetails;
}

const formikInitialValues = {
  name: '',
  mobileNumber: '',
  // assignedShopId: '',
};

const validation = Yup.object({
  name: Yup.string().required('Seller name is required'),
  mobileNumber: Yup.string().required('Phone Number is required'),
});
function AddOrEditEmployeeModal({ type, handleClose, sellerData }: Props) {
  const [currentFile, setCurrentFile] = useState<any>();
  const [createSeller, { isLoading }] = useCreateSellerMutation();
  const [updateSeller, { isLoading: isSellerUpdating }] =
    useUpdateSellerMutation();

  const formik = useFormik({
    initialValues: formikInitialValues,
    validationSchema: validation,

    onSubmit: async (values) => {
      const imgUrl = currentFile ? await UploadImageOnAws(currentFile) : null;
      const data = {
        // warehouseId,
        ...values,
        organizationId: Cookies.get('organizationId'),
        imgUrl: imgUrl || (sellerData?.imgUrl ? sellerData?.imgUrl : null),
      };
      if (type === 'new') {
        toast.promise(createSeller(data).unwrap(), {
          pending: 'Creating New Seller...',
          success: {
            render({ data: res }) {
              if (res?.statusCode === 200 || res?.statusCode === 201) {
                handleClose();
              }
              return 'Seller created Successfully';
            },
          },
          error: {
            render({ data: error }) {
              console.log(error);
              return 'Error on creating Seller';
            },
          },
        });
      } else {
        toast.promise(
          updateSeller({
            data,
            id: sellerData?.id,
          }).unwrap(),
          {
            pending: 'Updating Seller...',
            success: {
              render({ data: res }) {
                if (res?.statusCode === 200 || res?.statusCode === 201) {
                  handleClose();
                }
                return 'Seller updated Successfully';
              },
            },
            error: {
              render({ data: error }) {
                console.log(error);
                return 'Error on updating Seller';
              },
            },
          },
        );
      }
    },
  });

  useEffect(() => {
    if (type === 'edit' && sellerData) {
      formik.setFieldValue('name', sellerData.name);
      formik.setFieldValue('mobileNumber', sellerData.mobileNumber);
      // formik.setFieldValue('assignedShopId', sellerData.assignedShopId);
    }
  }, [sellerData, type]);

  return (
    <div className="flex w-[340px] flex-col gap-4 rounded-xl bg-white p-4 md:w-[400px]">
      <ModalTitle
        text={type === 'new' ? 'Add Employee modal' : 'Edit Employee modal'}
        handleClose={handleClose}
      />
      <form
        onSubmit={formik.handleSubmit}
        className="flex w-full flex-col gap-4"
      >
        <div className="flex w-full gap-4">
          <div className="w-[100px]">
            <ImageSelector
              previousImage={sellerData?.imgUrl ?? ''}
              setNewImage={(e) => setCurrentFile(e)}
            />
          </div>
          <div className="flex w-full flex-col gap-4">
            <CustomInputField
              type="text"
              placeholder="Enter Employee Name"
              name="name"
              label="Name"
              formik={formik}
            />
            <CustomInputField
              type="text"
              placeholder="Enter Employee Phone Number"
              name="mobileNumber"
              label="Phone Number"
              formik={formik}
            />
          </div>
        </div>
        <div className="mt-[10px] flex w-full items-center justify-center">
          <FilledSubmitButton
            isLoading={isLoading || isSellerUpdating}
            text={type === 'new' ? 'Add Employee' : 'Done Editing'}
          />
        </div>
      </form>
    </div>
  );
}

export default AddOrEditEmployeeModal;
