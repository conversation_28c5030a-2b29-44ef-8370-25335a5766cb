import OrderPageFilterOptions from './OrderPageFilterOptions';

interface Props {
  handleClearAndClose?: () => void;
  handleFilter: (fieldName: string, value: string) => void;
}

export default function OrderPageFilterModal({
  handleClearAndClose,
  handleFilter,
}: Props) {
  return (
    <div className="fixed z-50 w-full p-4">
      <div className="commonModal absolute -left-[12px] top-0 py-4">
        <div className="relative">
          <div className="relative rounded-xl border-2 border-black bg-white p-4">
            {/* Outer Arrow (Black Border) */}
            <div className="absolute left-[13px] top-0 -mt-[14px] h-0 w-0 rotate-180 border-l-[12px] border-r-[12px] border-t-[12px] border-l-transparent border-r-transparent border-t-black">
              {/* Inner Arrow (White Center) */}
              <div className="absolute -left-[11px] -top-[14.5px] h-0 w-0 border-l-[11px] border-r-[11px] border-t-[11px] border-b-transparent border-l-transparent border-r-transparent border-t-white" />
            </div>

            <p className="mb-2">Order List Filter</p>
            <OrderPageFilterOptions handleFilter={handleFilter} />
            <div className="flex items-center justify-between gap-4 pt-2">
              <button
                className="w-full rounded-lg border border-primary py-2"
                type="button"
                onClick={handleClearAndClose}
              >
                Clear
              </button>
              <button
                className="w-full rounded-lg bg-primary py-2 text-white"
                type="button"
              >
                Search
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
