import { ChevronLeft, ChevronRight } from 'lucide-react';
import { useLocation, useNavigate } from 'react-router-dom';

interface Props {
  currentPage?: string;
  totalPages?: number;
  handleFilter?: (fieldName: string, value: any) => void;
  limit?: number;
  totalCount?: number;
  limits?: number[];
}

export default function Pagination({ totalPages, totalCount, limits }: Props) {
  const navigate = useNavigate();
  const location = useLocation();
  const router = new URLSearchParams(location.search);
  const currentPage = router.get('page') ?? '1';
  const limit = router.get('limit') ?? '10';

  // Handle page change
  const handlePageChange = (newPage: number) => {
    router.set('page', newPage.toString());
    navigate(`${location.pathname}?${router.toString()}`);
  };

  // Handle rows per page (limit) change
  const handleChangeRowsPerPage = (value: string) => {
    const newLimit = parseInt(value, 10);
    router.set('limit', newLimit.toString());
    router.set('page', '1');
    navigate(`${location.pathname}?${router.toString()}`);
  };

  return (
    <div className="flex gap-x-5">
      <div className="per-page-number flex items-center gap-x-2">
        <p>Rows Per Page:</p>
        <div className="mx-auto max-w-sm">
          <select
            id="countries"
            className="block w-full"
            value={limit ?? '10'}
            onChange={(e) => handleChangeRowsPerPage(e.target.value)}
          >
            {limits?.length ? (
              limits?.map((item: number) => (
                <option key={item} value={item}>
                  {item}
                </option>
              ))
            ) : (
              <>
                <option value={10}>10</option>
                <option value={20}>20</option>
                <option value={50}>50</option>
                {/* <option value={100}>100</option>
                <option value={200}>200</option> */}
              </>
            )}
          </select>
        </div>
      </div>
      <div className="page-number">
        <p>
          <span>{(Number(currentPage) - 1) * Number(limit) + 1}</span> -{' '}
          <span>
            {Number(currentPage) * Number(limit) > Number(totalCount)
              ? totalCount
              : Number(currentPage) * Number(limit)}
          </span>{' '}
          of <span>{totalCount ?? 0}</span>
        </p>
      </div>
      <div className="next-prev-btn">
        <button
          type="button"
          onClick={() => handlePageChange(Number(currentPage) - 1)}
          disabled={Number(currentPage) === 1}
          className="disabled:text-gray-400"
        >
          <ChevronLeft />
        </button>
        <button
          disabled={Number(currentPage) === Number(totalPages)}
          type="button"
          onClick={() => handlePageChange(Number(currentPage) + 1)}
          className="disabled:text-gray-400"
        >
          <ChevronRight />
        </button>
      </div>
    </div>
  );
}
