import { Pagination } from '@/redux/commonTypes';

export interface GetCashTransferListResponse {
  success: boolean;
  statusCode: number;
  message: string;
  data: SingleCashTransferDetails[];
  pagination: Pagination;
}

export interface SingleCashTransferDetails {
  id: string;
  createdAt: string;
  updatedAt: string;
  imgUrl: any;
  paymentDate: any;
  transferredTo: string;
  amount: number;
  shopId: string;
  warehouseId: string;
  senderId: string;
  Warehouse: Warehouse;
  Sender: Sender;
}

export interface Warehouse {
  id: string;
  createdAt: string;
  updatedAt: string;
  mobileNumber: string;
  name: string;
  address: string;
  street: string;
  district: string;
  zipCode: string;
  country: string;
  imgUrl: string;
  organizationId: string;
}

export interface Sender {
  id: string;
  createdAt: string;
  updatedAt: string;
  name: string;
  email: string;
  username: string;
  mobileNumber: string;
  password: string;
  refreshToken: string;
  imgUrl: any;
  organizationLimit: number;
  warehouseLimit: number;
  shopLimit: number;
  type: string;
}
