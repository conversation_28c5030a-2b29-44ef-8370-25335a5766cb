import { useParams } from 'react-router-dom';

import ProductsPageOverview from '@/components/WarehouseComponents/ProductsPageComponents/ProductsPageOverview';
import { ProtectedRoute } from '@/utils/ProtectedRoutes';

function ProductsPage() {
  const { warehouseId } = useParams();
  return (
    <ProtectedRoute>
      <ProductsPageOverview warehouseId={warehouseId ?? ''} />
    </ProtectedRoute>
  );
}

export default ProductsPage;
