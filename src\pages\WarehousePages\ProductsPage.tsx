import { useParams } from 'react-router-dom';

import ProductsPageOverview from '@/components/WarehouseComponents/ProductsPageComponents/ProductsPageOverview';
import { ViewFromType } from '@/types/commonTypes';
import { ProtectedRoute } from '@/utils/ProtectedRoutes';

function ProductsPage({ viewFrom }: { viewFrom: ViewFromType }) {
  const { warehouseId } = useParams();
  return (
    <ProtectedRoute>
      <ProductsPageOverview
        warehouseId={warehouseId ?? ''}
        viewFrom={viewFrom}
      />
    </ProtectedRoute>
  );
}

export default ProductsPage;
