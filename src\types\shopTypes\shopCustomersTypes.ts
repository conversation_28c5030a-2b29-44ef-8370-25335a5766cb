import { Pagination } from '@/redux/commonTypes';
import { ShopOrderDetails } from './shopOrderTypes';

export interface GetShopCustomersResponse {
  success: boolean;
  statusCode: number;
  message: string;
  data: ShopCustomerDetails[];
  pagination: Pagination;
}

export interface GetShopCustomerDetailsResponse {
  success: boolean;
  statusCode: number;
  message: string;
  data: ShopCustomerDetailsWithOrderList;
}

export interface ShopCustomerDetails {
  id: string;
  serialNo: number;
  createdAt: string;
  updatedAt: string;
  name: string;
  email: string;
  mobileNumber: string;
  imgUrl: any;
  street: string;
  district: string;
  zipCode: string;
  country: any;
  organizationId: string;
  gender: string;
  address: string;
  orderCount: number;
}

export interface ShopCustomerDetailsWithOrderList {
  id: string;
  createdAt: string;
  updatedAt: string;
  name: string;
  email: string;
  mobileNumber: string;
  imgUrl: any;
  address: string;
  gender: any;
  street: any;
  district: any;
  zipCode: any;
  country: any;
  organizationId: string;
  serialNo: number;
  createdById: string;
  Order: ShopOrderDetails[];
  totalPaid: number;
  totalDue: number;
  totalAmount: number;
}

