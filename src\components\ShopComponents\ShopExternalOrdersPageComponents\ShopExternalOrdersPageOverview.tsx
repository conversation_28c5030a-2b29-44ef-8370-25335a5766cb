import { EllipsisVert<PERSON>, Eye, ShieldCheck, Upload, X } from 'lucide-react';
import { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import Swal from 'sweetalert2';

import ShopAddExternalOrderToPosOrderModal from './ShopAddExternalOrderToPosOrderModal';

import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import CustomSelectForFilter from '@/components/reusable/Inputs/CustomSelectForFilter';
import SearchInput from '@/components/reusable/Inputs/SearchInput';
import Modal from '@/components/reusable/Modal/Modal';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import FraudCheckModal from '@/components/reusable/OrdersPagesReusableComponents/FraudCheckModal';
import OrderStatusViewer from '@/components/reusable/OrdersPagesReusableComponents/OrderStatusViewer';
import Pagination from '@/components/reusable/Pagination/Pagination';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import {
  Menubar,
  MenubarContent,
  MenubarItem,
  MenubarMenu,
  MenubarSeparator,
  MenubarTrigger,
} from '@/components/ui/menubar';
import {
  SingleExternalOrderDetails,
  useGetShopExternalOrdersQuery,
  useUpdateSingleExternalOrderMutation,
} from '@/redux/api/shopApis/shopExternalOrdersApis';
import { ROUTES } from '@/Routes';
import { generateFilterParams } from '@/utils/generateFilterParams';

interface Props {
  shopId?: string;
}
function ShopExternalOrdersPageOverview({ shopId }: Props) {
  const navigate = useNavigate();
  const router = new URLSearchParams(useLocation().search);
  const customerName = router.get('customerName');
  const mobileNumber = router.get('mobileNumber');
  const serialNo = router.get('serialNo');
  const page = router.get('page');
  const limit = router.get('limit');
  const startDate = router.get('startDate');
  const endDate = router.get('endDate');
  const orderStatus = router.get('orderStatus');
  const [isAddOrderToPosModalOpen, setIsAddOrderToPosModalOpen] =
    useState(false);
  const [selectedOrderId, setSelectedOrderId] = useState('');
  const [isFraudCheckModalOpen, setIsFraudCheckModalOpen] =
    useState<boolean>(false);
  const [fraudCheckMobileNumber, setFraudCheckMobileNumber] =
    useState<string>('');

  const { data, isLoading, isFetching, refetch } =
    useGetShopExternalOrdersQuery({
      shopId,
      page: page ?? '1',
      limit: limit ?? '10',
      status: orderStatus ?? undefined,
      startDate: startDate ?? undefined,
      endDate: endDate ?? undefined,
      orderNumber: serialNo ?? undefined,
      name: customerName ?? undefined,
      phone: mobileNumber ?? undefined,
    });

  const [updateSingleExternalOrder, { isLoading: isStatusUpdating }] =
    useUpdateSingleExternalOrderMutation();
  const handleBlockOrUnblockUser = (
    userDetails: SingleExternalOrderDetails,
  ) => {
    Swal.fire({
      title: `Are you sure? you want to cancel order no ${userDetails.orderNumber}`,
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: `Yes, cancel it!!`,
    }).then((result) => {
      if (result.isConfirmed) {
        toast.promise(
          updateSingleExternalOrder({
            data: { status: 'CANCELLED' },
            id: userDetails.id,
          }).unwrap(),
          {
            pending: 'Updating order status...',
            success: {
              render() {
                refetch();
                return 'status updated successfully';
              },
            },
            error: {
              render({ data: error }) {
                console.log(error);
                return 'Error on status update';
              },
            },
          },
        );
      }
    });
  };

  const handleFilter = (fieldName: string, value: string) => {
    const query = generateFilterParams(fieldName, value);
    navigate(ROUTES.SHOP.EXTERNAL_ORDERS(shopId ?? '', query));
  };

  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-2">
        <div className="flex items-center">
          <div className="search-title-and-btn flex items-center">
            <div className="relative">
              <div className="block xl:hidden">
                {/* <FilterButton
                  handleClick={() => setIsFilterModalOpen(!isFilterModalOpen)}
                /> */}
              </div>
              <div
              // className={`${isFilterModalOpen ? 'block' : 'hidden'} xl:hidden`}
              >
                {/* <CustomerPageFilterModal
                  handleClearAndClose={() => setIsFilterModalOpen(false)}
                  handleFilter={handleFilter}
                /> */}
              </div>
            </div>
          </div>
          <div className="hidden xl:block">
            <div className="flex items-center gap-x-2">
              <SearchInput
                placeholder="Search by Name"
                handleSubmit={(value: string) =>
                  handleFilter('customerName', value)
                }
                value={customerName ?? ''}
              />
              <SearchInput
                placeholder="Search by Mobile"
                handleSubmit={(value: string) =>
                  handleFilter('mobileNumber', value)
                }
                value={mobileNumber ?? ''}
              />
              <SearchInput
                placeholder="Search by Order Id"
                handleSubmit={(value: string) =>
                  handleFilter('serialNo', value)
                }
                value={serialNo ?? ''}
              />
              <CustomSelectForFilter
                options={[
                  { value: 'PENDING', label: 'Pending' },
                  { value: 'PROCESSED', label: 'Processed' },
                ]}
                selectedValue={orderStatus ?? ''}
                handleSelect={(e) => handleFilter('orderStatus', e)}
                placeHolder="Status"
              />
            </div>
          </div>
        </div>
      </div>
      <div>
        {!isLoading && !isFetching ? (
          <div>
            <div className="tableTop w-full">
              <p>Website Order List</p>
            </div>
            <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
              {data?.data?.length ? (
                <div className="full-table-box h-custom">
                  <table className="full-table">
                    <thead className="bg-gray-100">
                      <tr>
                        <th className="tableHead">#</th>
                        <th className="tableHead">Order No</th>
                        <th className="tableHead">POS Order No</th>
                        <th className="tableHead table-col-width">Name</th>
                        <th className="tableHead">Phone</th>
                        <th className="tableHead">From</th>
                        {/* <th className="tableHead">Total</th>
                        <th className="tableHead">Paid</th>
                        <th className="tableHead">Due</th>
                        <th className="tableHead">Payments</th> */}
                        <th className="tableHead">Status</th>
                        <th className="tableHead">Processed By</th>
                        <th className="tableHead">Notes</th>
                        <th className="tableHead">Order Date</th>
                        {/* <th className="tableHead">Created At</th> */}
                        <th className="tableHead">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y bg-slate-200">
                      {data?.data?.map(
                        (singleOrder: SingleExternalOrderDetails, index) => {
                          /* const totalAmount = singleOrder?.lineItems?.reduce(
                            (acc, item) => acc + item.price * item.quantity,
                            0,
                          ); */
                          return (
                            <tr key={singleOrder?.id}>
                              <td className="tableData">{index + 1}</td>

                              <td className="tableData">
                                {singleOrder?.orderNumber}
                              </td>
                              <td className="tableData">
                                {singleOrder?.Order?.id ? (
                                  <Link
                                    to={ROUTES.SHOP.ORDER_DETAILS(
                                      shopId ?? '',
                                      singleOrder?.Order?.id ?? '',
                                    )}
                                  >
                                    {singleOrder?.Order?.serialNo || '-'}
                                  </Link>
                                ) : (
                                  ''
                                )}
                              </td>
                              <td className="tableData table-col-width">
                                {singleOrder?.firstName} {singleOrder?.lastName}
                              </td>
                              <td className="tableData">
                                {singleOrder?.phone}
                              </td>
                              <td className="tableData">{singleOrder?.from}</td>
                              {/* 
                              <td className="tableData">{totalAmount}</td>
                              <td className="tableData">
                                {singleOrder?.totalDiscount}
                              </td>
                              <td className="tableData">
                                {singleOrder?.totalDiscount}
                              </td>
                              <td className="tableData">
                                {singleOrder?.totalDiscount}
                              </td> */}
                              <td className="tableData">
                                <OrderStatusViewer
                                  status={singleOrder?.status}
                                />
                              </td>
                              <td className="tableData">
                                {singleOrder?.ProcessedBy?.name || ''}
                              </td>
                              <td className="tableData">
                                {singleOrder?.note || '-'}
                              </td>
                              <td className="tableData">
                                <DateAndTimeViewer
                                  date={singleOrder.orderDate}
                                />
                              </td>
                              {/* <td className="tableData">
                                <DateAndTimeViewer
                                  date={singleOrder.createdAt}
                                />
                              </td> */}
                              <td className="tableData">
                                <div className="flex items-center justify-center gap-2">
                                  <Menubar
                                    style={{
                                      border: 'none',
                                      backgroundColor: 'transparent',
                                    }}
                                  >
                                    <MenubarMenu>
                                      <MenubarTrigger className="cursor-pointer">
                                        <EllipsisVertical />
                                      </MenubarTrigger>
                                      <MenubarContent
                                        style={{
                                          marginRight: '25px',
                                          borderColor: 'black',
                                        }}
                                      >
                                        <MenubarItem
                                          onClick={() => {
                                            setFraudCheckMobileNumber(
                                              singleOrder.phone,
                                            );
                                            setIsFraudCheckModalOpen(true);
                                          }}
                                          className="flex cursor-pointer items-center gap-1 bg-primary font-semibold text-white"
                                        >
                                          <ShieldCheck size={20} />
                                          <span>Fraud Check</span>
                                        </MenubarItem>
                                        <MenubarSeparator />
                                        <MenubarItem
                                          onClick={() =>
                                            navigate(
                                              ROUTES.SHOP.EXTERNAL_ORDER_DETAILS(
                                                shopId ?? '',
                                                singleOrder.id,
                                              ),
                                            )
                                          }
                                          className="flex cursor-pointer items-center gap-1 bg-primary font-semibold text-white"
                                        >
                                          <Eye size={20} />
                                          <span>View Details</span>
                                        </MenubarItem>

                                        {singleOrder?.status === 'PENDING' ? (
                                          <>
                                            <MenubarSeparator />
                                            <MenubarItem
                                              onClick={() => {
                                                setSelectedOrderId(
                                                  singleOrder.id,
                                                );
                                                setIsAddOrderToPosModalOpen(
                                                  true,
                                                );
                                              }}
                                              className="flex cursor-pointer items-center gap-1 bg-primary font-semibold text-white"
                                            >
                                              <Upload size={20} />
                                              <span>Entry To POS</span>
                                            </MenubarItem>
                                            <MenubarSeparator />
                                            <MenubarItem
                                              disabled={isStatusUpdating}
                                              onClick={() =>
                                                handleBlockOrUnblockUser(
                                                  singleOrder,
                                                )
                                              }
                                              className="flex cursor-pointer items-center gap-1 bg-red-500 font-semibold text-white"
                                            >
                                              <X size={20} />
                                              <span>Cancel Order</span>
                                            </MenubarItem>
                                          </>
                                        ) : (
                                          ''
                                        )}
                                      </MenubarContent>
                                    </MenubarMenu>
                                  </Menubar>
                                </div>
                              </td>
                            </tr>
                          );
                        },
                      )}
                    </tbody>
                  </table>
                </div>
              ) : (
                <NoResultFound pageType="customers" />
              )}
            </div>
            <div className="pagination-box flex justify-end rounded bg-white p-3">
              <Pagination
                totalCount={data?.pagination?.total}
                totalPages={Math.ceil(
                  Number(data?.pagination?.total) /
                    Number(data?.pagination?.limit),
                )}
              />
            </div>
          </div>
        ) : (
          <TableSkeletonLoader tableColumn={7} tableRow={6} />
        )}
      </div>
      <Modal
        setShowModal={setIsAddOrderToPosModalOpen}
        showModal={isAddOrderToPosModalOpen}
      >
        <ShopAddExternalOrderToPosOrderModal
          handleClose={() => setIsAddOrderToPosModalOpen(false)}
          selectedOrderId={selectedOrderId}
          shopId={shopId ?? ''}
        />
      </Modal>
      <Modal
        showModal={isFraudCheckModalOpen}
        setShowModal={setIsFraudCheckModalOpen}
      >
        <FraudCheckModal
          phoneNumber={fraudCheckMobileNumber}
          handleClose={() => setIsFraudCheckModalOpen(false)}
        />
      </Modal>
    </div>
  );
}

export default ShopExternalOrdersPageOverview;
