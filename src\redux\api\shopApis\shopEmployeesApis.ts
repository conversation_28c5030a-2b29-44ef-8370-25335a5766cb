import BaseApi from '../baseApi';

import { TagTypes } from '@/redux/tag-types';
import { GetSellersResponse } from '@/types/warehouseTypes/sellersTypes';

interface GetSellersParams {
  warehouseId?: string;
  organizationId?: string;
  shopId?: string | null;
  role?: string;
}

const ShopSellersApi = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getShopSellers: builder.query<GetSellersResponse, GetSellersParams>({
      query: (params) => ({
        url: '/employee',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP_SELLERS],
    }),
  }),
});

export const { useGetShopSellersQuery } = ShopSellersApi;
