server {
    listen 80;
    server_name _;

    root /usr/share/nginx/html;
    index index.html;

    # Handle React routes
    location / {
        try_files $uri /index.html;
    }

    # Optional: Handle static files efficiently
    location ~* \.(?:ico|css|js|json|png|jpg|jpeg|gif|svg|webp|woff|woff2|ttf|eot|otf|ttc|mp4|webm|wav|mp3|m4a|ogg|midi)$ {
        expires 6M;
        access_log off;
        add_header Cache-Control "public";
    }

    # Optional: Handle 404 errors gracefully
    error_page 404 /index.html;
}