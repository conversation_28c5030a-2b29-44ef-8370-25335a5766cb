import Cookies from 'js-cookie';
import ReactApexChart from 'react-apexcharts';
import { useLocation } from 'react-router-dom';
import { toast } from 'react-toastify';

import {
  BarcodeButton,
  TransparentEditButton,
} from '@/components/reusable/Buttons/CommonButtons';
import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import ImageViewer from '@/components/reusable/ImageViewer/ImageViewer';
import OrderListViewer from '@/components/reusable/OrdersPagesReusableComponents/OrderListViewer';
import Pagination from '@/components/reusable/Pagination/Pagination';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  useGetProductDetailsQuery,
  useGetProductStockLevelQuery,
  useGetProductTopSellingVariantsQuery,
} from '@/redux/api/warehouseApis/productsApi';
import { useGetStockListQuery } from '@/redux/api/warehouseApis/stockApis';
import { useGetWarehouseOrdersQuery } from '@/redux/api/warehouseApis/warehouseOrdersApis';
import { shopOrWarehouse } from '@/redux/commonTypes';
import { useAppSelector } from '@/redux/hooks';
import { ProductStockLevelData } from '@/types/warehouseTypes/productTypes';
import { SingleStockDetails } from '@/types/warehouseTypes/stockTypes';
import { CalculateDiscountPrice } from '@/utils/CalculateDiscountPrice';
import StockName from '@/utils/StockName';

interface Props {
  type: shopOrWarehouse;
  productId: string;
  warehouseId?: string;
  shopId?: string;
}

interface ProfitPieChartProps {
  data: ProductStockLevelData[];
}
interface TopVariantChartProps {
  data: any[];
}

function ProfitPieChart({ data }: ProfitPieChartProps) {
  const series = data?.map((item) => item.value);
  const labels = data.map((item) => item.label);

  const options: ApexCharts.ApexOptions = {
    labels,
    legend: {
      position: 'bottom',
    },
    title: {
      text: 'Stock Level Data',
      align: 'center',
    },
    tooltip: {
      y: {
        formatter: (val: number) => `$${val}`,
      },
    },
  };

  return (
    <div className="rounded-lg bg-white p-6 shadow-lg">
      <ReactApexChart
        options={options}
        series={series}
        type="pie"
        height={320}
      />
    </div>
  );
}

function TopVariantDonutChart({ data }: TopVariantChartProps) {
  const series = data?.map((item) => item.totalSold);
  const labels = data.map((item) => item.name);

  const options: ApexCharts.ApexOptions = {
    labels,
    legend: {
      position: 'bottom',
    },
    fill: {
      type: 'gradient',
    },
    title: {
      text: 'Top 5 Variants',
      align: 'center',
    },
    tooltip: {
      y: {
        formatter: (val: number) => `$${val}`,
      },
    },
  };

  return (
    <div className="rounded-lg bg-white p-6 shadow-lg">
      <ReactApexChart
        options={options}
        series={series}
        type="donut"
        height={320}
      />
    </div>
  );
}

function ProductDetailsPageOverview({ productId, shopId, type }: Props) {
  const { shopDetails, userDetails } = useAppSelector((state) => state);
  const router = new URLSearchParams(useLocation().search);
  const customerId = router.get('customerId');
  const customerName = router.get('customerName');
  const mobileNumber = router.get('mobileNumber');
  const serialNo = router.get('serialNo');
  const page = router.get('page');
  const limit = router.get('limit');
  const startDate = router.get('startDate');
  const endDate = router.get('endDate');
  const orderStatus = router.get('orderStatus');
  const { data, isLoading } = useGetProductDetailsQuery(productId);

  const { data: orderData, isLoading: isOrderDataLoading } =
    useGetWarehouseOrdersQuery({
      warehouseId: shopDetails?.warehouseId ?? undefined,
      shopId: shopId ?? undefined,
      page: page ?? '1',
      serialNo: serialNo ?? undefined,
      customerName: customerName ?? undefined,
      mobileNumber: mobileNumber ?? undefined,
      customerId: customerId ?? undefined,
      limit: limit ?? '10',
      startDate: startDate ?? undefined,
      endDate: endDate ?? undefined,
      orderStatus: orderStatus ?? undefined,
      productId,
    });
  const { data: stockData, isLoading: isStockDataLoading } =
    useGetStockListQuery(
      {
        organizationId: Cookies.get('organizationId') ?? '',
        warehouseId: shopDetails?.warehouseId ?? undefined,
        shopId,
        page: page ?? '1',
        productId: productId ?? undefined,
        // stat: undefined,
      },
      { skip: !shopDetails?.warehouseId && !shopId },
    );

  const { data: stockLevelData } = useGetProductStockLevelQuery({
    productId: productId ?? undefined,
  });

  const { data: top5SellingVariants } = useGetProductTopSellingVariantsQuery(
    {
      productId: productId ?? undefined,
      page: '1',
      limit: '5',
    },
    { skip: !productId },
  );
  const { data: topSellingVariants, isLoading: isTopSellingVariantsLoading } =
    useGetProductTopSellingVariantsQuery(
      {
        productId: productId ?? undefined,
      },
      { skip: !productId },
    );

  if (isLoading) {
    return <TableSkeletonLoader tableColumn={14} tableRow={6} />;
  }

  if (!data?.data) {
    return <div>No product found</div>;
  }

  const product = data.data;

  return (
    <div>
      <div className="mb-8 rounded-lg bg-white p-6 shadow-lg">
        <div className="grid grid-cols-12 gap-4">
          <div className="col col-span-8 flex flex-col items-center md:flex-row md:items-start md:gap-6">
            <div className="flex-shrink-0">
              <ImageViewer imageUrl={product.imgUrl} />
            </div>
            <div className="mt-4 flex-1 md:mt-0">
              <h1 className="mb-4 text-center text-3xl font-bold text-gray-800 md:text-left">
                {product.name}
              </h1>
              <p className="text-center text-gray-600 md:text-left">
                {product.description}
              </p>
              <p className="text-center text-gray-600 md:text-left">
                Created At: <DateAndTimeViewer date={product.createdAt} />
              </p>
              <p className="text-center text-gray-600 md:text-left">
                Updated At: <DateAndTimeViewer date={product.createdAt} />
              </p>
            </div>
          </div>
          <div className="col col-span-4">
            <div className="rounded-lg bg-gray-50 p-4 shadow-md">
              <h2 className="mb-2 text-lg font-semibold text-gray-700">
                Pricing
              </h2>
              <p>
                <strong>Serial No:</strong> {product.serialNo}
              </p>
              {userDetails?.type === 'CUSTOMER' ? (
                <p>
                  <strong>Purchase Price:</strong>{' '}
                  {product.currentPurchasePrice}
                </p>
              ) : (
                ''
              )}
              <p>
                <strong>Selling Price:</strong> {product.currentSellingPrice}
              </p>
              <p>
                <strong>Discount Price:</strong>{' '}
                {CalculateDiscountPrice({
                  retailPrice: product.currentSellingPrice,
                  discountType: product.discountType,
                  discount: product.discount,
                })}
              </p>
              <p>
                <strong>VAT:</strong> {product.vat}%
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
        <div className="rounded-lg bg-white p-4 shadow-md">
          <h2 className="text-lg font-semibold text-gray-700">Total Stock</h2>
          <p className="text-gray-600">{product.totalStock ?? 0}</p>
        </div>
        <div className="rounded-lg bg-white p-4 shadow-md">
          <h2 className="text-lg font-semibold text-gray-700">
            Available Stock
          </h2>
          <p className="text-gray-600">{product.totalAvailableStock ?? 0}</p>
        </div>
        <div className="rounded-lg bg-white p-4 shadow-md">
          <h2 className="text-lg font-semibold text-gray-700">Total Sold</h2>
          <p className="text-gray-600">{product.totalSold ?? 0}</p>
        </div>
        <div className="rounded-lg bg-white p-4 shadow-md">
          <h2 className="text-lg font-semibold text-gray-700">
            Previous Unsold Stock
          </h2>
          <p className="text-gray-600">
            {product.previousUnsoldStockCount ?? 0}
          </p>
        </div>
        <div className="rounded-lg bg-white p-4 shadow-md">
          <h2 className="text-lg font-semibold text-gray-700">
            This Month Entry
          </h2>
          <p className="text-gray-600">
            {product.currentMonthStockEntryCount ?? 0}
          </p>
        </div>
        <div className="rounded-lg bg-white p-4 shadow-md">
          <h2 className="text-lg font-semibold text-gray-700">
            This Month Sold
          </h2>
          <p className="text-gray-600">
            {product.thisMonthStockSoldCount ?? 0}
          </p>
        </div>
      </div>

      <div className="mt-8 grid grid-cols-1 gap-8 md:grid-cols-2">
        <ProfitPieChart data={stockLevelData?.data ?? []} />
        <TopVariantDonutChart data={top5SellingVariants?.data ?? []} />
      </div>

      <div className="mt-6">
        <Tabs defaultValue="account" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="account">Orders</TabsTrigger>
            <TabsTrigger value="password">Stocks</TabsTrigger>
            <TabsTrigger value="variants">Variants</TabsTrigger>
          </TabsList>
          <TabsContent value="account">
            <div className="mb-20 mt-6">
              {!isOrderDataLoading ? (
                <div>
                  <OrderListViewer
                    orders={orderData?.data}
                    total={orderData?.pagination.total}
                    shopType={type}
                    warehouseId={shopDetails?.warehouseId}
                  />
                  <div className="pagination-box flex justify-end rounded bg-white p-3">
                    <Pagination
                      currentPage={page ?? '1'}
                      limit={Number(limit ?? 10)}
                      totalCount={orderData?.pagination?.total}
                      totalPages={Math.ceil(
                        Number(orderData?.pagination?.total) /
                          Number(orderData?.pagination?.limit),
                      )}
                    />
                  </div>
                </div>
              ) : (
                <TableSkeletonLoader tableColumn={14} tableRow={6} />
              )}
            </div>
          </TabsContent>
          <TabsContent value="password">
            <div className="mb-20 mt-6">
              {!isStockDataLoading ? (
                <div>
                  <div className="tableTop w-full">
                    <p>Stock List</p>
                    <div className="flex items-center gap-2">
                      <p>Total : {stockData?.pagination?.total}</p>
                    </div>
                  </div>
                  <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
                    {stockData?.data?.length ? (
                      <div>
                        <div className="full-table-box h-custom">
                          <table className="full-table">
                            <thead className="bg-gray-100">
                              <tr>
                                <th className="tableHead">Barcode</th>
                                <th className="tableHead">ID</th>
                                <th className="tableHead table-col-width">
                                  Name
                                </th>
                                <th className="tableHead">Stock Location</th>
                                {userDetails?.type === 'CUSTOMER' ? (
                                  <th className="tableHead">TP</th>
                                ) : (
                                  ''
                                )}
                                <th className="tableHead">Wholesale Price</th>
                                <th className="tableHead">Regular Price</th>
                                <th className="tableHead">Discount Price</th>
                                <th className="tableHead">VAT</th>
                                <th className="tableHead">Status</th>
                                <th className="tableHead">Supplier</th>
                                <th className="tableHead">Created At</th>
                                <th className="tableHead">Actions</th>
                              </tr>
                            </thead>
                            <tbody className="divide-y">
                              {stockData?.data?.map(
                                (stock: SingleStockDetails) => (
                                  <tr
                                    key={stock?.id}
                                    className={
                                      stock?.isSold
                                        ? 'tableRowRed'
                                        : stock?.isHold
                                          ? 'tableRowYellow'
                                          : 'tableRowGreen'
                                    }
                                  >
                                    <td className="tableData">
                                      {stock?.barcode}
                                    </td>
                                    <td className="tableData">
                                      {stock?.Product?.serialNo ?? 0}
                                    </td>
                                    <td className="tableData table-col-width">
                                      <StockName
                                        stock={stock}
                                        name={stock.name}
                                      />
                                    </td>
                                    <td className="tableData">
                                      {stock?.AssignedShop?.name ?? 'Warehouse'}
                                    </td>
                                    {userDetails?.type === 'CUSTOMER' ? (
                                      <td className="tableData">
                                        {stock?.purchasePrice}
                                      </td>
                                    ) : (
                                      ''
                                    )}
                                    <td className="tableData">
                                      {stock?.wholesalePrice}
                                    </td>
                                    <td className="tableData">
                                      {stock?.retailPrice}
                                    </td>
                                    <td className="tableData">
                                      {CalculateDiscountPrice({
                                        retailPrice: stock?.retailPrice,
                                        discountType: stock.discountType,
                                        discount: stock.discount,
                                      })}
                                    </td>
                                    <td className="tableData">{stock?.vat}%</td>
                                    <td className="tableData">
                                      {stock?.isHold
                                        ? 'Hold'
                                        : stock?.isSold
                                          ? 'Sold'
                                          : 'Available'}
                                    </td>
                                    <td className="tableData">
                                      {stock?.Supplier?.User?.name}
                                    </td>
                                    <td className="tableData">
                                      <DateAndTimeViewer
                                        date={stock?.createdAt}
                                      />
                                    </td>
                                    <td className="tableData">
                                      <div className="flex items-center justify-center gap-2">
                                        {!stock?.isSold ? (
                                          <BarcodeButton
                                            handleClick={() => {
                                              // const encodedURL = `/single-stock-barcode/?data=${encodedData(stock)}`;
                                              // window.open(encodedURL, '_blank'); // Opens the URL in a new tab
                                            }}
                                          />
                                        ) : (
                                          ''
                                        )}
                                        <TransparentEditButton
                                          handleClick={() => {
                                            toast.warning(
                                              'you clicked on edit button no action set',
                                            );
                                          }}
                                        />
                                      </div>
                                    </td>
                                  </tr>
                                ),
                              )}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    ) : (
                      <div>
                        <h2>Not found</h2>
                      </div>
                    )}
                  </div>
                  <div className="pagination-box flex justify-end rounded bg-white p-3">
                    <Pagination
                      currentPage={page ?? '1'}
                      limit={Number(limit ?? 10)}
                      totalCount={stockData?.pagination?.total}
                    />
                  </div>
                </div>
              ) : (
                <TableSkeletonLoader tableColumn={12} tableRow={6} />
              )}
            </div>
          </TabsContent>
          <TabsContent value="variants">
            <div className="mb-20 mt-6">
              {!isTopSellingVariantsLoading ? (
                <div>
                  <div className="tableTop w-full">
                    <p>Variants List</p>
                    <div className="flex items-center gap-2">
                      <p>Total : {topSellingVariants?.pagination?.total}</p>
                    </div>
                  </div>
                  <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
                    {topSellingVariants?.data?.length ? (
                      <div>
                        <div className="full-table-box h-custom">
                          <table className="full-table">
                            <thead className="bg-gray-100">
                              <tr>
                                <th className="tableHead">Name</th>
                                <th className="tableHead">Total Sold</th>
                              </tr>
                            </thead>
                            <tbody className="divide-y">
                              {topSellingVariants?.data?.map((stock: any) => (
                                <tr key={stock?.variantId}>
                                  <td className="tableData">{stock?.name}</td>
                                  <td className="tableData">
                                    {stock.totalSold}
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    ) : (
                      <div>
                        <h2>Not found</h2>
                      </div>
                    )}
                  </div>
                  <div className="pagination-box flex justify-end rounded bg-white p-3">
                    <Pagination
                      currentPage={page ?? '1'}
                      limit={Number(limit ?? 10)}
                      totalCount={topSellingVariants?.pagination?.total}
                    />
                  </div>
                </div>
              ) : (
                <TableSkeletonLoader tableColumn={12} tableRow={6} />
              )}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}

export default ProductDetailsPageOverview;
