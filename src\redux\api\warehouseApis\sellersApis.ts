import { TagTypes } from '@/redux/tag-types';
import { GetSellersResponse } from '@/types/warehouseTypes/sellersTypes';
import BaseApi from '../baseApi';

interface GetSellersParams {
  warehouseId?: string;
  shopId?: string;
  organizationId?: string;
  role?: string;
  page?: string;
  limit?: string;
  name?: string;
  mobileNumber?: string;
}

const SellersApi = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getSellers: builder.query<GetSellersResponse, GetSellersParams>({
      query: (params) => ({
        url: '/employee',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SELLERS],
    }),
    createSeller: builder.mutation({
      query: (data) => ({
        url: '/employee/new',
        method: 'POST',
        data,
      }),
      invalidatesTags: [TagTypes.SELLERS],
    }),
    updateSeller: builder.mutation({
      query: ({ data, id }) => ({
        url: `/employee/${id}`,
        method: 'PATCH',
        data,
      }),
      invalidatesTags: [TagTypes.SELLERS],
    }),
    assignEmployeeToShop: builder.mutation({
      query: ({ data, id }) => ({
        url: `/employee/${id}`,
        method: 'PUT',
        data,
      }),
      invalidatesTags: [TagTypes.SELLERS],
    }),
    blockOrUnblockEmployee: builder.mutation({
      query: ({ data, id }) => ({
        url: `/employee/${id}`,
        method: 'PATCH',
        data,
      }),
      invalidatesTags: [TagTypes.SELLERS],
    }),
    deleteSeller: builder.mutation({
      query: (id) => ({
        url: `/employee/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: [TagTypes.SELLERS],
    }),
  }),
});

export const {
  useGetSellersQuery,
  useCreateSellerMutation,
  useUpdateSellerMutation,
  useAssignEmployeeToShopMutation,
  useBlockOrUnblockEmployeeMutation,
  useDeleteSellerMutation,
} = SellersApi;
