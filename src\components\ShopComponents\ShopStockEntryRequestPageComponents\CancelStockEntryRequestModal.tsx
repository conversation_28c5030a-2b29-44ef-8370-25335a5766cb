import { useState } from 'react';
import { toast } from 'react-toastify';

import FilledButton from '@/components/reusable/Buttons/FilledButton';
import ModalTitle from '@/components/reusable/Modal/ModalTitle';
import { useAcceptStockEntryMutation } from '@/redux/api/shopApis/shopStockApis';

interface Props {
  handleClose: () => void;
  entryId: string;
  shopId: string;
  closeParent?: boolean;
  handleCloseParent?: () => void;
}

function CancelStockEntryRequestModal({
  handleClose,
  entryId,
  shopId,
  closeParent,
  handleCloseParent,
}: Props) {
  const [acceptStockEntry] = useAcceptStockEntryMutation();
  const [reason, setReason] = useState('');
  const isLoading = false;
  const handleSubmit = () => {
    toast.promise(
      acceptStockEntry({
        id: entryId,
        data: {
          shouldApproved: false,
          shopId,
          reason,
        },
      }).unwrap(),
      {
        pending: 'Rejecting Stock Entry...',
        success: {
          render({ data: res }) {
            if (res?.statusCode === 200 || res?.statusCode === 201) {
              handleClose();
              if (closeParent && handleCloseParent) {
                handleCloseParent();
              }
            }
            return 'Stock Entry Rejected Successfully';
          },
        },
        error: {
          render({ data: error }) {
            console.log(error);
            return 'Error on reject stock entry';
          },
        },
      },
    );
  };
  return (
    <div className="flex w-[400px] flex-col gap-4 rounded-xl bg-white p-4">
      <ModalTitle text="Cancel Stock Entry" handleClose={handleClose} />
      <div className="group relative z-0 mt-[-20px] w-full">
        <span className="relative left-3 top-2.5 w-auto bg-white px-1 font-mono text-[12px] font-bold text-gray-900 group-focus-within:text-red-600 dark:text-gray-300">
          Cancel Reason
        </span>
        <textarea
          aria-multiline
          className="text-10 py-55-rem block h-20 w-full rounded-lg border bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 disabled:cursor-not-allowed disabled:bg-gray-100 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
          value={reason}
          onChange={(e) => setReason(e.target.value)}
          placeholder="Please Enter a cancel reason"
        />
      </div>
      <div className="mt-[10px] flex w-full items-center justify-center">
        <FilledButton
          isLoading={isLoading}
          text="Cancel Entry"
          handleClick={() => handleSubmit()}
          isDisabled={isLoading || reason.length === 0}
        />
      </div>
    </div>
  );
}

export default CancelStockEntryRequestModal;
