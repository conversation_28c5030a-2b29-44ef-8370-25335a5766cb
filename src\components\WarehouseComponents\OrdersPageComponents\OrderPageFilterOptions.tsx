import { useLocation } from 'react-router-dom';

import CustomDateFilterInput from '@/components/reusable/Inputs/CustomDateFilterInput';
import CustomSelectForFilter from '@/components/reusable/Inputs/CustomSelectForFilter';
import SearchInput from '@/components/reusable/Inputs/SearchInput';
import { statusList } from '@/utils/staticData';

interface Props {
  handleFilter: (fieldName: string, value: string) => void;
}

function OrderPageFilterOptions({ handleFilter }: Props) {
  const router = new URLSearchParams(useLocation().search);
  const customerId = router.get('customerId');
  const customerName = router.get('customerName');
  const mobileNumber = router.get('mobileNumber');
  const serialNo = router.get('serialNo');
  const orderStatus = router.get('orderStatus');
  const paymentStatus = router.get('paymentStatus');
  const hasDue = router.get('hasDue');
  const invoiceNo = router.get('invoiceNo');
  const startDate = router.get('startDate');
  const endDate = router.get('endDate');

  return (
    // <div className="flex flex-col gap-3 md:flex-row md:gap-4">
    <>
      <div className="grid grid-cols-1 gap-3 p-2 md:grid-cols-2 md:gap-4 lg:grid-cols-5">
        <SearchInput
          placeholder="Search by Name"
          handleSubmit={(value: string) => handleFilter('customerName', value)}
          value={customerName ?? ''}
        />
        <SearchInput
          placeholder="Search by Mobile"
          handleSubmit={(value: string) => handleFilter('mobileNumber', value)}
          value={mobileNumber ?? ''}
        />
        <SearchInput
          placeholder="Search by Order Id"
          handleSubmit={(value: string) => handleFilter('serialNo', value)}
          value={serialNo ?? ''}
        />
        <SearchInput
          placeholder="Search customer Id"
          handleSubmit={(value: string) => handleFilter('customerId', value)}
          value={customerId ?? ''}
        />
        <SearchInput
          placeholder="invoice Id"
          handleSubmit={(value: string) => handleFilter('invoiceNo', value)}
          value={invoiceNo ?? ''}
        />
        <CustomSelectForFilter
          options={statusList}
          selectedValue={orderStatus ?? ''}
          handleSelect={(e) => handleFilter('orderStatus', e)}
          placeHolder="Status"
        />
        <CustomSelectForFilter
          options={[
            { value: 'true', label: 'With Due' },
            { value: 'false', label: 'Without Due' },
          ]}
          selectedValue={hasDue ?? ''}
          handleSelect={(e) => handleFilter('hasDue', e)}
          placeHolder="With Due"
        />
        <CustomSelectForFilter
          options={[
            { value: 'Paid', label: 'Paid' },
            { value: 'Unpaid', label: 'Unpaid' },
          ]}
          selectedValue={paymentStatus ?? ''}
          handleSelect={(e) => handleFilter('paymentStatus', e)}
          placeHolder="Payment Status"
        />
        <CustomDateFilterInput
          value={startDate ?? ''}
          placeholder="Select Start Date"
          label="Start Date"
          handleChange={(value: string) => handleFilter('startDate', value)}
        />
        <CustomDateFilterInput
          value={endDate ?? ''}
          placeholder="Select Start Date"
          label="End Date"
          handleChange={(value: string) => handleFilter('endDate', value)}
          minimumDate={startDate ?? ''}
        />
      </div>
    </>
  );
}

export default OrderPageFilterOptions;
