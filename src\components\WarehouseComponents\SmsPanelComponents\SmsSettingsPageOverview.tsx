import Cookies from 'js-cookie';
import { useState } from 'react';
import { toast } from 'react-toastify';

import ElitbuzzSmsConfigModal from './ElitbuzzSmsConfigModal';

import {
  DeleteButton,
  EditButton,
} from '@/components/reusable/Buttons/CommonButtons';
import FilledButton from '@/components/reusable/Buttons/FilledButton';
import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import Modal from '@/components/reusable/Modal/Modal';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import { useGetSmsConfigListQuery } from '@/redux/api/warehouseApis/smsApis';

function SmsSettingsPageOverview() {
  const [isAddSmsConfigModalOpen, setIsAddSmsConfigModalOpen] =
    useState<boolean>(false);
  const { data, isLoading, refetch } = useGetSmsConfigListQuery({
    organizationId: Cookies.get('organizationId'),
  });
  return (
    <div>
      <div className="tableTop w-full">
        <p>SMS Configuration</p>
        <FilledButton
          isLoading={false}
          text="Configure SMS Panel"
          handleClick={() => setIsAddSmsConfigModalOpen(true)}
          isDisabled={false}
        />
      </div>
      {!isLoading ? (
        <div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {data?.data?.length ? (
              <div className="full-table-box h-custom">
                <table className="full-table">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="tableHead">No</th>
                      <th className="tableHead">Name</th>
                      <th className="tableHead">Api Key</th>
                      <th className="tableHead">Secret Key</th>
                      <th className="tableHead">Created At</th>
                      <th className="tableHead">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y bg-slate-200">
                    <tr>
                      <td className="tableData">Will Be Added</td>
                      <td className="tableData">Will Be Added</td>
                      <td className="tableData">Will Be Added</td>
                      <td className="tableData">Will Be Added</td>
                      <td className="tableData">
                        <DateAndTimeViewer date={new Date().toDateString()} />
                      </td>
                      <td className="tableData">
                        <div className="flex items-center justify-center gap-2">
                          <EditButton
                            handleClick={() => {
                              toast.warning('clicked');
                            }}
                          />
                          <DeleteButton
                            handleClick={() => {
                              toast.warning('clicked');
                            }}
                          />
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            ) : (
              <div>
                <h2>Not Configured</h2>
              </div>
            )}
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={6} tableRow={6} />
      )}
      <Modal
        setShowModal={setIsAddSmsConfigModalOpen}
        showModal={isAddSmsConfigModalOpen}
      >
        <ElitbuzzSmsConfigModal
          type="new"
          handleClose={() => setIsAddSmsConfigModalOpen(false)}
          updateRefreshCounter={refetch}
        />
      </Modal>
    </div>
  );
}

export default SmsSettingsPageOverview;
