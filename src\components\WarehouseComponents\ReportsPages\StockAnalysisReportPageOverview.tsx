import { TransparentPrintButtonTop } from '@/components/reusable/Buttons/CommonButtons';
import ImageViewer from '@/components/reusable/ImageViewer/ImageViewer';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import { useGetWarehouseStockAnalysisReportQuery } from '@/redux/api/warehouseApis/warehouseReportsApis';
import { SingleProductDataOnDetails } from '@/types/warehouseTypes/productTypes';
import { CalculateDiscountPrice } from '@/utils/CalculateDiscountPrice';
import { handleGenerateStockAnalysisPdf } from '@/utils/GenerateReportPdf';

interface Props {
  warehouseId: string;
}

function StockAnalysisReportPageOverview({ warehouseId }: Props) {
  const { data, isLoading } = useGetWarehouseStockAnalysisReportQuery({
    warehouseId,
  });

  return (
    <div>
      {!isLoading ? (
        <div>
          <div className="tableTop w-full">
            <p>Products Stock Analysis</p>
            <TransparentPrintButtonTop
              handleClick={() => {
                handleGenerateStockAnalysisPdf(
                  data?.data?.result ?? [],
                  data?.data?.warehouse,
                );
              }}
            />
          </div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {data?.data ? (
              <div className="full-table-box h-custom3">
                <table className="full-table">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="tableHead">Image</th>
                      <th className="tableHead">ID</th>
                      <th className="tableHead table-col-width">Name</th>
                      <th className="tableHead">TP</th>
                      <th className="tableHead">Regular Price</th>
                      <th className="tableHead">Discount Price</th>
                      <th className="tableHead">Total </th>
                      <th className="tableHead">Available</th>
                      <th className="tableHead">Previous</th>
                      <th className="tableHead">This Month Entry</th>
                      <th className="tableHead">This Month Sold</th>
                      <th className="tableHead">Total Sold</th>
                      {/* <th className="tableHead">Created At</th> */}
                    </tr>
                  </thead>
                  <tbody className="divide-y bg-slate-200">
                    {data?.data?.result?.map(
                      (product: SingleProductDataOnDetails) => (
                        <tr key={product?.name}>
                          <td className="tableData">
                            <ImageViewer imageUrl={product?.imgUrl} />
                          </td>
                          <td className="tableData">
                            {product?.serialNo ?? 0}
                          </td>
                          <td className="tableData table-col-width">
                            {product?.name}
                          </td>
                          <td className="tableData">
                            {product?.currentPurchasePrice}
                          </td>
                          <td className="tableData">
                            {product?.currentSellingPrice}
                          </td>
                          <td className="tableData">
                            {CalculateDiscountPrice({
                              retailPrice: product?.currentSellingPrice,
                              discountType: product?.discountType,
                              discount: product?.discount,
                            })}
                          </td>
                          <td className="tableData">{product?.totalStock}</td>
                          <td className="tableData">
                            {product?.totalAvailableStock ?? 0}
                          </td>
                          <td className="tableData">
                            {Number(product?.totalAvailableStock) -
                              Number(product?.currentMonthStockEntryCount)}
                          </td>
                          <td className="tableData">
                            {product?.currentMonthStockEntryCount}
                          </td>
                          <td className="tableData">
                            {product?.thisMonthStockSoldCount}
                          </td>
                          <td className="tableData">
                            {product?.totalSold ?? 0}
                          </td>

                          {/* <td className="tableData">
                          <DateViewer date={product?.createdAt} />
                        </td> */}
                        </tr>
                      ),
                    )}
                  </tbody>
                </table>
              </div>
            ) : (
              <NoResultFound pageType="products" />
            )}
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={14} tableRow={6} />
      )}
    </div>
  );
}

export default StockAnalysisReportPageOverview;
