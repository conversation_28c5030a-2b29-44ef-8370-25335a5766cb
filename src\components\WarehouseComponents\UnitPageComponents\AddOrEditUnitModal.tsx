import { useFormik } from 'formik';
import { useEffect } from 'react';
import { toast } from 'react-toastify';
import * as Yup from 'yup';

import FilledSubmitButton from '../../reusable/Buttons/FilledSubmitButton';
import CustomInputField from '../../reusable/CustomInputField/CustomInputField';
import ModalTitle from '../../reusable/Modal/ModalTitle';

import {
  useCreateUnitMutation,
  useUpdateUnitMutation,
} from '@/redux/api/warehouseApis/unitsApi';
import { SingleUnitDetails } from '@/types/warehouseTypes/unitTypes';

interface Props {
  type: string;
  warehouseId: string | undefined;
  handleClose: () => void;
  updateRefreshCounter: () => void;
  unitData?: SingleUnitDetails;
}

const formikInitialValues = {
  name: '',
};

const validation = Yup.object({
  name: Yup.string().required('Unit name is required'),
});

function AddOrEditUnitModal({
  type,
  warehouseId,
  handleClose,
  updateRefreshCounter,
  unitData,
}: Props) {
  const [createUnit, { isLoading }] = useCreateUnitMutation();
  const [updateUnit, { isLoading: isUpdatingUnit }] = useUpdateUnitMutation();

  const formik = useFormik({
    initialValues: formikInitialValues,
    validationSchema: validation,

    onSubmit: async (values) => {
      if (type === 'new') {
        toast.promise(
          createUnit({
            name: values.name,
            warehouseId,
          }).unwrap(),
          {
            pending: 'Creating New Unit...',
            success: {
              render({ data: res }) {
                if (res?.statusCode === 200 || res?.statusCode === 201) {
                  updateRefreshCounter();
                  handleClose();
                }
                return 'Unit created Successfully';
              },
            },
            error: {
              render({ data: error }) {
                console.log(error);
                return 'Error on creating Unit';
              },
            },
          },
        );
      } else {
        toast.promise(
          updateUnit({
            data: { name: values.name },
            id: unitData?.id,
          }).unwrap(),
          {
            pending: 'Updating Unit...',
            success: {
              render({ data: res }) {
                if (res?.statusCode === 200 || res?.statusCode === 201) {
                  updateRefreshCounter();
                  handleClose();
                }
                return 'Unit updated Successfully';
              },
            },
            error: {
              render({ data: error }) {
                console.log(error);
                return 'Error on update Unit';
              },
            },
          },
        );
      }
    },
  });

  useEffect(() => {
    if (type === 'edit' && unitData) {
      formik.setFieldValue('name', unitData?.name);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [type, unitData]);

  return (
    <div className="flex w-[400px] flex-col gap-4 rounded-xl bg-white p-4">
      <ModalTitle
        text={type === 'new' ? 'Create Unit' : 'Edit Unit'}
        handleClose={handleClose}
      />
      <form onSubmit={formik.handleSubmit} className="w-full">
        <CustomInputField
          type="text"
          placeholder="Enter Unit Name"
          name="name"
          label="Name"
          formik={formik}
        />
        <div className="mt-[10px] flex w-full items-center justify-center">
          <FilledSubmitButton
            text={type === 'new' ? 'Create Brand' : 'Done Editing'}
            isLoading={isLoading || isUpdatingUnit}
          />
        </div>
      </form>
    </div>
  );
}

export default AddOrEditUnitModal;
