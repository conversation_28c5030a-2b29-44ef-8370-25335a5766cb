import {
  EllipsisVertical,
  Eye,
  FileText,
  NotepadText,
  PencilLine,
  Printer,
  ShieldCheck,
  Truck,
  Undo2,
  Wallet,
  X,
} from 'lucide-react';
import { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

import ExportButton from '../Buttons/ExportButton';
import DateAndTimeViewer from '../DateAndTimeViewer/DateAndTimeViewer';
import Modal from '../Modal/Modal';
import NoResultFound from '../NoResultFound/NoResultFound';
import EditPaymentMethodModal from '../OrderPaymentsComponents/EditPaymentMethodModal';
import PdfGeneratingLoader from '../PdfGeneratingLoader/PdfGeneratingLoader';

import AddOrderNoteModal from './AddOrderNoteModal';
import CancelOrderModal from './CancelOrderModal';
import FraudCheckModal from './FraudCheckModal';
import OrderStatusViewer from './OrderStatusViewer';

import ShopCourierBookingModal from '@/components/ShopComponents/ShopCourierBooking/ShopCourierBookingModal';
import ShopOrderProductReturnModal from '@/components/ShopComponents/ShopOrdersPageComponents/ShopOrderProductReturnModal';
import {
  Menubar,
  MenubarContent,
  MenubarItem,
  MenubarMenu,
  MenubarSeparator,
  MenubarTrigger,
} from '@/components/ui/menubar';
import OrderDueCollectionModal from '@/components/WarehouseComponents/OrdersPageComponents/OrderDueCollectionModal';
import { useUpdateSingleOrderMutation } from '@/redux/api/shopApis/shopOrdersApis';
import { useAppSelector } from '@/redux/hooks';
import { ROUTES } from '@/Routes';
import {
  OrderSinglePaymentDetails,
  ShopOrderDetails,
} from '@/types/shopTypes/shopOrderTypes';
import { formatNumberWithComma } from '@/utils/formatNumberWithComma';
import { handleGenerateSalesSummaryCsv } from '@/utils/GenerateCsv';
import {
  handleGenerateBulkOrderPdf,
  handleGenerateSingleOrderPdf,
  handleGenerateSingleOrderPosPdf,
} from '@/utils/GenerateOrderPdf';

interface Props {
  orders?: ShopOrderDetails[];
  total?: number;
  shopType: 'WAREHOUSE' | 'SHOP';
  warehouseId?: string;
}
function OrderListViewer({ orders, total, shopType, warehouseId = '' }: Props) {
  const navigate = useNavigate();
  // const router = new URLSearchParams(useLocation().search);
  // const page = router.get('page');
  // const limit = router.get('limit');
  const { shopSettings } = useAppSelector((state) => state);
  const [addNoteToOrder] = useUpdateSingleOrderMutation();

  // states
  const [isCourierBookingModalOpen, setIsCourierBookingModalOpen] =
    useState<boolean>(false);
  const [courierBookingOrderDetails, setCourierBookingOrderDetails] =
    useState<ShopOrderDetails>();

  const [isProductReturnModalOpen, setIsProductReturnModalOpen] =
    useState<boolean>(false);
  const [selectedOrderForReturn, setSelectedOrderForReturn] =
    useState<ShopOrderDetails>();
  const [isCollectDueModalOpen, setIsCollectDueModalOpen] =
    useState<boolean>(false);
  const [isCCancelOrderModalOpen, setIsCCancelOrderModalOpen] =
    useState<boolean>(false);
  const [dueCollectionOrderDetails, setDueCollectionOrderDetails] =
    useState<ShopOrderDetails>();
  const [
    isUpdateOrderPaymentMethodModalOpe,
    setIsUpdateOrderPaymentMethodModalOpe,
  ] = useState(false);
  const [selectedPayment, setSelectedPayment] =
    useState<OrderSinglePaymentDetails>();
  const [isAddOrderNoteModalOpen, setIsAddOrderNoteModalOpen] =
    useState<boolean>(false);

  const [isFraudCheckModalOpen, setIsFraudCheckModalOpen] =
    useState<boolean>(false);
  const [fraudCheckMobileNumber, setFraudCheckMobileNumber] =
    useState<string>('');
  const [isPdfGenerating, setIsPdfGenerating] = useState<boolean>(false);

  const handleUpdateInvoiceStatus = async (id: string) => {
    toast.promise(
      addNoteToOrder({
        orderId: id,
        data: {
          isInvoicePrinted: true,
        },
      }).unwrap(),
      {
        pending: 'Updating Invoice Status',
        success: {
          render() {
            return 'Marked As Printed';
          },
        },
        error: {
          render() {
            return 'Error on update';
          },
        },
      },
    );
  };

  const [selectedOrderList, setSelectedOrderList] =
    useState<ShopOrderDetails[]>();
  const handleSelectOrder = (order: ShopOrderDetails) => {
    const isExist = selectedOrderList?.some(
      (sin: ShopOrderDetails) => sin.id === order.id,
    );
    if (!isExist) {
      setSelectedOrderList([...(selectedOrderList ?? []), order]);
    } else {
      setSelectedOrderList(
        selectedOrderList?.filter(
          (sin: ShopOrderDetails) => sin.id !== order.id,
        ),
      );
    }
  };

  const handleSelectOrUnselectAll = () => {
    if (selectedOrderList?.length === orders?.length) {
      setSelectedOrderList([]);
    } else {
      setSelectedOrderList(orders);
    }
  };
  return (
    <div>
      <div className="tableTop w-full">
        <p>Order List</p>
        <div className="flex items-center gap-4">
          {selectedOrderList?.length ? (
            <button
              type="button"
              onClick={() =>
                handleGenerateBulkOrderPdf(
                  selectedOrderList,
                  setIsPdfGenerating,
                )
              }
              className="flex items-center gap-1 rounded border-2 border-slate-200 px-2 py-1 text-xs font-bold text-white"
            >
              <Printer size={16} />
              <span>{selectedOrderList?.length}</span>
            </button>
          ) : (
            ''
          )}
          <p>Total : {total}</p>
          <div className="ml-4">
            <ExportButton
              totalCount={orders?.length ?? 0}
              handleExportCsv={() =>
                handleGenerateSalesSummaryCsv({
                  orderList: orders,
                  startDate: '',
                  endDate: '',
                })
              }
              // handleExportPdf={() => handleGenerateSalesReportPdf(orders)}
            />
          </div>
        </div>
      </div>
      <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
        {orders?.length ? (
          <div className="full-table-box h-orderTable">
            <table className="full-table">
              <thead className="bg-gray-100">
                <tr>
                  <th className="tableHead">
                    <input
                      type="checkbox"
                      onClick={handleSelectOrUnselectAll}
                      onChange={() => {}}
                      checked={selectedOrderList?.length === orders?.length}
                    />
                  </th>
                  {/* <th className="tableHead">#</th> */}
                  <th className="tableHead">Order No</th>

                  <th className="tableHead table-col-width">Name & Phone</th>
                  {/* <th className="tableHead">Phone</th> */}
                  {shopType === 'WAREHOUSE' ? (
                    <th className="tableHead">Shop</th>
                  ) : (
                    ''
                  )}
                  <th className="tableHead">Total</th>
                  <th className="tableHead">Paid</th>
                  <th className="tableHead">Due</th>
                  {/* {userDetails?.type === 'CUSTOMER' ? (
                    <th className="tableHead">Profit</th>
                  ) : (
                    ''
                  )} */}
                  <th className="tableHead">Biller & Seller</th>
                  {/* <th className="tableHead">Seller</th> */}
                  <th className="tableHead">Payments</th>
                  <th className="tableHead">Status</th>
                  <th className="tableHead">Notes</th>
                  <th className="tableHead">Created At</th>
                  <th className="tableHead">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y bg-slate-200">
                {orders?.map((order: ShopOrderDetails) => {
                  // const calculatedProfit = ProfitCalculator(order);

                  return (
                    <tr key={order?.id}>
                      <td className="tableData">
                        <input
                          type="checkbox"
                          onClick={() => handleSelectOrder(order)}
                          onChange={() => {}}
                          checked={selectedOrderList?.some(
                            (ord: ShopOrderDetails) => ord.id === order.id,
                          )}
                        />
                      </td>
                      {/* <td className="tableData">
                        {(Number(page ?? 1) - 1) * Number(limit ?? 10) +
                          index +
                          1}
                      </td> */}
                      <td className="tableData">{order?.serialNo}</td>
                      <td className="tableData table-col-width">
                        <div className="flex flex-col gap-2">
                          <Link
                            to={ROUTES.SHOP.CUSTOMER_DETAILS(
                              order?.shopId,
                              order.Customer.id,
                            )}
                            className="hover:text-blue-500 hover:underline"
                          >
                            {order?.customerName ?? order?.Customer.name}
                          </Link>
                          <Link
                            to={ROUTES.SHOP.CUSTOMER_DETAILS(
                              order?.shopId,
                              order.Customer.id,
                            )}
                            className="hover:text-blue-500 hover:underline"
                          >
                            {order?.customerMobileNumber ??
                              order?.Customer.mobileNumber}
                          </Link>
                        </div>
                      </td>
                      {/* <td className="tableData">
                        <Link
                          to={ROUTES.SHOP.CUSTOMER_DETAILS(
                            order?.shopId,
                            order.Customer.id,
                          )}
                          className="hover:text-blue-500 hover:underline"
                        >
                          {order?.customerMobileNumber ??
                            order?.Customer.mobileNumber}
                        </Link>
                      </td> */}
                      {shopType === 'WAREHOUSE' ? (
                        <td className="tableData">
                          {order?.Shop?.name} ({order.Shop?.nickName})
                        </td>
                      ) : (
                        ''
                      )}
                      <td className="tableData">
                        {formatNumberWithComma(
                          Number(order?.grandTotal) +
                            Number(order?.deliveryCharge),
                        )}
                      </td>
                      <td className="tableData">
                        {formatNumberWithComma(order?.totalPaid)}
                      </td>
                      <td className="tableData">
                        {formatNumberWithComma(order?.totalDue)}
                      </td>
                      {/* {userDetails?.type === 'CUSTOMER' ? (
                        <td className="tableData">
                          <span>
                            {formatNumberWithComma(calculatedProfit?.profit)}
                          </span>
                          <br />
                          <span
                            className={`${Number(calculatedProfit?.profitPercentage) < shopSettings?.profitMarginPercentage ? 'blink text-orange-600' : 'text-green-600'}`}
                          >
                            ({calculatedProfit?.profitPercentage}%)
                          </span>
                        </td>
                      ) : (
                        ''
                      )} */}
                      <td className="tableData">
                        <div className="flex flex-col gap-2">
                          <span>{order?.CreatedBy?.name ?? '--'}</span>
                          <span>{order?.Employee?.User?.name ?? '--'}</span>
                        </div>
                      </td>
                      {/* <td className="tableData" /> */}
                      <td className="tableData table-col-width">
                        {order?.Payment?.map(
                          (singlePayment: OrderSinglePaymentDetails) => (
                            <div
                              className="flex items-center justify-between"
                              key={singlePayment?.id}
                            >
                              <div>
                                {singlePayment?.paymentMethod} -{' '}
                                {singlePayment?.amount}
                              </div>
                              {shopSettings?.maximumPaymentMethodEditPolicy &&
                              singlePayment?.createdAt &&
                              new Date().getTime() -
                                new Date(singlePayment.createdAt).getTime() <=
                                shopSettings.maximumPaymentMethodEditPolicy *
                                  24 *
                                  60 *
                                  60 *
                                  1000 ? (
                                <button
                                  type="button"
                                  onClick={() => {
                                    setIsUpdateOrderPaymentMethodModalOpe(true);
                                    setSelectedPayment(singlePayment);
                                  }}
                                  className="commonActionButton watchButton"
                                >
                                  <PencilLine
                                    size={16}
                                    className="watchButtonIcon"
                                  />
                                </button>
                              ) : (
                                ''
                              )}
                            </div>
                          ),
                        )}
                      </td>
                      <td className="tableData">
                        <div className="flex flex-col gap-2">
                          <OrderStatusViewer status={order.orderStatus} />
                          <OrderStatusViewer status={order.paymentStatus} />
                          {/* {userDetails?.shopType === 'ONLINE' ? (
                            <>
                              {order?.isInvoicePrinted ? (
                                <p>
                                  {order?.isInvoicePrinted ? 'PRINTED' : ''}
                                </p>
                              ) : (
                                <button
                                  type="button"
                                  onClick={() =>
                                    handleUpdateInvoiceStatus(order?.id)
                                  }
                                  disabled={isLoading}
                                >
                                  Mark As Printed
                                </button>
                              )}
                              <OrderStatusViewer status={order.paymentStatus} />
                            </>
                          ) : (
                            ''
                          )} */}
                        </div>
                      </td>
                      <td
                        className="tableDataLeftAlign"
                        title={order?.orderNotes
                          ?.map((note) => note.notes)
                          ?.join(', ')}
                      >
                        <p>
                          {order?.orderNotes?.length
                            ? `${order?.orderNotes[0]?.notes?.slice(0, 10)}...`
                            : ''}
                        </p>
                      </td>
                      <td className="tableData">
                        <DateAndTimeViewer date={order?.createdAt} />
                      </td>
                      <td className="tableData">
                        <div className="flex items-center justify-center">
                          <Menubar
                            style={{
                              border: 'none',
                              backgroundColor: 'transparent',
                            }}
                          >
                            <MenubarMenu>
                              <MenubarTrigger className="cursor-pointer">
                                <EllipsisVertical />
                              </MenubarTrigger>
                              <MenubarContent
                                style={{
                                  marginRight: '25px',
                                  borderColor: 'black',
                                }}
                              >
                                <MenubarItem
                                  onClick={() =>
                                    navigate(
                                      shopType === 'SHOP'
                                        ? ROUTES.SHOP.ORDER_DETAILS(
                                            order?.shopId,
                                            order?.id,
                                          )
                                        : ROUTES.WAREHOUSE.ORDER_DETAILS(
                                            warehouseId,
                                            order?.id,
                                          ),
                                    )
                                  }
                                  className="flex cursor-pointer items-center gap-1 bg-primary font-semibold text-white"
                                >
                                  <Eye size={20} />
                                  <span>View Details</span>
                                </MenubarItem>

                                <MenubarSeparator />
                                {order?.totalDue > 0 ? (
                                  <>
                                    <MenubarItem
                                      onClick={() => {
                                        setDueCollectionOrderDetails(order);
                                        setIsCollectDueModalOpen(true);
                                      }}
                                      className="flex cursor-pointer items-center gap-1 bg-primary font-semibold text-white"
                                    >
                                      <Wallet size={20} />
                                      <span>Collect Due</span>
                                    </MenubarItem>
                                    <MenubarSeparator />
                                  </>
                                ) : (
                                  ''
                                )}

                                {!order?.trackingNumber &&
                                order.Shop.type === 'ONLINE' ? (
                                  <>
                                    <MenubarItem
                                      onClick={() => {
                                        setFraudCheckMobileNumber(
                                          order.customerMobileNumber,
                                        );
                                        setIsFraudCheckModalOpen(true);
                                      }}
                                      className="flex cursor-pointer items-center gap-1 bg-primary font-semibold text-white"
                                    >
                                      <ShieldCheck size={20} />
                                      <span>Fraud Check</span>
                                    </MenubarItem>
                                    <MenubarSeparator />
                                  </>
                                ) : (
                                  ''
                                )}
                                {!order?.trackingNumber &&
                                order.Shop.type === 'ONLINE' &&
                                order.orderStatus === 'PENDING' ? (
                                  <>
                                    <MenubarItem
                                      onClick={() => {
                                        setCourierBookingOrderDetails(order);
                                        setIsCourierBookingModalOpen(true);
                                      }}
                                      className="flex cursor-pointer items-center gap-1 bg-primary font-semibold text-white"
                                    >
                                      <Truck size={20} />
                                      <span>Book On Courier</span>
                                    </MenubarItem>
                                    <MenubarSeparator />
                                  </>
                                ) : (
                                  ''
                                )}
                                <MenubarItem
                                  onClick={() => {
                                    setDueCollectionOrderDetails(order);
                                    setIsAddOrderNoteModalOpen(true);
                                  }}
                                  className="flex cursor-pointer items-center gap-1 bg-primary font-semibold text-white"
                                >
                                  <NotepadText size={20} />
                                  <span>Add Note</span>
                                </MenubarItem>
                                <MenubarSeparator />
                                <MenubarItem
                                  onClick={() => {
                                    handleGenerateSingleOrderPdf(
                                      order,
                                      shopSettings.returnPolicyText ?? '',
                                    );
                                    if (!order?.isInvoicePrinted) {
                                      handleUpdateInvoiceStatus(order?.id);
                                    }
                                  }}
                                  className="flex cursor-pointer items-center gap-1 bg-primary font-semibold text-white"
                                >
                                  <FileText size={20} />
                                  <span>Print A4 Invoice</span>
                                </MenubarItem>
                                <MenubarSeparator />
                                <MenubarItem
                                  onClick={() => {
                                    handleGenerateSingleOrderPosPdf(
                                      order,
                                      shopSettings.returnPolicyText ?? '',
                                    );
                                  }}
                                  className="flex cursor-pointer items-center gap-1 bg-primary font-semibold text-white"
                                >
                                  <Printer size={20} />
                                  <span>Print POS Invoice</span>
                                </MenubarItem>

                                {order?.orderStatus !== 'RETURN' &&
                                order.orderStatus === 'DELIVERED' &&
                                shopSettings?.maximumOrderReturnPolicy &&
                                order?.createdAt &&
                                new Date().getTime() -
                                  new Date(order.createdAt).getTime() <=
                                  shopSettings.maximumOrderReturnPolicy *
                                    24 *
                                    60 *
                                    60 *
                                    1000 ? (
                                  <>
                                    <MenubarSeparator />
                                    <MenubarItem
                                      onClick={() => {
                                        setSelectedOrderForReturn(order);
                                        setIsProductReturnModalOpen(true);
                                      }}
                                      className="flex cursor-pointer items-center gap-1 bg-red-500 font-semibold text-white"
                                    >
                                      <Undo2 size={20} />
                                      <span>Return</span>
                                    </MenubarItem>
                                  </>
                                ) : (
                                  ''
                                )}
                                <MenubarSeparator />
                                {order?.orderStatus === 'PENDING' && (
                                  <MenubarItem
                                    onClick={() => {
                                      setDueCollectionOrderDetails(order);
                                      setIsCCancelOrderModalOpen(true);
                                    }}
                                    className="flex cursor-pointer items-center gap-1 bg-red-500 font-semibold text-white"
                                  >
                                    <X size={20} />
                                    <span>Cancel Order</span>
                                  </MenubarItem>
                                )}
                              </MenubarContent>
                            </MenubarMenu>
                          </Menubar>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        ) : (
          <NoResultFound pageType="order" />
        )}
      </div>
      <Modal
        showModal={isCourierBookingModalOpen}
        setShowModal={setIsCourierBookingModalOpen}
      >
        <ShopCourierBookingModal
          orderDetails={courierBookingOrderDetails}
          handleClose={() => setIsCourierBookingModalOpen(false)}
        />
      </Modal>
      <Modal
        showModal={isProductReturnModalOpen}
        setShowModal={setIsProductReturnModalOpen}
      >
        <ShopOrderProductReturnModal
          orderDetails={selectedOrderForReturn}
          handleClose={() => setIsProductReturnModalOpen(false)}
          shopId={selectedOrderForReturn?.shopId ?? ''}
        />
      </Modal>
      <Modal
        showModal={isCollectDueModalOpen}
        setShowModal={setIsCollectDueModalOpen}
      >
        <OrderDueCollectionModal
          orderDetails={dueCollectionOrderDetails}
          handleClose={() => setIsCollectDueModalOpen(false)}
        />
      </Modal>
      <Modal
        showModal={isUpdateOrderPaymentMethodModalOpe}
        setShowModal={setIsUpdateOrderPaymentMethodModalOpe}
      >
        <EditPaymentMethodModal
          paymentDetails={selectedPayment}
          handleClose={() => setIsUpdateOrderPaymentMethodModalOpe(false)}
        />
      </Modal>
      <Modal
        showModal={isCCancelOrderModalOpen}
        setShowModal={setIsCCancelOrderModalOpen}
      >
        <CancelOrderModal
          orderDetails={dueCollectionOrderDetails}
          handleClose={() => setIsCCancelOrderModalOpen(false)}
        />
      </Modal>
      <Modal
        showModal={isAddOrderNoteModalOpen}
        setShowModal={setIsAddOrderNoteModalOpen}
      >
        <AddOrderNoteModal
          orderDetails={dueCollectionOrderDetails}
          handleClose={() => setIsAddOrderNoteModalOpen(false)}
        />
      </Modal>
      <Modal
        showModal={isFraudCheckModalOpen}
        setShowModal={setIsFraudCheckModalOpen}
      >
        <FraudCheckModal
          phoneNumber={fraudCheckMobileNumber}
          handleClose={() => setIsFraudCheckModalOpen(false)}
        />
      </Modal>
      <Modal showModal={isPdfGenerating} setShowModal={setIsPdfGenerating}>
        <PdfGeneratingLoader />
      </Modal>
    </div>
  );
}

export default OrderListViewer;
