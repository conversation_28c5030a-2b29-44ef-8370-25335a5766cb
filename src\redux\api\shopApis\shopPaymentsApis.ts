import BaseApi from '../baseApi';

import { CreatedBy, Pagination } from '@/redux/commonTypes';
import { TagTypes } from '@/redux/tag-types';

interface GetShopPaymentsParams {
  organizationId?: string | null;
  shopId?: string | null;
  warehouseId?: string | null;
  page?: string;
  limit?: string;
  collectFrom?: string;
  serialNo?: string;
}

export interface GetPaymentsListResponse {
  success: boolean;
  message: string;
  statusCode: number;
  pagination: Pagination;
  data: SinglePaymentDetails[];
}

export interface SinglePaymentDetails {
  id: string;
  createdAt: string;
  collectFrom: string;
  purpose: string;
  updatedAt: string;
  amount: number;
  paymentMethod: string;
  orderId: string;
  createdById: string;
  CreatedBy: CreatedBy;
  Order: {
    id: string;
    serialNo: number;
  };
}

const ShopPaymentsApi = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getShopPayments: builder.query<
      GetPaymentsListResponse,
      GetShopPaymentsParams
    >({
      query: (params) => ({
        url: '/payment',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP_PAYMENTS],
    }),
    updateOrderPaymentMethod: builder.mutation({
      query: ({ data, id }) => ({
        url: `/order-payment/${id}`,
        method: 'PATCH',
        data,
      }),
      invalidatesTags: [TagTypes.SHOP_PAYMENTS, TagTypes.SHOP_ORDERS],
    }),
  }),
});

export const { useGetShopPaymentsQuery, useUpdateOrderPaymentMethodMutation } =
  ShopPaymentsApi;
