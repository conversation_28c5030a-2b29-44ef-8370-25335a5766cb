import { Pagination } from '@/redux/commonTypes';
import { SingleShopDetails } from './shopTypes';

export interface GetUserProfileResponse {
  success: boolean;
  message: string;
  statusCode: number;
  data: UserProfileData;
}

export interface UserProfileData {
  id: string;
  name: string;
  email: any;
  username: string;
  mobileNumber: string;
  imgUrl: any;
  organizationLimit: number;
  warehouseLimit: number;
  shopLimit: number;
  type: string;
  Employee: Employee;
}

export interface Employee {
  id: string;
  createdAt: string;
  updatedAt: string;
  lastSalary: number;
  currentSalary: number;
  userId: string;
  organizationId: string;
  serialNo: number;
  isActive: boolean;
  EmployeePermission: EmployeePermission[];
}

export interface EmployeePermission {
  id: string;
  createdAt: string;
  updatedAt: string;
  employeeId: string;
  assignedShopId: string;
  warehouseId: string;
  isActive: boolean;
  role: string;
  createdById: string;
  AssignedShop: SingleShopDetails;
  Warehouse: {
    id: string;
    name: string;
  };
}

export interface GetUserWarehouseAndShopPermissionsType {
  success: boolean;
  message: string;
  statusCode: number;
  data: WarehouseOrShopPermission[];
  pagination: Pagination;
}

export interface WarehouseOrShopPermission {
  role: string;
  permissionLevel: 'warehouse' | 'shop';
  warehouseId?: string;
  name: string;
  imgUrl?: string;
  nickName: string;
  type: string;
  shopId?: string;
}
