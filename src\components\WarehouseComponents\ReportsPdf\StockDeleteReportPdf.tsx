import { Page, Text, View } from '@react-pdf/renderer';

import PrintedTime from './PrintedTime';
import { reportPdfStyles } from './ReportPdfStyles';
import SoftwareMarketingOnPdf from './SoftwareMarketingOnPdf';

import { Warehouse } from '@/types/shopTypes/shopExpensesTypes';
import { SingleStockDeleteDetails } from '@/types/shopTypes/shopStockTransferReportTypes';

const products = [
  {
    id: 1,
    deleteDate: 'Oct 13, 2024, 07:01 PM',
    totalQuantity: 2,
    reason: '2wrong entry',
    deletedBy: '<PERSON><PERSON><PERSON> Rahman',
  },
];

interface Props {
  deleteList?: SingleStockDeleteDetails[];
  warehouse?: Warehouse;
}

function StockDeleteReportPdf({ deleteList, warehouse }: Props) {
  return (
    <Page size="A4" style={reportPdfStyles.page}>
      <View>
        <View style={reportPdfStyles.header}>
          <Text style={reportPdfStyles.userName}>{warehouse?.name}</Text>
          {/* <Text style={reportPdfStyles.address}>
            Tangail, Dhaka, Bangladesh
          </Text>
          <Text style={reportPdfStyles.phone}>01739719796</Text> */}
        </View>
        <PrintedTime />

        <View style={reportPdfStyles.table}>
          <View style={reportPdfStyles.tableRow}>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.StockDeleteCol,
                reportPdfStyles.StockDeleteNoCol,
              ]}
            >
              No
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.StockDeleteCol,
              ]}
            >
              Deleted By
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.StockDeleteCol,
              ]}
            >
              Reason
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.StockDeleteCol,
              ]}
            >
              Total Quantity
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.tableColLast,
                reportPdfStyles.StockDeleteCol,
              ]}
            >
              Delete Date
            </Text>
          </View>
          {deleteList?.map((product, index) => (
            <View
              key={product.id}
              style={
                index === products.length - 1
                  ? [reportPdfStyles.tableRowLast]
                  : [reportPdfStyles.tableRow]
              }
            >
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.StockDeleteCol,
                  reportPdfStyles.StockDeleteNoCol,
                ]}
              >
                {index + 1}
              </Text>
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.StockDeleteCol,
                ]}
              >
                {product.CreatedBy?.name}
              </Text>
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.StockDeleteCol,
                ]}
              >
                {product.reason}
              </Text>
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.StockDeleteCol,
                ]}
              >
                {product?.stockIds?.length}
              </Text>
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.StockDeleteCol,
                  reportPdfStyles.tableColLast,
                ]}
              >
                {new Intl.DateTimeFormat('en-US', {
                  day: '2-digit',
                  month: 'short',
                  year: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit',
                }).format(new Date(product.createdAt))}
              </Text>
            </View>
          ))}
        </View>

        <SoftwareMarketingOnPdf />
      </View>
    </Page>
  );
}

export default StockDeleteReportPdf;
