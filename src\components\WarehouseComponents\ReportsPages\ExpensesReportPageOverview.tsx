import { useLocation, useNavigate } from 'react-router-dom';

import ExportButton from '@/components/reusable/Buttons/ExportButton';
import FilterButton from '@/components/reusable/Buttons/FilterButton';
import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import CustomDateFilterInput from '@/components/reusable/Inputs/CustomDateFilterInput';
import CustomSelectForFilter from '@/components/reusable/Inputs/CustomSelectForFilter';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import { useGetShopsQuery } from '@/redux/api/shopApi';
import { useGetExpenseCategoriesQuery } from '@/redux/api/warehouseApis/expensesApis';
import { useGetWarehouseExpensesReportQuery } from '@/redux/api/warehouseApis/warehouseReportsApis';
import { useAppSelector } from '@/redux/hooks';
import { ROUTES } from '@/Routes';
import { SingleShopDetails } from '@/types/shopTypes';
import { ShopExpenseDetails } from '@/types/shopTypes/shopExpensesTypes';
import { generateFilterParams } from '@/utils/generateFilterParams';
import {
  handleGenerateExpensesReportCsv,
  handleGenerateExpensesReportPdf,
} from '@/utils/ReportExport/ExportExpenseReport';

interface Props {
  warehouseId: string;
}

function ExpensesReportPageOverview({ warehouseId }: Props) {
  const { warehouseDetails } = useAppSelector((state) => state);
  const navigate = useNavigate();
  const router = new URLSearchParams(useLocation().search);
  const expenseCategoryId = router.get('expenseCategoryId');
  const shopId = router.get('shopId');
  const startDate = router.get('startDate') || `${new Date().toISOString()}`;
  const endDate = router.get('endDate') || `${new Date().toISOString()}`;

  const { data, isLoading } = useGetWarehouseExpensesReportQuery({
    warehouseId,
    type: 'custom',
    startDate,
    endDate,
    expenseCategoryId: expenseCategoryId ?? null,
    shopId: shopId ?? null,
  });

  const { data: expenseCategories, isLoading: isExpenseCategoriesLoading } =
    useGetExpenseCategoriesQuery({
      warehouseId,
    });

  const { data: shopData, isLoading: isShopListLoading } = useGetShopsQuery({
    warehouseId,
    isFromStockTransfer: false,
  });

  const handleFilter = (fieldName: string, value: string) => {
    const query = generateFilterParams(fieldName, value);
    navigate(ROUTES.WAREHOUSE.EXPENSES_REPORT(warehouseId, query));
  };

  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-1">
        <div className="flex items-center gap-x-2">
          <div className="search-title-and-btn flex items-center gap-x-3">
            {/* <p className="whitespace-nowrap">Search Filters</p> */}
            <div className="relative">
              <div className="block xl:hidden">
                <FilterButton handleClick={() => console.log('higbig')} />
              </div>
              <div className="block xl:hidden">
                {/* <ProductPageFilterModal /> */}
              </div>
            </div>
          </div>
          <div className="hidden xl:block">
            <div className="flex items-center gap-x-2">
              <CustomDateFilterInput
                value={startDate}
                placeholder="Select Start Date"
                label="Start Date"
                handleChange={(value: string) =>
                  handleFilter('startDate', value)
                }
              />
              <CustomDateFilterInput
                value={endDate}
                placeholder="Select Start Date"
                label="End Date"
                handleChange={(value: string) => handleFilter('endDate', value)}
                minimumDate={startDate}
              />
              <CustomSelectForFilter
                options={
                  !isExpenseCategoriesLoading && expenseCategories?.data?.length
                    ? expenseCategories?.data?.map((single: any) => {
                        return { value: single.id, label: single.name };
                      })
                    : []
                }
                selectedValue={expenseCategoryId ?? ''}
                handleSelect={(e) => handleFilter('expenseCategoryId', e)}
                placeHolder="Select Category"
              />
              <CustomSelectForFilter
                options={
                  !isShopListLoading && shopData?.data?.length
                    ? shopData?.data?.map((single: SingleShopDetails) => {
                        return {
                          value: single.id,
                          label: `${single.name} (${single.nickName})`,
                        };
                      })
                    : []
                }
                selectedValue={shopId ?? ''}
                handleSelect={(e) => handleFilter('shopId', e)}
                placeHolder="Select Shop"
              />
            </div>
          </div>
        </div>
      </div>
      {!isLoading ? (
        <div>
          <div className="tableTop w-full">
            <p>Expenses List</p>
            <div className="flex items-center">
              <p>Total : {data?.data?.result?.length}</p>
              <div className="ml-4">
                <ExportButton
                  totalCount={data?.data?.result.length ?? 0}
                  handleExportCsv={() =>
                    handleGenerateExpensesReportCsv({ data })
                  }
                  handleExportPdf={() =>
                    handleGenerateExpensesReportPdf(warehouseDetails, data)
                  }
                />
              </div>
            </div>
          </div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {data?.data?.result?.length ? (
              <div className="full-table-box h-customExc">
                <table className="full-table">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="tableHead">No</th>
                      <th className="tableHead table-col-width">Name</th>
                      <th className="tableHead table-col-width">Category</th>
                      <th className="tableHead table-col-width">Created By</th>
                      <th className="tableHead table-col-width">Location</th>
                      <th className="tableHead">Amount</th>
                      <th className="tableHead">Created At</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y bg-slate-200">
                    {data?.data?.result?.map(
                      (expense: ShopExpenseDetails, index: number) => (
                        <tr key={expense?.id}>
                          <td className="tableData">{index + 1}</td>
                          <td className="tableData table-col-width">
                            {expense?.name}
                          </td>
                          <td className="tableData table-col-width">
                            {expense?.ExpenseCategory?.name}
                          </td>
                          <td className="tableData table-col-width">
                            {expense?.CreatedBy?.name}
                          </td>
                          <td className="tableData table-col-width">
                            {expense?.Warehouse?.name}
                          </td>
                          <td className="tableData">{expense?.amount}</td>
                          <td className="tableData">
                            <DateAndTimeViewer date={expense?.createdAt} />
                          </td>
                        </tr>
                      ),
                    )}
                  </tbody>
                </table>
              </div>
            ) : (
              <NoResultFound pageType="expense" />
            )}
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={4} tableRow={6} />
      )}
    </div>
  );
}

export default ExpensesReportPageOverview;
