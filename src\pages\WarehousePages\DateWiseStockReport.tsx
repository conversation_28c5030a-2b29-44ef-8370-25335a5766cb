import { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import FilterButton from '@/components/reusable/Buttons/FilterButton';
import CustomDateFilterInput from '@/components/reusable/Inputs/CustomDateFilterInput';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import Pagination from '@/components/reusable/Pagination/Pagination';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import { useGetInventoryReportQuery } from '@/redux/api/warehouseApis/reportsApis';
import { ROUTES } from '@/Routes';
import { generateFilterParams } from '@/utils/generateFilterParams';

interface DateWiseStockReportProps {
  shopId?: string;
  warehouseId?: string;
}

function DateWiseStockReport({
  shopId,
  warehouseId,
}: DateWiseStockReportProps) {
  const [maxDate, setMaxDate] = useState('');
  const navigate = useNavigate();
  const router = new URLSearchParams(useLocation().search);
  const startDate = router.get('date');
  const page = router.get('page');
  const limit = router.get('limit');
  const { data, isLoading, isFetching } = useGetInventoryReportQuery(
    {
      warehouseId: warehouseId ?? undefined,
      shopId: shopId ?? undefined,
      startDate: startDate ?? '',
      endDate: startDate ?? '',
      type: 'custom',
      page: page ?? '1',
      limit: limit ?? '10',
    },
    // { skip: !warehouseId || !shopId },
  );
  useEffect(() => {
    const today = new Date();
    today.setDate(today.getDate() - 1); // Get yesterday

    const yyyy = today.getFullYear();
    const mm = String(today.getMonth() + 1).padStart(2, '0');
    const dd = String(today.getDate()).padStart(2, '0');
    const formattedDate = `${yyyy}-${mm}-${dd}`;

    setMaxDate(formattedDate);
  }, []);
  const handleFilter = (fieldName: string, value: string) => {
    const query = generateFilterParams(fieldName, value);

    if (warehouseId) {
      navigate(
        ROUTES.WAREHOUSE.DATE_WISE_STOCK_REPORT(warehouseId ?? '', query),
      );
    }
    if (shopId) {
      navigate(ROUTES.SHOP.DATE_WISE_STOCK_REPORT(shopId ?? '', query));
    }
  };
  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-1">
        <div className="flex items-center gap-x-2">
          <div className="search-title-and-btn flex items-center gap-x-3">
            <div className="relative">
              <div className="block xl:hidden">
                <FilterButton handleClick={() => console.log('higbig')} />
              </div>
              <div className="block xl:hidden" />
            </div>
          </div>
          <div className="hidden xl:block">
            <div className="flex items-center gap-x-2">
              <CustomDateFilterInput
                value={startDate ?? ''}
                placeholder="Select Date"
                label="Date"
                handleChange={(value: string) => handleFilter('date', value)}
                maximumDate={maxDate}
              />
            </div>
          </div>
        </div>
      </div>
      {!isLoading && !isFetching ? (
        <div>
          <div className="tableTop w-full">
            <p>Inventory Report</p>
            <div className="flex items-center">
              <p>Total : {data.pagination.total}</p>
              {/* <div className="ml-4">
                <ExportButton
                  totalCount={0}
                  handleExportCsv={() =>
                    toast.error('CSV export is not available yet.')
                  }
                />
              </div> */}
            </div>
          </div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {data?.data?.length ? (
              <div className="full-table-box h-customExc">
                <table className="full-table">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="tableHead">No</th>
                      <th className="tableHead table-col-width">Name</th>
                      <th className="tableHead">Opening Qty</th>
                      <th className="tableHead">Purchase Qty</th>
                      <th className="tableHead">Purchase Return Qty</th>
                      <th className="tableHead">Damage Qty</th>
                      <th className="tableHead">Sale Qty</th>
                      <th className="tableHead">Return Qty</th>
                      <th className="tableHead">Hold Qty</th>
                      <th className="tableHead">Shop Receive Qty</th>
                      <th className="tableHead">Marketing Qty</th>
                      <th className="tableHead">Closing Qty</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y bg-slate-200">
                    {data?.data?.map((product: any, index: number) => (
                      <tr key={product}>
                        <td className="tableData">
                          {(Number(page ?? 1) - 1) * Number(limit ?? 10) +
                            index +
                            1}
                        </td>
                        <td className="tableData table-col-width">
                          {product?.Product?.name}
                        </td>
                        <td className="tableData">
                          {product?.openingStockQuantity}
                        </td>
                        <td className="tableData">
                          {product?.purchaseQuantity}
                        </td>
                        <td className="tableData">
                          {product?.purchaseReturnQuantity}
                        </td>
                        <td className="tableData">
                          {product?.purchaseDamageQuantity}
                        </td>
                        <td className="tableData">{product?.saleQuantity}</td>
                        <td className="tableData">
                          {product?.saleReturnQuantity}
                        </td>
                        <td className="tableData">{product?.holdQuantity}</td>
                        <td className="tableData">
                          {product?.shopReceiveQuantity}
                        </td>
                        <td className="tableData">
                          {product?.marketingQuantity}
                        </td>
                        <td className="tableData">
                          {product?.closingStockQuantity}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <NoResultFound pageType="product" />
            )}
          </div>
          <div className="pagination-box flex justify-end rounded bg-white p-3">
            <Pagination
              currentPage={page ?? '1'}
              limit={Number(limit ?? 10)}
              handleFilter={(fieldName: string, value: any) =>
                handleFilter(fieldName, value)
              }
              totalCount={data?.pagination?.total}
              totalPages={Math.ceil(
                Number(data?.pagination?.total) /
                  Number(data?.pagination?.limit),
              )}
            />
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={5} tableRow={6} />
      )}
    </div>
  );
}

export default DateWiseStockReport;
