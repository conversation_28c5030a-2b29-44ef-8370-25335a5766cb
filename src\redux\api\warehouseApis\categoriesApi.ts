import { GetCategoriesResponse } from '@/types/warehouseTypes/categoriesTypes';
import BaseApi from '../baseApi';
import { TagTypes } from '@/redux/tag-types';

interface GetCategoriesParams {
  warehouseId?: string;
  name?: string;
  page?: string;
  limit?: string;
  organizationId?: string;
}
const CategoriesApi = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getCategories: builder.query<GetCategoriesResponse, GetCategoriesParams>({
      query: (params) => ({
        url: '/product-category',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.CATEGORY],
    }),
    createCategory: builder.mutation({
      query: (data) => ({
        url: '/product-category/new',
        method: 'POST',
        data,
      }),
      invalidatesTags: [TagTypes.CATEGORY],
    }),
    updateCategory: builder.mutation({
      query: ({ data, id }) => ({
        url: `/product-category/${id}`,
        method: 'PATCH',
        data,
      }),
      invalidatesTags: [TagTypes.CATEGORY],
    }),
    deleteCategory: builder.mutation({
      query: (id) => ({
        url: `/product-category/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: [TagTypes.CATEGORY],
    }),
  }),
});

export const {
  useGetCategoriesQuery,
  useCreateCategoryMutation,
  useUpdateCategoryMutation,
  useDeleteCategoryMutation,
} = CategoriesApi;
