import { SingleCourierDetails } from './warehouseTypes/settingsTypes';

export interface GetShopListResponse {
  success: boolean;
  statusCode: number;
  message: string;
  data: SingleShopDetails[];
}

export interface GetShopDetailsResponse {
  success: boolean;
  statusCode: number;
  message: string;
  data: SingleShopDetails;
}

export interface SingleShopDetails {
  id: string;
  createdAt: string;
  updatedAt: string;
  name: string;
  imgUrl: any;
  Address: {
    street: any;
    district: any;
    zipCode: any;
    division: any;
    country: any;
  };
  warehouseId: string;
  mobileNumber: string;
  websiteUrl: string;
  fbUrl: string;
  address: string;
  nickName: string;
  type: string;
  Courier: SingleCourierDetails[];
  ShopSettings?: {
    returnPolicyText: string;
  };
}
