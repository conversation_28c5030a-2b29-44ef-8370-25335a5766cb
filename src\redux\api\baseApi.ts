import { createApi } from '@reduxjs/toolkit/query/react';

import { tagTypesList } from '../tag-types';

import AxiosBaseQuery from '@/lib/axiosBaseQuery';

const BaseApi = createApi({
  reducerPath: 'api',
  baseQuery: AxiosBaseQuery({ baseUrl: import.meta.env.VITE_BASEURL }),
  refetchOnFocus: true,
  refetchOnMountOrArgChange: true,
  endpoints: () => ({}),
  tagTypes: [...tagTypesList],
});

export default BaseApi;
