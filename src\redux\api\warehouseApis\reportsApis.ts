import BaseApi from '../baseApi';

import { TagTypes } from '@/redux/tag-types';
import {
  GetLocationAnalysisResponse,
  GetShopAccountsSummeryResponse,
  GetShopCategoryWiseSummeryResponse,
  GetShopProductWiseSummeryResponse,
  GetShopSalesReportResponse,
} from '@/types/shopTypes/shopStockTransferReportTypes';

interface GetStockLocationParams {
  warehouseId?: string;
  type?: string;
  startDate?: string;
  endDate?: string;
  sortBy?: string;
}

interface GetLocationAnalysisParams {
  warehouseId?: string;
  shopId?: string;
}

const ReportsApi = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getAccountsSummeryReport: builder.query<
      GetShopAccountsSummeryResponse,
      GetStockLocationParams
    >({
      query: (params) => ({
        url: '/report/profit-loss',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP_STOCK],
    }),
    getShopSummeryReport: builder.query<
      GetShopAccountsSummeryResponse,
      GetStockLocationParams
    >({
      query: (params) => ({
        url: '/report/shop-summary',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP_STOCK],
    }),
    getSalesReport: builder.query<
      GetShopSalesReportResponse,
      GetStockLocationParams
    >({
      query: (params) => ({
        url: '/warehouse-report/sales/report',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP_STOCK],
    }),
    getCategoryWiseSaleSummery: builder.query<
      GetShopCategoryWiseSummeryResponse,
      GetStockLocationParams
    >({
      query: (params) => ({
        url: '/report/product-category',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP_STOCK],
    }),
    getProductWiseSaleSummery: builder.query<
      GetShopProductWiseSummeryResponse,
      GetStockLocationParams
    >({
      query: (params) => ({
        url: '/report/product-summary',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP_STOCK],
    }),
    getOrderLocationAnalysis: builder.query<
      GetLocationAnalysisResponse,
      GetLocationAnalysisParams
    >({
      query: (params) => ({
        url: '/report/sales-locations',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP_STOCK],
    }),
    getInventoryReport: builder.query({
      query: (params) => ({
        url: '/report/inventory',
        method: 'GET',
        params,
      }),
      // providesTags: [TagTypes.SHOP_STOCK],
    }),
    getStockHandoverReport: builder.query({
      query: (params) => ({
        url: '/report/stock-handover',
        method: 'GET',
        params,
      }),
      // providesTags: [TagTypes.SHOP_STOCK],
    }),
  }),
});

export const {
  useGetAccountsSummeryReportQuery,
  useGetShopSummeryReportQuery,
  useGetSalesReportQuery,
  useGetCategoryWiseSaleSummeryQuery,
  useGetProductWiseSaleSummeryQuery,
  useGetOrderLocationAnalysisQuery,
  useGetInventoryReportQuery,
  useGetStockHandoverReportQuery,
} = ReportsApi;
