import { Page, Text, View } from '@react-pdf/renderer';

import PrintedTime from './PrintedTime';
import { reportPdfStyles } from './ReportPdfStyles';
import SoftwareMarketingOnPdf from './SoftwareMarketingOnPdf';
import WarehousePdfHeader from './WarehousePdfHeader';

import { Warehouse } from '@/types/shopTypes/shopTransferAmountToOwnerTypes';
import { SingleSellerDetails } from '@/types/warehouseTypes/sellersTypes';

interface Props {
  employees?: SingleSellerDetails[];
  warehouseDetails?: Warehouse;
}
function EmployeeReportPdf({ employees, warehouseDetails }: Props) {
  return (
    <Page size="A4" style={reportPdfStyles.page}>
      <View>
        <WarehousePdfHeader warehouseDetails={warehouseDetails} />
        <PrintedTime />

        <View style={reportPdfStyles.table}>
          <View style={reportPdfStyles.tableRow}>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.employeeCol,
              ]}
            >
              No
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.employeeCol,
                reportPdfStyles.employeeNameTableCol,
              ]}
            >
              Name
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.employeeCol,
                reportPdfStyles.employeePhoneTableCol,
              ]}
            >
              Phone Number
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.employeeCol,
              ]}
            >
              Order
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.tableColLast,
                reportPdfStyles.employeeCol,
              ]}
            >
              Amount
            </Text>
          </View>
          {employees?.map((employee, index) => (
            <View
              key={employee.id}
              style={
                index === employees.length - 1
                  ? [reportPdfStyles.tableRowLast]
                  : [reportPdfStyles.tableRow]
              }
            >
              <Text
                style={[reportPdfStyles.tableCol, reportPdfStyles.employeeCol]}
              >
                {index + 1}
              </Text>
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.employeeCol,
                  reportPdfStyles.employeeNameTableCol,
                ]}
              >
                {employee.User.name}
              </Text>
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.employeeCol,
                  reportPdfStyles.employeePhoneTableCol,
                ]}
              >
                {employee.User.mobileNumber}
              </Text>
              <Text
                style={[reportPdfStyles.tableCol, reportPdfStyles.employeeCol]}
              >
                {employee.orderCount}
              </Text>
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.employeeCol,
                  reportPdfStyles.tableColLast,
                ]}
              >
                {employee.totalSales}
              </Text>
            </View>
          ))}
        </View>

        <SoftwareMarketingOnPdf />
      </View>
    </Page>
  );
}

export default EmployeeReportPdf;
