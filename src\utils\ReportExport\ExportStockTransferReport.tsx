import { Document, pdf } from '@react-pdf/renderer';
import { toast } from 'react-toastify';
import * as XLSX from 'xlsx';

import StockTransferReportPdf from '@/components/WarehouseComponents/ReportsPdf/StrockTransferReportPdf';
import { WarehouseDetailsInRedux } from '@/redux/slice/warehouseSlice';
import { ShopStockTransferDetails } from '@/types/shopTypes/shopStockTransferReportTypes';

export const handleGenerateStockTransferReportPdf = async (
  stockTransferlist?: ShopStockTransferDetails[],
  warehouse?: WarehouseDetailsInRedux,
) => {
  const blob = await pdf(
    <Document>
      <StockTransferReportPdf
        stockTransferlist={stockTransferlist}
        warehouse={warehouse}
      />
    </Document>,
  ).toBlob();

  const url = URL.createObjectURL(blob);
  window.open(url);
};

export const handleGenerateStockTransferReportCsv = ({
  data,
}: {
  data?: ShopStockTransferDetails[];
}) => {
  if (!data || data.length === 0) {
    toast.error('No data available to generate Excel');
    return;
  }

  // Define Excel headers
  const headers = [
    'No',
    'Created At',
    'Date',
    'Updated At',
    'Created By',
    'Quantity',
    'Status',
    'Shop Name',
  ];

  // Map data rows
  const rows = data.map((entry: ShopStockTransferDetails, index: number) => {
    return [
      index + 1,
      entry.id,
      entry.date,
      entry.onHold ? 'Yes' : 'No',
      entry.CreatedBy?.name || 'N/A',
      entry.stockIds?.length || 0,
      entry.status,
      entry.Shop?.name || 'N/A',
    ];
  });

  // Combine headers and rows
  const sheetData = [headers, ...rows];

  // Create a worksheet
  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // Create a workbook and add the worksheet
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Stock Transfer Report');

  // Generate Excel file and download
  const excelFileName = `stock_transfer_report.xlsx`;
  XLSX.writeFile(workbook, excelFileName);
};
