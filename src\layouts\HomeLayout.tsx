import { Outlet } from 'react-router-dom';

import Footer from '@/components/reusable/SidebarNavbar/Footer';
import HomePageNavbar from '@/components/reusable/SidebarNavbar/HomePageNavbar';

function HomeLayout() {
  return (
    <div className="h-[100vh]">
      <div className="fixed top-0 w-full">
        <HomePageNavbar />
      </div>
      <div
        className="fixed top-[60px] w-full"
        style={{
          height: `calc(100vh - 100px)`,
        }}
      >
        <div className="flex h-full bg-slate-200">
          <div className="h-full w-full overflow-y-auto">
            <Outlet />
          </div>
        </div>
      </div>
      <div className="fixed bottom-0 w-full">
        <Footer />
      </div>
    </div>
  );
}

export default HomeLayout;
