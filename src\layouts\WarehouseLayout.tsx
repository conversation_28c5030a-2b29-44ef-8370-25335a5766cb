import { useEffect, useState } from 'react';
import { Outlet, useParams } from 'react-router-dom';

import Navbar from '@/components/reusable/SidebarNavbar/Navbar';
import WarehouseSidebar from '@/components/reusable/SidebarNavbar/WarehouseSidebar';
import { useGetWarehouseDetailsQuery } from '@/redux/api/warehouseApis/warehouseDetailsApi';
import { useAppDispatch } from '@/redux/hooks';
import { setWarehouseDetails } from '@/redux/slice/warehouseSlice';

function WarehouseLayout() {
  const { warehouseId } = useParams();
  const [isSidebarOpen, setIsSidebarOpen] = useState<boolean>(false);

  const handleKeyDown = (event: React.KeyboardEvent<HTMLDivElement>) => {
    if (event.key === 'Enter' || event.key === ' ') {
      setIsSidebarOpen(false);
    }
  };

  const { data, isLoading } = useGetWarehouseDetailsQuery(warehouseId, {
    skip: !warehouseId,
  });

  const dispatch = useAppDispatch();
  useEffect(() => {
    if (!isLoading && data?.data) {
      dispatch(setWarehouseDetails(data?.data));
    }
  }, [data, dispatch, isLoading]);

  return (
    <div className="h-[100vh]">
      <div className="fixed top-0 w-full">
        <Navbar
          isSidebarOpen={isSidebarOpen}
          setIsSidebarOpen={setIsSidebarOpen}
        />
      </div>
      <div
        className="fixed top-[60px] w-full"
        style={{ height: 'calc(100vh - 60px)' }}
      >
        <div className="flex h-full bg-slate-200">
          <WarehouseSidebar isSidebarOpen={isSidebarOpen} />
          {isSidebarOpen && (
            <div
              className="absolute z-40 h-full w-full cursor-pointer bg-[#28243d5e]"
              onClick={() => setIsSidebarOpen(false)}
              onKeyDown={handleKeyDown}
              role="button"
              tabIndex={0}
              aria-label="Close sidebar"
            />
          )}
          <div className="flex w-full justify-center">
            <div className="h-full w-full overflow-y-auto px-4 pt-4">
              <Outlet />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default WarehouseLayout;
