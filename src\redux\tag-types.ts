export enum TagTypes {
  SHOP = 'shop',
  BRAND = 'brand',
  CATEGORY = 'category',
  UNIT = 'unit',
  PRODUCTS = 'products',
  SUPPLIERS = 'suppliers',
  STOCK = 'stock',
  ORGANIZATIONS = 'organizations',
  CUSTOMERS = 'customers',
  SELLERS = 'sellers',
  SHOP_SELLERS = 'shop-sellers',
  DASHBOARD = 'dashboard',

  SHOP_PRODUCTS = 'shop-products',
  SHOP_BRANDS = 'shop-brands',
  SHOP_CATEGORIES = 'shop-categories',
  SHOP_SUB_CATEGORIES = 'shop-sub-categories',
  SHOP_STOCK = 'shop-stock',
  SHOP_CUSTOMERS = 'shop-customers',
  SHOP_ORDERS = 'shop-orders',
  SHOP_EXPENSES = 'shop-expenses',
  SHOP_PAYMENTS = 'shop-payments',
  STATUS_WISE_REPORT = 'status-wise-report',

  // warehouse
  WAREHOUSE_DETAILS = 'warehouse-details',

  // courier
  PATHAO = 'pathao',

  // organization
  ORG_PAYMENTS = 'payments',
  ORG_SUBSCRIPTIONS = 'subscriptions',

  // stock audit
  STOCK_AUDIT = 'stock-audit',

  // feature requests
  FEATURE_REQUESTS = 'feature-requests',
}

export const tagTypesList = [
  TagTypes.SHOP,
  TagTypes.BRAND,
  TagTypes.CATEGORY,
  TagTypes.CUSTOMERS,
  TagTypes.UNIT,
  TagTypes.PRODUCTS,
  TagTypes.SUPPLIERS,
  TagTypes.STOCK,
  TagTypes.ORGANIZATIONS,
  TagTypes.SHOP_PRODUCTS,
  TagTypes.SHOP_STOCK,
  TagTypes.SHOP_CUSTOMERS,
  TagTypes.SHOP_ORDERS,
  TagTypes.SELLERS,
  TagTypes.SHOP_PAYMENTS,
  TagTypes.STATUS_WISE_REPORT,
  TagTypes.WAREHOUSE_DETAILS,
  TagTypes.PATHAO,
  TagTypes.ORG_PAYMENTS,
  TagTypes.ORG_SUBSCRIPTIONS,
  TagTypes.STOCK_AUDIT,
  TagTypes.FEATURE_REQUESTS,
];
