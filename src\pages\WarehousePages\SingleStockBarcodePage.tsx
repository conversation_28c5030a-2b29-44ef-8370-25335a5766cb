import { useLocation } from 'react-router-dom';

import SingleStockBarcode from '@/components/WarehouseComponents/StockListPageComponents/SingleStockBarcode';

function SingleStockBarcodePage() {
  const searchParams = new URLSearchParams(useLocation().search);
  const searchParam = searchParams.get('data') ?? '';

  const data = JSON.parse(atob(searchParam));

  return (
    <div
      style={{
        padding: '0px',
        margin: '0px',
        maxWidth: '1.5in',
      }}
    >
      <SingleStockBarcode
        barcode={data?.barcode}
        name={data?.name}
        price={data?.price}
      />
    </div>
  );
}

export default SingleStockBarcodePage;
