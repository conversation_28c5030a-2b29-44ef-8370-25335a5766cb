import { ApexOptions } from 'apexcharts'; // Import the correct ApexOptions type
import ReactApex<PERSON>hart from 'react-apexcharts';

import {
  DashboardLastSevenDaysSalesSummeryResponse,
  ShopDetailsInSalesSummery,
  SingleDaySalesSummery,
} from '@/types/warehouseTypes/dashboardTypes';

interface Props {
  chartData?: DashboardLastSevenDaysSalesSummeryResponse;
}

function ShopWeeklyCustomerCountChart({ chartData }: Props) {
  // Step 1: Extract the unique dates (X-axis)
  const dates = chartData?.data.map(
    (entry: SingleDaySalesSummery) => entry.date,
  );

  // Step 4: Add a series for totalAmount
  const totalAmountSeries = {
    name: 'TOTAL',
    data:
      chartData?.data.map((entry: SingleDaySalesSummery) =>
        entry.shops.reduce(
          (sum, shop: ShopDetailsInSalesSummery) => sum + shop.totalOrderCount,
          0,
        ),
      ) ?? [],
  };

  // Combine all series
  const series = [totalAmountSeries];

  // Defining chartOptions and explicitly typing it as ApexOptions
  const chartOptions: ApexOptions = {
    chart: {
      type: 'line', // Specify a valid ApexCharts chart type like 'line', 'bar', etc.
    },
    xaxis: {
      categories: dates, // X-axis will show the dates
      title: {
        text: 'Date',
      },
    },
    yaxis: {
      title: {
        text: 'Total Amount',
      },
    },
    stroke: {
      curve: 'smooth',
    },
    markers: {
      size: 5,
    },
    dataLabels: {
      enabled: true,
    },
    tooltip: {
      shared: true,
      intersect: false,
    },
    title: {
      text: 'Weekly Customer Count',
    },
  };

  return (
    <div className="w-full">
      <ReactApexChart
        options={chartOptions} // Ensure options has the correct type
        series={series}
        type="line" // Set the type explicitly here
        height={350}
      />
    </div>
  );
}

export default ShopWeeklyCustomerCountChart;
