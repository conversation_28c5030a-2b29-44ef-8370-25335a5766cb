import {
  BadgeDollarSign,
  ChevronDown,
  CircleParking,
  Contact,
  CopyCheck,
  GitPullRequestCreateArrow,
  Handshake,
  LayoutDashboard,
  ListChecks,
  ListTodo,
  Receipt,
  ShoppingBasket,
  SquareTerminal,
  Store,
  Truck,
  Warehouse,
  Webhook,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { Link, useLocation } from 'react-router-dom';

import { ROUTES } from '@/Routes';
import { SidebarItem, SingleSubmenu } from '@/types/sidebarTypes';

interface Props {
  isSidebarOpen: boolean;
}

function OrganizationSidebar({ isSidebarOpen }: Props) {
  const [expanded, setExpanded] = useState<string>('');
  const [selectedPath, setSelectedPath] = useState('');
  const location = useLocation();
  const { pathname } = location;
  const orgId = pathname.split('/')[2]; // Extract orgId from the URL

  useEffect(() => {
    setSelectedPath(pathname);
  }, [pathname]);

  const sidebarItems: SidebarItem[] = [
    {
      label: 'Dashboard',
      icon: <LayoutDashboard className="w-4" />,
      path: ROUTES.ORGANIZATION.DASHBOARD(orgId),
      slug: `/organization/${orgId}/dashboard`,
      visible: true,
    },
    {
      label: 'FeatureRequests',
      icon: <GitPullRequestCreateArrow className="w-4" />,
      path: ROUTES.ORGANIZATION.FEATURE_REQUESTS(orgId),
      slug: `/organization/${orgId}/feature-requests`,
      visible: true,
    },
    {
      label: 'Warehouses',
      icon: <Warehouse className="w-4" />,
      path: ROUTES.ORGANIZATION.WAREHOUSES(orgId),
      slug: `/organization/${orgId}/warehouses`,
      visible: true,
    },
    {
      label: 'Shops',
      icon: <Store className="w-4" />,
      path: ROUTES.ORGANIZATION.SHOP(orgId),
      slug: `/organization/${orgId}/shops`,
      visible: true,
    },
    {
      label: 'Products',
      icon: <ShoppingBasket className="w-4" />,
      slug: `/organization/${orgId}/products`,
      visible: true,
      submenu: [
        {
          label: 'Products',
          icon: <CircleParking className="w-3" />,
          path: ROUTES.ORGANIZATION.PRODUCTS(orgId),
          slug: `/organization/${orgId}/products`,
          visible: true,
        },
        {
          label: 'Brands',
          icon: <Handshake className="w-3" />,
          path: ROUTES.ORGANIZATION.BRANDS(orgId),
          slug: `/organization/${orgId}/brands`,
          visible: true,
        },
        {
          label: 'Categories',
          icon: <ListTodo className="w-3" />,
          path: ROUTES.ORGANIZATION.CATEGORIES(orgId),
          slug: `/organization/${orgId}/categories`,
          visible: true,
        },
        // {
        //   label: 'Sub Categories',
        //   icon: <Replace className="w-3" />,
        //   path: ROUTES.ORGANIZATION.SUB_CATEGORIES(warehouseId),
        //   slug: `/organization/${warehouseId}/sub-categories`,
        //   visible: true,
        // },
      ],
    },
    {
      label: 'Employees',
      icon: <Contact className="w-4" />,
      path: ROUTES.ORGANIZATION.EMPLOYEES(orgId),
      slug: `/organization/${orgId}/employees`,
      visible: true,
    },
    {
      label: 'Couriers',
      icon: <Truck className="w-4" />,
      path: ROUTES.ORGANIZATION.COURIERS(orgId),
      slug: `/organization/${orgId}/couriers`,
      visible: true,
    },
    {
      label: 'Webhooks',
      icon: <Webhook className="w-4" />,
      path: ROUTES.ORGANIZATION.WEBHOOKS(orgId),
      slug: `/organization/${orgId}/webhooks`,
      visible: true,
    },
    {
      label: 'Stock Audit',
      icon: <CopyCheck className="w-4" />,
      path: ROUTES.ORGANIZATION.STOCK_AUDIT(orgId),
      slug: `/organization/${orgId}/stock-audit`,
      visible: true,
    },
    {
      label: 'API Docs',
      icon: <SquareTerminal className="w-4" />,
      path: ROUTES.ORGANIZATION.API_DOCS(orgId),
      slug: `/organization/${orgId}/api-docs`,
      visible: true,
    },
    {
      label: 'Billing',
      icon: <ListChecks className="w-4" />,
      slug: `/organization/${orgId}/billing`,
      visible: true,
      submenu: [
        {
          label: 'Invoices',
          icon: <Receipt className="w-4" />,
          path: ROUTES.ORGANIZATION.SUBSCRIPTIONS(orgId),
          slug: `/organization/${orgId}/subscriptions`,
          visible: true,
        },
        {
          label: 'Transactions',
          icon: <BadgeDollarSign className="w-4" />,
          path: ROUTES.ORGANIZATION.BILLING(orgId),
          slug: `/organization/${orgId}/billing`,
          visible: true,
        },
      ],
    },
  ];

  const link =
    'flex items-center px-4 py-2 text-[12px] lg:text-[14px] xl:text-[16px] hover:bg-gray-700 rounded-r-2xl';
  const selectedLink =
    'flex items-center px-4 py-2 text-[12px] lg:text-[14px] xl:text-[16px] hover:bg-gray-700 bg-gray-400 rounded-r-2xl';

  const subLink = `dropdown-link flex items-center py-2 pr-4 text-[12px] lg:text-[14px] xl:text-[16px] before:mr-2 before:inline-block before:h-[1px] before:w-5 before:bg-gray-400 before:pl-[-10px] before:content-[''] hover:bg-gray-700 rounded-r-2xl`;
  const selectedSubLink = `dropdown-link flex items-center py-2 pr-4 sm:text-[12px] text-[14px] xl:text-[16px] before:mr-2 before:inline-block before:h-[1px] before:w-5 before:bg-gray-400 before:pl-[-10px] before:content-[''] hover:bg-gray-700 bg-gray-700 before:bg-white rounded-r-2xl`;

  return (
    <div className="relative z-50 flex h-full bg-gray-100">
      <div
        className={`fixed inset-y-0 top-[60px] z-30 h-sidebar w-[200px] transform overflow-y-auto bg-[#28243D] text-white transition-transform delay-200 duration-200 md:relative md:top-0 md:translate-x-0 lg:w-[200px] xl:w-[200px] ${isSidebarOpen ? 'md:translate-x-0' : '-translate-x-full'}`}
      >
        <div className="flex h-full flex-col overflow-y-auto">
          {sidebarItems?.map((item: SidebarItem) => {
            return item?.visible ? (
              item?.submenu?.length ? (
                <div key={item?.slug}>
                  {item?.visible ? (
                    <button
                      type="button"
                      onClick={() =>
                        expanded === item.label
                          ? setExpanded('')
                          : setExpanded(item.label)
                      }
                      className="flex w-full items-center justify-between px-4 py-2 text-[12px] hover:bg-gray-700 lg:text-[14px] xl:text-[16px]"
                    >
                      <span className="flex items-center">
                        <span className="mr-4">{item?.icon}</span> {item.label}
                      </span>
                      <ChevronDown
                        className={`${expanded === item.label ? 'rotate-180' : ''} w-4`}
                      />
                    </button>
                  ) : (
                    ''
                  )}
                  <div
                    className={`${expanded === item.label ? 'opacity-500 block transition-opacity delay-500 duration-500 ease-in-out' : 'hidden opacity-0'} ml-[23px] border-l border-gray-400 ease-in-out`}
                  >
                    {item?.submenu?.map((subMenu: SingleSubmenu) => {
                      return subMenu?.visible ? (
                        <Link
                          to={subMenu.path ?? ''}
                          className={
                            selectedPath.includes(subMenu.path ?? '')
                              ? selectedSubLink
                              : subLink
                          }
                          key={`${subMenu.path}-${Date.now()}}`}
                        >
                          <span className="mr-[5px] translate-y-[.5px]">
                            {subMenu.icon}
                          </span>{' '}
                          {subMenu.label}
                        </Link>
                      ) : (
                        ''
                      );
                    })}
                  </div>
                </div>
              ) : (
                <Link
                  to={item.path ?? ''}
                  className={
                    selectedPath.includes(item?.slug ?? '')
                      ? selectedLink
                      : link
                  }
                  onClick={() => setExpanded(item.slug ?? '--')}
                  key={`${item.path}-$Date.now()}`}
                >
                  <span className="mr-4">{item.icon}</span> {item.label}
                </Link>
              )
            ) : (
              ''
            );
          })}
        </div>
      </div>
      <div
        className={`fixed bottom-0 z-50 w-[200px] transition-transform delay-200 duration-200 md:translate-x-0 lg:w-[200px] xl:w-[200px] ${isSidebarOpen ? 'md:translate-x-0' : '-translate-x-full'}`}
      >
        <div className="bg-[#221e33] py-2">
          <h2 className="text-center text-[10px] text-gray-300">
            {`Softs.ai © ${new Date().getFullYear()}`}
          </h2>
        </div>
      </div>
    </div>
  );
}

export default OrganizationSidebar;
