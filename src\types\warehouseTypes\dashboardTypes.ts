export interface GetDashboardUpdatesResponse {
  success: boolean;
  statusCode: number;
  message: string;
  data: DashboardUpdates;
}

export interface DashboardUpdates {
  totalSaleAmount: number;
  totalRetailPrice: number;
  totalWholesalePrice: number;
  totalPaidAmount: number;
  totalStockEntryAmount: number;
  totalStockEntryQuantity: number;
  totalStockTransfer: number;
  totalStockPull: number;
  totalPurchasePrice: number;
  totalProfit: number;
}

export interface GetDashboardCustomersSummeryResponse {
  success: boolean;
  statusCode: number;
  message: string;
  data: SingleDayCustomerSummery[];
}

export interface SingleDayCustomerSummery {
  date: string;
  regular: number;
  general: number;
  premium: number;
  totalOrders: number;
}

export interface GetDashboardPaidOrderListResponse {
  success: boolean;
  statusCode: number;
  message: string;
  data: SinglePaidOrderDetails[];
}

export interface SinglePaidOrderDetails {
  id: string;
  createdAt: string;
  updatedAt: string;
  shopId: string;
  customerId: string;
  note?: string;
  address: string;
  trackingNumber: any;
  subTotal: number;
  grandTotal: number;
  additionalCost: number;
  deliveryCharge: number;
  productDiscount: number;
  adminDiscount: number;
  vat: number;
  totalPaid: number;
  totalDue: number;
  customerPaid: number;
  returnAmount: number;
  deliveryPartner: string;
  paymentMethod: string;
  orderStatus: string;
  serialNo: number;
  employeeId?: string;
  Customer: {
    name: string;
    serialNo: number;
  };
  Employee?: {
    type: string;
    User: {
      name: string;
    };
  };
}

export interface DashboardLastSevenDaysSalesSummeryResponse {
  success: boolean;
  message: string;
  statusCode: number;
  data: SingleDaySalesSummery[];
}

export interface SingleDaySalesSummery {
  date: string;
  shops: ShopDetailsInSalesSummery[];
}

export interface ShopDetailsInSalesSummery {
  shopId: string;
  shopName: string;
  nickName: string;
  orderStatuses: SingleOrderStatusDetailsInSalesSummery[];
  totalAmount: number;
  totalOrderCount: number;
}

export interface SingleOrderStatusDetailsInSalesSummery {
  orderStatus: string;
  totalAmount: number;
}

export interface TopTenSellingItemsResponse {
  success: boolean;
  message: string;
  statusCode: number;
  data: SingleItemOfTopTenProduct[];
}

export interface SingleItemOfTopTenProduct {
  id: string;
  name: string;
  imgUrl: string;
  totalSold: number;
}

export interface TopTenCustomersResponse {
  success: boolean;
  message: string;
  statusCode: number;
  data: SingleTopTenCustomerDetails[];
}

export interface SingleTopTenCustomerDetails {
  id: string;
  name: string;
  mobileNumber: string;
  imgUrl: any;
  email: any;
  orderCount: number;
  totalOrderAmount: number;
}


