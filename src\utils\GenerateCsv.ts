import { ShopCustomerDetails } from '@/types/shopTypes/shopCustomersTypes';
import { toast } from 'react-toastify';
import * as XLSX from 'xlsx';
import { generateDateString } from './generateDateFormat';
import {
  ShopOrderDetails,
  ShopSingleOrderItem,
} from '@/types/shopTypes/shopOrderTypes';

export const handleGenerateCustomerCsv = (
  customers?: ShopCustomerDetails[],
) => {
  if (!customers || customers.length === 0) {
    toast.error('No data available to generate Excel');
    return;
  }

  // Define Excel headers
  const headers = [
    'No',
    'Customer Name',
    'Phone Number',
    'Serial No',
    'Address',
    'Total Orders',
  ];

  // Map data rows
  const rows = customers.map((customer, index) => [
    index + 1,
    customer.name,
    customer.mobileNumber,
    customer.serialNo,
    customer.address || 'N/A',
    customer.orderCount,
  ]);

  // Combine headers and rows
  const sheetData = [headers, ...rows];

  // Create a worksheet
  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // Create a workbook and add the worksheet
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Customer Report');

  // Generate Excel file and download
  const excelFileName = `Customer_Report_${generateDateString(new Date().toString())}.xlsx`;
  XLSX.writeFile(workbook, excelFileName);
};

export const handleGenerateSalesSummaryCsv = ({
  orderList,
  startDate,
  endDate,
}: {
  orderList?: ShopOrderDetails[];
  startDate?: string;
  endDate?: string;
}) => {
  if (!orderList || orderList.length === 0) {
    toast.error('No data available to generate Excel');
    return;
  }

  // Define Excel headers
  const headers = [
    'No',
    'Order No',
    'Shop',
    'CUSTOMER NAME',
    'CUSTOMER Serial No',
    'ADDRESS',
    'Phone',
    'Product MRP',
    'Discount',
    'Vat',
    'Delivery Charge',
    'Total',
    'Paid',
    'Due',
    'Order Status',
    'Payment Status',
    'Courier Payment Invoice',
    'Seller',
    'Payment Method',
    'Order Descriptions',
    'Delivery Partner',
    'Courier Tracking ID',
    'Created At',
  ];

  // Map data rows
  const rows = orderList.map((order: ShopOrderDetails, index: number) => {
    // Combine all payment methods into a single string, separated by commas
    const paymentMethods = order.Payment
      ? order.Payment.map(
          (payment) => `${payment.paymentMethod} - ${payment.amount}`,
        ).join(', ')
      : '';

    // Combine all product names into a single string, separated by new lines
    const productDescriptions = order.OrderItem
      ? order.OrderItem.map(
          (product: ShopSingleOrderItem) =>
            `${product.Stock?.barcode}-${product.Stock?.Product?.name}-${product.Stock?.retailPrice} BDT`,
        ).join(', ')
      : '';

    return [
      index + 1,
      order.serialNo,
      `${order?.Shop?.name}(${order?.Shop?.nickName})`,
      order.customerName || order.Customer?.name || '',
      order.Customer?.serialNo || '',
      order.address || order.Customer?.address || '',
      order.customerMobileNumber || order.Customer?.mobileNumber || '',
      Number(order.subTotal ?? 0),
      Number(order?.productDiscount ?? 0) + Number(order?.adminDiscount ?? 0),
      order.vat ?? 0,
      order.deliveryCharge ?? 0,
      Number(order.grandTotal ?? 0) + Number(order.deliveryCharge),
      order.totalPaid ?? 0,
      order.totalDue ?? 0,
      order.orderStatus,
      order.paymentStatus,
      order.invoiceNo || '',
      order.Employee?.User?.name || '',
      paymentMethods, // Payment methods as a string
      productDescriptions, // Product descriptions as a string with new lines
      order?.deliveryPartner,
      order.trackingNumber,
      new Date(order.createdAt).toLocaleString(),
    ];
  });

  // Combine headers and rows
  const sheetData = [headers, ...rows];

  // Create a worksheet
  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // Create a workbook and add the worksheet
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Sales Report');

  // Generate Excel file and download
  const excelFileName = `Sales_Report_${generateDateString(startDate ?? '')}_to_${generateDateString(endDate ?? '')}.xlsx`;
  XLSX.writeFile(workbook, excelFileName);
};