import { useLocation } from 'react-router-dom';

import ExportButton from '@/components/reusable/Buttons/ExportButton';
import FilterButton from '@/components/reusable/Buttons/FilterButton';
import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import StartDateEndDateWithSearch from '@/components/reusable/ReusableFilters/StartDateEndDateWithSearch';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import { useGetWarehouseStockDeleteReportQuery } from '@/redux/api/warehouseApis/warehouseReportsApis';
import { SingleStockDeleteDetails } from '@/types/shopTypes/shopStockTransferReportTypes';
import {
  handleGenerateStockDeleteReportCsv,
  handleGenerateStockDeleteReportPdf,
} from '@/utils/ReportExport/ExportStockDeleteReport';

interface Props {
  warehouseId: string;
}
function StockDeleteReportPageOverview({ warehouseId }: Props) {
  const router = new URLSearchParams(useLocation().search);
  const startDate = router.get('startDate') || `${new Date().toISOString()}`;
  const endDate = router.get('endDate') || `${new Date().toISOString()}`;
  const { data, isLoading, isFetching } = useGetWarehouseStockDeleteReportQuery(
    {
      warehouseId,
      type: 'custom',
      startDate,
      endDate,
    },
  );

  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-1">
        <div className="flex items-center gap-x-2">
          <div className="search-title-and-btn flex items-center gap-x-3">
            {/* <p className="whitespace-nowrap">Search Filters</p> */}
            <div className="relative">
              <div className="block xl:hidden">
                <FilterButton handleClick={() => console.log('higbig')} />
              </div>
              <div className="block xl:hidden">
                {/* <ProductPageFilterModal /> */}
              </div>
            </div>
          </div>
          <div className="hidden xl:block">
            <StartDateEndDateWithSearch />
          </div>
        </div>
      </div>
      {!isLoading && !isFetching ? (
        <div>
          <div className="tableTop w-full">
            <p>Stock Delete List</p>
            <div className="flex items-center">
              <p>Total : {data?.data?.result?.length}</p>
              <div className="ml-4">
                <ExportButton
                  totalCount={data?.data?.result.length ?? 0}
                  handleExportCsv={() =>
                    handleGenerateStockDeleteReportCsv({
                      data: data?.data?.result,
                    })
                  }
                  handleExportPdf={() =>
                    handleGenerateStockDeleteReportPdf(
                      data?.data?.result,
                      data?.data?.warehouse,
                    )
                  }
                />
              </div>
            </div>
          </div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {data?.data?.result?.length ? (
              <div className="full-table-box h-customExc">
                <table className="full-table">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="tableHead">No</th>
                      <th className="tableHead">Deleted By</th>
                      <th className="tableHead">Reason</th>
                      <th className="tableHead">Total Quantity</th>
                      <th className="tableHead">Delete Date</th>
                      <th className="tableHead">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y bg-slate-200">
                    {data?.data?.result?.map(
                      (stockEntry: SingleStockDeleteDetails, index: number) => (
                        <tr key={stockEntry.id}>
                          <td className="tableData">{index + 1}</td>
                          <td className="tableData">
                            {stockEntry?.CreatedBy?.name}
                          </td>
                          <td className="tableData">{stockEntry?.reason}</td>
                          <td className="tableData">
                            {stockEntry?.stockIds?.length}
                          </td>
                          <td className="tableData">
                            <DateAndTimeViewer date={stockEntry?.createdAt} />
                          </td>
                          <td className="tableData">action</td>
                        </tr>
                      ),
                    )}
                  </tbody>
                </table>
              </div>
            ) : (
              <NoResultFound pageType="delete report" />
            )}
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={7} tableRow={6} />
      )}
    </div>
  );
}

export default StockDeleteReportPageOverview;
