import { CreatedBy, Pagination } from '@/redux/commonTypes';

export interface GetSuppliersResponse {
  success: boolean;
  statusCode: number;
  message: string;
  data: SingleSupplier[];
  pagination: Pagination;
}

export interface SingleSupplier {
  id: string;
  createdAt: string;
  updatedAt: string;
  warehouseId: string;
  userId: string;
  serialNo: number;
  User: UserInSupplier;
  totalPaid: number;
  totalDue: number;
  totalAmount: number;
}

export interface UserInSupplier {
  name: string;
  mobileNumber: string;
  imgUrl: any;
}

export interface GetSupplierDetailsResponse {
  success: boolean;
  message: string;
  statusCode: number;
  data: SupplierDetails;
}

export interface SupplierDetails {
  id: string;
  createdAt: string;
  updatedAt: string;
  warehouseId: string;
  userId: string;
  serialNo: number;
  Purchase: SingleBillOfSupplierDetails[];
  User: SingleSupplierDetails;
  totalPaid: number;
  totalDue: number;
}

export interface SingleBillOfSupplierDetails {
  id: string;
  createdAt: string;
  updatedAt: string;
  supplierId: string;
  productId: string;
  quantity: number;
  supplierBillId: string;
  warehouseId: string;
  purchasePrice: number;
  retailPrice: number;
  serialNo: number;
  createdById: string;
}

export interface SingleSupplierDetails {
  id: string;
  createdAt: string;
  updatedAt: string;
  name: string;
  email: any;
  username: string;
  mobileNumber: string;
  password: string;
  refreshToken: any;
  imgUrl: any;
  organizationLimit: number;
  warehouseLimit: number;
  shopLimit: number;
  type: string;
}

export interface SupplierDetailsBillsListResponse {
  success: boolean;
  message: string;
  statusCode: number;
  pagination: Pagination;
  data: SupplierSingleBill[];
}

export interface SupplierSingleBill {
  id: string;
  createdAt: string;
  updatedAt: string;
  supplierId: string;
  supplierInvoiceNo: string;
  note: string;
  proofUrl: string;
  totalPrice: number;
  totalPaid: number;
  advancePayment: number;
  totalDue: number;
  purchaseDate: string;
  warehouseId: string;
  serialNo: number;
  createdById: string;
}

export interface SupplierPaymentsListResponse {
  success: boolean;
  message: string;
  statusCode: number;
  pagination: Pagination;
  data: SupplierSinglePayment[];
}

export interface SupplierSinglePayment {
  id: string;
  createdAt: string;
  updatedAt: string;
  amount: number;
  paymentMethod: string;
  supplierBillId: string;
  createdById: string;
  imgUrl: any;
  SupplierBill: {
    id: string;
    serialNo: number;
  };
  CreatedBy: CreatedBy;
}
