import Cookies from 'js-cookie';
import { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import FilterButton from '@/components/reusable/Buttons/FilterButton';
import OrderListViewer from '@/components/reusable/OrdersPagesReusableComponents/OrderListViewer';
import Pagination from '@/components/reusable/Pagination/Pagination';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import OrderPageFilterModal from '@/components/WarehouseComponents/OrdersPageComponents/OrderPageFilterModal';
import OrderPageFilterOptions from '@/components/WarehouseComponents/OrdersPageComponents/OrderPageFilterOptions';
import StatusWiseOrderCountView from '@/components/WarehouseComponents/OrdersPageComponents/StatusWiseOrderCountView';
import { useGetShopOrdersQuery } from '@/redux/api/shopApis/shopOrdersApis';
import { useGetShopOrderStatusWiseSaleSummeryQuery } from '@/redux/api/shopApis/shopReportsApis';
import { ROUTES } from '@/Routes';
import { generateFilterParams } from '@/utils/generateFilterParams';

interface Props {
  shopId: string;
  warehouseId: string;
}
function ShopOrdersPageOverview({ shopId }: Props) {
  const navigate = useNavigate();
  const router = new URLSearchParams(useLocation().search);
  const customerId = router.get('customerId');
  const customerName = router.get('customerName');
  const mobileNumber = router.get('mobileNumber');
  const serialNo = router.get('serialNo');
  const page = router.get('page');
  const limit = router.get('limit');
  const orderStatus = router.get('orderStatus');
  const startDate = router.get('startDate');
  const endDate = router.get('endDate');
  const invoiceNo = router.get('invoiceNo');
  const paymentStatus = router.get('paymentStatus');
  const hasDue = router.get('hasDue');
  const organizationId = Cookies.get('organizationId') as string;
  const [isFilterModalOpen, setIsFilterModalOpen] = useState<boolean>(false);

  const { data, isLoading, isFetching } = useGetShopOrdersQuery(
    {
      organizationId,
      shopId,
      page: page ?? '1',
      serialNo: serialNo ?? undefined,
      customerName: customerName ?? undefined,
      mobileNumber: mobileNumber ?? undefined,
      customerId: customerId ?? undefined,
      limit: limit ?? '10',
      orderStatus: orderStatus ?? undefined,
      startDate: startDate ?? undefined,
      endDate: endDate ?? undefined,
      paymentStatus: paymentStatus ?? undefined,
      invoiceNo: invoiceNo ?? undefined,
      type: startDate && endDate ? 'custom' : undefined,
      hasDue: hasDue ? hasDue === 'true' : undefined,
    },
    { skip: !(shopId && organizationId) },
  );

  const { data: statusData } = useGetShopOrderStatusWiseSaleSummeryQuery({
    shopId,
  });

  const handleFilter = (fieldName: string, value: string) => {
    const query = generateFilterParams(fieldName, value);
    navigate(ROUTES.SHOP.ORDERS(shopId, query));
  };

  return (
    <div>
      <div className="search-filters mb-1 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-2">
        <div className="flex items-center">
          <div className="search-title-and-btn flex items-center">
            <div className="relative">
              <div className="block xl:hidden">
                <FilterButton
                  handleClick={() => setIsFilterModalOpen(!isFilterModalOpen)}
                />
              </div>
              <div
                className={`${isFilterModalOpen ? 'block' : 'hidden'} xl:hidden`}
              >
                <OrderPageFilterModal
                  handleClearAndClose={() => setIsFilterModalOpen(false)}
                  handleFilter={handleFilter}
                />
              </div>
            </div>
          </div>
          <div className="hidden xl:block">
            {/* <div className="flex items-center gap-x-2"> */}
            <div className="grid grid-cols-1 gap-3">
              <OrderPageFilterOptions handleFilter={handleFilter} />
            </div>
          </div>
        </div>
      </div>
      {!isLoading && !isFetching ? (
        <div>
          <div className="my-2">
            <StatusWiseOrderCountView
              statusData={statusData}
              handleFilter={handleFilter}
              orderStatus={orderStatus ?? ''}
            />
          </div>

          <OrderListViewer
            orders={data?.data}
            total={data?.pagination.total}
            shopType="SHOP"
          />
          <div className="pagination-box flex justify-end rounded bg-white p-3">
            <Pagination
              currentPage={page ?? '1'}
              limit={Number(limit ?? 10)}
              handleFilter={(fieldName: string, value: any) =>
                handleFilter(fieldName, value)
              }
              totalCount={data?.pagination?.total}
              totalPages={Math.ceil(
                Number(data?.pagination?.total) /
                  Number(data?.pagination?.limit),
              )}
              limits={[10, 20, 50]}
            />
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={13} tableRow={6} />
      )}
    </div>
  );
}

export default ShopOrdersPageOverview;
