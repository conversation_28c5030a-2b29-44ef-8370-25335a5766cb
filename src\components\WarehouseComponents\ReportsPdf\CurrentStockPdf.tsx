import { Page, Text, View } from '@react-pdf/renderer';

import PrintedTime from './PrintedTime';
import { reportPdfStyles } from './ReportPdfStyles';
import SoftwareMarketingOnPdf from './SoftwareMarketingOnPdf';

import { Warehouse } from '@/types/shopTypes/shopExpensesTypes';
import { SingleProductStockReport } from '@/types/shopTypes/shopStockTransferReportTypes';
import { truncateText } from '@/utils/stringTruncate';

interface Props {
  products: SingleProductStockReport[];
  warehouse?: Warehouse;
  date?: string;
  totalPurchasePrice?: number;
  totalQuantity?: number;
  totalRetailPrice?: number;
}

function CurrentStockPdf({
  products,
  warehouse,
  date,
  totalPurchasePrice,
  totalQuantity,
  totalRetailPrice,
}: Props) {
  return (
    <Page size="A4" style={reportPdfStyles.page}>
      <View>
        <View style={reportPdfStyles.header}>
          <Text style={reportPdfStyles.userName}>{warehouse?.name}</Text>
          <Text style={reportPdfStyles.userName}>
            Date:{' '}
            {new Intl.DateTimeFormat('en-GB', {
              day: '2-digit',
              month: 'short',
              year: 'numeric',
            }).format(new Date(date ?? ''))}
          </Text>
          <PrintedTime />
        </View>

        <View style={reportPdfStyles.table}>
          <View style={reportPdfStyles.tableRow}>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.currentStockCol,
                reportPdfStyles.currentStockNumCol,
              ]}
            >
              No
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.currentStockCol,
                reportPdfStyles.currentStockTabeName,
              ]}
            >
              Name
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.currentStockCol,
              ]}
            >
              Quantity
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.currentStockCol,
              ]}
            >
              Purchase Price
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.tableColLast,
                reportPdfStyles.currentStockCol,
              ]}
            >
              Retail Price
            </Text>
          </View>
          {products.map((product, index) => (
            <View key={product.id} style={reportPdfStyles.tableRow}>
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.currentStockCol,
                  reportPdfStyles.currentStockNumCol,
                ]}
              >
                {index + 1}
              </Text>
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.currentStockCol,
                  reportPdfStyles.currentStockTabeName,
                ]}
              >
                {truncateText(product?.name)}
              </Text>
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.currentStockCol,
                ]}
              >
                {product.quantity}
              </Text>
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.currentStockCol,
                ]}
              >
                {product.purchasePrice}
              </Text>
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.currentStockCol,
                  reportPdfStyles.tableColLast,
                ]}
              >
                {product.retailPrice}
              </Text>
            </View>
          ))}
          <View style={reportPdfStyles.tableRowLast}>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.currentStockCol,
                reportPdfStyles.currentStockNumCol,
              ]}
            />
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.currentStockCol,
                reportPdfStyles.currentStockTabeName,
              ]}
            >
              Total
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.currentStockCol,
              ]}
            >
              {totalQuantity}
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.currentStockCol,
              ]}
            >
              {totalPurchasePrice}
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.currentStockCol,
                reportPdfStyles.tableColLast,
              ]}
            >
              {totalRetailPrice}
            </Text>
          </View>
        </View>

        <View style={reportPdfStyles.tableCalculationContainer}>
          <View style={reportPdfStyles.tableCalculation}>
            <View style={reportPdfStyles.tableSingleCalculation}>
              <Text>Total quantity:</Text>
              <Text>{totalQuantity ?? 0}</Text>
            </View>
            <View style={reportPdfStyles.tableSingleCalculation}>
              <Text>Total Purchase Price:</Text>
              <Text>{totalPurchasePrice ?? 0}</Text>
            </View>
            <View style={reportPdfStyles.tableSingleCalculation}>
              <Text>Total Retail Price:</Text>
              <Text>{totalRetailPrice ?? 0}</Text>
            </View>
          </View>
        </View>

        <SoftwareMarketingOnPdf />
      </View>
    </Page>
  );
}

export default CurrentStockPdf;
