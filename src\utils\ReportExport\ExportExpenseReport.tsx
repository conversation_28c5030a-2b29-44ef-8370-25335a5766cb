import { Document, pdf } from '@react-pdf/renderer';
import { toast } from 'react-toastify';
import * as XLSX from 'xlsx';

import ExpensesReportPdf from '@/components/WarehouseComponents/ReportsPdf/ExpensesReportPdf';
import { WarehouseDetailsInRedux } from '@/redux/slice/warehouseSlice';
import { ShopExpenseDetails } from '@/types/shopTypes/shopExpensesTypes';
import { ShopExpensesReportResponse } from '@/types/shopTypes/shopStockTransferReportTypes';

export const handleGenerateExpensesReportPdf = async (
  warehouseDetails: WarehouseDetailsInRedux,
  expenseReportData?: ShopExpensesReportResponse,
) => {
  const blob = await pdf(
    <Document>
      <ExpensesReportPdf
        expenseReportData={expenseReportData}
        warehouseDetails={warehouseDetails}
      />
    </Document>,
  ).toBlob();

  const url = URL.createObjectURL(blob);
  window.open(url);
};

export const handleGenerateExpensesReportCsv = ({
  data,
}: {
  data?: ShopExpensesReportResponse;
}) => {
  if (!data || data.data.result.length === 0) {
    toast.error('No data available to generate Excel');
    return;
  }

  // Define Excel headers
  const headers = [
    'No',
    'Expense Name',
    'Category',
    'Amount',
    'Location',
    'Created By',
    'Created At',
  ];

  // Map data rows
  const rows = data.data?.result.map(
    (deleteData: ShopExpenseDetails, index: number) => {
      return [
        index + 1,
        deleteData?.name || 'N/A',
        deleteData?.ExpenseCategory?.name || 'N/A',
        deleteData?.amount || 0,
        deleteData?.Warehouse?.name || 'N/A',
        deleteData?.createdById || 'N/A',
        deleteData?.createdAt || 'N/A',
      ];
    },
  );

  // Combine headers and rows
  const sheetData = [headers, ...rows];

  // Create a worksheet
  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // Create a workbook and add the worksheet
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Expense Report');

  // Generate Excel file and download
  const excelFileName = `expense_report.xlsx`;
  XLSX.writeFile(workbook, excelFileName);
};
