import Cookies from 'js-cookie';
import {
  ArrowRightLeft,
  AudioWaveform,
  BaggageClaim,
  BrickWall,
  ChevronDown,
  CircleParking,
  CopyCheck,
  DollarSign,
  FileClock,
  FileText,
  Globe,
  HandCoins,
  Handshake,
  History,
  LayoutDashboard,
  ListChecks,
  ListTodo,
  MapPin,
  Package,
  PackagePlus,
  PiggyBank,
  Scroll,
  ScrollText,
  Settings,
  ShoppingBasket,
  SquareTerminal,
  Store,
  Truck,
  Undo2,
  Users,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { Link, useLocation } from 'react-router-dom';

import { useGetUserProfileQuery } from '@/redux/api/userApi';
import { useGeneratePathaoTokenQuery } from '@/redux/api/warehouseApis/couriersApi';
import { useAppDispatch } from '@/redux/hooks';
import { setUserDetails } from '@/redux/slice/userSlice';
import { ROUTES } from '@/Routes';
import { SidebarItem, SingleSubmenu } from '@/types/sidebarTypes';
import { EmployeePermission } from '@/types/userTypes';
import { SingleCourierDetails } from '@/types/warehouseTypes/settingsTypes';

interface Props {
  isSidebarOpen: boolean;
}

function ShopSidebar({ isSidebarOpen }: Props) {
  const [employeeRole, setEmployeeRole] = useState<string>('');
  const [shopType, setShopType] = useState('ONLINE');
  const [expanded, setExpanded] = useState<string>('');
  const [selectedPath, setSelectedPath] = useState('');
  const [pathaoCourierId, setPathaoCourierId] = useState<string>('');
  const location = useLocation();
  const { pathname } = location;
  const shopId = pathname.split('/')[2];
  const defaultStartEndDate = `startDate=${new Date().toISOString().split('T')[0]}&endDate=${new Date().toISOString().split('T')[0]}`;

  useEffect(() => {
    setSelectedPath(pathname);
  }, [pathname, location]);

  const { data: userDetails, isLoading: isUserDetailsLoading } =
    useGetUserProfileQuery({});

  const dispatch = useAppDispatch();
  useEffect(() => {
    if (!isUserDetailsLoading && userDetails?.data) {
      const permission = userDetails?.data?.Employee?.EmployeePermission?.find(
        (single: EmployeePermission) => single?.assignedShopId === shopId,
      );
      setEmployeeRole(permission?.role ?? '');
      setShopType(permission?.AssignedShop?.type ?? 'ONLINE');
      setPathaoCourierId(
        permission?.AssignedShop?.Courier.find(
          (sin: SingleCourierDetails) => sin.type === 'PATHAO',
        )?.id ?? '',
      );

      dispatch(
        setUserDetails({
          id: userDetails?.data?.id,
          name: userDetails?.data?.name,
          email: userDetails?.data?.email,
          username: userDetails?.data?.username,
          mobileNumber: userDetails?.data?.mobileNumber,
          imgUrl: userDetails?.data?.imgUrl,
          type: userDetails?.data?.type,
          permissionInShop: permission?.role ?? '',
          shopName: permission?.AssignedShop?.name ?? '',
          shopNickname: permission?.AssignedShop?.nickName ?? '',
          shopType: permission?.AssignedShop?.type ?? '',
        }),
      );
    }
  }, [userDetails, isUserDetailsLoading]);

  const { data: pathaoTokenData } = useGeneratePathaoTokenQuery(
    { courierId: pathaoCourierId },
    { skip: !pathaoCourierId.length },
  );

  useEffect(() => {
    if (pathaoTokenData) {
      Cookies.set('pathaoAccessToken', pathaoTokenData?.data?.token);
    }
  }, [pathaoTokenData]);

  const isVisible =
    employeeRole === 'ADMIN' ||
    employeeRole === 'MANAGER' ||
    Cookies.get('type') === 'SUPER_ADMIN';

  const isVisibleForAuditOfficer =
    employeeRole === 'AUDITOR' ||
    employeeRole === 'ADMIN' ||
    employeeRole === 'MANAGER' ||
    Cookies.get('type') === 'SUPER_ADMIN';

  const sidebarItems: SidebarItem[] = [
    {
      label: 'Home',
      icon: <Undo2 className="w-4" />,
      path:
        Cookies.get('type') === 'SUPER_ADMIN'
          ? ROUTES.SUPER_ADMIN.DASHBOARD
          : Cookies.get('type') === 'EMPLOYEE'
            ? ROUTES.HOME
            : ROUTES.ORGANIZATION.DASHBOARD,
      slug: '/home',
      visible: true,
    },
    {
      label: 'Dashboard',
      icon: <LayoutDashboard className="w-4" />,
      path: ROUTES.SHOP.DASHBOARD(shopId),
      slug: `/shop/${shopId}/dashboard`,
      visible: true,
    },
    {
      label: 'Products',
      icon: <ShoppingBasket className="w-4" />,
      slug: `/shop/${shopId}/products`,
      visible: true,
      submenu: [
        {
          label: 'Products List',
          icon: <CircleParking className="w-3" />,
          path: ROUTES.SHOP.PRODUCTS(shopId),
          slug: `/shop/${shopId}/products`,
          visible: true,
        },
        {
          label: 'Brands',
          icon: <Handshake className="w-3" />,
          path: ROUTES.SHOP.BRANDS(shopId),
          slug: `/shop/${shopId}/brands`,
          visible: true,
        },
        {
          label: 'Categories',
          icon: <ListTodo className="w-3" />,
          path: ROUTES.SHOP.CATEGORIES(shopId),
          slug: `/shop/${shopId}/categories`,
          visible: true,
        },
        // {
        //   label: 'Sub Categories',
        //   icon: <Replace className="w-3" />,
        //   path: ROUTES.SHOP.SUB_CATEGORIES(shopId),
        //   slug: `/shop/${shopId}/sub-categories`,
        //   visible: true,
        // },
      ],
    },
    {
      label: 'Inventory',
      icon: <AudioWaveform className="w-4" />,
      slug: `/shop/${shopId}/stock`,
      visible: true,
      submenu: [
        {
          label: 'Stock In',
          icon: <PackagePlus className="w-3" />,
          path: ROUTES.SHOP.STOCK_ENTRY_REQUEST(shopId),
          slug: `/shop/${shopId}/stock-entry-request`,
          visible: true,
        },
        {
          label: 'Stock List',
          icon: <ScrollText className="w-3" />,
          path: ROUTES.SHOP.STOCK_LIST(shopId),
          slug: `/shop/${shopId}/stock-list`,
          visible: true,
        },
        // {
        //   label: 'Stock Location',
        //   icon: <LocateFixed className="w-3" />,
        //   path: ROUTES.SHOP.STOCK_SEARCH(shopId),
        //   slug: `/shop/${shopId}/stock-search`,
        //   visible: true,
        // },
      ],
    },
    {
      label: 'Customers',
      icon: <Users className="w-4" />,
      path: ROUTES.SHOP.CUSTOMERS(shopId),
      slug: `/shop/${shopId}/customers`,
      visible: true,
    },
    {
      label: 'Billing',
      icon: <ListChecks className="w-4" />,
      slug: `/shop/${shopId}/billing`,
      visible: true,
      submenu: [
        {
          label: 'Add New Order',
          icon: <BaggageClaim className="w-3" />,
          path: ROUTES.SHOP.ADD_NEW_ORDER(shopId),
          slug: `/shop/${shopId}/new-order`,
          visible: true,
        },
        {
          label: 'Orders List',
          icon: <Scroll className="w-3" />,
          path: ROUTES.SHOP.ORDERS(shopId),
          slug: `/shop/${shopId}/orders`,
          visible: true,
        },
        {
          label: 'Website Orders',
          icon: <Globe className="w-3" />,
          path: ROUTES.SHOP.EXTERNAL_ORDERS(shopId),
          slug: `/shop/${shopId}/external-orders`,
          visible: shopType === 'ONLINE',
        },
        {
          label: 'Payments List',
          icon: <DollarSign className="w-3" />,
          path: ROUTES.SHOP.ORDER_PAYMENTS(shopId),
          slug: `/shop/${shopId}/order-payments`,
          visible: true,
        },
        {
          label: 'Sales Summary',
          icon: <History className="w-3" />,
          path: ROUTES.SHOP.SALES_SUMMERY(shopId, defaultStartEndDate),
          slug: `/shop/${shopId}/sales-summery`,
          visible: true,
        },
        {
          label: 'Profit & Loss',
          icon: <PiggyBank className="w-3" />,
          path: ROUTES.SHOP.ACCOUNT_CLOSE(shopId, defaultStartEndDate),
          slug: `/shop/${shopId}/account-close`,
          visible: isVisible,
        },
      ],
    },
    {
      label: 'Courier',
      icon: <Truck className="w-4" />,
      slug: `/shop/${shopId}/courier`,
      visible: shopType === 'ONLINE',
      submenu: [
        {
          label: 'Bulk Entry',
          icon: <BrickWall className="w-3" />,
          path: ROUTES.SHOP.COURIER_BULK_ENTRY(shopId),
          slug: `/shop/${shopId}/courier-bulk-entry`,
          visible: true,
        },
        {
          label: 'Bookings',
          icon: <Package className="w-3" />,
          path: ROUTES.SHOP.COURIER_BOOKINGS(shopId),
          slug: `/shop/${shopId}/courier-bookings`,
          visible: true,
        },
      ],
    },
    {
      label: 'Expenses',
      icon: <Handshake className="w-3" />,
      path: ROUTES.SHOP.EXPENSES(shopId),
      slug: `/shop/${shopId}/expenses`,
      visible: true,
    },
    {
      label: 'Stock Audit',
      icon: <CopyCheck className="w-3" />,
      path: ROUTES.SHOP.WHOLE_STOCK_AUDIT(shopId),
      slug: `/shop/${shopId}/stock-audit`,
      visible: isVisibleForAuditOfficer,
    },
    {
      label: 'Transfer Cash',
      icon: <HandCoins className="w-3" />,
      path: ROUTES.SHOP.TRANSFER_CASH(shopId),
      slug: `/shop/${shopId}/transfer-cash`,
      visible: true,
    },
    /* {
      label: 'Cashbook',
      icon: <CircleDollarSign className="w-4" />,
      slug: `/shop/${shopId}/cashbook`,
      visible: true,
      submenu: [
        {
          label: 'Expenses',
          icon: <Handshake className="w-3" />,
          path: ROUTES.SHOP.EXPENSES(shopId),
          slug: `/shop/${shopId}/expenses`,
          visible: true,
        },
        {
          label: 'Transfer Cash',
          icon: <HandCoins className="w-3" />,
          path: ROUTES.SHOP.TRANSFER_CASH(shopId),
          slug: `/shop/${shopId}/transfer-cash`,
          visible: true,
        },
        // {
        //   label: 'Accounts Summary',
        //   icon: <Landmark className="w-3" />,
        //   path: ROUTES.SHOP.ACCOUNTS_SUMMERY(shopId),
        //   slug: `/shop/${shopId}/accounts-summery`,
        //   visible: true,
        // },
      ],
    }, */
    {
      label: 'Reports',
      icon: <FileText className="w-4" />,
      slug: `/shop/${shopId}/reports`,
      visible: true,
      submenu: [
        {
          label: 'Current Stock',
          icon: <AudioWaveform className="w-3" />,
          path: ROUTES.SHOP.STOCK_REPORT(shopId, defaultStartEndDate),
          slug: `/shop/${shopId}/stock-report`,
          visible: true,
        },
        {
          label: 'Inventory',
          icon: <FileClock className="w-3" />,
          path: ROUTES.SHOP.DATE_WISE_STOCK_REPORT(
            shopId,
            `date=${new Date().toISOString().split('T')[0]}`,
          ),
          slug: `/shopId/${shopId}/date-wise-stock-report`,
          visible: true,
        },
        {
          label: 'Sales',
          icon: <Store className="w-3" />,
          path: ROUTES.SHOP.SALES_REPORT(shopId, defaultStartEndDate),
          slug: `/shop/${shopId}/sales-report`,
          visible: true,
        },
        {
          label: 'Location Analysis',
          icon: <MapPin className="w-3" />,
          path: ROUTES.SHOP.LOCATION_ANALYSIS_REPORT(
            shopId,
            defaultStartEndDate,
          ),
          slug: `/shop/${shopId}/location-analysis-report`,
          visible: true,
        },

        // {
        //   label: 'Stock Analysis',
        //   icon: <Activity className="w-3" />,
        //   path: ROUTES.SHOP.STOCK_ANALYSIS_REPORT(shopId),
        //   slug: `/shop/${shopId}/stock-analysis-report`,
        //   visible: true,
        // },
        {
          label: 'Stock Transfer ',
          icon: <ArrowRightLeft className="w-3" />,
          path: ROUTES.SHOP.STOCK_TRANSFER_REPORT(shopId, defaultStartEndDate),
          slug: `/shop/${shopId}/stock-transfer-report`,
          visible: true,
        },
        // {
        //   label: 'Return Report',
        //   icon: <Undo2 className="w-3" />,
        //   path: ROUTES.SHOP.STOCK_RETURN_REPORT(shopId, defaultStartEndDate),
        //   slug: `/shop/${shopId}/stock-return-report`,
        //   visible: true,
        // },
        {
          label: 'Expenses ',
          icon: <HandCoins className="w-3" />,
          path: ROUTES.SHOP.EXPENSE_REPORT(shopId, defaultStartEndDate),
          slug: `/shop/${shopId}/expense-report`,
          visible: true,
        },
      ],
    },
    {
      label: 'API Docs',
      icon: <SquareTerminal className="w-4" />,
      path: ROUTES.SHOP.API_DOCS(shopId),
      slug: `/shop/${shopId}/api-docs`,
      visible: isVisible,
    },
    {
      label: 'Settings',
      icon: <Settings className="w-4" />,
      path: ROUTES.SHOP.SETTINGS(shopId, 'general'),
      slug: `/shop/${shopId}/settings`,
      visible: isVisible,
    },
  ];

  const link =
    'flex items-center px-4 py-2 text-[12px] lg:text-[14px] xl:text-[16px] hover:bg-gray-700 rounded-r-2xl';
  const selectedLink =
    'flex items-center px-4 py-2 text-[12px] lg:text-[14px] xl:text-[16px] hover:bg-gray-700 bg-gray-400 rounded-r-2xl';

  const fontSizeForLink = 'text-[12px] lg:text-[14px] xl:text-[16px]';

  const baseSubLink = `dropdown-link flex items-center py-2 pr-4 before:mr-2 before:inline-block before:h-[1px] before:w-5 before:bg-gray-400 before:pl-[-10px] before:content-[''] hover:bg-gray-700 rounded-r-2xl`;

  const subLink = `${baseSubLink} ${fontSizeForLink}`;
  const selectedSubLink = `${baseSubLink} ${fontSizeForLink} bg-gray-700 before:bg-white`;

  return (
    <div className="relative z-50 flex h-full bg-gray-100">
      <div
        className={`fixed inset-y-0 top-[60px] z-30 h-sidebar w-[200px] transform overflow-y-auto bg-[#28243D] text-white transition-transform delay-200 duration-200 md:relative md:top-0 md:translate-x-0 lg:w-[220px] xl:w-[260px] ${isSidebarOpen ? 'md:translate-x-0' : '-translate-x-full'}`}
      >
        <div className="flex h-full flex-col overflow-y-auto">
          {sidebarItems?.map((item: SidebarItem) => {
            return item?.visible ? (
              item?.submenu?.length ? (
                <div key={item?.slug}>
                  {item?.visible ? (
                    <button
                      type="button"
                      onClick={() =>
                        expanded === item.label
                          ? setExpanded('')
                          : setExpanded(item.label)
                      }
                      className="flex w-full items-center justify-between px-4 py-2 text-[12px] hover:bg-gray-700 lg:text-[14px] xl:text-[16px]"
                    >
                      <span className="flex items-center">
                        <span className="mr-4">{item?.icon}</span> {item.label}
                      </span>
                      <ChevronDown
                        className={`${expanded === item.label ? 'rotate-180' : ''} w-4`}
                      />
                    </button>
                  ) : (
                    ''
                  )}
                  <div
                    className={`${expanded === item.label ? 'opacity-500 block transition-opacity delay-500 duration-500 ease-in-out' : 'hidden opacity-0'} ml-[23px] border-l border-gray-400 ease-in-out`}
                  >
                    {item?.submenu?.map((subMenu: SingleSubmenu) => {
                      return subMenu?.visible ? (
                        <Link
                          to={subMenu.path ?? ''}
                          className={
                            selectedPath.includes(subMenu.slug ?? '')
                              ? selectedSubLink
                              : subLink
                          }
                          key={`${subMenu.path}-$Date.now()}`}
                        >
                          <span className="mr-[5px] translate-y-[.5px]">
                            {subMenu.icon}
                          </span>{' '}
                          {subMenu.label}
                        </Link>
                      ) : (
                        ''
                      );
                    })}
                  </div>
                </div>
              ) : (
                <Link
                  to={item.path ?? ''}
                  className={
                    selectedPath.includes(item?.slug ?? '')
                      ? selectedLink
                      : link
                  }
                  onClick={() => setExpanded(item.slug ?? '--')}
                  key={`${item.path}-$Date.now()}`}
                >
                  <span className="mr-4">{item.icon}</span> {item.label}
                </Link>
              )
            ) : (
              ''
            );
          })}
        </div>
      </div>
      <div
        className={`fixed bottom-0 z-50 w-[200px] transition-transform delay-200 duration-200 md:translate-x-0 lg:w-[220px] xl:w-[260px] ${isSidebarOpen ? 'md:translate-x-0' : '-translate-x-full'}`}
      >
        <div className="bg-[#221e33] py-2">
          <h2 className="text-center text-[10px] text-gray-300">
            {`Softs.ai © ${new Date().getFullYear()}`}
          </h2>
        </div>
      </div>
    </div>
  );
}

export default ShopSidebar;
