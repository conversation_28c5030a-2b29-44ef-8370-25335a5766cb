export const ROUTES = {
  HOME: '/',
  LOG_IN: '/login',
  SIGN_UP: '/sign-up',
  FORGET_PASSWORD: '/forget-password',
  SETUP_NEW_PASSWORD: '/setup-new-password',
  UNAUTHORIZED: '/unauthorized',
  EMPLOYEES: (query?: string) =>
    `/employees${query?.length ? `?${query}` : ''}`,
  ORGANIZATION: {
    DASHBOARD: (orgId: string) => `/organization/${orgId}/dashboard`,
    FEATURE_REQUESTS: (orgId: string) =>
      `/organization/${orgId}/feature-requests`,
    DASHBOARD_FILTER: (orgId: string, query?: string) =>
      `/organization/${orgId}/dashboard${query?.length ? `?${query}` : ''}`,
    SETTINGS: (orgId: string) => `/organization/${orgId}/settings`,
    WAREHOUSES: (orgId: string) => `/organization/${orgId}/warehouses`,
    SHOP: (orgId: string) => `/organization/${orgId}/shops`,
    EMPLOYEES: (orgId: string, query?: string) =>
      `/organization/${orgId}/employees${query?.length ? `?${query}` : ''}`,
    EMPLOYEE_PERMISSIONS: (orgId: string) =>
      `/organization/${orgId}/employee-permissions`,
    COURIERS: (orgId: string) => `/organization/${orgId}/couriers`,
    WEBHOOKS: (orgId: string) => `/organization/${orgId}/webhooks`,
    STOCK_AUDIT: (orgId: string) => `/organization/${orgId}/stock-audit`,
    API_DOCS: (orgId: string) => `/organization/${orgId}/api-docs`,
    SUBSCRIPTIONS: (orgId: string) => `/organization/${orgId}/subscriptions`,
    SUBSCRIPTION_DETAILS: (orgId: string, id?: string) =>
      `/organization/${orgId}/subscriptions/${id}`,
    BILLING: (orgId: string) => `/organization/${orgId}/billing`,
    EMPLOYEE_DETAILS: (orgId: string, id?: string) =>
      `/organization/${orgId}/employee/${id}`,
    // products
    PRODUCTS: (warehouseId: string, query?: string) =>
      `/organization/${warehouseId}/products${query?.length ? `?${query}` : ''}`,
    PRODUCT_DETAILS: (warehouseId: string, productId?: string) =>
      `/organization/${warehouseId}/products/${productId}`,
    BRANDS: (warehouseId: string, query?: string) =>
      `/organization/${warehouseId}/brands${query?.length ? `?${query}` : ''}`,
    CATEGORIES: (warehouseId: string, query?: string) =>
      `/organization/${warehouseId}/categories${query?.length ? `?${query}` : ''}`,
    SUB_CATEGORIES: (warehouseId: string, query?: string) =>
      `/organization/${warehouseId}/sub-categories${query?.length ? `?${query}` : ''}`,
  },
  WAREHOUSE: {
    DASHBOARD: (warehouseId: string, query?: string) =>
      `/warehouse/${warehouseId}/dashboard${query?.length ? `?${query}` : ''}`,
    SETTINGS: (warehouseId: string) => `/warehouse/${warehouseId}/settings`,
    PRODUCTS: (warehouseId: string, query?: string) =>
      `/warehouse/${warehouseId}/products${query?.length ? `?${query}` : ''}`,
    PRODUCT_DETAILS: (warehouseId: string, productId?: string) =>
      `/warehouse/${warehouseId}/products/${productId}`,
    BRANDS: (warehouseId: string, query?: string) =>
      `/warehouse/${warehouseId}/brands${query?.length ? `?${query}` : ''}`,
    CATEGORIES: (warehouseId: string, query?: string) =>
      `/warehouse/${warehouseId}/categories${query?.length ? `?${query}` : ''}`,
    SUB_CATEGORIES: (warehouseId: string, query?: string) =>
      `/warehouse/${warehouseId}/sub-categories${query?.length ? `?${query}` : ''}`,
    UNIT: (warehouseId: string) => `/warehouse/${warehouseId}/unit`,
    SUPPLIERS: (warehouseId: string, query?: string) =>
      `/warehouse/${warehouseId}/suppliers${query?.length ? `?${query}` : ''}`,
    PENDING_RETURNS: (warehouseId: string, query?: string) =>
      `/warehouse/${warehouseId}/pending-returns${query?.length ? `?${query}` : ''}`,
    SUPPLIER_DETAILS: (
      warehouseId?: string,
      supplierId?: string,
      query?: string,
    ) =>
      `/warehouse/${warehouseId}/suppliers/${supplierId}${query?.length ? `?${query}` : ''}`,
    CUSTOMERS: (warehouseId: string, query?: string) =>
      `/warehouse/${warehouseId}/customers${query?.length ? `?${query}` : ''}`,
    CUSTOMER_DETAILS: (warehouseId: string, customerId: string) =>
      `/warehouse/${warehouseId}/customers/${customerId}`,
    ORDERS: (warehouseId: string, query?: string) =>
      `/warehouse/${warehouseId}/orders${query?.length ? `?${query}` : ''}`,
    ORDER_DETAILS: (warehouseId: string, orderId: string) =>
      `/warehouse/${warehouseId}/orders/${orderId}`,
    EXPENSES: (warehouseId: string, query?: string) =>
      `/warehouse/${warehouseId}/expenses${query?.length ? `?${query}` : ''}`,
    EXPENSES_CATEGORIES: (warehouseId: string, query?: string) =>
      `/warehouse/${warehouseId}/expense-categories${query?.length ? `?${query}` : ''}`,
    INVOICE: (warehouseId: string, orderId: string) =>
      `/warehouse/${warehouseId}/invoice/${orderId}`,
    SELLERS: (warehouseId: string) => `/warehouse/${warehouseId}/sellers`,
    STOCK_SEARCH: (warehouseId: string, productId?: string) =>
      `/warehouse/${warehouseId}/stock-search${productId ? `?productId=${productId}` : ''}`,
    CURRENT_STOCK_REPORT: (warehouseId: string, query?: string) =>
      `/warehouse/${warehouseId}/current-stock-report${query?.length ? `?${query}` : ''}`,
    DATE_WISE_STOCK_REPORT: (warehouseId: string, query?: string) =>
      `/warehouse/${warehouseId}/date-wise-stock-report${query?.length ? `?${query}` : ''}`,
    DATE_WISE_STOCK_HANDOVER_REPORT: (warehouseId: string, query?: string) =>
      `/warehouse/${warehouseId}/date-wise-stock-handover-report${query?.length ? `?${query}` : ''}`,
    STOCK_ANALYSIS_REPORT: (warehouseId: string) =>
      `/warehouse/${warehouseId}/stock-analysis-report`,
    LOCATION_ANALYSIS_REPORT: (warehouseId: string) =>
      `/warehouse/${warehouseId}/location-analysis-report`,
    STOCK_ENTRY_REPORT: (warehouseId: string, query?: string) =>
      `/warehouse/${warehouseId}/stock-entry-report?${query}`,
    STOCK_TRANSFER_REPORT: (warehouseId: string, query?: string) =>
      `/warehouse/${warehouseId}/stock-transfer-report?${query}`,
    STOCK_DELETE_REPORT: (warehouseId: string, query?: string) =>
      `/warehouse/${warehouseId}/stock-delete-report?${query}`,
    SALES_REPORT: (warehouseId: string, query?: string) =>
      `/warehouse/${warehouseId}/sales-report${query?.length ? `?${query}` : ''}`,
    STOCK_RETURN_REPORT: (warehouseId: string, query?: string) =>
      `/warehouse/${warehouseId}/stock-return-report${query?.length ? `?${query}` : ''}`,
    EXPENSES_REPORT: (warehouseId: string, query?: string) =>
      `/warehouse/${warehouseId}/expenses-report${query?.length ? `?${query}` : ''}`,
    SELLERS_REPORT: (warehouseId: string, query?: string) =>
      `/warehouse/${warehouseId}/employees-report?${query}`,
    COURIER_SETTINGS: (warehouseId: string) =>
      `/warehouse/${warehouseId}/courier-settings`,
    COURIER_WEBHOOK: (warehouseId: string) =>
      `/warehouse/${warehouseId}/courier-webhook`,
    SMS_SETTINGS: (warehouseId: string) =>
      `/warehouse/${warehouseId}/sms-settings`,
    COURIER_BOOKINGS: (warehouseId: string) =>
      `/warehouse/${warehouseId}/courier-bookings`,
    BOOK_NEW_PARCEL: (warehouseId: string) =>
      `/warehouse/${warehouseId}/book-new-parcel`,
    SMS_BALANCE_AND_HISTORY: (warehouseId: string) =>
      `/warehouse/${warehouseId}/balance-and-history`,
    SEND_SMS: (warehouseId: string, query?: string) =>
      `/warehouse/${warehouseId}/send-sms${query?.length ? `?${query}` : ''}`,
    SALES_SUMMERY: (warehouseId: string, query?: string) =>
      `/warehouse/${warehouseId}/sales-summery?${query}`,
    ACCOUNT_CLOSE: (warehouseId: string, query?: string) =>
      `/warehouse/${warehouseId}/account-close?${query}`,
    WHOLESALE_NEW_ORDER: (warehouseId: string) =>
      `/warehouse/${warehouseId}/add-new-wholesale-order`,
    WHOLESALE_ORDERS: (warehouseId: string, query?: string) =>
      `/warehouse/${warehouseId}/wholesale-orders${query?.length ? `?${query}` : ''}`,
  },
  STOCK: {
    SINGLE_ENTRY_BARCODE: (entryId: string, warehouseId: string) =>
      `/single-entry-barcode/?entryId=${entryId}&warehouseId=${warehouseId}`,
    SINGLE_ENTRY_BARCODE_PDF: (entryId: string, warehouseId: string) =>
      `/single-entry-barcode-pdf/?entryId=${entryId}&warehouseId=${warehouseId}`,
    SINGLE_PRODUCT_BARCODE: (entryId: string, warehouseId: string) =>
      `/single-product-barcode/?invoiceId=${entryId}&warehouseId=${warehouseId}`,
    ENTRY: (warehouseId: string) => `/warehouse/${warehouseId}/stock-entry`,
    ENTRY_LIST: (warehouseId: string, query?: string) =>
      `/warehouse/${warehouseId}/purchase-invoices${query?.length ? `?${query}` : ''}`,
    ENTRY_DETAILS: (warehouseId: string, entryId: string) =>
      `/warehouse/${warehouseId}/purchase-invoices/${entryId}`,
    LIST: (warehouseId: string, query?: string) =>
      `/warehouse/${warehouseId}/stock-list${query?.length ? `?${query}` : ''}`,
    TRANSFER: (warehouseId: string, query?: string) =>
      `/warehouse/${warehouseId}/stock-transfer${query?.length ? `?${query}` : ''}`,
    TRANSFER_HISTORY: (warehouseId: string, query?: string) =>
      `/warehouse/${warehouseId}/stocks-transfer-history${query?.length ? `?${query}` : ''}`,
    TRANSFER_SCAN: (warehouseId: string, query?: string) =>
      `/warehouse/${warehouseId}/scan-stock-transfer${query?.length ? `?${query}` : ''}`,
    PULL: (warehouseId: string, query?: string) =>
      `/warehouse/${warehouseId}/stock-pull${query?.length ? `?${query}` : ''}`,
    BRANDS: (warehouseId: string) => `/warehouse/${warehouseId}/brands`,
    CATEGORIES: (warehouseId: string) => `/warehouse/${warehouseId}/categories`,
    UNIT: (warehouseId: string) => `/warehouse/${warehouseId}/unit`,
    DETAILS: (warehouseId: string, stockId: string) =>
      `/warehouse/${warehouseId}/stock-list/${stockId}`,
  },
  SHOP: {
    DASHBOARD: (shopId: string) => `/shop/${shopId}/dashboard`,
    DATE_WISE_STOCK_REPORT: (shopId: string, query?: string) =>
      `/shop/${shopId}/date-wise-stock-report${query?.length ? `?${query}` : ''}`,
    SETTINGS: (shopId: string, tab: string) =>
      `/shop/${shopId}/settings?currentTab=${tab ?? 'general'}`,
    API_DOCS: (shopId: string) => `/shop/${shopId}/api-docs`,
    PRODUCTS: (shopId: string, query?: string) =>
      `/shop/${shopId}/products${query?.length ? `?${query}` : ''}`,
    PRODUCT_DETAILS: (shopId: string, productId?: string) =>
      `/shop/${shopId}/products/${productId}`,
    BRANDS: (shopId: string, query?: string) =>
      `/shop/${shopId}/brands${query?.length ? `?${query}` : ''}`,
    CATEGORIES: (shopId: string, query?: string) =>
      `/shop/${shopId}/categories${query?.length ? `?${query}` : ''}`,
    SUB_CATEGORIES: (shopId: string, query?: string) =>
      `/shop/${shopId}/sub-categories${query?.length ? `?${query}` : ''}`,
    UNIT: (shopId: string) => `/shop/${shopId}/unit`,
    CUSTOMERS: (shopId: string, query?: string) =>
      `/shop/${shopId}/customers${query?.length ? `?${query}` : ''}`,
    CUSTOMER_DETAILS: (shopId: string, customerId: string) =>
      `/shop/${shopId}/customers/${customerId}`,
    STOCK_ENTRY_REQUEST: (shopId: string, query?: string) =>
      `/shop/${shopId}/stock-entry-request${query?.length ? `?${query}` : ''}`,
    STOCK_LIST: (shopId: string, query?: string) =>
      `/shop/${shopId}/stock-list${query?.length ? `?${query}` : ''}`,
    STOCK_SEARCH: (shopId: string, productId?: string) =>
      `/shop/${shopId}/stock-search${productId ? `?productId=${productId}` : ''}`,
    STOCK_AUDIT: (shopId: string, productId: string, query?: string) =>
      `/shop/${shopId}/stock-audit/${productId}${query?.length ? `?${query}` : ''}`,
    STOCK_AUDIT_DETAILS: (shopId: string, productId: string) =>
      `/shop/${shopId}/stock-audit/${productId}/details`,
    ORDERS: (shopId: string, query?: string) =>
      `/shop/${shopId}/orders${query?.length ? `?${query}` : ''}`,
    EXTERNAL_ORDERS: (shopId: string, query?: string) =>
      `/shop/${shopId}/external-orders${query?.length ? `?${query}` : ''}`,
    ORDER_PAYMENTS: (shopId?: string, query?: string) =>
      `/shop/${shopId}/payments${query?.length ? `?${query}` : ''}`,
    ORDER_DETAILS: (shopId: string, orderId: string) =>
      `/shop/${shopId}/orders/${orderId}`,
    EXTERNAL_ORDER_DETAILS: (shopId: string, orderId: string) =>
      `/shop/${shopId}/external-orders/${orderId}`,
    EXPENSES: (shopId: string) => `/shop/${shopId}/expenses`,
    WHOLE_STOCK_AUDIT: (shopId: string) => `/shop/${shopId}/stock-audit`,
    STOCK_REPORT: (shopId: string, query?: string) =>
      `/shop/${shopId}/stock-report?${query}`,
    STOCK_ANALYSIS_REPORT: (shopId: string) =>
      `/shop/${shopId}/stock-analysis-report`,
    LOCATION_ANALYSIS_REPORT: (shopId: string, query?: string) =>
      `/shop/${shopId}/location-analysis-report?${query}`,
    SALES_REPORT: (shopId: string, query?: string) =>
      `/shop/${shopId}/sales-report${query?.length ? `?${query}` : ''}`,
    STOCK_TRANSFER_REPORT: (shopId: string, query?: string) =>
      `/shop/${shopId}/stock-transfer-report?${query}`,
    STOCK_RETURN_REPORT: (shopId: string, query?: string) =>
      `/shop/${shopId}/stock-return-report?${query}`,
    STOCK_DETAILS: (shopId: string, stockId: string) =>
      `/shop/${shopId}/stock-list/${stockId}`,
    EXPENSE_REPORT: (shopId: string, query?: string) =>
      `/shop/${shopId}/expense-report?${query}`,
    ACCOUNTS_SUMMERY: (shopId: string, query?: string) =>
      `/shop/${shopId}/accounts-summery${query?.length ? `?${query}` : ''}`,
    TRANSFER_CASH: (shopId: string) => `/shop/${shopId}/transfer-cash`,
    INVOICE: (shopId: string, orderId: string) =>
      `/shop/${shopId}/invoice/${orderId}`,
    ADD_NEW_ORDER: (shopId: string, customerId?: string) =>
      `/shop/${shopId}/new-order${customerId?.length ? `?customerId=${customerId}` : ''}`,
    COURIER_BOOKINGS: (shopId: string, query?: string) =>
      `/shop/${shopId}/courier-bookings${query?.length ? `?${query}` : ''}`,
    COURIER_BULK_ENTRY: (shopId: string, query?: string) =>
      `/shop/${shopId}/courier-bulk-entry${query?.length ? `?${query}` : ''}`,
    COURIER_LIST: (shopId: string, query?: string) =>
      `/shop/${shopId}/courier-list${query?.length ? `?${query}` : ''}`,
    WEBHOOKS: (shopId: string, query?: string) =>
      `/shop/${shopId}/webhooks${query?.length ? `?${query}` : ''}`,
    COURIER_WEBHOOK: (shopId: string) => `/shop/${shopId}/courier-webhook`,
    BOOK_NEW_PARCEL: (shopId: string) => `/shop/${shopId}/book-new-parcel`,
    SALES_SUMMERY: (shopId: string, query?: string) =>
      `/shop/${shopId}/sales-summery?${query}`,
    ACCOUNT_CLOSE: (shopId: string, query?: string) =>
      `/shop/${shopId}/account-close${query?.length ? `?${query}` : ''}`,
  },

  SUPER_ADMIN: {
    DASHBOARD: '/super-admin/dashboard',
    SHOP: '/super-admin/organizations',
    ADMINS: '/super-admin/admins',
    PLANS: '/super-admin/plans',
    SUBSCRIPTIONS: '/super-admin/subscriptions',
    TRANSACTIONS: '/super-admin/transactions',
    ORGANIZATION_DETAILS: (orgId: string) =>
      `/super-admin/organization/${orgId}`,
  },
} as const;
