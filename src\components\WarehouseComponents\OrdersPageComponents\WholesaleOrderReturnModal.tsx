import { useState } from 'react';

import FilledButton from '@/components/reusable/Buttons/FilledButton';
import ModalTitle from '@/components/reusable/Modal/ModalTitle';
import {
  ShopOrderDetails,
  SingleWholesaleOrderItem,
} from '@/types/shopTypes/shopOrderTypes';

interface Props {
  handleClose: () => void;
  orderDetails?: ShopOrderDetails;
}

function WholesaleOrderReturnModal({ handleClose, orderDetails }: Props) {
  console.log(orderDetails);

  const [note, setNote] = useState<string>('');
  // const [returnOrderItems, { isLoading }] = useReturnOrderItemsMutation();
  const [selectedProductsForReturn, setSelectedProductsForReturn] = useState<
    string[]
  >([]);
  const [fullReturn, setFullReturn] = useState<boolean>(true);
  const [newAdminDiscount, setNewAdminDiscount] = useState<number>(0);

  const handleSelectUnselect = (id: string) => {
    if (selectedProductsForReturn?.length) {
      const isSelected = selectedProductsForReturn.some(
        (sin: any) => sin.id === id,
      );
      if (isSelected) {
        const remaining = selectedProductsForReturn.filter(
          (sin: any) => sin.id !== id,
        );
        setSelectedProductsForReturn(remaining);
      } else {
        setSelectedProductsForReturn([...selectedProductsForReturn, id]);
      }
    } else {
      setSelectedProductsForReturn([id]);
    }
  };
  return (
    <div className="flex w-[340px] flex-col gap-4 rounded-xl bg-white p-4 md:w-[500px] xl:w-[800px]">
      <ModalTitle text="Product Return Modal" handleClose={handleClose} />
      <div className="flex flex-col gap-2">
        <div className="flex items-center gap-4">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={fullReturn}
              onChange={() => setFullReturn(!fullReturn)}
              // disabled={userDetailsFromState.shopType !== 'ONLINE'}
              className="form-radio h-4 w-4 cursor-pointer text-blue-600 transition duration-150 ease-in-out"
            />
            <button
              type="button"
              className="cursor-pointer select-none text-gray-700"
              onClick={() => setFullReturn(!fullReturn)}
              // disabled={userDetailsFromState.shopType !== 'ONLINE'}
            >
              Full Return
            </button>
          </div>
          {/* <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={isDeliveryChargePaid}
              onChange={() => setIsDeliveryChargePaid(!isDeliveryChargePaid)}
              className="form-radio h-4 w-4 text-blue-600 transition duration-150 ease-in-out"
            />
            <button
              type="button"
              className="cursor-pointer select-none text-gray-700"
              onClick={() => setIsDeliveryChargePaid(!isDeliveryChargePaid)}
            >
              Delivery Charge Paid
            </button>
          </div> */}
        </div>
        <div className="flex items-center gap-4">
          <div className="group relative z-0 w-full">
            <span className="relative left-3 top-2.5 w-auto bg-white px-1 font-mono text-[12px] font-bold text-gray-900 group-focus-within:text-red-600 dark:text-gray-300">
              Previous Admin Discount
            </span>
            <input
              disabled
              className="text-10 py-55-rem block w-full rounded-lg border bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 disabled:cursor-not-allowed disabled:bg-gray-100 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
              value={orderDetails?.adminDiscount}
              placeholder="Enter Return Note"
            />
          </div>
          <div className="group relative z-0 w-full">
            <span className="relative left-3 top-2.5 w-auto bg-white px-1 font-mono text-[12px] font-bold text-gray-900 group-focus-within:text-red-600 dark:text-gray-300">
              New Admin Discount
            </span>
            <input
              type="number"
              disabled={fullReturn}
              max={orderDetails?.adminDiscount}
              min={0}
              aria-multiline
              className="text-10 py-55-rem block w-full rounded-lg border bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 disabled:cursor-not-allowed disabled:bg-gray-100 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
              value={
                fullReturn ? orderDetails?.adminDiscount : newAdminDiscount
              }
              onChange={(e) => setNewAdminDiscount(Number(e.target.value))}
              placeholder="Enter New Admin Discount"
            />
          </div>
        </div>
        {orderDetails?.wholesaleOrderItem?.map(
          (product: SingleWholesaleOrderItem) => (
            <div
              className="flex w-full items-center gap-2 rounded border px-4 py-1"
              key={product?.id}
            >
              <input
                type="checkbox"
                checked={
                  fullReturn || selectedProductsForReturn?.includes(product.id)
                }
                onClick={() => handleSelectUnselect(product?.id)}
                /* disabled={
                fullReturn ||
                product?.status === 'RETURNED' ||
                product?.status === 'DELIVERED'
              } */
              />
              <span>{product.name}</span>
              <span>
                {product.orderedQuantity} x {product.wholesalePrice}
              </span>
              {/* <span>{product?.status}</span> */}
            </div>
          ),
        )}
      </div>
      <div className="group relative z-0 mt-[-20px] w-full">
        <span className="relative left-3 top-2.5 w-auto bg-white px-1 font-mono text-[12px] font-bold text-gray-900 group-focus-within:text-red-600 dark:text-gray-300">
          Return Note
        </span>
        <textarea
          aria-multiline
          className="text-10 py-55-rem block h-20 w-full rounded-lg border bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 disabled:cursor-not-allowed disabled:bg-gray-100 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
          value={note}
          onChange={(e) => setNote(e.target.value)}
          placeholder="Enter Return Note"
        />
      </div>
      <div className="mt-[10px] flex w-full items-center justify-center">
        <FilledButton
          isLoading={false}
          text="Return"
          handleClick={() => console.log('ff')}
          isDisabled={false}
        />
      </div>
    </div>
  );
}

export default WholesaleOrderReturnModal;
