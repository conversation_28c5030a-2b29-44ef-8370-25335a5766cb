import { useFormik } from 'formik';
import { Check } from 'lucide-react';
import { toast } from 'react-toastify';
import * as Yup from 'yup';

import CustomInputField from '../reusable/CustomInputField/CustomInputField';
import DateAndTimeViewer from '../reusable/DateAndTimeViewer/DateAndTimeViewer';
import SpinnerLoader from '../reusable/SpinnerLoader/SpinnerLoader';

import {
  useGetUserProfileQuery,
  useUpdatePasswordMutation,
} from '@/redux/api/userApi';

const formikInitialValues = {
  oldPassword: '',
  newPassword: '',
  confirmPassword: '',
};

const validation = Yup.object({
  oldPassword: Yup.string().required('Old Password is required'),
  newPassword: Yup.string().required('New Password is required'),
});

function ChangePasswordOverview() {
  const { data: userDetails, isLoading: isUserDetailsLoading } =
    useGetUserProfileQuery({});

  const [updatePassword, { isLoading: isUpdatingBrand }] =
    useUpdatePasswordMutation();

  const formik = useFormik({
    initialValues: formikInitialValues,
    validationSchema: validation,

    onSubmit: async (values) => {
      toast.promise(
        updatePassword({
          oldPassword: values.oldPassword,
          newPassword: values.newPassword,
        }).unwrap(),
        {
          pending: 'Updating Password...',
          success: {
            render({ data: res }) {
              if (res?.statusCode === 200 || res?.statusCode === 201) {
                window.location.reload();
              }
              return 'Password Updated Successfully';
            },
          },
          error: {
            render({ data: error }: { data: any }) {
              console.log(error);
              return error?.data?.message ?? 'Error on update password';
            },
          },
        },
      );
    },
  });

  const baseUrl = 'https://retail-pluse-upload.s3.ap-southeast-1.amazonaws.com';
  return (
    <div className="container px-4">
      {!isUserDetailsLoading ? (
        <form onSubmit={formik.handleSubmit}>
          <div className="mt-16 flex items-center justify-center">
            <div className="max-w-4xl rounded-xl bg-[white] shadow-lg">
              <div className="grid grid-cols-1 md:grid-cols-11">
                <div className="col-span-1 flex items-center rounded-xl bg-gradient-to-r from-[#28243D] to-[#332966] p-4 md:col-span-4">
                  <div>
                    <div className="flex justify-center">
                      <img
                        className="h-20 w-20 rounded-full"
                        src={
                          userDetails?.data?.imgUrl
                            ? `${baseUrl}/${userDetails?.data?.imgUrl}`
                            : 'https://i.ibb.co/wc7Hz45/Avatar.png'
                        }
                        alt="user"
                      />
                    </div>
                    <div className="user-details mt-4">
                      <h2 className="text-center text-2xl font-semibold text-white">
                        {userDetails?.data?.name}
                      </h2>
                    </div>
                    <div className="user-stats mt-8">
                      <div className="stats-card flex items-center text-white">
                        <div className="flex h-12 w-12 items-center justify-center rounded-md bg-[#8b67cc]">
                          <Check className="text-white" />
                        </div>
                        <div className="ml-2">
                          <h5 className="text-xl text-white">
                            Last Updated At
                          </h5>
                          <DateAndTimeViewer
                            date={userDetails?.data?.Employee?.updatedAt ?? ''}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="col-span-1 px-8 py-4 md:col-span-7">
                  <h3 className="border-b border-[#28243D] text-2xl font-semibold uppercase">
                    Update Password
                  </h3>
                  <div className="mt-5 flex flex-col gap-4">
                    <CustomInputField
                      type="text"
                      placeholder="Enter Current Password"
                      name="oldPassword"
                      label="Current Password"
                      formik={formik}
                    />
                    <CustomInputField
                      type="text"
                      placeholder="Enter New Password"
                      name="newPassword"
                      label="New Password"
                      formik={formik}
                    />
                    <CustomInputField
                      type="text"
                      placeholder="Confirm New Password"
                      name="confirmPassword"
                      label="Re-Enter New Password"
                      formik={formik}
                    />

                    {formik.values.newPassword?.length &&
                    formik.values.confirmPassword?.length &&
                    formik.values.newPassword !==
                      formik.values.confirmPassword ? (
                      <div className="text-red-600">Password not match</div>
                    ) : (
                      ''
                    )}
                  </div>

                  <div className="mt-5 flex justify-end">
                    <div className="space-x-2">
                      <button
                        type="submit"
                        className="rounded-md bg-[#28243D] px-6 py-2 uppercase text-white disabled:bg-gray-400"
                        disabled={
                          isUpdatingBrand ||
                          formik.values.newPassword !==
                            formik.values.confirmPassword
                        }
                      >
                        Update Password
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </form>
      ) : (
        <SpinnerLoader />
      )}
    </div>
  );
}

export default ChangePasswordOverview;
