import Cookies from 'js-cookie';
import { useState } from 'react';
import { toast } from 'react-toastify';

import AddOrEditSellerModal from './AddOrEditSellerModal';
import SellersPageFilterModal from './SellersPageFilterModal';

import {
  DeleteButton,
  EditButton,
} from '@/components/reusable/Buttons/CommonButtons';
import FilterButton from '@/components/reusable/Buttons/FilterButton';
import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import SearchInput from '@/components/reusable/Inputs/SearchInput';
import Modal from '@/components/reusable/Modal/Modal';
import Pagination from '@/components/reusable/Pagination/Pagination';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import { useGetShopsQuery } from '@/redux/api/shopApi';
import { useGetSellersQuery } from '@/redux/api/warehouseApis/sellersApis';
import { SingleSellerDetails } from '@/types/warehouseTypes/sellersTypes';

interface Props {
  warehouseId: string;
}
function SellersPageOverview({ warehouseId }: Props) {
  // apis
  const { data, isLoading } = useGetSellersQuery({
    organizationId: Cookies.get('organizationId'),
    // role: 'MODERATOR',
  });
  const { data: shopData } = useGetShopsQuery({
    warehouseId,
  });

  // states
  const [isCreateSellerModalOpen, setIsCreateSellerModalOpen] =
    useState<boolean>(false);
  const [isEditSellerModalOpen, setIsEditSellerModalOpen] =
    useState<boolean>(false);
  const [selectedSeller, setSelectedSeller] = useState<SingleSellerDetails>();
  const [isFilterModalOpen, setIsFilterModalOpen] = useState<boolean>(false);

  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-1">
        <div className="flex items-center">
          <div className="search-title-and-btn flex items-center">
            <div className="relative">
              <div className="block xl:hidden">
                <FilterButton
                  handleClick={() => setIsFilterModalOpen(!isFilterModalOpen)}
                />
              </div>
              <div
                className={`${isFilterModalOpen ? 'block' : 'hidden'} xl:hidden`}
              >
                <SellersPageFilterModal />
              </div>
            </div>
          </div>
          <div className="hidden xl:block">
            <div className="flex items-center gap-x-2">
              <SearchInput
                placeholder="Search by Name"
                handleSubmit={(value: string) => console.log(value)}
              />
              <SearchInput
                placeholder="Search by Phone"
                handleSubmit={(value: string) => console.log(value)}
              />
            </div>
          </div>
        </div>
        {/* <div>
          <FilledButton
            isLoading={false}
            text="Add New Seller"
            handleClick={() => setIsCreateSellerModalOpen(true)}
            isDisabled={false}
          />
        </div> */}
      </div>
      {!isLoading ? (
        <div>
          <div className="tableTop w-full">
            <p>Employees List</p>
            <p>Total : {data?.pagination?.total}</p>
          </div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {data?.data?.length ? (
              <div className="full-table-box h-custom">
                <table className="full-table">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="tableHead">No</th>
                      <th className="tableHead table-col-width">Name</th>
                      <th className="tableHead">Phone Number</th>
                      <th className="tableHead">Shop</th>
                      <th className="tableHead">Role</th>
                      <th className="tableHead">Order</th>
                      <th className="tableHead">Amount</th>
                      <th className="tableHead">Salary</th>
                      <th className="tableHead">Created At</th>
                      <th className="tableHead">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y bg-slate-200">
                    {data?.data?.map(
                      (seller: SingleSellerDetails, index: number) => (
                        <tr key={seller?.id}>
                          <td className="tableData">{index + 1}</td>
                          <td className="tableData table-col-width">
                            {seller?.User?.name}
                          </td>
                          <td className="tableData">
                            {seller?.User?.mobileNumber}
                          </td>
                          <td className="tableData">
                            {seller?.assignedShopId}
                          </td>
                          <td className="tableData">{seller?.type}</td>
                          <td className="tableData">{seller?.orderCount}</td>
                          <td className="tableData">{seller?.totalSales}</td>
                          <td className="tableData">{seller?.currentSalary}</td>
                          <td className="tableData">
                            <DateAndTimeViewer date={seller?.createdAt} />
                          </td>
                          <td className="tableData">
                            <div className="flex items-center justify-center gap-2">
                              <EditButton
                                handleClick={() => {
                                  setSelectedSeller(seller);
                                  setIsEditSellerModalOpen(true);
                                }}
                              />
                              <DeleteButton
                                handleClick={() => {
                                  toast.warning('Nothing added yet');
                                }}
                              />
                            </div>
                          </td>
                        </tr>
                      ),
                    )}
                  </tbody>
                </table>
              </div>
            ) : (
              <div>
                <h2>No seller found</h2>
              </div>
            )}
          </div>
          <div className="pagination-box flex justify-end rounded bg-white p-3">
            <Pagination
              currentPage="1"
              limit={Number(10)}
              handleFilter={(fieldName: string, value: any) =>
                // handleFilter(fieldName, value)
                console.log(fieldName, value)
              }
              totalCount={data?.pagination?.total}
              totalPages={Math.ceil(
                Number(data?.pagination?.total) /
                  Number(data?.pagination?.limit),
              )}
            />
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={10} tableRow={6} />
      )}
      <Modal
        setShowModal={setIsCreateSellerModalOpen}
        showModal={isCreateSellerModalOpen}
      >
        <AddOrEditSellerModal
          type="new"
          warehouseId={warehouseId ?? ''}
          handleClose={() => setIsCreateSellerModalOpen(false)}
          shopList={shopData?.data ?? []}
        />
      </Modal>
      <Modal
        setShowModal={setIsEditSellerModalOpen}
        showModal={isEditSellerModalOpen}
      >
        <AddOrEditSellerModal
          type="edit"
          warehouseId={warehouseId ?? ''}
          handleClose={() => setIsEditSellerModalOpen(false)}
          shopList={shopData?.data ?? []}
          sellerData={selectedSeller}
        />
      </Modal>
    </div>
  );
}

export default SellersPageOverview;
