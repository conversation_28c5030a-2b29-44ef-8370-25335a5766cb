import BaseApi from '../baseApi';

import { TagTypes } from '@/redux/tag-types';
import {
  GetBarcodeListOfSingleProductResponse,
  GetBarcodeListResponse,
  GetSupplierBillListResponse,
  SingleStockEntryDetailsResponse,
} from '@/types/warehouseTypes/purchaseTypes';

interface GetBrandParams {
  warehouseId?: string;
  page?: string;
  limit?: string;
  serialNo?: string;
  supplierName?: string;
  supplierInvoiceNo?: string;
  startDate?: string;
  endDate?: string;
  type?: string;
}

const PurchaseApi = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getSupplierBills: builder.query<
      GetSupplierBillListResponse,
      GetBrandParams
    >({
      query: (params) => ({
        url: '/supplier-bill',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.BRAND],
    }),
    getSingleEntryDetails: builder.query<SingleStockEntryDetailsResponse, any>({
      query: ({ id, warehouseId }) => ({
        url: `/supplier-bill/${id}?warehouseId=${warehouseId}`,
        method: 'GET',
      }),
      providesTags: [TagTypes.SUPPLIERS],
    }),
    getSingleEntryBarcodeList: builder.query<GetBarcodeListResponse, any>({
      query: (params) => ({
        url: `/supplier-bill/bar-codes`,
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SUPPLIERS],
    }),
    getSingleProductBarcodeList: builder.query<
      GetBarcodeListOfSingleProductResponse,
      any
    >({
      query: ({ id, warehouseId }) => ({
        url: `/purchase/bar-codes/${id}?warehouseId=${warehouseId}`,
        method: 'GET',
      }),
      providesTags: [TagTypes.SUPPLIERS],
    }),
  }),
});

export const {
  useGetSupplierBillsQuery,
  useGetSingleEntryDetailsQuery,
  useGetSingleEntryBarcodeListQuery,
  useGetSingleProductBarcodeListQuery,
} = PurchaseApi;
