import axios from 'axios';
import Cookies from 'js-cookie';

export const UploadImageOnAws = async (imageFile: any) => {
  const baseUrl = import.meta.env.VITE_BASEURL;
  const organizationId = Cookies.get('organizationId');
  const shopOrWarehouseId = window.location.pathname.split('/')[2];
  const routeType = window.location.pathname.split('/')[1];
  try {
    const res = await axios.post(
      `${baseUrl}/upload/new`,
      {
        extension: 'png',
        contentType: 'images/png',
      },
      {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${Cookies.get('accessToken')}`,
          shopId:
            routeType === 'shop' ? shopOrWarehouseId ?? undefined : undefined,
          warehouseId:
            routeType === 'warehouse'
              ? shopOrWarehouseId ?? undefined
              : undefined,
          organizationId: organizationId ?? undefined,
        },
      },
    );
    if (res.status === 200 || res?.status === 201) {
      handlePutOnAws(res.data.data?.url, imageFile);
      return res?.data?.data?.key;
    }
  } catch (error) {
    console.log(error);
  }
};

const handlePutOnAws = async (signedUrl: string, imageFile: any) => {
  try {
    await fetch(signedUrl, {
      method: 'PUT',
      headers: {
        'Content-Type': imageFile.type,
      },
      body: imageFile,
    });
  } catch (error) {
    console.log(error);
  }
};
