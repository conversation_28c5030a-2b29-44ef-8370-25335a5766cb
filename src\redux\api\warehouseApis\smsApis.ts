import { TagTypes } from '@/redux/tag-types';
import BaseApi from '../baseApi';

interface GetSmsConfigParams {
  organizationId?: string;
}

const SmsApis = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getSmsConfigList: builder.query<any, GetSmsConfigParams>({
      query: (params) => ({
        url: '/sms',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.BRAND],
    }),
    createSmsPanel: builder.mutation({
      query: (data) => ({
        url: '/sms/new',
        method: 'POST',
        data,
      }),
      invalidatesTags: [TagTypes.BRAND],
    }),
    updateSmsPanel: builder.mutation({
      query: ({ data, id }) => ({
        url: `/brand/${id}`,
        method: 'PATCH',
        data,
      }),
      invalidatesTags: [TagTypes.BRAND],
    }),
    deleteSmsPanel: builder.mutation({
      query: (id) => ({
        url: `/brand/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: [TagTypes.BRAND],
    }),
    sentBulkSms: builder.mutation({
      query: (data) => ({
        url: '/sms/sent/new',
        method: 'POST',
        data,
      }),
      invalidatesTags: [TagTypes.BRAND],
    }),
  }),
});

export const {
  useGetSmsConfigListQuery,
  useCreateSmsPanelMutation,
  useUpdateSmsPanelMutation,
  useDeleteSmsPanelMutation,
  useSentBulkSmsMutation,
} = SmsApis;
