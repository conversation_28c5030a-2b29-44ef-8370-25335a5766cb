import { AddressDetailsType, CreatedBy, Pagination } from '@/redux/commonTypes';
import {
  ProductVariant,
  SingleProductDetails,
} from '../warehouseTypes/productTypes';
import { SingleSellerDetails } from '../warehouseTypes/sellersTypes';
import { ShopCustomerDetails } from './shopCustomersTypes';
import { SingleShopDetails } from '../shopTypes';
import { SingleStockDetails } from '../warehouseTypes/stockTypes';

export interface GetShopOrdersListResponse {
  success: boolean;
  statusCode: number;
  message: string;
  data: ShopOrderDetails[];
  pagination: Pagination;
}

export interface GetShopOrderDetailsResponse {
  success: boolean;
  statusCode: number;
  message: string;
  data: ShopOrderDetails;
}

export interface ShopSingleOrderDetailsResponse {
  success: boolean;
  statusCode: number;
  message: string;
  data: ShopOrderDetails;
}

export interface ShopOrderDetails {
  id: string;
  customerMobileNumber: string;
  customerName: string;
  createdAt: string;
  updatedAt: string;
  shopId: string;
  customerId: string;
  note: any;
  address: string;
  recipientArea: string;
  recipientCity: string;
  recipientZone: string;
  trackingNumber: any;
  subTotal: number;
  grandTotal: number;
  additionalCost: number;
  deliveryCharge: number;
  deliveryCost: number;
  productDiscount: number;
  adminDiscount: number;
  vat: number;
  isInvoicePrinted: boolean;
  totalPaid: number;
  totalDue: number;
  customerPaid: number;
  serialNo: number;
  returnAmount: number;
  deliveryPartner: string;
  paymentMethod: string;
  orderStatus: string;
  bulkBookingStatus: string;
  invoiceNo: string;
  paymentStatus: string;
  OrderItem: ShopSingleOrderItem[];
  wholesaleOrderItem: SingleWholesaleOrderItem[];
  Address: AddressDetailsType;
  Customer: ShopCustomerDetails;
  Employee: SingleSellerDetails;
  CreatedBy: CreatedBy;
  CourierBooking: CourierDataOnSingleOrder[];
  Shop: SingleShopDetails;
  Payment: OrderSinglePaymentDetails[];
  Warehouse: SingleShopDetails;
  Return: { id: string }[];
  orderNotes: SingleNoteOnOrder[];
  WebhookLog: SingleWebhookLog[];
}

export interface SingleWebhookLog {
  id: string;
  createdAt: string;
  updatedAt: string;
  payload: WebhookPayload;
  orderId: string;
  webhookId: string;
}

export interface WebhookPayload {
  event: string;
  store_id: number;
  timestamp: string;
  updated_at: string;
  delivery_fee?: number;
  order_status: string;
  consignment_id: string;
  merchant_order_id: string;
  order_status_slug: string;
}

export interface ShopSingleOrderItem {
  id: string;
  createdAt: string;
  updatedAt: string;
  stockId: string;
  orderId: string;
  status: string;
  Stock: SingleStockDetails;
}

export interface SingleWholesaleOrderItem {
  id: string;
  name: string;
  serialNo: number;
  orderedQuantity: number;
  wholesalePrice: number;
  retailPrice: number;
  purchasePrice: number;
}

export interface Stock {
  id: string;
  createdAt: string;
  updatedAt: string;
  warehouseId: string;
  productId: string;
  supplierId: string;
  assignedShopId: string;
  isSold: boolean;
  purchasePrice: number;
  retailPrice: number;
  barcode: number;
  manufactureDate: any;
  expireDate: any;
  discountType: any;
  discount: number;
  vat: number;
  Product: Product;
  Variant: ProductVariant;
}

export interface Product {
  id: string;
  createdAt: string;
  updatedAt: string;
  warehouseId: string;
  brandId: string;
  name: string;
  description: string;
  imgUrl: any;
  currentPurchasePrice: number;
  currentSellingPrice: number;
  discountType: string;
  discount: number;
  vat: number;
  unitId: string;
  subUnitId: any;
  unitWithSubUnitMultiplier: any;
  productCategoryId: string;
  productSubCategoryId: string;
  quantity: number;
  totalSold: any;
  ProductCategory: ProductCategory;
  ProductSubCategory: ProductSubCategory;
  Brand: Brand;
}

export interface ProductCategory {
  id: string;
  createdAt: string;
  updatedAt: string;
  name: string;
  imgUrl: any;
  warehouseId: string;
}

export interface ProductSubCategory {
  id: string;
  createdAt: string;
  updatedAt: string;
  productCategoryId: string;
  name: string;
  imgUrl: any;
  warehouseId: string;
}

export interface Brand {
  id: string;
  createdAt: string;
  updatedAt: string;
  warehouseId: string;
  name: string;
  imgUrl: any;
}

export interface SingleOrderProducts {
  id: string;
  createdAt: string;
  updatedAt: string;
  stockId: string;
  orderId: string;
  Stock: SingleOrderProductStock;
}

export interface SingleOrderProductStock {
  id: string;
  createdAt: string;
  updatedAt: string;
  warehouseId: string;
  productId: string;
  supplierId: string;
  assignedShopId: string;
  isSold: boolean;
  purchasePrice: number;
  retailPrice: number;
  barcode: number;
  manufactureDate: any;
  expireDate: any;
  discountType: string;
  discount: number;
  vat: number;
  Product: SingleProductDetails;
  Variant: ProductVariant;
}

export interface CourierDataOnSingleOrder {
  id: string;
  createdAt: string;
  updatedAt: string;
  invoice: string;
  recipientName: string;
  recipientPhone: string;
  recipientAddress: string;
  codAmount: number;
  note: any;
  orderId: string;
  courierId: string;
  createdById: string;
}

export interface ShopDetailsOnSingleOrderDetails {
  name: string;
  imgUrl: any;
  mobileNumber: string;
  address: string;
  websiteUrl: string;
  fbUrl: string;
  type: string;
}

export interface OrderSinglePaymentDetails {
  id: string;
  createdAt: string;
  updatedAt: string;
  amount: number;
  paymentMethod: string;
  orderId: string;
  createdById: string;
}

export interface SingleNoteOnOrder {
  id: string;
  createdAt: string;
  updatedAt: string;
  notes: string;
  orderId: string;
  createdById: string;
}
