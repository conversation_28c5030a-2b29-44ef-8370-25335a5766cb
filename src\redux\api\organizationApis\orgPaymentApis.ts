import { TagTypes } from '@/redux/tag-types';
import BaseApi from '../baseApi';
import { Pagination } from '@/redux/commonTypes';
import { SingleSubscription } from './orgSubscriptionsApis';

interface GetPaymentsReq {
  organizationId?: string;
}
const OrgPaymentsApis = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getOrgPaymentList: builder.query<getOrgPaymentListResponse, GetPaymentsReq>(
      {
        query: (params) => ({
          url: `/payment`,
          method: 'GET',
          params,
        }),
        providesTags: [TagTypes.ORG_PAYMENTS],
      },
    ),
    getSingleOrgPaymentDetails: builder.query<
      getOrgSinglePaymentDetailsResponse,
      string
    >({
      query: (id) => ({
        url: `/payment/${id}`,
        method: 'GET',
        // params,
      }),
      providesTags: [TagTypes.ORG_PAYMENTS],
    }),
  }),
});

export const { useGetOrgPaymentListQuery, useGetSingleOrgPaymentDetailsQuery } =
  OrgPaymentsApis;

export interface getOrgPaymentListResponse {
  success: boolean;
  message: string;
  statusCode: number;
  data: SingleOrgBill[];
  pagination: Pagination;
}
export interface getOrgSinglePaymentDetailsResponse {
  success: boolean;
  message: string;
  statusCode: number;
  data: SingleOrgBill;
}

export interface SingleOrgBill {
  id: string;
  createdAt: string;
  updatedAt: string;
  organizationId: string;
  subscriptionId: string;
  amount: number;
  dueDate: string;
  paidAt: any;
  isPaid: boolean;
  paymentMethod: string;
  paymentID: string;
  status: string;
  payUrl: string;
  referenceId: any;
  for: string;
  Subscription: SingleSubscription;
  serialNo: string;
  Organization: {
    id: string;
    name: string;
  };
}