import BaseApi from '../baseApi';

import { Organization, SingleSubscription } from './orgSubscriptionsApis';

import { Pagination } from '@/redux/commonTypes';
import { TagTypes } from '@/redux/tag-types';

interface GetPaymentsReq {
  organizationId?: string;
}
const OrgPaymentsApis = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getOrgPaymentList: builder.query<GetPaymentListResponse, GetPaymentsReq>({
      query: (params) => ({
        url: `/payment`,
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.ORG_PAYMENTS],
    }),
    getSingleOrgPaymentDetails: builder.query<
      GetOrgSinglePaymentDetailsResponse,
      string
    >({
      query: (id) => ({
        url: `/payment/${id}`,
        method: 'GET',
        // params,
      }),
      providesTags: [TagTypes.ORG_PAYMENTS],
    }),
  }),
});

export const { useGetOrgPaymentListQuery, useGetSingleOrgPaymentDetailsQuery } =
  OrgPaymentsApis;

export interface GetOrgSinglePaymentDetailsResponse {
  success: boolean;
  message: string;
  statusCode: number;
  data: Payment;
  pagination: Pagination;
}
export interface GetPaymentListResponse {
  success: boolean;
  message: string;
  statusCode: number;
  data: Payment[];
  pagination: Pagination;
}

export interface Payment {
  id: string;
  createdAt: string;
  updatedAt: string;
  amount: number;
  dueDate: any;
  paidAt: string;
  isPaid: boolean;
  paymentID: any;
  payUrl: any;
  referenceId: any;
  mobileNumber: any;
  imgUrl: any;
  serialNo: any;
  giftVoucherSerialNo: any;
  subscriptionId: any;
  orderId: string;
  supplierBillId: any;
  organizationId: string;
  createdById: string;
  paymentMethod: string;
  status: string;
  purpose: string;
  collectFrom: string;
  Subscription: SingleSubscription;
  Organization: Organization;
}

export interface SingleOrgBill {
  id: string;
  createdAt: string;
  updatedAt: string;
  organizationId: string;
  subscriptionId: string;
  amount: number;
  dueDate: string;
  paidAt: any;
  isPaid: boolean;
  paymentMethod: string;
  paymentID: string;
  status: string;
  payUrl: string;
  referenceId: any;
  for: string;
  Subscription: SingleSubscription;
  serialNo: string;
  Organization: {
    id: string;
    name: string;
  };
}
