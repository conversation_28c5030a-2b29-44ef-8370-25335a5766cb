import { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import Switch from 'react-switch';

import SpinnerLoader from '@/components/reusable/SpinnerLoader/SpinnerLoader';
import { useGetWarehouseOrderStatusWiseReportQuery } from '@/redux/api/warehouseApis/warehouseReportsApis';
import { ROUTES } from '@/Routes';
import { SingleStatusWiseSummary } from '@/types/shopTypes/shopStockTransferReportTypes';
import { formatNumberWithComma } from '@/utils/formatNumberWithComma';

// Define colors based on order status
const statusColors: Record<string, string> = {
  Delivered: 'bg-green-500 text-white',
  DELIVERED: 'bg-green-500 text-white',
  CANCELLED: 'bg-red-500 text-white',
  RETURNED: 'bg-red-400 text-white',
  PENDING: 'bg-yellow-400 text-black ',
  BOOKED: 'bg-blue-500 text-white',
  On_Hold: 'bg-orange-400 text-black',
  Assigned_for_Delivery: 'bg-indigo-500 text-white',
  Pickup_Requested: 'bg-teal-500 text-white',
  In_Transit: 'bg-purple-500 text-white',
  PARTIALLY_DELIVERED: 'bg-amber-500 text-black',
  PAID_RETURNED: 'bg-pink-500 text-white',
  At_the_Sorting_HUB: 'bg-cyan-500 text-white',
  Received_at_Last_Mile_HUB: 'bg-fuchsia-500 text-white',
  Pickup_Cancelled: 'bg-gray-500 text-white',
  Return: 'bg-red-400 text-white',
  Partial_Delivery: 'bg-lime-500 text-black',
};

interface Props {
  type: 'warehouse' | 'shop';
  warehouseId?: string;
  shopId?: string;
}

function StatusWiseSummaryForDashboard({ warehouseId, type, shopId }: Props) {
  const [viewAmount, setViewAmount] = useState(false);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [totalAmount, setTotalAmount] = useState<number>(0);
  const { data, isLoading } = useGetWarehouseOrderStatusWiseReportQuery({
    type,
    warehouseId: type === 'warehouse' ? warehouseId ?? undefined : undefined,
    shopId: type === 'shop' ? shopId ?? undefined : undefined,
  });

  useEffect(() => {
    const tot = data?.data?.reduce(
      (acc: number, sin: SingleStatusWiseSummary) => acc + Number(sin.count),
      0,
    );

    setTotalCount(Number(tot));
    const totAm = data?.data?.reduce(
      (acc: number, sin: SingleStatusWiseSummary) => acc + Number(sin.subTotal),
      0,
    );

    setTotalAmount(Number(totAm));
  }, [data]);

  return (
    <div className="rounded-lg bg-white p-4 shadow-md">
      {/* Header */}
      <div className="mb-4 flex items-center justify-between">
        <span className="text-xl font-semibold">Order Status Summary</span>
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">Show Amount</span>
          <Switch
            onChange={() => setViewAmount(!viewAmount)}
            checked={viewAmount}
            title="Show Amount"
          />
        </div>
      </div>

      {/* Loading State */}
      {isLoading ? (
        <div className="flex h-40 items-center justify-center">
          <SpinnerLoader />
        </div>
      ) : (
        <div className="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6">
          <Link
            to={
              type === 'warehouse'
                ? ROUTES.WAREHOUSE.ORDERS(warehouseId ?? '')
                : ROUTES.SHOP.ORDERS(shopId ?? '')
            }
            className="flex flex-col items-center justify-center gap-2 rounded-md bg-blue-500 p-3 text-white shadow-md"
          >
            {/* Order Status */}
            <span className="text-center text-xs font-medium">TOTAL</span>

            {/* Count or Amount */}
            <span className="text-lg font-semibold">
              {formatNumberWithComma(
                viewAmount ? totalAmount || 0 : totalCount || 0,
              )}
            </span>
          </Link>
          {data?.data?.map((sin: SingleStatusWiseSummary) => {
            const status = sin?.orderStatus.replace(/_/g, ' ');
            const bgColor = statusColors[sin.orderStatus] || 'bg-gray-300';

            return (
              <Link
                to={
                  type === 'warehouse'
                    ? ROUTES.WAREHOUSE.ORDERS(
                        warehouseId ?? '',
                        `orderStatus=${sin.orderStatus}&page=1`,
                      )
                    : ROUTES.SHOP.ORDERS(
                        shopId ?? '',
                        `orderStatus=${sin.orderStatus}&page=1`,
                      )
                }
                key={sin.orderStatus}
                className={`flex flex-col items-center justify-center gap-2 rounded-md p-3 shadow-md ${bgColor}`}
              >
                {/* Order Status */}
                <span className="text-center text-xs font-medium">
                  {status.toUpperCase()}
                </span>

                {/* Count or Amount */}
                <span className="text-lg font-semibold">
                  {viewAmount
                    ? new Intl.NumberFormat('en-IN', {
                        maximumSignificantDigits: 3,
                      }).format(sin.grandTotal)
                    : sin?.count}
                </span>
              </Link>
            );
          })}
        </div>
      )}
    </div>
  );
}

export default StatusWiseSummaryForDashboard;
