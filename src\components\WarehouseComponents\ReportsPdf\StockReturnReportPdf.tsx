import { Page, Text, View } from '@react-pdf/renderer';

import PrintedTime from './PrintedTime';
import { reportPdfStyles } from './ReportPdfStyles';
import ShopPdfHeader from './ShopPdfHeader';
import WarehousePdfHeader from './WarehousePdfHeader';

import { ShopDetailsInRedux } from '@/redux/slice/storeSlice';
import { WarehouseDetailsInRedux } from '@/redux/slice/warehouseSlice';
import {
  SingleStockReturnReport,
  StockReturnReportResponseType,
} from '@/types/reportTypes/stockReturnReportType';

function StockReturnReportPdf({
  warehouseDetails,
  data,
  shopDetails,
  viewFrom,
}: {
  warehouseDetails: WarehouseDetailsInRedux;
  data?: StockReturnReportResponseType;
  shopDetails?: ShopDetailsInRedux;
  viewFrom: 'shop' | 'warehouse';
}) {
  return (
    <Page size="A4" style={reportPdfStyles.page}>
      <View>
        {viewFrom === 'warehouse' ? (
          <WarehousePdfHeader warehouseDetails={warehouseDetails} />
        ) : (
          <ShopPdfHeader shopDetails={shopDetails} />
        )}
        <PrintedTime />
        <View
          style={{
            flexDirection: 'row',
            borderBottom: 1,
            borderColor: '#000',
            backgroundColor: '#eee',
            padding: 4,
          }}
        >
          <Text style={{ width: '6%', fontSize: 10 }}>No</Text>
          <Text style={{ width: '18%', fontSize: 10 }}>Barcode</Text>
          <Text style={{ width: '18%', fontSize: 10 }}>OrderId</Text>
          <Text style={{ width: '25%', fontSize: 10 }}>Status</Text>
          <Text style={{ width: '13%', fontSize: 10 }}>Regular Price</Text>
          <Text style={{ width: '20%', fontSize: 10 }}>Return Date</Text>
        </View>
        {data?.data?.length ? (
          data.data.map((item: SingleStockReturnReport, idx) => (
            <View
              key={item.id || idx}
              style={{
                flexDirection: 'row',
                borderBottom: 1,
                borderColor: '#eee',
                padding: 4,
              }}
            >
              <Text style={{ width: '6%', fontSize: 9 }}>{idx + 1}</Text>
              <Text style={{ width: '18%', fontSize: 9 }}>
                {item.barcode || '-'}
              </Text>
              <Text style={{ width: '18%', fontSize: 9 }}>
                {item.OrderItem[0].Order.serialNo || '-'}
              </Text>
              <Text style={{ width: '25%', fontSize: 9 }}>
                {item.status || '-'}
              </Text>
              <Text style={{ width: '13%', fontSize: 9 }}>
                {item.retailPrice != null ? item.retailPrice : '-'}
              </Text>
              <Text style={{ width: '20%', fontSize: 9 }}>
                {new Intl.DateTimeFormat('en-US', {
                  day: '2-digit',
                  month: 'short',
                  year: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit',
                }).format(new Date(item.updatedAt))}
              </Text>
            </View>
          ))
        ) : (
          <View style={{ padding: 8 }}>
            <Text style={{ fontSize: 10 }}>No data found.</Text>
          </View>
        )}
      </View>
    </Page>
  );
}

export default StockReturnReportPdf;
