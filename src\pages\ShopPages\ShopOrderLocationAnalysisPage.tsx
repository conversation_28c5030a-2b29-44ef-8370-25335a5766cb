import { useParams } from 'react-router-dom';

import LocationAnalysisPageOverview from '@/components/reusable/LocationAnalysisPageComponents/LocationAnalysisPageOverview';

function ShopOrderLocationAnalysisPage() {
  const { shopId } = useParams();

  return (
    <div>
      <LocationAnalysisPageOverview type="shop" id={shopId ?? ''} />
    </div>
  );
}

export default ShopOrderLocationAnalysisPage;
