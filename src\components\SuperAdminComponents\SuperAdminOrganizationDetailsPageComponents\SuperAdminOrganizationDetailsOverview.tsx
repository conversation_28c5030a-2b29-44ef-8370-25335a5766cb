import Cookies from 'js-cookie';
import { useEffect } from 'react';
import { Link, useParams } from 'react-router-dom';

import SpinnerLoader from '@/components/reusable/SpinnerLoader/SpinnerLoader';
import {
  useGetOrgShopsQuery,
  useGetOrgWarehousesQuery,
} from '@/redux/api/organizationApis/orgShopAndWarehouseApis';
import { useGetOrganizationDetailsQuery } from '@/redux/api/superAdminOrganizationsApi';
import { ROUTES } from '@/Routes';
import { SingleShopDetails } from '@/types/shopTypes';

function SuperAdminOrganizationDetailsOverview() {
  const { orgId } = useParams();
  const { data, isLoading } = useGetOrganizationDetailsQuery(orgId);

  const { data: warehouseData, isLoading: isWarehouseLoading } =
    useGetOrgWarehousesQuery({
      organizationId: orgId,
    });

  const { data: shopData, isLoading: isShopDataLoading } = useGetOrgShopsQuery({
    organizationId: orgId,
  });

  useEffect(() => {
    if (orgId) {
      Cookies.set('organizationId', orgId);
    }
  }, [orgId]);

  return (
    <div>
      {!isLoading ? (
        <div>
          {data?.data ? (
            <div>
              <div className="flex flex-wrap gap-6">
                <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-md">
                  <h2 className="text-lg font-semibold text-gray-800">
                    Owner Name:
                  </h2>
                  <p className="text-gray-600">{data.data.name}</p>
                  <h2 className="mt-4 text-lg font-semibold text-gray-800">
                    Owner Phone Number:
                  </h2>
                  <p className="text-gray-600">{data.data.mobileNumber}</p>
                  <h2 className="mt-4 text-lg font-semibold text-gray-800">
                    Owner Email:
                  </h2>
                  <p className="text-gray-600">{data.data.email}</p>
                </div>
                <div className="flex-1">
                  <h2 className="text-xl font-bold text-gray-800">
                    Organizations
                  </h2>
                  {data?.data?.organizations?.length ? (
                    <div className="mt-4 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                      {data?.data?.organizations?.map((organization: any) => (
                        <div
                          className="rounded-lg border border-gray-200 bg-white p-6 shadow-md"
                          key={organization?.id}
                        >
                          <h2 className="text-lg font-semibold text-gray-800">
                            Organization Name:
                          </h2>
                          <p className="text-gray-600">{organization.name}</p>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="mt-4 text-gray-600">No Organization Found</p>
                  )}
                </div>
              </div>
              {!isWarehouseLoading ? (
                <div className="mt-4">
                  <h2 className="mb-2 text-xl font-bold">Warehouses</h2>
                  {warehouseData?.data?.length ? (
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4">
                      {warehouseData?.data?.map((warehouse: any) => (
                        <Link
                          to={ROUTES.WAREHOUSE.DASHBOARD(
                            warehouse.id as string,
                          )}
                          className="relative rounded-xl border border-gray-400 bg-white p-4 shadow-lg"
                        >
                          <div className="grid grid-cols-12 items-center gap-4 xl:gap-7">
                            <img
                              src={
                                warehouse?.imgUrl
                                  ? `https://retail-pluse-upload.s3.ap-southeast-1.amazonaws.com/${
                                      warehouse?.imgUrl
                                    }`
                                  : warehouse?.permissionLevel === 'shop'
                                    ? 'https://cdn.pixabay.com/photo/2013/07/12/15/49/shop-150362_640.png'
                                    : 'https://c8.alamy.com/comp/2J2WW0P/warehouse-text-written-on-black-wooden-frame-school-blackboard-2J2WW0P.jpg'
                              }
                              alt=""
                              className="col col-span-3"
                            />
                            <div className="col col-span-9">
                              <div className="w-[85%]">
                                <span className="font-bold">
                                  {warehouse?.name}
                                </span>
                              </div>
                              <div className="mt-2 flex flex-col xl:mt-3">
                                <p className="text-sm">
                                  <span className="font-bold capitalize">
                                    Type:
                                  </span>{' '}
                                  Warehouse
                                </p>
                              </div>
                            </div>
                          </div>
                        </Link>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-600">No Warehouse Found</p>
                  )}
                </div>
              ) : (
                <SpinnerLoader />
              )}
              <hr className="my-2 h-[2px] bg-black text-black" />
              {!isShopDataLoading ? (
                <div className="mt-2">
                  <h2 className="mb-2 text-xl font-bold">Shops</h2>
                  {shopData?.data?.length ? (
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4">
                      {shopData?.data?.map((shop: SingleShopDetails) => (
                        <Link
                          to={ROUTES.SHOP.DASHBOARD(shop?.id as string)}
                          className="relative rounded-xl border border-gray-400 bg-white p-4 shadow-lg"
                        >
                          <div className="grid grid-cols-12 items-center gap-4 xl:gap-7">
                            <img
                              src={
                                shop?.imgUrl
                                  ? `https://retail-pluse-upload.s3.ap-southeast-1.amazonaws.com/${
                                      shop?.imgUrl
                                    }`
                                  : 'https://cdn.pixabay.com/photo/2013/07/12/15/49/shop-150362_640.png'
                              }
                              alt=""
                              className="col col-span-3"
                            />
                            <div className="col col-span-9">
                              <div className="w-[85%]">
                                <span className="font-bold">
                                  {shop?.name} ({shop?.nickName})
                                </span>
                              </div>
                              <div className="mt-2 flex flex-col xl:mt-3">
                                <p className="text-sm">
                                  <span className="font-bold capitalize">
                                    Type:
                                  </span>{' '}
                                  Shop
                                </p>
                              </div>
                            </div>
                          </div>
                        </Link>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-600">No Shop Found</p>
                  )}
                </div>
              ) : (
                <SpinnerLoader />
              )}
            </div>
          ) : (
            <h2>No Details Found</h2>
          )}
        </div>
      ) : (
        <SpinnerLoader />
      )}
    </div>
  );
}

export default SuperAdminOrganizationDetailsOverview;
