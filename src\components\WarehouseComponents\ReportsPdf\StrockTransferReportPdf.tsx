import { Page, Text, View } from '@react-pdf/renderer';

import PrintedTime from './PrintedTime';
import { reportPdfStyles } from './ReportPdfStyles';
import SoftwareMarketingOnPdf from './SoftwareMarketingOnPdf';
import WarehousePdfHeader from './WarehousePdfHeader';

import { WarehouseDetailsInRedux } from '@/redux/slice/warehouseSlice';
import { ShopStockTransferDetails } from '@/types/shopTypes/shopStockTransferReportTypes';

interface Props {
  stockTransferlist?: ShopStockTransferDetails[];
  warehouse?: WarehouseDetailsInRedux;
}

function StockTransferReportPdf({ stockTransferlist, warehouse }: Props) {
  return (
    <Page size="A4" style={reportPdfStyles.page}>
      <View>
        <WarehousePdfHeader warehouseDetails={warehouse} />
        <PrintedTime />

        <View style={reportPdfStyles.table}>
          <View style={reportPdfStyles.tableRow}>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.stockTransferCol,
                reportPdfStyles.stockTransferNoTableCol,
              ]}
            >
              No
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.stockTransferCol,
                reportPdfStyles.stockTransferDateTableCol,
              ]}
            >
              Transfer Date
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.stockTransferCol,
              ]}
            >
              Transfer From
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.stockTransferCol,
                reportPdfStyles.stockTransferToTableCol,
              ]}
            >
              Transfer To
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.stockTransferCol,
              ]}
            >
              Transfer By
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.stockTransferCol,
                reportPdfStyles.stockTransferQuantityTableCol,
              ]}
            >
              Quantity
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.tableColLast,
                reportPdfStyles.stockTransferCol,
                reportPdfStyles.stockTransferStatusTableCol,
              ]}
            >
              Status
            </Text>
          </View>
          {stockTransferlist?.map((product, index) => (
            <View
              key={product.id}
              style={
                index === stockTransferlist.length - 1
                  ? [reportPdfStyles.tableRowLast]
                  : [reportPdfStyles.tableRow]
              }
            >
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.stockTransferCol,
                  reportPdfStyles.stockTransferNoTableCol,
                ]}
              >
                {index + 1}
              </Text>
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.stockTransferCol,
                  reportPdfStyles.stockTransferDateTableCol,
                ]}
              >
                {new Intl.DateTimeFormat('en-US', {
                  day: '2-digit',
                  month: 'short',
                  year: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit',
                }).format(new Date(product.createdAt))}
              </Text>
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.stockTransferCol,
                ]}
              >
                Warehouse
              </Text>
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.stockTransferCol,
                  reportPdfStyles.stockTransferToTableCol,
                ]}
              >
                {product.Shop?.name}({product.Shop?.nickName || '-'})
              </Text>
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.stockTransferCol,
                ]}
              >
                {product.CreatedBy?.name || '-'}
              </Text>
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.stockTransferCol,
                  reportPdfStyles.stockTransferQuantityTableCol,
                ]}
              >
                {product.stockIds?.length}
              </Text>
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.stockTransferCol,
                  reportPdfStyles.tableColLast,
                  reportPdfStyles.stockTransferStatusTableCol,
                ]}
              >
                {product.status}
              </Text>
            </View>
          ))}
        </View>

        <SoftwareMarketingOnPdf />
      </View>
    </Page>
  );
}

export default StockTransferReportPdf;
