import { useFormik } from 'formik';
import { useState } from 'react';
import { toast } from 'react-toastify';
import * as Yup from 'yup';

import FilledSubmitButton from '../../reusable/Buttons/FilledSubmitButton';
import CustomInputField from '../../reusable/CustomInputField/CustomInputField';
import ModalTitle from '../../reusable/Modal/ModalTitle';

import CustomDropdown from '@/components/reusable/CustomInputField/CustomDropdown';
import ImageSelector from '@/components/reusable/ImageSelector/ImageSelector';
import { usePayToSupplierMutation } from '@/redux/api/warehouseApis/suppliersApi';
import { SingleSupplier } from '@/types/warehouseTypes/suppliersTypes';
import { UploadImageOnAws } from '@/utils/ImageUploadModule';
import { PaymentMethods } from '@/utils/staticData';

interface Props {
  handleClose: () => void;
  updateRefreshCounter: () => void;
  supplierDetails?: SingleSupplier;
}

const formikInitialValues = {
  paymentMethod: '',
  amount: 0,
};

const validation = Yup.object({
  paymentMethod: Yup.string().required('Payment Method is required'),
  amount: Yup.number().required('Payment Amount is required'),
});

function SupplierPaymentModal({
  handleClose,
  updateRefreshCounter,
  supplierDetails,
}: Props) {
  const [currentFile, setCurrentFile] = useState<any>();
  const [payToSupplier, { isLoading }] = usePayToSupplierMutation();

  const formik = useFormik({
    initialValues: formikInitialValues,
    validationSchema: validation,

    onSubmit: async (values) => {
      const imgUrl = currentFile ? await UploadImageOnAws(currentFile) : null;
      const data = {
        supplierId: supplierDetails?.id,
        paymentMethod: values.paymentMethod,
        amount: values.amount,
        imgUrl,
      };
      toast.promise(payToSupplier(data).unwrap(), {
        pending: 'Creating New Payment...',
        success: {
          render({ data: res }) {
            if (res?.statusCode === 200 || res?.statusCode === 201) {
              updateRefreshCounter();
              handleClose();
            }
            return 'Payment Done Successfully';
          },
        },
        error: {
          render({ data: error }) {
            console.log(error);
            return 'Error on creating payment';
          },
        },
      });
    },
  });

  return (
    <div className="flex w-[400px] flex-col gap-4 rounded-xl bg-white p-4">
      <ModalTitle text="Pay Supplier" handleClose={handleClose} />
      <form
        onSubmit={formik.handleSubmit}
        className="flex w-full flex-col items-center justify-center gap-2"
      >
        <div className="flex w-full gap-4">
          <div className="w-[100px]">
            <ImageSelector
              previousImage=""
              setNewImage={(e) => setCurrentFile(e)}
            />
          </div>
          <div className="flex w-full flex-col items-center justify-center gap-2">
            <CustomDropdown
              placeholder="Select Payment Method"
              name="paymentMethod"
              label="Payment Method"
              formik={formik}
              options={PaymentMethods}
            />
            <CustomInputField
              type="number"
              placeholder="Enter Paid Amount"
              name="amount"
              label="Paid Amount"
              formik={formik}
            />
          </div>
        </div>
        <div className="mt-[10px] flex w-full items-center justify-center">
          <FilledSubmitButton text="Pay" isLoading={isLoading} />
        </div>
      </form>
    </div>
  );
}

export default SupplierPaymentModal;
