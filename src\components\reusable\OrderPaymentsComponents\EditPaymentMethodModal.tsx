import { useState } from 'react';
import { toast } from 'react-toastify';

import FilledButton from '../Buttons/FilledButton';
import ModalTitle from '../Modal/ModalTitle';

import { useUpdateOrderPaymentMethodMutation } from '@/redux/api/shopApis/shopPaymentsApis';
import { OrderSinglePaymentDetails } from '@/types/shopTypes/shopOrderTypes';
import { PaymentMethods } from '@/utils/staticData';

interface Props {
  handleClose: () => void;
  paymentDetails?: OrderSinglePaymentDetails;
}

function EditPaymentMethodModal({ handleClose, paymentDetails }: Props) {
  const [selectedMethod, setSelectedMethod] = useState<string>(
    paymentDetails?.paymentMethod ?? '',
  );

  const [updateOrderPaymentMethod, { isLoading: isUpdatingPaymentMethod }] =
    useUpdateOrderPaymentMethodMutation();

  const handleUpdatePayment = () => {
    toast.promise(
      updateOrderPaymentMethod({
        id: paymentDetails?.id,
        data: {
          orderId: paymentDetails?.orderId,
          amount: paymentDetails?.amount,
          paymentMethod: selectedMethod,
        },
      }).unwrap(),
      {
        pending: 'Updating Payment...',
        success: {
          render({ data: res }) {
            if (res?.statusCode === 200 || res?.statusCode === 201) {
              handleClose();
            }
            return 'Payment updated Successfully';
          },
        },
        error: {
          render({ data: error }) {
            console.log(error);
            return 'Error on update Payment';
          },
        },
      },
    );
  };

  return (
    <div className="min-w-[450px] rounded-xl bg-white p-4">
      <ModalTitle text="Update Payment Method" handleClose={handleClose} />
      <div className="mt-4 flex items-center justify-between gap-4">
        <div className="group relative z-0 mt-[-20px] w-full">
          <span className="relative left-3 top-2.5 w-auto bg-white px-1 font-mono text-[12px] font-bold text-gray-900 group-focus-within:text-red-600 dark:text-gray-300">
            Paid Amount
          </span>
          <input
            disabled
            type="text"
            className="text-10 py-55-rem block h-10 w-full cursor-not-allowed rounded-lg border bg-gray-200 p-2.5 text-sm"
            placeholder="Price After Discount"
            value={paymentDetails?.amount}
          />
        </div>
        <div className="group relative z-0 mt-[-20px] w-full">
          <span className="relative left-3 top-2.5 w-auto bg-white px-1 font-mono text-[12px] font-bold text-gray-900 group-focus-within:text-red-600 dark:text-gray-300">
            Payment Method
          </span>
          <select
            className="} block h-10 w-full rounded-lg border bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
            onChange={(e) => setSelectedMethod(e.target.value)}
            value={selectedMethod}
          >
            <option value="" label="Select Option" />
            {PaymentMethods?.map((option: any) => (
              <option
                key={option.value}
                value={option.value}
                label={option.label}
              >
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>
      <div className="mt-4 flex items-center justify-center">
        <FilledButton
          isLoading={false}
          text="Update Payment Method"
          handleClick={() => handleUpdatePayment()}
          isDisabled={
            paymentDetails?.paymentMethod === selectedMethod ||
            isUpdatingPaymentMethod
          }
        />
      </div>
    </div>
  );
}

export default EditPaymentMethodModal;
