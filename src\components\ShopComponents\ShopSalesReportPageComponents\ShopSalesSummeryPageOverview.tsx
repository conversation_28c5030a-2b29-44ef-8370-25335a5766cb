import Cookies from 'js-cookie';
import { useLocation } from 'react-router-dom';

import FilterButton from '@/components/reusable/Buttons/FilterButton';
import StartDateEndDateWithSearch from '@/components/reusable/ReusableFilters/StartDateEndDateWithSearch';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import CategoryWiseSalesSummary from '@/components/WarehouseComponents/SalesSummeryPageComponents/CategoryWiseSalesSummary';
import ProductWiseSalesSummery from '@/components/WarehouseComponents/SalesSummeryPageComponents/ProductWiseSalesSummery';
import {
  useGetShopAccountsSummeryReportQuery,
  useGetShopCategoryWiseSaleSummeryQuery,
  useGetShopProductWiseSaleSummeryQuery,
} from '@/redux/api/shopApis/shopReportsApis';
import { SingleReceivedMethod } from '@/types/shopTypes/shopStockTransferReportTypes';
import { formatNumberWithComma } from '@/utils/formatNumberWithComma';

interface Props {
  shopId: string;
  warehouseId: string;
}

function ShopSalesSummeryPageOverview({ shopId, warehouseId }: Props) {
  const organizationId = Cookies.get('organizationId') as string;
  const router = new URLSearchParams(useLocation().search);
  const startDate = router.get('startDate') || `${new Date().toISOString()}`;
  const endDate = router.get('endDate') || `${new Date().toISOString()}`;
  const { data, isLoading, isFetching } = useGetShopAccountsSummeryReportQuery(
    {
      organizationId,
      warehouseId,
      shopId,
      type: 'custom',
      startDate,
      endDate,
    },
    { skip: !(shopId && organizationId) },
  );

  const { data: categorySummery } = useGetShopCategoryWiseSaleSummeryQuery(
    {
      organizationId,
      warehouseId,
      shopId,
      type: 'custom',
      startDate,
      endDate,
    },
    { skip: !(shopId && organizationId) },
  );

  const { data: productSummery } = useGetShopProductWiseSaleSummeryQuery(
    {
      organizationId,
      warehouseId,
      shopId,
      type: 'custom',
      startDate,
      endDate,
      sortBy: 'alphabetical_asc',
    },
    { skip: !(shopId && organizationId) },
  );

  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-1">
        <div className="flex items-center gap-x-2">
          <div className="search-title-and-btn flex items-center gap-x-3">
            {/* <p className="whitespace-nowrap">Search Filters</p> */}
            <div className="relative">
              <div className="block xl:hidden">
                <FilterButton handleClick={() => console.log('higbig')} />
              </div>
              <div className="block xl:hidden">
                {/* <ProductPageFilterModal /> */}
              </div>
            </div>
          </div>
          <div className="hidden xl:block">
            <StartDateEndDateWithSearch />
          </div>
        </div>
      </div>
      <div>
        {!isLoading && !isFetching ? (
          <div>
            <div className="tableTop w-full">
              <p>Sales Report</p>
              {/* <TransparentPrintButtonTop
                handleClick={() => {
                  handleGenerateSalesSummeryReportPdf(data);
                }}
              /> */}
            </div>
            <div className="w-full">
              <div className="full-table-box h-[160px]">
                <table className="full-table">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="tableHead">Shop</th>
                      <th className="tableHead">Invoice Qty</th>
                      <th className="tableHead">Total Items</th>
                      <th className="tableHead">MRP</th>
                      <th className="tableHead">Delivery Charge</th>
                      <th className="tableHead">Discount</th>
                      <th className="tableHead">VAT</th>
                      <th className="tableHead">Payable</th>
                      <th className="tableHead">Received</th>
                      <th className="tableHead">Due</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y bg-slate-200">
                    <tr>
                      <td className="tableData">
                        {data?.data?.shop.name}({data?.data.shop.nickName})
                      </td>
                      <td className="tableData">
                        {formatNumberWithComma(data?.data?.totalOrderCount)}
                      </td>
                      <td className="tableData">
                        {formatNumberWithComma(data?.data?.totalStockSold)}
                      </td>
                      <td className="tableData">
                        {formatNumberWithComma(data?.data?.totalSubtotal)}
                      </td>
                      <td className="tableData">
                        {formatNumberWithComma(data?.data?.totalDeliveryCharge)}
                      </td>
                      <td className="tableData">
                        {formatNumberWithComma(data?.data?.totalDiscount)}
                      </td>
                      <td className="tableData">
                        {formatNumberWithComma(data?.data?.totalVat)}
                      </td>
                      <td className="tableData">
                        {formatNumberWithComma(
                          Number(data?.data?.totalPayable) +
                            Number(data?.data?.totalDeliveryCharge),
                        )}
                      </td>
                      <td className="tableData">
                        {formatNumberWithComma(data?.data?.cashReceive)}
                      </td>
                      <td className="tableData">
                        {formatNumberWithComma(data?.data?.totalDue)}
                      </td>
                    </tr>
                    <tr>
                      <td className="tableData" colSpan={3} />
                      <td className="tableData">
                        {formatNumberWithComma(data?.data?.totalSubtotal)}
                      </td>
                      <td className="tableData">
                        {data?.data?.totalDeliveryCharge}
                      </td>
                      <td className="tableData">
                        {formatNumberWithComma(data?.data?.totalDiscount)}
                      </td>
                      <td className="tableData">
                        {formatNumberWithComma(data?.data?.totalVat)}
                      </td>
                      <td className="tableData">
                        {' '}
                        {formatNumberWithComma(
                          Number(data?.data?.totalPayable) +
                            Number(data?.data?.totalDeliveryCharge),
                        )}
                      </td>
                      <td className="tableData">
                        {formatNumberWithComma(data?.data?.cashReceive)}
                      </td>
                      <td className="tableData">
                        {formatNumberWithComma(data?.data?.totalDue)}
                      </td>
                    </tr>
                    <tr>
                      <td className="tableDataRightAlign" colSpan={9}>
                        <div className="flex items-center justify-end gap-2">
                          {data?.data?.methodBasedPaymentList?.map(
                            (singleMethod: SingleReceivedMethod) => (
                              <div>
                                <span>{singleMethod?.paymentMethod} - </span>
                                <span>
                                  {formatNumberWithComma(
                                    singleMethod?.totalAmount,
                                  )}
                                </span>
                              </div>
                            ),
                          )}
                        </div>
                      </td>
                      <td className="tableData" colSpan={1} />
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        ) : (
          <TableSkeletonLoader tableColumn={9} tableRow={6} />
        )}
      </div>
      <div className="mb-8">
        {!isLoading && !isFetching ? (
          <ProductWiseSalesSummery
            productSummery={productSummery}
            data={data}
          />
        ) : (
          <TableSkeletonLoader tableColumn={4} tableRow={2} />
        )}
      </div>

      <div className="mb-20">
        {!isLoading && !isFetching ? (
          <CategoryWiseSalesSummary
            categorySummery={categorySummery}
            data={data}
          />
        ) : (
          <TableSkeletonLoader tableColumn={4} tableRow={6} />
        )}
      </div>
    </div>
  );
}

export default ShopSalesSummeryPageOverview;
