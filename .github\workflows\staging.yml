name: Retail Pilot Staging CI/CD

on:
  push:
    branches: [dev]

permissions:
  contents: write

jobs:
  build-and-push:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Fetch full history

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Build and push
        uses: docker/build-push-action@v5
        with:
          context: .
          file: Dockerfile.staging
          push: true
          tags: |
            ${{ secrets.DOCKER_STAGING_REPOSITORY }}:latest
          build-args: |
            VITE_BASEURL=${{ secrets.VITE_STAGING_BASEURL }}

  deploy:
    needs: build-and-push
    runs-on: ubuntu-latest

    steps:
      - name: Install sshpass
        run: sudo apt-get update && sudo apt-get install -y sshpass

      - name: Deploy to Production
        env:
          SERVER_PASSWORD: ${{ secrets.SERVER_PASSWORD }}
        run: |
          # Debugging: Check SSH connection first
          echo "Checking SSH connection to ${{ secrets.SERVER_USERNAME }}@${{ secrets.SERVER_IP }}"
          sshpass -p "$SERVER_PASSWORD" ssh -o StrictHostKeyChecking=no ${{ secrets.SERVER_USERNAME }}@${{ secrets.SERVER_IP }} "echo 'SSH connection successful!'"

          # Pull the latest image
          echo "Pulling the latest Docker image..."
          sshpass -p "$SERVER_PASSWORD" ssh -o StrictHostKeyChecking=no ${{ secrets.SERVER_USERNAME }}@${{ secrets.SERVER_IP }} << 'EOF'
            
            # Pull the latest Docker image
            docker pull ${{ secrets.DOCKER_STAGING_REPOSITORY }}:latest
            
            # Check if the pull was successful
            if [ $? -eq 0 ]; then
              echo "Docker image pulled successfully."
            else
              echo "Failed to pull Docker image."
              exit 1
            fi

            # Stop existing container if it exists
            docker ps -a --filter "name=retail-pilot-staging-frontend" -q | grep -q . && docker stop retail-pilot-staging-frontend && docker rm retail-pilot-staging-frontend || true

            # Run new container with environment variables
            docker run -d \
              --name retail-pilot-staging-frontend \
              -p 5174:80 \
              ${{ secrets.DOCKER_STAGING_REPOSITORY }}:latest
          EOF
