import Cookies from 'js-cookie';
import { useState } from 'react';

import AddOrEditShopModal from '@/components/HomePageComponents/AddOrEditShopModal';
import OrganizationSingleShopCard from '@/components/OrganizationsComponents/OrganizationShopAndWarehouseComponents/OrganizationSingleShopCard';
import FilledButton from '@/components/reusable/Buttons/FilledButton';
import Modal from '@/components/reusable/Modal/Modal';
import SpinnerLoader from '@/components/reusable/SpinnerLoader/SpinnerLoader';
import { useGetOrgShopsQuery } from '@/redux/api/organizationApis/orgShopAndWarehouseApis';
import { ProtectedRoute } from '@/utils/ProtectedRoutes';

function OrganizationShopsPage() {
  const organizationId = Cookies.get('organizationId');
  const { data, isLoading, refetch } = useGetOrgShopsQuery({
    organizationId,
  });
  const [isCreateShopModalOpen, setIsCreateShopModalOpen] = useState(false);
  return (
    <ProtectedRoute>
      <div>
        <div className="mb-4 flex items-center justify-between">
          <span className="text-2xl font-bold text-[#28243D]">Shop</span>
          <FilledButton
            isLoading={false}
            text="Create New Shop"
            handleClick={() => setIsCreateShopModalOpen(true)}
            isDisabled={false}
          />
        </div>
        {!isLoading ? (
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4">
            {data?.data ? (
              data?.data?.map((shop: any) => (
                <OrganizationSingleShopCard
                  shop={shop}
                  key={shop.warehouseId || shop.shopId}
                  type="shop"
                />
              ))
            ) : (
              <p>No Shop Found</p>
            )}
          </div>
        ) : (
          <SpinnerLoader />
        )}
        <Modal
          setShowModal={setIsCreateShopModalOpen}
          showModal={isCreateShopModalOpen}
        >
          <AddOrEditShopModal
            type="new"
            handleClose={() => setIsCreateShopModalOpen(false)}
            updateRefreshCounter={refetch}
          />
        </Modal>
      </div>
    </ProtectedRoute>
  );
}

export default OrganizationShopsPage;
