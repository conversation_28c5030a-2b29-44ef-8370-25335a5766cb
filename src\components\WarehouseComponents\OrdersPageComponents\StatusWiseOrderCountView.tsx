import { useEffect, useState } from 'react';

import {
  GetShopOrderStatusWiseSummeryResponse,
  SingleStatusWiseSummary,
} from '@/types/shopTypes/shopStockTransferReportTypes';

interface Props {
  handleFilter: (field: string, value: string) => void;
  statusData?: GetShopOrderStatusWiseSummeryResponse;
  orderStatus?: string;
}

function StatusWiseOrderCountView({
  handleFilter,
  statusData,
  orderStatus,
}: Props) {
  const [totalCount, setTotalCount] = useState<number>(0);
  const [colorMap, setColorMap] = useState<
    Record<string, { bgColor: string; textColor: string }>
  >({});

  useEffect(() => {
    const tot = statusData?.data?.reduce(
      (acc: number, sin: SingleStatusWiseSummary) => acc + Number(sin.count),
      0,
    );

    setTotalCount(Number(tot));
  }, [statusData]);

  function getRandomColorWithText() {
    const letters = '0123456789abcdef';
    let bgColor = '#';
    // eslint-disable-next-line no-plusplus
    for (let i = 0; i < 6; i++) {
      bgColor += letters[Math.floor(Math.random() * 16)];
    }

    // Convert hex to RGB
    const r = parseInt(bgColor.slice(1, 3), 16);
    const g = parseInt(bgColor.slice(3, 5), 16);
    const b = parseInt(bgColor.slice(5, 7), 16);

    // Calculate luminance
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

    // If luminance is high, use dark text; otherwise, use white
    const textColor = luminance > 0.5 ? '#000000' : '#ffffff';

    return { bgColor, textColor };
  }

  useEffect(() => {
    if (statusData?.data) {
      const map: Record<string, { bgColor: string; textColor: string }> = {};
      statusData.data.forEach((sin) => {
        map[sin.orderStatus] = getRandomColorWithText();
      });
      setColorMap(map);
    }
  }, [statusData]);

  return (
    <div className="flex w-full items-center gap-1 overflow-x-auto py-1">
      <button
        type="button"
        className={`flex items-center gap-1 rounded-2xl border border-black px-2 py-1 text-xs ${orderStatus === null || orderStatus === '' ? 'bg-primary text-white' : 'bg-white'}`}
        onClick={() => handleFilter('orderStatus', '')}
      >
        <span>ALL</span>
        <span className="font-bold">{totalCount}</span>
      </button>
      {statusData?.data?.map((sin: SingleStatusWiseSummary) => {
        const { bgColor, textColor } = colorMap[sin.orderStatus] || {
          bgColor: '#ffffff',
          textColor: '#000000',
        };

        return (
          <button
            key={sin.orderStatus}
            type="button"
            className="flex items-center gap-1 rounded-2xl border border-black px-2 py-1 text-xs"
            style={
              orderStatus === sin?.orderStatus
                ? { backgroundColor: '#000000', color: '#ffffff' }
                : { backgroundColor: bgColor, color: textColor }
            }
            onClick={() => handleFilter('orderStatus', sin.orderStatus)}
          >
            <span className="text-nowrap">
              {sin.orderStatus.replace(/_/g, ' ').toUpperCase()}
            </span>
            <span className="font-bold">{sin.count}</span>
          </button>
        );
      })}
    </div>
  );
}

export default StatusWiseOrderCountView;
