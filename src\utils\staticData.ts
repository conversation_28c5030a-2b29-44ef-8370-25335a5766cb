export interface divisionTypes {
  division: string;
  districts: string[];
}
export const divisionsWithDistrict: divisionTypes[] = [
  {
    division: 'Dhaka',
    districts: [
      'Dhaka',
      'Faridpur',
      'Gazipur',
      'Gopalganj',
      'Kishoreganj',
      'Madaripur',
      'Manikganj',
      'Munshiganj',
      'Narayanganj',
      'Narsingdi',
      'Rajbari',
      'Shariatpur',
      'Tangail',
    ],
  },
  {
    division: 'Chittagong',
    districts: [
      'Chittagong',
      'Bandarban',
      'Brahmanbaria',
      'Chandpur',
      'Comilla',
      "Cox's Bazar",
      'Feni',
      'Khagrachari',
      'Lakshmipur',
      'Noakhali',
      'Rangamati',
    ],
  },
  {
    division: 'Khulna',
    districts: [
      'Bagerhat',
      'Chuadanga',
      'Jessore',
      'Jhenaidah',
      'Khulna',
      'Kushtia',
      'Magura',
      'Meherpur',
      'Narail',
      'Satkhira',
    ],
  },
  {
    division: 'Rajshahi',
    districts: [
      'Bogura',
      'Chapainawabganj',
      'Joypurhat',
      'Naogaon',
      'Natore',
      'Pabna',
      'Rajshahi',
      'Sirajganj',
    ],
  },
  {
    division: 'Barisal',
    districts: [
      'Barisal',
      'Barguna',
      'Bhola',
      'Jhalokathi',
      'Patuakhali',
      'Pirojpur',
    ],
  },
  {
    division: 'Sylhet',
    districts: ['Habiganj', 'Moulvibazar', 'Sunamganj', 'Sylhet'],
  },
  {
    division: 'Rangpur',
    districts: [
      'Dinajpur',
      'Gaibandha',
      'Kurigram',
      'Lalmonirhat',
      'Nilphamari',
      'Panchagarh',
      'Rangpur',
      'Thakurgaon',
    ],
  },
];

export const PaymentMethods = [
  { label: 'CASH', value: 'CASH' },
  { label: 'BKASH', value: 'BKASH' },
  { label: 'NAGAD', value: 'NAGAD' },
  { label: 'ROCKET', value: 'ROCKET' },
  { label: 'CARD/BANK', value: 'BANK' },
  { label: 'OTHER', value: 'OTHER' },
];

export const statusList = [
  { label: 'PENDING', value: 'PENDING' },
  { label: 'BOOKED', value: 'BOOKED' },
  { label: 'CANCELLED', value: 'CANCELLED' },
  { label: 'PICKUP REQUESTED', value: 'Pickup_Requested' },
  { label: 'ASSIGNED FOR PICKUP', value: 'Assigned_for_Pickup' },
  { label: 'PICKED', value: 'Picked' },
  { label: 'PICKUP FAILED', value: 'Pickup_Failed' },
  { label: 'PICKUP CANCELLED', value: 'Pickup_Cancelled' },
  { label: 'AT THE SORTING HUB', value: 'At_the_Sorting_HUB' },
  { label: 'IN TRANSIT', value: 'In_Transit' },
  { label: 'RECEIVED AT LAST MILE HUB', value: 'Received_at_Last_Mile_HUB' },
  { label: 'ASSIGNED FOR DELIVERY', value: 'Assigned_for_Delivery' },
  { label: 'DELIVERED', value: 'Delivered' },
  { label: 'PARTIAL DELIVERY', value: 'Partial_Delivery' },
  { label: 'RETURN', value: 'Return' },
  { label: 'DELIVERY FAILED', value: 'Delivery_Failed' },
  { label: 'ON HOLD', value: 'On_Hold' },
  { label: 'PAYMENT INVOICE', value: 'Payment_Invoice' },
  { label: 'PAID RETURN', value: 'paid_return' },
  { label: 'EXCHANGE', value: 'exchange' },
  { label: 'OTHER', value: 'OTHER' },
];
