import { useLocation } from 'react-router-dom';

import ExportButton from '@/components/reusable/Buttons/ExportButton';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import { useGetWarehouseStockReportQuery } from '@/redux/api/warehouseApis/warehouseReportsApis';
import { SingleProductStockReport } from '@/types/shopTypes/shopStockTransferReportTypes';
import { formatNumberWithComma } from '@/utils/formatNumberWithComma';
import {
  handleGenerateCurrentStockCsv,
  handleGenerateCurrentStockPdf,
} from '@/utils/ReportExport/ExportCurrentStockReport';

interface Props {
  warehouseId: string;
}

function CurrentStockReportPageOverview({ warehouseId }: Props) {
  // const navigate = useNavigate();
  const router = new URLSearchParams(useLocation().search);
  // const stockType = router.get('stockType');
  // const startDate = router.get('startDate') || `${new Date().toISOString()}`;
  const endDate = router.get('endDate') || `${new Date().toISOString()}`;
  // const stockType = router.get('stockType');
  const { data, isLoading, isFetching } = useGetWarehouseStockReportQuery(
    {
      warehouseId,
      status: 'AVAILABLE',
    },
    { skip: !warehouseId },
  );

  // const handleFilter = (fieldName: string, value: string) => {
  //   const query = generateFilterParams(fieldName, value);
  //   navigate(ROUTES.WAREHOUSE.CURRENT_STOCK_REPORT(warehouseId, query));
  // };

  return (
    <div>
      {/* <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-1">
        <div className="flex items-center gap-x-2">
          <div className="search-title-and-btn flex items-center gap-x-3">
            <div className="relative">
              <div className="block xl:hidden">
                <FilterButton handleClick={() => console.log('higbig')} />
              </div>
              <div className="block xl:hidden" />
            </div>
          </div>
          <div className="hidden xl:block">
            <div className="flex items-center gap-x-2">
              <CustomDateFilterInput
                value={endDate}
                placeholder="Select Start Date"
                label="End Date"
                handleChange={(value: string) => handleFilter('endDate', value)}
              />
              <CustomSelectForFilter
                options={[
                  { label: 'ALL', value: 'ALL' },
                  { label: 'WAREHOUSE', value: 'WAREHOUSE' },
                ]}
                selectedValue={stockType ?? ''}
                handleSelect={(e) => handleFilter('stockType', e)}
                placeHolder="Stock Type"
              />
            </div>
          </div>
        </div>
      </div> */}
      {!isLoading && !isFetching ? (
        <div>
          <div className="tableTop w-full">
            <p>Stock Report</p>
            <div className="flex items-center">
              <p>Total : {data?.data?.result?.length}</p>
              <div className="ml-4">
                <ExportButton
                  totalCount={data?.data?.result.length ?? 0}
                  handleExportCsv={() =>
                    handleGenerateCurrentStockCsv({
                      data,
                      userRole: 'ADMIN',
                    })
                  }
                  handleExportPdf={() =>
                    handleGenerateCurrentStockPdf(
                      data?.data?.result ?? [],
                      data?.data?.warehouse,
                      endDate,
                      data?.data.totalPurchasePrice,
                      data?.data.totalQuantity,
                      data?.data.totalRetailPrice,
                    )
                  }
                />
              </div>
            </div>
          </div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {data?.data?.result?.length ? (
              <div className="full-table-box h-customExc">
                <table className="full-table">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="tableHead">No</th>
                      <th className="tableHead table-col-width">Name</th>
                      <th className="tableHead">Quantity</th>
                      <th className="tableHead">Purchase Price</th>
                      <th className="tableHead">Retail Price</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y bg-slate-200">
                    {data?.data?.result?.map(
                      (product: SingleProductStockReport, index: number) => (
                        <tr key={product?.id}>
                          <td className="tableData">{index + 1}</td>
                          <td className="tableData table-col-width">
                            {product?.name}
                          </td>
                          <td className="tableData">
                            {formatNumberWithComma(product?.quantity)}
                          </td>
                          <td className="tableData">
                            {formatNumberWithComma(product?.purchasePrice)}
                          </td>
                          <td className="tableData">
                            {formatNumberWithComma(product?.retailPrice)}
                          </td>
                        </tr>
                      ),
                    )}
                    <tr>
                      <td className="tableData" />
                      <td className="tableData table-col-width">Total</td>
                      <td className="tableData">
                        {formatNumberWithComma(data?.data?.totalQuantity)}
                      </td>
                      <td className="tableData">
                        {formatNumberWithComma(data?.data?.totalPurchasePrice)}
                      </td>
                      <td className="tableData">
                        {formatNumberWithComma(data?.data?.totalRetailPrice)}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            ) : (
              <NoResultFound pageType="product" />
            )}
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={5} tableRow={6} />
      )}
    </div>
  );
}

export default CurrentStockReportPageOverview;
