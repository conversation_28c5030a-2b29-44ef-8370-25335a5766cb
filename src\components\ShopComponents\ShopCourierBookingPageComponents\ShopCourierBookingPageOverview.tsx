import {
  EllipsisVert<PERSON>,
  Eye,
  FileText,
  NotepadText,
  Printer,
} from 'lucide-react';
import { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';

import ShopOrderPageFilterModal from '../ShopOrdersPageComponents/ShopOrderPageFilterModal';

import ExportButton from '@/components/reusable/Buttons/ExportButton';
import FilterButton from '@/components/reusable/Buttons/FilterButton';
import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import Modal from '@/components/reusable/Modal/Modal';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import AddOrderNoteModal from '@/components/reusable/OrdersPagesReusableComponents/AddOrderNoteModal';
import OrderStatusViewer from '@/components/reusable/OrdersPagesReusableComponents/OrderStatusViewer';
import Pagination from '@/components/reusable/Pagination/Pagination';
import SpinnerLoader from '@/components/reusable/SpinnerLoader/SpinnerLoader';
import {
  Menubar,
  MenubarContent,
  MenubarItem,
  MenubarMenu,
  MenubarSeparator,
  MenubarTrigger,
} from '@/components/ui/menubar';
import OrderPageFilterOptions from '@/components/WarehouseComponents/OrdersPageComponents/OrderPageFilterOptions';
import { useGetShopOrdersQuery } from '@/redux/api/shopApis/shopOrdersApis';
import { useAppSelector } from '@/redux/hooks';
import { ROUTES } from '@/Routes';
import { ShopOrderDetails } from '@/types/shopTypes/shopOrderTypes';
import { handleGenerateSalesSummaryCsv } from '@/utils/GenerateCsv';
import { generateFilterParams } from '@/utils/generateFilterParams';
import {
  handleGenerateSingleOrderPdf,
  handleGenerateSingleOrderPosPdf,
} from '@/utils/GenerateOrderPdf';

interface Props {
  shopId: string;
}

function ShopCourierBookingPageOverview({ shopId }: Props) {
  const [isAddOrderNoteModalOpen, setIsAddOrderNoteModalOpen] =
    useState<boolean>(false);
  const [dueCollectionOrderDetails, setDueCollectionOrderDetails] =
    useState<ShopOrderDetails>();
  const { shopSettings } = useAppSelector((state) => state);
  const navigate = useNavigate();
  const router = new URLSearchParams(useLocation().search);
  const customerId = router.get('customerId');
  const customerName = router.get('customerName');
  const mobileNumber = router.get('mobileNumber');
  const serialNo = router.get('serialNo');
  const invoiceNo = router.get('invoiceNo');
  const paymentStatus = router.get('paymentStatus');
  const page = router.get('page');
  const limit = router.get('limit');
  const startDate = router.get('startDate');
  const endDate = router.get('endDate');
  const orderStatus = router.get('orderStatus');
  const { data, isLoading, isFetching } = useGetShopOrdersQuery({
    shopId,
    isBooked: true,
    page: page ?? '1',
    serialNo: serialNo ?? undefined,
    customerName: customerName ?? undefined,
    mobileNumber: mobileNumber ?? undefined,
    customerId: customerId ?? undefined,
    limit: limit ?? '10',
    paymentStatus: paymentStatus ?? undefined,
    invoiceNo: invoiceNo ?? undefined,
    startDate: startDate ?? undefined,
    endDate: endDate || startDate || undefined,
    type: startDate && endDate ? 'custom' : undefined,
    orderStatus: orderStatus ?? undefined,
  });

  const handleFilter = (fieldName: string, value: string) => {
    const query = generateFilterParams(fieldName, value);
    navigate(ROUTES.SHOP.COURIER_BOOKINGS(shopId, query));
  };

  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-1">
        <div className="flex items-center gap-x-2">
          <div className="search-title-and-btn flex items-center gap-x-3">
            <div className="relative">
              <div className="block xl:hidden">
                <FilterButton handleClick={() => console.log('higbig')} />
              </div>
              <div className="block xl:hidden">
                <ShopOrderPageFilterModal />
              </div>
            </div>
          </div>
          <div className="hidden xl:block">
            <div className="flex items-center gap-x-2">
              <OrderPageFilterOptions handleFilter={handleFilter} />
            </div>
          </div>
        </div>
      </div>
      {!isLoading && !isFetching ? (
        <div>
          <div className="tableTop w-full">
            <p>Booking List</p>
            <div className="flex items-center gap-4">
              <p>Total : {data?.pagination?.total}</p>
              <div className="ml-4">
                <ExportButton
                  totalCount={data?.data?.length ?? 0}
                  handleExportCsv={() =>
                    handleGenerateSalesSummaryCsv({
                      orderList: data?.data,
                      startDate: '',
                      endDate: '',
                    })
                  }
                  // handleExportPdf={() => handleGenerateSalesReportPdf(orders)}
                />
              </div>
            </div>
          </div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {data?.data?.length ? (
              <div className="full-table-box h-custom">
                <table className="full-table">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="tableHead">No</th>
                      <th className="tableHead">Order No</th>

                      <th className="tableHead table-col-width">Name</th>
                      <th className="tableHead">Phone</th>
                      {/* <th className="tableHead">Address</th> */}
                      <th className="tableHead">Total</th>
                      <th className="tableHead">Paid</th>
                      <th className="tableHead">COD</th>
                      <th className="tableHead">Booked By</th>
                      <th className="tableHead">Courier</th>
                      <th className="tableHead">Tracking</th>
                      <th className="tableHead">Status</th>
                      <th className="tableHead">Payment</th>
                      <th className="tableHead">Invoice</th>
                      {/* <th className="tableHead">Created At</th> */}
                      <th className="tableHead">Courier Entry Date</th>
                      <th className="tableHead">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y bg-slate-200">
                    {data?.data?.map(
                      (order: ShopOrderDetails, index: number) => (
                        <tr key={order?.id}>
                          <td className="tableData">
                            {(Number(page ?? 1) - 1) * Number(limit ?? 10) +
                              index +
                              1}
                          </td>
                          <td className="tableData">{order?.serialNo}</td>
                          <td className="tableData table-col-width">
                            {order?.Customer.name}
                          </td>
                          <td className="tableData">
                            {order?.Customer.mobileNumber}
                          </td>
                          {/* <td className="tableData">{order?.address}</td> */}
                          <td className="tableData">
                            {Number(order?.grandTotal ?? 0) +
                              Number(order.deliveryCharge ?? 0)}
                          </td>
                          <td className="tableData">{order?.totalPaid}</td>
                          <td className="tableData">
                            {/* {order?.CourierBooking[0]?.codAmount} */}
                            {order?.totalDue}
                          </td>
                          <td className="tableData">
                            {order?.CreatedBy?.name ?? '--'}
                          </td>
                          <td className="tableData">
                            {order?.deliveryPartner ?? '--'}
                          </td>
                          <td className="tableData">
                            {order?.deliveryPartner === 'PATHAO' ||
                            order.deliveryPartner === 'STEADFAST' ? (
                              <Link
                                to={
                                  order?.deliveryPartner === 'PATHAO' &&
                                  order.trackingNumber
                                    ? `https://merchant.pathao.com/tracking?consignment_id=${order?.trackingNumber}&phone=${order?.Customer?.mobileNumber}`
                                    : `https://steadfast.com.bd/user/consignment/${order?.trackingNumber}`
                                }
                                target="_blank"
                                className="hover:text-blue-500 hover:underline"
                              >
                                <div className="rounded bg-red-600 px-1 py-1 font-semibold text-white">
                                  {order?.trackingNumber}
                                </div>
                              </Link>
                            ) : (
                              <div className="rounded bg-red-600 px-1 py-1 font-semibold text-white">
                                {order?.trackingNumber}
                              </div>
                            )}
                          </td>
                          <td className="tableData">
                            <OrderStatusViewer status={order.orderStatus} />
                          </td>
                          <td className="tableData">
                            <OrderStatusViewer status={order.paymentStatus} />
                          </td>
                          <td className="tableData">{order.invoiceNo}</td>
                          {/* <td className="tableData">
                            <DateAndTimeViewer date={order?.createdAt} />
                          </td> */}
                          <td className="tableData">
                            {order?.CourierBooking?.length ? (
                              <DateAndTimeViewer
                                date={order?.CourierBooking[0]?.createdAt ?? ''}
                              />
                            ) : (
                              ''
                            )}
                          </td>
                          <td className="tableData">
                            <div className="flex items-center justify-center gap-2">
                              <div className="flex items-center justify-center">
                                <Menubar
                                  style={{
                                    border: 'none',
                                    backgroundColor: 'transparent',
                                  }}
                                >
                                  <MenubarMenu>
                                    <MenubarTrigger className="cursor-pointer">
                                      <EllipsisVertical />
                                    </MenubarTrigger>
                                    <MenubarContent
                                      style={{
                                        marginRight: '25px',
                                        borderColor: 'black',
                                      }}
                                    >
                                      <MenubarItem
                                        onClick={() =>
                                          navigate(
                                            ROUTES.SHOP.ORDER_DETAILS(
                                              order?.shopId,
                                              order?.id,
                                            ),
                                          )
                                        }
                                        className="flex cursor-pointer items-center gap-1 bg-primary font-semibold text-white"
                                      >
                                        <Eye size={20} />
                                        <span>View Details</span>
                                      </MenubarItem>
                                      <MenubarSeparator />
                                      <MenubarItem
                                        onClick={() => {
                                          setDueCollectionOrderDetails(order);
                                          setIsAddOrderNoteModalOpen(true);
                                        }}
                                        className="flex cursor-pointer items-center gap-1 bg-primary font-semibold text-white"
                                      >
                                        <NotepadText size={20} />
                                        <span>Add Note</span>
                                      </MenubarItem>
                                      <MenubarSeparator />
                                      <MenubarItem
                                        onClick={() => {
                                          handleGenerateSingleOrderPdf(
                                            order,
                                            shopSettings.returnPolicyText ?? '',
                                          );
                                        }}
                                        className="flex cursor-pointer items-center gap-1 bg-primary font-semibold text-white"
                                      >
                                        <FileText size={20} />
                                        <span>Print A4 Invoice</span>
                                      </MenubarItem>
                                      <MenubarSeparator />
                                      <MenubarItem
                                        onClick={() => {
                                          handleGenerateSingleOrderPosPdf(
                                            order,
                                            shopSettings.returnPolicyText ?? '',
                                          );
                                        }}
                                        className="flex cursor-pointer items-center gap-1 bg-primary font-semibold text-white"
                                      >
                                        <Printer size={20} />
                                        <span>Print POS Invoice</span>
                                      </MenubarItem>
                                    </MenubarContent>
                                  </MenubarMenu>
                                </Menubar>
                              </div>
                            </div>
                          </td>
                        </tr>
                      ),
                    )}
                  </tbody>
                </table>
              </div>
            ) : (
              <NoResultFound pageType="order" />
            )}
          </div>
          {data?.pagination?.total ? (
            <div className="pagination-box flex justify-end rounded bg-white p-3">
              <Pagination
                currentPage={page ?? '1'}
                limit={Number(limit ?? 10)}
                handleFilter={(fieldName: string, value: any) =>
                  handleFilter(fieldName, value)
                }
                totalCount={data?.pagination?.total}
                totalPages={Math.ceil(
                  Number(data?.pagination?.total) /
                    Number(data?.pagination?.limit),
                )}
              />
            </div>
          ) : (
            ''
          )}
        </div>
      ) : (
        <SpinnerLoader />
      )}
      <Modal
        showModal={isAddOrderNoteModalOpen}
        setShowModal={setIsAddOrderNoteModalOpen}
      >
        <AddOrderNoteModal
          orderDetails={dueCollectionOrderDetails}
          handleClose={() => setIsAddOrderNoteModalOpen(false)}
        />
      </Modal>
    </div>
  );
}

export default ShopCourierBookingPageOverview;
