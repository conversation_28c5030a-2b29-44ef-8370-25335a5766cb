import { SingleShopDetails } from '../shopTypes';
import { ProductVariant, SingleProductDetails } from './productTypes';
import { SupplierDetails } from './suppliersTypes';

import { CreatedBy, Pagination } from '@/redux/commonTypes';

export interface GetStockListResponse {
  success: boolean;
  statusCode: number;
  message: string;
  data: SingleStockDetails[];
  pagination: Pagination;
}

export interface GetStockEntryRequestListResponse {
  success: boolean;
  message: string;
  statusCode: number;
  pagination: Pagination;
  data: SingleStockEntryRequestType[];
}

export interface GetSingleStockEntryRequestDetailsResponse {
  success: boolean;
  message: string;
  statusCode: number;
  data: {
    id: string;
    createdAt: string;
    updatedAt: string;
    shopId: string;
    warehouseId: string;
    onHold: boolean;
    createdById: string;
    CreatedBy: CreatedBy;
    products: SingleEntryDetailsProduct[];
    status: 'PENDING' | 'ACCEPTED' | 'REJECTED';
  };
}

export interface SingleEntryDetailsProduct {
  productId: string;
  productName: string;
  quantity: number;
  stocks: StockDetailsInEntryRequest[];
}

export interface StockDetailsInEntryRequest {
  barcode: number;
  retailPrice: number;
  id: string;
  wholesalePrice: number;
  discount: number;
  discountType: string;
}

export interface AssignedShop {
  id: string;
  createdAt: string;
  updatedAt: string;
  name: string;
  imgUrl: any;
  street: any;
  district: any;
  zipCode: any;
  country: any;
  warehouseId: string;
}

export interface GetStockLocationResponse {
  success: boolean;
  statusCode: number;
  message: string;
  pagination: Pagination;
  data: StockLocationDetails[];
}

export interface StockLocationDetails {
  name: string;
  location: Location[];
}

export interface Location {
  shopName: string;
  quantity: number;
  nickname: string;
}

export interface SingleStockEntryRequestType {
  id: string;
  createdAt: string;
  updatedAt: string;
  stockIds: string[];
  shopId: string;
  warehouseId: string;
  onHold: boolean;
  status: 'PENDING' | 'ACCEPTED' | 'REJECTED';
  createdById: string;
  stocks: SingleStockDetails[];
  CreatedBy: CreatedBy;
  ReceivedBy: CreatedBy;
  Shop: SingleShopDetails;
}

export interface GetSingleStockDetailsResponse {
  success: boolean;
  message: string;
  statusCode: number;
  data: SingleStockDetails;
}

export interface SingleStockDetails {
  id: string;
  createdAt: string;
  updatedAt: string;
  warehouseId: string;
  productId: string;
  supplierId: string;
  assignedShopId: string;
  isSold: boolean;
  purchasePrice: number;
  retailPrice: number;
  barcode: number;
  manufactureDate: any;
  expireDate: any;
  discountType: string;
  discount: number;
  vat: number;
  lastTransferDate: string;
  purchaseId: string;
  createdById: string;
  isHold: boolean;
  isLocked: boolean;
  isDeleted: boolean;
  wholesalePrice: number;
  variantId: string;
  name: string;
  status: string;
  retailProfit: number;
  wholesaleProfit: number;
  StockLog: SingleStockLog[];
  OrderItem: OrderItem[];
  AssignedShop: SingleShopDetails;
  Warehouse: Warehouse;
  Supplier: SupplierDetails;
  Purchase: PurchaseDetailsOfStock;
  Product: SingleProductDetails;
  CreatedBy: CreatedBy;
  Variant: ProductVariant;
}

export interface SingleStockLog {
  id: string;
  createdAt: string;
  updatedAt: string;
  stockId: string;
  type?: string;
  message?: string;
  TransferredShopId?: string;
  createdById: string;
}

export interface OrderItem {
  status: string;
  Order: Order;
}

export interface Order {
  id: string;
  serialNo: number;
  orderStatus: string;
}

export interface AssignedShop {
  id: string;
  name: string;
  nickName: string;
}

export interface Warehouse {
  id: string;
  name: string;
}

export interface Supplier {
  User: User;
}

export interface User {
  name: string;
}

export interface PurchaseDetailsOfStock {
  id: string;
  serialNo: number;
}
