import { useParams } from 'react-router-dom';

import ShopSalesSummeryPageOverview from '@/components/ShopComponents/ShopSalesReportPageComponents/ShopSalesSummeryPageOverview';
import { useAppSelector } from '@/redux/hooks';

function ShopSalesSummeryPage() {
  const { shopId } = useParams();
  const { warehouseId } = useAppSelector((state) => state.shopDetails);
  return (
    <div>
      <ShopSalesSummeryPageOverview
        shopId={shopId ?? ''}
        warehouseId={warehouseId}
      />
    </div>
  );
}

export default ShopSalesSummeryPage;
