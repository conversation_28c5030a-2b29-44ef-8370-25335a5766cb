import BaseApi from '../baseApi';

import { TagTypes } from '@/redux/tag-types';
import { GetShopExpensesResponse } from '@/types/shopTypes/shopExpensesTypes';
import { GetBrandsResponse } from '@/types/warehouseTypes/brandsTypes';

interface GetBrandParams {
  warehouseId?: string;
  name?: string;
  page?: string;
  limit?: string;
}

const ExpensesApis = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getExpenseCategories: builder.query<GetBrandsResponse, GetBrandParams>({
      query: (params) => ({
        url: '/expense-category',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.BRAND],
    }),
    createExpenseCategory: builder.mutation({
      query: (data) => ({
        url: '/expense-category/new',
        method: 'POST',
        data,
      }),
      invalidatesTags: [TagTypes.BRAND],
    }),
    updateBrand: builder.mutation({
      query: ({ data, id }) => ({
        url: `/brand/${id}`,
        method: 'PATCH',
        data,
      }),
      invalidatesTags: [TagTypes.BRAND],
    }),
    deleteBrand: builder.mutation({
      query: (id) => ({
        url: `/brand/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: [TagTypes.BRAND],
    }),
    getExpenses: builder.query<GetShopExpensesResponse, any>({
      query: (params) => ({
        url: '/expense',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP_EXPENSES],
    }),
    addExpense: builder.mutation({
      query: (data) => ({
        url: '/expense/new',
        method: 'POST',
        data,
      }),
      invalidatesTags: [TagTypes.SHOP_EXPENSES],
    }),
  }),
});

export const {
  useGetExpenseCategoriesQuery,
  useCreateExpenseCategoryMutation,
  useDeleteBrandMutation,
  useUpdateBrandMutation,
  useGetExpensesQuery,
  useAddExpenseMutation,
} = ExpensesApis;
