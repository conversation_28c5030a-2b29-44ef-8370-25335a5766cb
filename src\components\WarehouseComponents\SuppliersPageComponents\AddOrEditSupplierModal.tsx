import { useFormik } from 'formik';
import Cookies from 'js-cookie';
import { useEffect } from 'react';
import { toast } from 'react-toastify';
import * as Yup from 'yup';

import FilledSubmitButton from '../../reusable/Buttons/FilledSubmitButton';
import CustomInputField from '../../reusable/CustomInputField/CustomInputField';
import ModalTitle from '../../reusable/Modal/ModalTitle';

import {
  useCreateSupplierMutation,
  useUpdateSupplierMutation,
} from '@/redux/api/warehouseApis/suppliersApi';
import { SingleSupplier } from '@/types/warehouseTypes/suppliersTypes';

interface Props {
  type: string;
  warehouseId: string | undefined;
  handleClose: () => void;
  updateRefreshCounter: () => void;
  supplierData?: SingleSupplier;
}

const formikInitialValues = {
  name: '',
  mobileNumber: '',
};

const validation = Yup.object({
  name: Yup.string().required('Brand name is required'),
  mobileNumber: Yup.string().required('Mobile Number is required'),
});

function AddOrEditSupplierModal({
  type,
  handleClose,
  updateRefreshCounter,
  supplierData,
}: Props) {
  const organizationId = Cookies.get('organizationId') as string;
  const [createSupplier, { isLoading }] = useCreateSupplierMutation();
  const [updateSupplier, { isLoading: isUpdatingBrand }] =
    useUpdateSupplierMutation();

  const formik = useFormik({
    initialValues: formikInitialValues,
    validationSchema: validation,

    onSubmit: async (values) => {
      if (type === 'new') {
        toast.promise(
          createSupplier({
            ...values,
            organizationId,
          }).unwrap(),
          {
            pending: 'Creating New Supplier...',
            success: {
              render({ data: res }) {
                if (res?.statusCode === 200 || res?.statusCode === 201) {
                  updateRefreshCounter();
                  handleClose();
                }
                return 'Supplier created Successfully';
              },
            },
            error: {
              render({ data: error }) {
                console.log(error);
                return 'Error on creating Supplier';
              },
            },
          },
        );
      } else {
        toast.promise(
          updateSupplier({
            data: { name: values.name },
            id: supplierData?.id,
          }).unwrap(),
          {
            pending: 'Updating Supplier...',
            success: {
              render({ data: res }) {
                if (res?.statusCode === 200 || res?.statusCode === 201) {
                  updateRefreshCounter();
                  handleClose();
                }
                return 'Supplier updated Successfully';
              },
            },
            error: {
              render({ data: error }) {
                console.log(error);
                return 'Error on update Supplier';
              },
            },
          },
        );
      }
    },
  });

  useEffect(() => {
    if (type === 'edit' && supplierData) {
      formik.setFieldValue('name', supplierData?.User?.name);
      formik.setFieldValue('mobileNumber', supplierData?.User?.mobileNumber);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [type, supplierData]);

  return (
    <div className="flex w-[400px] flex-col gap-4 rounded-xl bg-white p-4">
      <ModalTitle
        text={type === 'new' ? 'Create Supplier' : 'Edit Supplier'}
        handleClose={handleClose}
      />
      <form
        onSubmit={formik.handleSubmit}
        className="flex w-full flex-col gap-4"
      >
        <CustomInputField
          type="text"
          placeholder="Enter Supplier Name"
          name="name"
          label="Name"
          formik={formik}
        />
        <CustomInputField
          type="text"
          placeholder="Enter Supplier phone number"
          name="mobileNumber"
          label="Phone Number"
          formik={formik}
        />
        <div className="mt-[10px] flex w-full items-center justify-center">
          <FilledSubmitButton
            text={type === 'new' ? 'Create Supplier' : 'Done Editing'}
            isLoading={isLoading || isUpdatingBrand}
          />
        </div>
      </form>
    </div>
  );
}

export default AddOrEditSupplierModal;
