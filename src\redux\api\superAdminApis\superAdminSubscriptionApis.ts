import { TagTypes } from '@/redux/tag-types';
import BaseApi from '../baseApi';
import { SinglePlanOfSubscription } from '../organizationApis/orgSubscriptionsApis';
import { Pagination } from '@/redux/commonTypes';

const SuperAdminSubscriptionApis = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getSubscriptionPlansPlans: builder.query<
      GetSubscriptionsPlansResponse,
      any
    >({
      query: (params) => ({
        url: '/subscription/plans',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.ORGANIZATIONS],
    }),
  }),
});

export const { useGetSubscriptionPlansPlansQuery } = SuperAdminSubscriptionApis;

export interface GetSubscriptionsPlansResponse {
  success: boolean;
  message: string;
  statusCode: number;
  data: SinglePlanOfSubscription[];
  pagination: Pagination;
}
