import { useEffect, useRef, useState } from 'react';

import ShopWeeklyCustomerCountChart from './ShopWeeklyCustomerCountChart';
import ShopWeeklySalesSummeryChart from './ShopWeeklySalesSummeryChart';

import CustomMarqueText from '@/components/reusable/MarqueComponent/CustomMarqueText';
import SpinnerLoader from '@/components/reusable/SpinnerLoader/SpinnerLoader';
import DashboardUpdatesSection from '@/components/WarehouseComponents/DashboardPageComponents/DashboardUpdates';
import StatusWiseSummaryForDashboard from '@/components/WarehouseComponents/DashboardPageComponents/StatusWiseSummaryForDashboard';
import TopTenCustomersList from '@/components/WarehouseComponents/DashboardPageComponents/TopTenCustomersList';
import TopTenProductCards from '@/components/WarehouseComponents/DashboardPageComponents/TopTenProductCards';
import { useGetShopDashboardUpdatesQuery } from '@/redux/api/shopApis/shopDashboardApis';
import { useGetDashboardLastSevenDaysSummeryQuery } from '@/redux/api/warehouseApis/dashboardApis';

interface Props {
  shopId: string;
}

function ShopDashboardOverview({ shopId }: Props) {
  const { data: dashboardUpdates } = useGetShopDashboardUpdatesQuery({
    shopId,
    type: 'daily',
  });
  // const { data: dashboardCustomers, isLoading: isCustomersLoading } =
  //   useGetShopDashboardCustomersDataQuery({
  //     shopId,
  //     type: 'last7days',
  //   });

  // const { data: dashboardPaidOrderList, isLoading: isPaidOrderListLoading } =
  //   useGetShopDashboardPaidOrderListQuery({
  //     shopId,
  //     type: 'weekly',
  //   });

  const {
    data: dashboardSalesSummery,
    isLoading: isDashboardSalesSummeryLoading,
  } = useGetDashboardLastSevenDaysSummeryQuery({
    warehouseId: null,
    type: 'last7days',
    shopId,
  });

  // Lazy load TopTenProductCards and TopTenCustomersList
  const [showTopTen, setShowTopTen] = useState(false);
  const topTenRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const observer = new window.IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          setShowTopTen(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 },
    );
    if (topTenRef.current) {
      observer.observe(topTenRef.current);
    }
    return () => observer.disconnect();
  }, []);

  return (
    <div>
      <div className="mb-2 bg-orange-600 p-2 font-semibold text-white">
        <CustomMarqueText />
      </div>
      <div className="mb-4">
        <DashboardUpdatesSection dashboardUpdates={dashboardUpdates} />
      </div>
      <div className="mb-4">
        <StatusWiseSummaryForDashboard type="shop" shopId={shopId} />
      </div>

      <div className="mb-6 grid grid-cols-1 gap-5 xl:grid-cols-2">
        <div className="rounded-md bg-white p-5 shadow-xl">
          <div className="flex items-center justify-center">
            {!isDashboardSalesSummeryLoading ? (
              <ShopWeeklySalesSummeryChart chartData={dashboardSalesSummery} />
            ) : (
              <SpinnerLoader />
            )}
          </div>
        </div>
        <div className="rounded-md bg-white p-5 shadow-xl">
          <div className="flex items-center justify-center">
            {!isDashboardSalesSummeryLoading ? (
              <ShopWeeklyCustomerCountChart chartData={dashboardSalesSummery} />
            ) : (
              <SpinnerLoader />
            )}
          </div>
        </div>
        {/* <div className="rounded-md bg-white p-5 shadow-xl">
          {!isCustomersLoading ? (
            <WeeklyCustomerReportChart weeklyData={dashboardCustomers?.data} />
          ) : (
            <SpinnerLoader />
          )}
        </div> */}
      </div>
      <div
        className="mb-6 grid grid-cols-1 gap-5 xl:grid-cols-2"
        ref={topTenRef}
      >
        {showTopTen && (
          <>
            <TopTenProductCards type="shop" shopId={shopId} />
            <TopTenCustomersList type="shop" shopId={shopId} />
          </>
        )}
      </div>
    </div>
  );
}

export default ShopDashboardOverview;
