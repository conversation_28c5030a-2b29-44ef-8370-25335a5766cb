import BaseApi from '../baseApi';

import { TagTypes } from '@/redux/tag-types';
import { GetSubCategoriesResponse } from '@/types/warehouseTypes/subCategoriesTypes';

interface GetSubCategoriesParams {
  warehouseId?: string;
  name?: string;
  page?: string;
  limit?: string;
}
const SubCategoriesApi = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getSubCategories: builder.query<
      GetSubCategoriesResponse,
      GetSubCategoriesParams
    >({
      query: (params) => ({
        url: '/product-sub-category',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.CATEGORY],
    }),
    createSubCategory: builder.mutation({
      query: (data) => ({
        url: '/product-sub-category/new',
        method: 'POST',
        data,
      }),
      invalidatesTags: [TagTypes.CATEGORY],
    }),
    updateSubCategory: builder.mutation({
      query: ({ data, id }) => ({
        url: `/product-sub-category/${id}`,
        method: 'PATCH',
        data,
      }),
      invalidatesTags: [TagTypes.CATEGORY],
    }),
    deleteSubCategory: builder.mutation({
      query: (id) => ({
        url: `/product-sub-category/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: [TagTypes.CATEGORY],
    }),
  }),
});

export const {
  useGetSubCategoriesQuery,
  useCreateSubCategoryMutation,
  useUpdateSubCategoryMutation,
  useDeleteSubCategoryMutation,
} = SubCategoriesApi;
