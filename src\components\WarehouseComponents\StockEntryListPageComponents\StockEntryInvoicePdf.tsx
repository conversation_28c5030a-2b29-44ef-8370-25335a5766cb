import { Page, Text, View } from '@react-pdf/renderer';

import stockEntryInvoicePdfStyle from './stockEntryPdfStyle';

import { WarehouseDetailsInRedux } from '@/redux/slice/warehouseSlice';
import { SingleStockEntryDetails } from '@/types/warehouseTypes/reportTypes';
import { formatNumberWithComma } from '@/utils/formatNumberWithComma';

interface Props {
  supplierBillData?: SingleStockEntryDetails;
  warehouseDetails: WarehouseDetailsInRedux;
}

function StockEntryInvoicePdf({ supplierBillData, warehouseDetails }: Props) {
  if (!supplierBillData) return null;

  return (
    <Page size="A4" style={stockEntryInvoicePdfStyle.page}>
      {/* Header */}
      <View style={stockEntryInvoicePdfStyle.header}>
        <Text style={stockEntryInvoicePdfStyle.companyName}>
          {warehouseDetails?.name}
        </Text>
        <Text style={stockEntryInvoicePdfStyle.companyAddress}>
          {warehouseDetails?.address}
        </Text>
        <Text style={stockEntryInvoicePdfStyle.companyAddress}>
          Phone: {warehouseDetails?.mobileNumber}
        </Text>
        <View
          style={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <Text style={stockEntryInvoicePdfStyle.invoiceTitle}>
            Stock Entry Invoice
          </Text>
          <Text style={stockEntryInvoicePdfStyle.companyAddress}>
            Printed at :{' '}
            {new Intl.DateTimeFormat('en-GB', {
              day: '2-digit',
              month: 'short',
              year: 'numeric',
              hour: '2-digit',
              minute: '2-digit',
            }).format(new Date())}
          </Text>
        </View>
      </View>

      {/* Supplier Details */}
      <View style={stockEntryInvoicePdfStyle.section}>
        <Text style={stockEntryInvoicePdfStyle.sectionTitle}>
          Supplier Information
        </Text>
        <View style={stockEntryInvoicePdfStyle.row}>
          <Text style={stockEntryInvoicePdfStyle.label}>Supplier Name:</Text>
          <Text style={stockEntryInvoicePdfStyle.value}>
            {supplierBillData.Supplier?.User?.name ?? '--'}
          </Text>
        </View>
        <View style={stockEntryInvoicePdfStyle.row}>
          <Text style={stockEntryInvoicePdfStyle.label}>Invoice Number:</Text>
          <Text style={stockEntryInvoicePdfStyle.value}>
            {supplierBillData.supplierInvoiceNo}
          </Text>
        </View>
        <View style={stockEntryInvoicePdfStyle.row}>
          <Text style={stockEntryInvoicePdfStyle.label}>Purchase Date:</Text>
          <Text style={stockEntryInvoicePdfStyle.value}>
            {new Intl.DateTimeFormat('en-GB', {
              day: '2-digit',
              month: 'short',
              year: 'numeric',
            }).format(new Date(supplierBillData.purchaseDate))}
          </Text>
        </View>
      </View>

      {/* Products Table */}
      <View style={stockEntryInvoicePdfStyle.section}>
        <Text style={stockEntryInvoicePdfStyle.sectionTitle}>Products</Text>
        <View style={stockEntryInvoicePdfStyle.table}>
          {/* Table Header */}
          <View style={stockEntryInvoicePdfStyle.tableHeader}>
            <Text
              style={[
                stockEntryInvoicePdfStyle.tableCellHeader,
                stockEntryInvoicePdfStyle.col1,
              ]}
            >
              #
            </Text>
            <Text
              style={[
                stockEntryInvoicePdfStyle.tableCellHeader,
                { width: '50%' },
              ]}
            >
              Product Name
            </Text>
            <Text
              style={[
                stockEntryInvoicePdfStyle.tableCellHeader,
                stockEntryInvoicePdfStyle.col3,
                { textAlign: 'right' },
              ]}
            >
              Quantity
            </Text>
            <Text
              style={[
                stockEntryInvoicePdfStyle.tableCellHeader,
                stockEntryInvoicePdfStyle.col4,
                { textAlign: 'right' },
              ]}
            >
              TP
            </Text>
            <Text
              style={[
                stockEntryInvoicePdfStyle.tableCellHeader,
                stockEntryInvoicePdfStyle.col6,
                { textAlign: 'right' },
              ]}
            >
              Total
            </Text>
          </View>
          {/* Table Rows */}
          {supplierBillData.Purchase?.map((product, index) => (
            <View
              key={product?.Product?.name}
              style={stockEntryInvoicePdfStyle.tableRow}
            >
              <Text
                style={[
                  stockEntryInvoicePdfStyle.tableCell,
                  stockEntryInvoicePdfStyle.col1,
                ]}
              >
                {index + 1}
              </Text>
              <Text
                style={[stockEntryInvoicePdfStyle.tableCell, { width: '50%' }]}
              >
                {product.Product?.name}
              </Text>
              <Text
                style={[
                  stockEntryInvoicePdfStyle.tableCell,
                  stockEntryInvoicePdfStyle.col3,
                  { textAlign: 'right' },
                ]}
              >
                {product.quantity}
              </Text>
              <Text
                style={[
                  stockEntryInvoicePdfStyle.tableCell,
                  stockEntryInvoicePdfStyle.col4,
                  { textAlign: 'right' },
                ]}
              >
                {product.purchasePrice} Tk
              </Text>
              <Text
                style={[
                  stockEntryInvoicePdfStyle.tableCell,
                  stockEntryInvoicePdfStyle.col6,
                  { textAlign: 'right' },
                ]}
              >
                {(product.purchasePrice * product.quantity).toFixed(2)} Tk
              </Text>
            </View>
          ))}
        </View>
      </View>

      {/* Invoice Summary */}
      <View style={stockEntryInvoicePdfStyle.section}>
        <Text style={stockEntryInvoicePdfStyle.sectionTitle}>
          Invoice Summary
        </Text>
        <View style={stockEntryInvoicePdfStyle.amountRow}>
          <Text style={stockEntryInvoicePdfStyle.amountLabel}>
            Total Price:
          </Text>
          <Text style={stockEntryInvoicePdfStyle.amountValue}>
            {formatNumberWithComma(supplierBillData.totalPrice)} Tk
          </Text>
        </View>
        <View style={stockEntryInvoicePdfStyle.amountRow}>
          <Text style={stockEntryInvoicePdfStyle.amountLabel}>
            Advance Payment:
          </Text>
          <Text style={stockEntryInvoicePdfStyle.amountValue}>
            {formatNumberWithComma(supplierBillData.advancePayment)} Tk
          </Text>
        </View>
        <View style={stockEntryInvoicePdfStyle.amountRow}>
          <Text style={stockEntryInvoicePdfStyle.amountLabel}>Total Paid:</Text>
          <Text style={stockEntryInvoicePdfStyle.amountValue}>
            {formatNumberWithComma(supplierBillData.totalPaid)} Tk
          </Text>
        </View>
        <View style={stockEntryInvoicePdfStyle.totalRow}>
          <Text style={stockEntryInvoicePdfStyle.totalLabel}>Total Due:</Text>
          <Text style={stockEntryInvoicePdfStyle.totalValue}>
            {formatNumberWithComma(supplierBillData.totalDue)} Tk
          </Text>
        </View>
      </View>

      {/* Note Section */}
      {supplierBillData.note && (
        <View style={stockEntryInvoicePdfStyle.noteSection}>
          <Text style={stockEntryInvoicePdfStyle.noteLabel}>Note:</Text>
          <Text style={stockEntryInvoicePdfStyle.noteText}>
            {supplierBillData.note}
          </Text>
        </View>
      )}

      {/* Footer */}
      <View style={stockEntryInvoicePdfStyle.footer}>
        <Text>Software Made By SOFTS.AI.</Text>
        <Text>Call: 01723-714141</Text>
      </View>
    </Page>
  );
}

export default StockEntryInvoicePdf;
