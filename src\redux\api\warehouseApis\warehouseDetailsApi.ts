import BaseApi from '../baseApi';

import { TagTypes } from '@/redux/tag-types';

export interface WarehouseDetailsResponse {
  success: boolean;
  message: string;
  statusCode: number;
  data: Data;
}

export interface Data {
  id: string;
  createdAt: string;
  updatedAt: string;
  name: string;
  imgUrl: string;
  mobileNumber: string;
  address: any;
  street: string;
  district: string;
  websiteUrl: string;
  fbUrl: string;
  zipCode: string;
  country: string;
  warehouseId: string;
  type: string;
  lastOrderNo: number;
}

const WarehouseDetailsApi = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getWarehouseDetails: builder.query<WarehouseDetailsResponse, any>({
      query: (id) => ({
        url: `/warehouse/${id}`,
        method: 'GET',
      }),
      providesTags: [TagTypes.WAREHOUSE_DETAILS],
    }),
  }),
});

export const { useGetWarehouseDetailsQuery } = WarehouseDetailsApi;
