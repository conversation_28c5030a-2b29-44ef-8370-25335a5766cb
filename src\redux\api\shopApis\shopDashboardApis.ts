import BaseApi from '../baseApi';

import { TagTypes } from '@/redux/tag-types';
import {
  GetDashboardCustomersSummeryResponse,
  GetDashboardPaidOrderListResponse,
  GetDashboardUpdatesResponse,
} from '@/types/warehouseTypes/dashboardTypes';

interface GetDashboardUpdatesParams {
  organizationId: string;
  shopId?: string;
  type?: string;
}

const ShopDashboardApi = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getShopDashboardUpdates: builder.query<
      GetDashboardUpdatesResponse,
      GetDashboardUpdatesParams
    >({
      query: (params) => ({
        url: '/dashboard',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.DASHBOARD],
    }),
    getShopDashboardCustomersData: builder.query<
      GetDashboardCustomersSummeryResponse,
      GetDashboardUpdatesParams
    >({
      query: (params) => ({
        url: '/dashboard/shop/customer',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.DASHBOARD],
    }),
    getShopDashboardPaidOrderList: builder.query<
      GetDashboardPaidOrderListResponse,
      GetDashboardUpdatesParams
    >({
      query: (params) => ({
        url: '/dashboard/shop/paid-order',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.DASHBOARD],
    }),
  }),
});

export const {
  useGetShopDashboardUpdatesQuery,
  useGetShopDashboardCustomersDataQuery,
  useGetShopDashboardPaidOrderListQuery,
} = ShopDashboardApi;
