import { useFormik } from 'formik';
import Cookies from 'js-cookie';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import * as Yup from 'yup';

import FilledSubmitButton from '@/components/reusable/Buttons/FilledSubmitButton';
import CustomDropdown from '@/components/reusable/CustomInputField/CustomDropdown';
import CustomInputField from '@/components/reusable/CustomInputField/CustomInputField';
import CustomTextArea from '@/components/reusable/CustomInputField/CustomTextArea';
import { useBookOrderOnPathaoMutation } from '@/redux/api/shopApis/shopOrdersApis';
import {
  useGetPathaoAreasQuery,
  useGetPathaoCitiesQuery,
  useGetPathaoZonesQuery,
  usePathaoAddressParserMutation,
} from '@/redux/api/warehouseApis/couriersApi';
import { useAppSelector } from '@/redux/hooks';
import {
  ShopOrderDetails,
  ShopSingleOrderItem,
} from '@/types/shopTypes/shopOrderTypes';
import {
  PathaoParsedAddressDetails,
  PathaoSingleCity,
} from '@/types/warehouseTypes/settingsTypes';
import { handleGenerateSingleOrderPdf } from '@/utils/GenerateOrderPdf';
import { OrderTotalDueCalculator } from '@/utils/OrderTotalPriceCalculator';

interface Props {
  orderDetails?: ShopOrderDetails;
  courierId?: string;
  handleClose: () => void;
}

const formikInitialValues = {
  merchantOrderId: null,
  recipientName: null,
  recipientPhone: '',
  recipientAddress: null,
  recipientCity: null,
  recipientZone: null,
  recipientArea: null,
  deliveryType: 48,
  itemType: 2,
  specialInstruction: null,
  itemDescription: null,
  itemWeight: 0.5,
  itemQuantity: null,
  amountToCollect: null,
};

const validation = Yup.object({
  merchantOrderId: Yup.string().required('Order Id is required'),
});

function PathaoBookingForm({ orderDetails, courierId, handleClose }: Props) {
  const { shopSettings } = useAppSelector((state) => state);
  const [isAddressSelectManually, setIsAddressSelectManually] =
    useState<boolean>(false);
  const [pathaoParsedAddress, setPathaoParsedAddress] =
    useState<PathaoParsedAddressDetails>();

  const [bookOrderOnPathao, { isLoading: isOrderCreating }] =
    useBookOrderOnPathaoMutation();
  const [pathaoAddressParser, { isLoading: isPathaoAddressParsing }] =
    usePathaoAddressParserMutation();
  // const [updateSingleOrderAddress] = useUpdateSingleOrderAddressMutation();

  const { data: citiesData, isLoading: isCitiesLoading } =
    useGetPathaoCitiesQuery(
      {
        pathaoToken: Cookies.get('pathaoAccessToken'),
      },
      { skip: !isAddressSelectManually },
    );

  const formik = useFormik({
    initialValues: formikInitialValues,
    validationSchema: validation,

    onSubmit: async (values) => {
      const afterBooking = localStorage.getItem('afterBooking');
      toast.promise(
        bookOrderOnPathao({
          ...values,
          recipientPhone: values?.recipientPhone.toString().trim(),
          courierId,
          orderId: orderDetails?.id,
          pathaoToken: Cookies.get('pathaoAccessToken'),
          recipientCity: Number(formik.values.recipientCity),
          recipientZone: Number(formik.values.recipientZone),
          recipientArea: Number(formik.values.recipientArea) || null,
        }).unwrap(),
        {
          pending: 'Order Booking on Courier...',
          success: {
            render({ data: res }) {
              if (res?.statusCode === 200 || res?.statusCode === 201) {
                /* if (pathaoParsedAddress?.district_name?.length) {
                  updateSingleOrderAddress({
                    orderId: orderDetails?.id,
                    data: {
                      division: getDivisionByDistrict(
                        pathaoParsedAddress?.district_name ?? '',
                      ),
                      district: pathaoParsedAddress?.district_name,
                      orderId: orderDetails?.id,
                    },
                  });
                } */
                if (afterBooking === 'print-invoice') {
                  handleGenerateSingleOrderPdf(
                    res?.data,
                    shopSettings.returnPolicyText,
                  );
                }

                handleClose();
              }
              return 'Order booked Successfully';
            },
          },
          error: {
            render({ data: error }) {
              console.log(error);
              return 'Error on booking order';
            },
          },
        },
      );
    },
  });

  const { data: zonesData, isLoading: isZonesLoading } = useGetPathaoZonesQuery(
    {
      pathaoToken: Cookies.get('pathaoAccessToken'),
      city: formik.values.recipientCity,
    },
    {
      skip: Boolean(!formik.values.recipientCity) || !isAddressSelectManually,
    },
  );

  const { data: areasData, isLoading: isAreasLoading } = useGetPathaoAreasQuery(
    {
      pathaoToken: Cookies.get('pathaoAccessToken'),
      zone: formik.values.recipientZone,
    },
    {
      skip: Boolean(!formik.values.recipientZone) || !isAddressSelectManually,
    },
  );

  useEffect(() => {
    if (orderDetails) {
      const cod = OrderTotalDueCalculator({
        productPrice: orderDetails.subTotal,
        deliveryCharge: orderDetails.deliveryCharge,
        productDiscount: orderDetails?.productDiscount,
        adminDiscount: orderDetails.adminDiscount,
        paidAmount: orderDetails.totalPaid,
      });
      let description = '';
      orderDetails.OrderItem?.map(
        (single: ShopSingleOrderItem, index: number) => {
          description += `${index + 1} ) ${single.Stock?.barcode}-${single.Stock?.Product?.name} - ${single.Stock?.retailPrice} \n`;
          return 0;
        },
      );
      formik.setFieldValue(
        'merchantOrderId',
        orderDetails?.serialNo?.toString(),
      );
      formik.setFieldValue(
        'recipientName',
        orderDetails?.customerName ?? orderDetails?.Customer?.name,
      );
      formik.setFieldValue(
        'recipientPhone',
        orderDetails?.customerMobileNumber ??
          orderDetails?.Customer?.mobileNumber,
      );
      formik.setFieldValue('recipientAddress', orderDetails?.address);
      formik.setFieldValue('amountToCollect', cod);
      formik.setFieldValue(
        'specialInstruction',
        orderDetails?.orderNotes[0]?.notes ?? '',
      );
      formik.setFieldValue('orderId', orderDetails?.id);
      formik.setFieldValue('itemQuantity', orderDetails?.OrderItem?.length);
      formik.setFieldValue('itemDescription', description);
    }
  }, [orderDetails]);

  const parseAddress = async () => {
    try {
      const data = await pathaoAddressParser({
        pathaoToken: Cookies.get('pathaoAccessToken'),
        // courierId,
        address: orderDetails?.address,
      });
      if (data.data?.data.data) {
        setPathaoParsedAddress(data.data?.data?.data);
        formik.setFieldValue(
          'recipientCity',
          data.data?.data?.data?.district_id ?? null,
        );
        formik.setFieldValue(
          'recipientZone',
          data.data?.data?.data?.zone_id ?? null,
        );
        formik.setFieldValue(
          'recipientArea',
          data.data?.data?.data?.area_id ?? null,
        );
      }
    } catch (error) {
      console.log(error);
    }
  };
  useEffect(() => {
    if (
      orderDetails &&
      !orderDetails?.Address?.street &&
      !orderDetails?.Address?.district
    ) {
      parseAddress();
    }
  }, [orderDetails]);

  useEffect(() => {
    setPathaoParsedAddress({
      hub_id: 0,
      hub_name: '',
      area_name: '',
      zone_id: Number(orderDetails?.recipientZone),
      zone_name: orderDetails?.Address?.street ?? '',
      is_implicit: false,
      district_name: orderDetails?.Address?.district ?? '',
      district_id: Number(orderDetails?.recipientCity) ?? '',
      score: 0,
      debug_info: '',
    });
    formik.setFieldValue(
      'recipientCity',
      Number(orderDetails?.recipientCity) ?? null,
    );
    formik.setFieldValue(
      'recipientZone',
      Number(orderDetails?.recipientZone) ?? null,
    );
    formik.setFieldValue(
      'recipientArea',
      Number(orderDetails?.recipientArea) ?? null,
    );
  }, [orderDetails]);

  return (
    <div className="flex flex-col gap-4">
      <form
        onSubmit={formik.handleSubmit}
        className="flex w-full flex-col gap-4"
      >
        <div className="flex w-full gap-2">
          <CustomInputField
            type="text"
            placeholder="Enter Phone Number"
            name="recipientName"
            label="Name"
            formik={formik}
          />
          <CustomInputField
            type="text"
            placeholder="Enter Phone Number"
            name="recipientPhone"
            label="Phone"
            formik={formik}
          />
        </div>
        <CustomTextArea
          placeholder="Enter Customer Address"
          name="recipientAddress"
          label="Address"
          formik={formik}
        />
        <div className="flex w-full gap-2">
          <CustomTextArea
            placeholder="Note"
            name="specialInstruction"
            label="Note"
            formik={formik}
          />
        </div>
        <div className="flex items-center justify-between rounded-lg border border-gray-300 pl-2">
          <p>
            Suggestion :{' '}
            {!isPathaoAddressParsing ? (
              <>
                {pathaoParsedAddress?.district_name} {`->`}
                {pathaoParsedAddress?.zone_name} {`->`}{' '}
                {pathaoParsedAddress?.area_name}
              </>
            ) : (
              'Loading...'
            )}
          </p>
          <button
            type="button"
            className="rounded-r bg-primary px-2 py-1 text-white"
            onClick={() => setIsAddressSelectManually(!isAddressSelectManually)}
          >
            {isAddressSelectManually ? 'Cancel Edit' : 'Edit'}
          </button>
        </div>
        {isAddressSelectManually ? (
          <div className="grid w-full grid-cols-3 gap-4">
            <CustomDropdown
              placeholder="Select City"
              name="recipientCity"
              label="City"
              formik={formik}
              options={
                !isCitiesLoading
                  ? citiesData?.data?.map((singleCity: PathaoSingleCity) => {
                      return {
                        value: singleCity.city_id,
                        label: singleCity.city_name,
                      };
                    })
                  : []
              }
            />
            <CustomDropdown
              placeholder="Select Zone"
              name="recipientZone"
              label="Zone"
              formik={formik}
              options={
                !isZonesLoading
                  ? zonesData?.data?.map((singleZone: any) => {
                      return {
                        value: singleZone.zone_id,
                        label: singleZone.zone_name,
                      };
                    })
                  : []
              }
            />
            <CustomDropdown
              placeholder="Select Area"
              name="recipientArea"
              label="Area"
              formik={formik}
              options={
                !isAreasLoading
                  ? areasData?.data?.map((singleArea: any) => {
                      return {
                        value: singleArea.area_id,
                        label: singleArea.area_name,
                      };
                    })
                  : []
              }
            />

            <CustomInputField
              type="text"
              placeholder="Order Id"
              name="merchantOrderId"
              label="Order Id"
              formik={formik}
            />
            <CustomDropdown
              placeholder="Delivery Type"
              name="deliveryType"
              label="Delivery Type"
              formik={formik}
              options={[
                { value: 48, label: 'Normal Delivery' },
                { value: 12, label: 'On Demand Delivery' },
              ]}
            />
            <CustomDropdown
              placeholder="Item Type"
              name="itemType"
              label="Item Type"
              formik={formik}
              options={[
                { value: 1, label: 'Document' },
                { value: 2, label: 'Parcel' },
              ]}
            />
            <CustomInputField
              type="number"
              placeholder="Quantity"
              name="itemQuantity"
              label="Quantity (PCS)"
              formik={formik}
            />
            <CustomInputField
              type="number"
              placeholder="COD Amount"
              name="amountToCollect"
              label="Amount To Collect"
              formik={formik}
            />
            <CustomInputField
              type="text"
              placeholder="Weight"
              name="itemWeight"
              label="Weight (KG)"
              formik={formik}
            />
          </div>
        ) : (
          ''
        )}
        <CustomTextArea
          placeholder="Products Description"
          name="itemDescription"
          label="Products Description"
          formik={formik}
        />
        <FilledSubmitButton text="Book On Pathao" isLoading={isOrderCreating} />
      </form>
    </div>
  );
}

export default PathaoBookingForm;
