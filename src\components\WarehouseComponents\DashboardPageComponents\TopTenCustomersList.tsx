import { useState } from 'react';
import { Link } from 'react-router-dom';
import Switch from 'react-switch';

import ImageViewer from '@/components/reusable/ImageViewer/ImageViewer';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import { useGetTopTenCustomerListQuery } from '@/redux/api/warehouseApis/dashboardApis';
import { ROUTES } from '@/Routes';
import { SingleTopTenCustomerDetails } from '@/types/warehouseTypes/dashboardTypes';

interface Props {
  type: 'warehouse' | 'shop';
  warehouseId?: string;
  shopId?: string;
}

function TopTenCustomersList({ warehouseId, type, shopId }: Props) {
  const [metricType, setMetricType] = useState<'count' | 'amount'>('count');
  const { data, isLoading } = useGetTopTenCustomerListQuery({
    metricType,
    warehouseId: type === 'warehouse' ? warehouseId ?? undefined : undefined,
    shopId: type === 'shop' ? shopId ?? undefined : undefined,
  });
  return (
    <div>
      <div className="tableTop w-full">
        <p>Top Ten Customers</p>
        <div className="flex items-center gap-2 text-white">
          <span>Count</span>
          <Switch
            onChange={() =>
              setMetricType(metricType === 'amount' ? 'count' : 'amount')
            }
            checked={metricType === 'amount'}
            title="Show Amount"
            height={25}
          />
          <span>Amount</span>
        </div>
      </div>
      {!isLoading ? (
        <div className="w-full">
          {data?.data?.length ? (
            <div className="full-table-box h-auto">
              <table className="full-table">
                <thead className="bg-gray-100">
                  <tr>
                    <th className="tableHead">No</th>
                    <th className="tableHead">Image</th>
                    <th className="tableHead table-col-width">Name</th>
                    <th className="tableHead">Mobile</th>
                    <th className="tableHead">Total</th>
                  </tr>
                </thead>
                <tbody className="divide-y bg-slate-200">
                  {data?.data?.map(
                    (product: SingleTopTenCustomerDetails, index: number) => (
                      <tr key={`${product?.name}-${product.mobileNumber}`}>
                        <td className="tableData">{index + 1}</td>
                        <td className="tableData">
                          <ImageViewer imageUrl={product.imgUrl} />
                        </td>
                        <td className="tableData table-col-width">
                          <Link
                            to={
                              type === 'shop'
                                ? ROUTES.SHOP.CUSTOMER_DETAILS(
                                    shopId ?? '',
                                    product.id,
                                  )
                                : ROUTES.WAREHOUSE.CUSTOMER_DETAILS(
                                    warehouseId ?? '',
                                    product.id,
                                  )
                            }
                            className="hover:text-blue-600 hover:underline"
                          >
                            {product?.name}
                          </Link>
                        </td>
                        <td className="tableData">
                          <Link
                            to={
                              type === 'shop'
                                ? ROUTES.SHOP.CUSTOMER_DETAILS(
                                    shopId ?? '',
                                    product.id,
                                  )
                                : ROUTES.WAREHOUSE.CUSTOMER_DETAILS(
                                    warehouseId ?? '',
                                    product.id,
                                  )
                            }
                            className="hover:text-blue-600 hover:underline"
                          >
                            {product?.mobileNumber}
                          </Link>
                        </td>
                        <td className="tableData">
                          {metricType === 'amount'
                            ? product?.totalOrderAmount
                            : product?.orderCount}
                        </td>
                      </tr>
                    ),
                  )}
                </tbody>
              </table>
            </div>
          ) : (
            <div>
              <h2>No product found</h2>
            </div>
          )}
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={4} tableRow={10} />
      )}
    </div>
  );
}
export default TopTenCustomersList;
