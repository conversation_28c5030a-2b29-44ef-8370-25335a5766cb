import BaseApi from '../baseApi';

import { TagTypes } from '@/redux/tag-types';
import { GetProductsResponse } from '@/types/warehouseTypes/productTypes';

interface GetProductsParams {
  shopId?: string;
  name?: string;
  serialNo?: string;
  page?: string;
  limit?: string;
}

const ShopProductsApi = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getShopProducts: builder.query<GetProductsResponse, GetProductsParams>({
      query: (params) => ({
        url: '/products',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP_PRODUCTS],
    }),
  }),
});

export const { useGetShopProductsQuery } = ShopProductsApi;
