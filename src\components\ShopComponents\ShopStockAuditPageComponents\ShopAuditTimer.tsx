import { useEffect, useState } from 'react';

function ShopAuditTimer({ audit }: any) {
  // Timer state for IN_PROGRESS status or completed audits
  // Timer state for IN_PROGRESS status or completed audits
  const [elapsed, setElapsed] = useState<string>('00:00:00:00');

  useEffect(() => {
    let interval: NodeJS.Timeout | undefined;

    const formatDiff = (diff: number) => {
      let remaining = diff;
      const days = Math.floor(remaining / (24 * 60 * 60 * 1000));
      remaining -= days * 24 * 60 * 60 * 1000;
      const hours = Math.floor(remaining / (60 * 60 * 1000));
      remaining -= hours * 60 * 60 * 1000;
      const minutes = Math.floor(remaining / (60 * 1000));
      remaining -= minutes * 60 * 1000;
      const seconds = Math.floor(remaining / 1000);
      return `${String(days).padStart(2, '0')}:${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
    };

    if (audit?.status === 'IN_PROGRESS' && audit.startedAt) {
      const updateElapsed = () => {
        const start = new Date(audit.startedAt).getTime();
        const now = Date.now();
        const diff = Math.max(0, now - start);
        setElapsed(formatDiff(diff));
      };
      updateElapsed();
      interval = setInterval(updateElapsed, 1000);
    } else if (
      audit?.status !== 'DRAFT' &&
      audit?.startedAt &&
      audit?.completedAt
    ) {
      const start = new Date(audit.startedAt).getTime();
      const end = new Date(audit.completedAt).getTime();
      const diff = Math.max(0, end - start);
      setElapsed(formatDiff(diff));
    } else {
      setElapsed('00:00:00:00');
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [audit?.status, audit?.startedAt, audit?.completedAt]);
  return (
    <div>
      {audit.status === 'IN_PROGRESS' && audit.startedAt && (
        <span className="ml-4 inline-block rounded bg-blue-100 px-2 py-1 font-mono text-sm text-blue-700">
          Audit Time: {elapsed}
        </span>
      )}
      {audit.status !== 'DRAFT' &&
        audit.status !== 'IN_PROGRESS' &&
        audit.startedAt &&
        audit.completedAt && (
          <span className="ml-4 inline-block rounded bg-green-100 px-2 py-1 font-mono text-sm text-green-700">
            Duration: {elapsed}
          </span>
        )}
    </div>
  );
}

export default ShopAuditTimer;
