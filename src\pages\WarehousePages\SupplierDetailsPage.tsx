import { useParams } from 'react-router-dom';

import SupplierDetailsPageOverview from '@/components/WarehouseComponents/SuppliersPageComponents/SupplierDetailsPageOverview';
import { ProtectedRoute } from '@/utils/ProtectedRoutes';

function SupplierDetailsPage() {
  const { supplierId } = useParams();
  return (
    <ProtectedRoute>
      <SupplierDetailsPageOverview supplierId={supplierId ?? ''} />
    </ProtectedRoute>
  );
}

export default SupplierDetailsPage;
