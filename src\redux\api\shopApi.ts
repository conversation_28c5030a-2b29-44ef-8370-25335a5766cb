import { TagTypes } from '../tag-types';

import BaseApi from './baseApi';

import { GetShopDetailsResponse, GetShopListResponse } from '@/types/shopTypes';

const ShopApi = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getShops: builder.query<GetShopListResponse, any>({
      query: (params) => ({
        url: '/shop',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP],
    }),
    getWarehouses: builder.query<GetShopListResponse, any>({
      query: (id) => ({
        url: `/warehouse/${id}`,
        method: 'GET',
      }),
      providesTags: [TagTypes.SHOP],
    }),
    getShopById: builder.query<GetShopDetailsResponse, any>({
      query: (id) => ({
        url: `/shop/${id}`,
        method: 'GET',
      }),
      providesTags: [TagTypes.SHOP],
    }),
    createShop: builder.mutation({
      query: (data) => ({
        url: '/shop/new',
        method: 'POST',
        data,
      }),
      invalidatesTags: [TagTypes.SHOP],
    }),
    updateShop: builder.mutation({
      query: ({ data, id }) => ({
        url: `/shop/${id}`,
        method: 'PATCH',
        data,
      }),
      invalidatesTags: [TagTypes.SHOP],
    }),
    updateWarehouse: builder.mutation({
      query: ({ data, id }) => ({
        url: `/warehouse/${id}`,
        method: 'PATCH',
        data,
      }),
      invalidatesTags: [TagTypes.SHOP],
    }),
    deleteShop: builder.mutation({
      query: (id) => ({
        url: `/shop/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: [TagTypes.SHOP],
    }),
  }),
});

export const {
  useGetShopByIdQuery,
  useGetShopsQuery,
  useGetWarehousesQuery,
  useCreateShopMutation,
  useUpdateShopMutation,
  useUpdateWarehouseMutation,
  useDeleteShopMutation,
} = ShopApi;
