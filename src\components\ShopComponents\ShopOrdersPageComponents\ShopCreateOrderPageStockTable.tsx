import { CartButtonv2 } from '@/components/reusable/Buttons/CommonButtons';
import { SingleStockDetails } from '@/types/warehouseTypes/stockTypes';
import { CalculateDiscountPrice } from '@/utils/CalculateDiscountPrice';
import StockName from '@/utils/StockName';

function ShopCreateOrderPageStockTable({
  stocks,
  handleSelectProduct,
  selectedProducts,
}: {
  selectedProducts: SingleStockDetails[];
  stocks: SingleStockDetails[];
  handleSelectProduct: (stock: SingleStockDetails) => void;
}) {
  return (
    <table className="relative w-full border border-black">
      <thead className="bg-gray-100">
        <tr>
          {/* <th className="tableHead">No</th> */}
          <th className="tableHead">Barcode</th>
          <th className="tableHead table-col-width">Name</th>
          <th className="tableHead">Regular Price</th>
          <th className="tableHead">Discount Price</th>
          <th className="tableHead">Action</th>
        </tr>
      </thead>
      <tbody className="table-tbody divide-y bg-slate-200">
        {stocks?.map((stock: SingleStockDetails) => {
          const isExist = selectedProducts?.some(
            (sin: SingleStockDetails) => sin.id === stock.id,
          );
          return (
            <tr key={stock?.id}>
              {/* <td className="tableData">{index + 1}</td> */}
              <td className="tableData">{stock?.barcode}</td>
              <td className="tableData table-col-width">
                <StockName stock={stock} name={stock.name} />
              </td>
              <td className="tableData">{stock?.retailPrice}</td>
              <td className="tableData">
                {CalculateDiscountPrice({
                  retailPrice: stock?.retailPrice,
                  discountType: stock.discountType,
                  discount: stock.discount,
                })}
              </td>
              <td className="tableData">
                <CartButtonv2
                  handleClick={() => {
                    handleSelectProduct(stock);
                  }}
                  disabled={isExist}
                />
              </td>
            </tr>
          );
        })}
      </tbody>
    </table>
  );
}

export default ShopCreateOrderPageStockTable;
