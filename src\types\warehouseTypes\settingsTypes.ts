import { Pagination } from '@/redux/commonTypes';

export interface GetCourierSettingsListResponse {
  success: boolean;
  message: string;
  statusCode: number;
  pagination: Pagination;
  data: SingleCourierDetails[];
}

export interface SingleCourierDetails {
  id: string;
  createdAt: string;
  updatedAt: string;
  organizationId: string;
  apiKey: string;
  secretKey: string;
  clientId: any;
  clientSecret: any;
  storeId: any;
  userName: any;
  password: any;
  type: string;
  isActive: boolean;
  isSuccess: boolean;
  isDeliveryed: boolean;
  createdById: string;
  nickName: string;
  AssignedShop: {
    id: string;
    name: string;
    nickName: string;
  };
}

export interface GetPathaoCitiesResponse {
  success: boolean;
  message: string;
  statusCode: number;
  data: PathaoSingleCity[];
}

export interface PathaoSingleCity {
  city_id: number;
  city_name: string;
}

export interface GetPathaoZonesResponse {
  success: boolean;
  message: string;
  statusCode: number;
  data: PathaoSingleCity[];
}

export interface PathaoSingleZone {
  zone_id: number;
  zone_name: string;
}

export interface GetPathaoAreasResponse {
  success: boolean;
  message: string;
  statusCode: number;
  data: PathaoSingleArea[];
}

export interface PathaoSingleArea {
  area_id: number;
  area_name: string;
  home_delivery_available: boolean;
  pickup_available: boolean;
}

export interface PathaoAddressParseResponse {
  success: boolean;
  message: string;
  statusCode: number;
  data: AddressParseResponse;
}

export interface AddressParseResponse {
  message: string;
  type: string;
  code: number;
  data: PathaoParsedAddressDetails;
}

export interface PathaoParsedAddressDetails {
  hub_id?: number;
  hub_name?: string;
  area_id?: number;
  area_name?: string;
  zone_id?: number;
  zone_name?: string;
  is_implicit?: boolean;
  district_id?: number;
  district_name?: string;
  score?: number;
  debug_info?: any;
}
