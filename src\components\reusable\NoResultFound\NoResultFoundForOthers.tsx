// import emptyCartImage from '../../../images/emptyCartImage.png';
import emptyCartImage from '../../../images/notFound.gif';

interface NoResultFoundProps {
  pageType: 'stock' | 'product' | 'order' | string;
}

function NoResultFoundForOthers({ pageType }: NoResultFoundProps) {
  const getMessage = () => {
    switch (pageType) {
      case 'stock':
        return 'No stock items found.';
      case 'product':
        return 'No products available.';
      case 'order':
        return 'No orders to display.';
      default:
        return 'No results found.';
    }
  };

  return (
    <div className="full-table-box flex h-custom flex-col items-center justify-center gap-4 rounded-lg bg-gray-100 p-6 text-center shadow-md">
      <img src={emptyCartImage} alt="" className="h-60" />
      <h2 className="text-xl font-semibold text-gray-700">{getMessage()}</h2>
      <p className="mt-2 text-gray-500">
        Try adjusting your filters or adding new {pageType}.
      </p>
    </div>
  );
}

export default NoResultFoundForOthers;
