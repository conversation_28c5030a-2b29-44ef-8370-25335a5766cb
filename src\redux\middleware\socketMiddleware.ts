import { Middleware } from '@reduxjs/toolkit';
import { io, Socket } from 'socket.io-client';

import {
  handleGenerateSingleOrderPdf,
  handleGenerateSingleOrderPosPdf,
} from '@/utils/GenerateOrderPdf';

let socket: Socket | null = null;
const subscribedOrders = new Set<string>(); // Track active subscriptions

export const socketMiddleware: Middleware = () => (next) => (action: any) => {
  if (action.type === 'socket/connect') {
    const shopId = action.payload;

    if (!shopId) {
      return next(action);
    }

    if (!socket) {
      socket = io(import.meta.env.VITE_SOCKET_URL, {
        transports: ['websocket'],
        query: { shopId }, // Send shopId to backend
      });

      socket.on('connect', () =>
        console.log(`✅ Connected to WebSocket for shop ${shopId}`),
      );
      socket.on('disconnect', () =>
        console.log('🔴 Disconnected from WebSocket'),
      );
      socket.on('connect_error', (error) => {
        console.error('❌ Connection error:', error);
      });
    }
  }

  if (action.type === 'socket/subscribeOrder') {
    const orderId = action.payload;
    if (socket && !subscribedOrders.has(orderId)) {
      subscribedOrders.add(orderId);
      socket.emit('track-order', orderId); // ✅ Tell backend to track this order

      const eventName = `order-completed:${orderId}`;
      const eventHandler = (data: any) => {
        // console.log('✅ Order completed:', data);

        // 🔥 Dispatch order completion to Redux
        // store.dispatch({ type: 'order/completed', payload: data });

        if (data?.receiptData?.Shop.type === 'ONLINE') {
          // window.open(
          //   `/shop/${data?.receiptData?.shopId}/invoice/${orderId}`,
          //   '_blank',
          // );
          handleGenerateSingleOrderPdf(
            data?.receiptData,
            data?.receiptData?.Shop?.ShopSettings?.returnPolicyText,
          );
        } else {
          handleGenerateSingleOrderPosPdf(
            data?.receiptData,
            data?.receiptData?.Shop?.ShopSettings?.returnPolicyText,
          );
        }
        socket?.off(eventName);
        subscribedOrders.delete(orderId);
      };

      socket.on(eventName, eventHandler);
    }
  }

  return next(action);
};
