import Cookies from 'js-cookie';

import BaseApi from '../baseApi';

import { TagTypes } from '@/redux/tag-types';
import {
  GetShopCustomerDetailsResponse,
  GetShopCustomersResponse,
} from '@/types/shopTypes/shopCustomersTypes';

interface GetCustomersParams {
  organizationId?: string;
  name?: string;
  serialNo?: string;
  mobileNumber?: string;
  page?: string;
  limit?: string;
}

const CustomersApi = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getCustomers: builder.query<GetShopCustomersResponse, GetCustomersParams>({
      query: (params) => ({
        url: '/customers',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.CUSTOMERS],
    }),
    getCustomersDetails: builder.query<GetShopCustomerDetailsResponse, any>({
      query: (id) => ({
        url: `/customers/${id}?organizationId=${Cookies.get('organizationId')}`,
        method: 'GET',
      }),
      providesTags: [TagTypes.SUPPLIERS],
    }),
    createNewCustomer: builder.mutation({
      query: (data) => ({
        url: '/customers/new',
        method: 'POST',
        data,
      }),
      invalidatesTags: [TagTypes.CUSTOMERS],
    }),
  }),
});

export const {
  useGetCustomersQuery,
  useGetCustomersDetailsQuery,
  useCreateNewCustomerMutation,
} = CustomersApi;
