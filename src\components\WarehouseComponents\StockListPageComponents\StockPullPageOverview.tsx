import { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import Swal from 'sweetalert2';

import StockPullPageFilterModal from './StockPullPageFilterModal';
import StockPullPageFilterOptions from './StockPullPageFilterOptions';

import FilledButton from '@/components/reusable/Buttons/FilledButton';
import FilterButton from '@/components/reusable/Buttons/FilterButton';
import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import Pagination from '@/components/reusable/Pagination/Pagination';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import {
  useGetStockListQuery,
  usePullStockMutation,
} from '@/redux/api/warehouseApis/stockApis';
import { ROUTES } from '@/Routes';
import { SingleStockDetails } from '@/types/warehouseTypes/stockTypes';
import { CalculateDiscountPrice } from '@/utils/CalculateDiscountPrice';
import { generateFilterParams } from '@/utils/generateFilterParams';
import StockName from '@/utils/StockName';

interface Props {
  warehouseId: string;
}
function StockPullPageOverview({ warehouseId }: Props) {
  const navigate = useNavigate();
  const router = new URLSearchParams(useLocation().search);
  const page = router.get('page');
  const productName = router.get('productName');
  const limit = router.get('limit');
  const barcode = router.get('barcode');
  const productSerialNo = router.get('productSerialNo');
  const startDate = router.get('startDate');
  const endDate = router.get('endDate');

  const { data, isLoading, refetch, isFetching } = useGetStockListQuery({
    warehouseId,
    status: 'ASSIGNED',
    page: page ?? '1',
    barcode: barcode ?? undefined,
    limit: limit ?? '10',
    productName: productName ?? undefined,
    productSerialNo: productSerialNo ?? undefined,
    type: startDate && endDate ? 'custom' : undefined,
    startDate: startDate ?? undefined,
    endDate: endDate ?? undefined,
  });

  const [pullStock, { isLoading: isPullingStock }] = usePullStockMutation();
  const [selectedStocks, setSelectedStocks] = useState<string[]>();
  const [isFilterModalOpen, setIsFilterModalOpen] = useState<boolean>(false);

  const handlePullStock = () => {
    Swal.fire({
      title: 'Are you sure? you want to pull items to warehouse?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, pull it!',
    }).then((result) => {
      if (result.isConfirmed) {
        toast.promise(
          pullStock({
            stockIds: selectedStocks,
          }).unwrap(),
          {
            pending: 'Pulling Stock...',
            success: {
              render({ data: res }) {
                if (res?.statusCode === 200 || res?.statusCode === 201) {
                  refetch();
                  setSelectedStocks([]);
                }
                return 'Pull Stock done Successfully';
              },
            },
            error: {
              render({ data: error }) {
                console.log(error);
                return 'Error on pull stock';
              },
            },
          },
        );
      }
    });
  };

  const handleSelectAndUnselect = (stockId: string) => {
    if (selectedStocks?.length) {
      if (selectedStocks.includes(stockId)) {
        setSelectedStocks(selectedStocks.filter((item) => item !== stockId));
      } else {
        setSelectedStocks([...selectedStocks, stockId]);
      }
    } else {
      setSelectedStocks([stockId]);
    }
  };

  const handleFilter = (fieldName: string, value: string) => {
    const query = generateFilterParams(fieldName, value);
    navigate(ROUTES.STOCK.PULL(warehouseId, query));
  };

  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-1">
        <div className="flex items-center">
          <div className="search-title-and-btn flex items-center">
            <div className="relative">
              <div className="block xl:hidden">
                <FilterButton
                  handleClick={() => setIsFilterModalOpen(!isFilterModalOpen)}
                />
              </div>
              <div
                className={`${isFilterModalOpen ? 'block' : 'hidden'} xl:hidden`}
              >
                <StockPullPageFilterModal
                  handleClearAndClose={() => setIsFilterModalOpen(false)}
                  handleFilter={handleFilter}
                />
              </div>
            </div>
          </div>
          <div className="hidden xl:block">
            <div className="flex items-center gap-x-2">
              <StockPullPageFilterOptions handleFilter={handleFilter} />
            </div>
          </div>
        </div>
        <div>
          <FilledButton
            text="Pull Stock"
            isLoading={isPullingStock}
            handleClick={() => handlePullStock()}
            isDisabled={!selectedStocks?.length}
          />
        </div>
      </div>
      {!isLoading && !isFetching ? (
        <div>
          <div className="tableTop w-full">
            <p>Stock List</p>
            <p>Total : {data?.pagination?.total}</p>
          </div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {data?.data?.length ? (
              <div>
                <div className="full-table-box h-custom">
                  <table className="full-table">
                    <thead className="bg-gray-100">
                      <tr>
                        <th className="tableHead">
                          <input
                            type="checkbox"
                            name=""
                            id=""
                            checked={
                              data?.data?.length === selectedStocks?.length
                            }
                            onClick={() => {
                              if (
                                data?.data?.length === selectedStocks?.length
                              ) {
                                setSelectedStocks([]);
                              } else {
                                const ids = data?.data?.map(
                                  (single: SingleStockDetails) => single?.id,
                                );
                                setSelectedStocks(ids);
                              }
                            }}
                          />
                        </th>
                        <th className="tableHead">Barcode</th>
                        <th className="tableHead">Product Id</th>
                        <th className="tableHead table-col-width">Name</th>
                        <th className="tableHead">Stock Location</th>
                        <th className="tableHead">TP</th>
                        <th className="tableHead">Regular Price</th>
                        <th className="tableHead">Discount Price</th>
                        <th className="tableHead">Status</th>
                        <th className="tableHead">Supplier</th>
                        <th className="tableHead">Created At</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y bg-slate-200">
                      {data?.data?.map((stock: SingleStockDetails) => (
                        <tr
                          key={stock?.id}
                          className={
                            selectedStocks?.includes(stock.id)
                              ? 'tableRowYellow'
                              : 'bg-white'
                          }
                        >
                          <td className="tableData">
                            <input
                              type="checkbox"
                              name=""
                              id=""
                              onClick={() => handleSelectAndUnselect(stock.id)}
                              checked={selectedStocks?.includes(stock.id)}
                            />
                          </td>
                          <td className="tableData">{stock?.barcode}</td>
                          <td className="tableData">
                            {stock?.Product.serialNo ?? 0}
                          </td>
                          <td className="tableData table-col-width">
                            <StockName stock={stock} name={stock.name} />
                          </td>
                          <td className="tableData">
                            {stock?.AssignedShop?.name ?? 'Warehouse'}
                          </td>
                          <td className="tableData">{stock?.purchasePrice}</td>
                          <td className="tableData">{stock?.retailPrice}</td>
                          <td className="tableData">
                            {CalculateDiscountPrice({
                              retailPrice: stock?.retailPrice,
                              discountType: stock.discountType,
                              discount: stock.discount,
                            })}
                          </td>
                          <td className="tableData">
                            {stock?.isSold ? 'Sold' : 'Available'}
                          </td>
                          <td className="tableData">
                            {stock?.Supplier?.User?.name}
                          </td>
                          <td className="tableData">
                            <DateAndTimeViewer date={stock?.createdAt} />
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            ) : (
              <NoResultFound pageType="stock" />
            )}
          </div>
          <div className="pagination-box flex justify-end rounded bg-white p-3">
            <Pagination
              currentPage={page ?? '1'}
              limit={Number(limit ?? 10)}
              handleFilter={(fieldName: string, value: any) =>
                handleFilter(fieldName, value)
              }
              totalCount={data?.pagination?.total}
              totalPages={Math.ceil(
                Number(data?.pagination?.total) /
                  Number(data?.pagination?.limit),
              )}
            />
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={12} tableRow={6} />
      )}
    </div>
  );
}

export default StockPullPageOverview;
