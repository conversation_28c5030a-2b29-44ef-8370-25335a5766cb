import { useParams } from 'react-router-dom';

import StockTransferPageOverview from '@/components/WarehouseComponents/StockListPageComponents/StockTransferPageOverview';
import { ProtectedRoute } from '@/utils/ProtectedRoutes';

function StockTransferPage() {
  const { warehouseId } = useParams();
  return (
    <ProtectedRoute>
      <StockTransferPageOverview warehouseId={warehouseId ?? ''} />
    </ProtectedRoute>
  );
}

export default StockTransferPage;
