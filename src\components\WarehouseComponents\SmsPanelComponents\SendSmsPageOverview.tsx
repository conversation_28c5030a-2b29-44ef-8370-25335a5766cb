import Cookies from 'js-cookie';
import { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

import SendMessageModal from './SendMessageModal';
import SendSmsFilterOptions from './SendSmsFilterOptions';
import SendSmsPageFilterModal from './SendSmsPageFilterModal';

import {
  DeleteButton,
  EditButton,
  EyeButton,
} from '@/components/reusable/Buttons/CommonButtons';
import FilledButton from '@/components/reusable/Buttons/FilledButton';
import FilterButton from '@/components/reusable/Buttons/FilterButton';
import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import Modal from '@/components/reusable/Modal/Modal';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import { useGetCustomersQuery } from '@/redux/api/warehouseApis/customersApi';
import { useSentBulkSmsMutation } from '@/redux/api/warehouseApis/smsApis';
import { ROUTES } from '@/Routes';
import { ShopCustomerDetails } from '@/types/shopTypes/shopCustomersTypes';
import { generateFilterParams } from '@/utils/generateFilterParams';

interface Props {
  warehouseId: string;
}

function SendSmsPageOverview({ warehouseId }: Props) {
  const navigate = useNavigate();
  const router = new URLSearchParams(useLocation().search);
  const serialNo = router.get('serialNo');
  const name = router.get('name');
  const mobileNumber = router.get('mobileNumber');
  const page = router.get('page');
  const limit = router.get('limit');

  const [isSendSmsModalOpen, setIsSendSmsModalOpen] = useState<boolean>(false);
  const { data, isLoading } = useGetCustomersQuery({
    organizationId: Cookies.get('organizationId'),
    page: page ?? '1',
    serialNo: serialNo ?? undefined,
    name: name ?? undefined,
    mobileNumber: mobileNumber ?? undefined,
    limit: limit ?? '10',
  });

  const [sentBulkSms] = useSentBulkSmsMutation();

  const [selectedNumbers, setSelectedNumbers] = useState<string[]>();
  const [isFilterModalOpen, setIsFilterModalOpen] = useState<boolean>(false);

  const handleSelectAndUnselect = (theMobileNumber: string) => {
    if (selectedNumbers?.length) {
      if (selectedNumbers.includes(theMobileNumber)) {
        setSelectedNumbers(
          selectedNumbers.filter((item) => item !== theMobileNumber),
        );
      } else {
        setSelectedNumbers([...selectedNumbers, theMobileNumber]);
      }
    } else {
      setSelectedNumbers([theMobileNumber]);
    }
  };

  const handleSentMessage = (message: string) => {
    toast.promise(
      sentBulkSms({
        contacts: selectedNumbers,
        msg: message,
        organizationId: Cookies.get('organizationId'),
      }).unwrap(),
      {
        pending: 'Sending Message To Selected Customers...',
        success: {
          render({ data: res }) {
            if (res.statusCode === 200 || res.statusCode === 201) {
              setIsSendSmsModalOpen(false);
            }
            return 'Message Send Successfully';
          },
        },
        error: {
          render({ data: error }) {
            console.log(error);
            return 'Error on sending message';
          },
        },
      },
    );
  };

  const handleFilter = (fieldName: string, value: string) => {
    const query = generateFilterParams(fieldName, value);
    navigate(ROUTES.WAREHOUSE.SEND_SMS(warehouseId, query));
  };

  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-1">
        <div className="flex items-center">
          <div className="search-title-and-btn flex items-center">
            <div className="relative">
              <div className="block xl:hidden">
                <FilterButton
                  handleClick={() => setIsFilterModalOpen(!isFilterModalOpen)}
                />
              </div>
              <div
                className={`${isFilterModalOpen ? 'block' : 'hidden'} xl:hidden`}
              >
                <SendSmsPageFilterModal
                  handleClearAndClose={() => setIsFilterModalOpen(false)}
                  handleFilter={handleFilter}
                />
              </div>
            </div>
          </div>
          <div className="hidden xl:block">
            <div className="flex items-center gap-x-2">
              <SendSmsFilterOptions handleFilter={handleFilter} />
            </div>
          </div>
        </div>
        <div>
          <FilledButton
            isLoading={false}
            text="Send Message"
            handleClick={() => setIsSendSmsModalOpen(true)}
            isDisabled={false}
          />
        </div>
      </div>
      <div>
        {!isLoading ? (
          <div>
            <div className="tableTop w-full">
              <p>Customers List</p>
              <p>Total : {data?.pagination?.total}</p>
            </div>
            <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
              {data?.data?.length ? (
                <div className="full-table-box h-custom">
                  <table className="full-table">
                    <thead className="bg-gray-100">
                      <tr>
                        <th className="tableHead">
                          <input
                            type="checkbox"
                            name=""
                            id=""
                            checked={
                              data?.data?.length === selectedNumbers?.length
                            }
                            onClick={() => {
                              if (
                                data?.data?.length === selectedNumbers?.length
                              ) {
                                setSelectedNumbers([]);
                              } else {
                                const numbers = data?.data?.map(
                                  (single: ShopCustomerDetails) =>
                                    single?.mobileNumber,
                                );
                                setSelectedNumbers(numbers);
                              }
                            }}
                          />
                        </th>
                        <th className="tableHead">No</th>
                        <th className="tableHead">Customer No</th>
                        <th className="tableHead table-col-width">Name</th>
                        <th className="tableHead">Phone</th>
                        <th className="tableHead table-col-width">Address</th>
                        <th className="tableHead">Created At</th>
                        <th className="tableHead">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y bg-slate-200">
                      {data?.data?.map(
                        (customer: ShopCustomerDetails, index: number) => (
                          <tr key={customer?.id}>
                            <td className="tableData">
                              <input
                                type="checkbox"
                                name=""
                                id=""
                                onClick={() =>
                                  handleSelectAndUnselect(
                                    customer?.mobileNumber,
                                  )
                                }
                                checked={selectedNumbers?.includes(
                                  customer?.mobileNumber,
                                )}
                              />
                            </td>
                            <td className="tableData">{index + 1}</td>

                            <td className="tableData">{customer?.serialNo}</td>
                            <td className="tableData table-col-width">
                              {customer?.name}
                            </td>
                            <td className="tableData">
                              {customer?.mobileNumber}
                            </td>
                            <td className="tableData table-col-width">
                              {customer?.address}
                            </td>
                            <td className="tableData">
                              <DateAndTimeViewer date={customer.createdAt} />
                            </td>
                            <td className="tableData">
                              <div className="flex items-center justify-center gap-2">
                                <EyeButton
                                  handleClick={() =>
                                    navigate(
                                      ROUTES.WAREHOUSE.CUSTOMER_DETAILS(
                                        warehouseId,
                                        customer.id,
                                      ),
                                    )
                                  }
                                />
                                <EditButton
                                  handleClick={() => {
                                    toast.warning('clicked but no action set');
                                  }}
                                />
                                <DeleteButton
                                  handleClick={() => {
                                    toast.warning('clicked but no action set');
                                  }}
                                />
                              </div>
                            </td>
                          </tr>
                        ),
                      )}
                    </tbody>
                  </table>
                </div>
              ) : (
                <NoResultFound pageType="customer" />
              )}
            </div>
          </div>
        ) : (
          <TableSkeletonLoader tableColumn={7} tableRow={6} />
        )}
      </div>
      <Modal
        setShowModal={setIsSendSmsModalOpen}
        showModal={isSendSmsModalOpen}
      >
        <SendMessageModal
          handleClose={() => setIsSendSmsModalOpen(false)}
          handleSubmit={(message: string) => handleSentMessage(message)}
        />
      </Modal>
    </div>
  );
}

export default SendSmsPageOverview;
