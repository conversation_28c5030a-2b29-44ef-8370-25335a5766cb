import { useLocation } from 'react-router-dom';

import FilterButton from '@/components/reusable/Buttons/FilterButton';
import StartDateEndDateWithSearch from '@/components/reusable/ReusableFilters/StartDateEndDateWithSearch';
import TableSkeletonLoaderHalf from '@/components/reusable/SkeletonLoader/TableSkeletionLoaderHalf';
import AccountCloseDataRenderer from '@/components/WarehouseComponents/AccountClosePageComponents/AccountCloseDataRenderer';
import { useGetShopAccountsSummeryReportQuery } from '@/redux/api/shopApis/shopReportsApis';

interface Props {
  shopId: string;
}

function ShopAccountsClosePageOverview({ shopId }: Props) {
  const router = new URLSearchParams(useLocation().search);
  const startDate = router.get('startDate') || `${new Date().toISOString()}`;
  const endDate = router.get('endDate') || `${new Date().toISOString()}`;
  const { data, isLoading, isFetching } = useGetShopAccountsSummeryReportQuery({
    shopId,
    type: 'custom',
    startDate,
    endDate,
  });

  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-1">
        <div className="flex items-center gap-x-2">
          <div className="search-title-and-btn flex items-center gap-x-3">
            {/* <p className="whitespace-nowrap">Search Filters</p> */}
            <div className="relative">
              <div className="block xl:hidden">
                <FilterButton handleClick={() => console.log('higbig')} />
              </div>
              <div className="block xl:hidden">
                {/* <ProductPageFilterModal /> */}
              </div>
            </div>
          </div>
          <div className="hidden xl:block">
            <StartDateEndDateWithSearch />
          </div>
        </div>
      </div>
      <div>
        {!isLoading && !isFetching ? (
          <AccountCloseDataRenderer data={data} />
        ) : (
          <TableSkeletonLoaderHalf tableColumn={2} tableRow={6} />
        )}
      </div>
    </div>
  );
}

export default ShopAccountsClosePageOverview;
