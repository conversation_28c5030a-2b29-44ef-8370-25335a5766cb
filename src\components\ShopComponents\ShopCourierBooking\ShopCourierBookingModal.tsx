import Cookies from 'js-cookie';
import { useEffect, useState } from 'react';

import PathaoBookingForm from './PathaoBookingForm';
import SteadFastBookingForm from './SteadFastBookingForm';

import ModalTitle from '@/components/reusable/Modal/ModalTitle';
import SpinnerLoader from '@/components/reusable/SpinnerLoader/SpinnerLoader';
import { useGetCouriersQuery } from '@/redux/api/warehouseApis/couriersApi';
import { ShopOrderDetails } from '@/types/shopTypes/shopOrderTypes';
import { SingleCourierDetails } from '@/types/warehouseTypes/settingsTypes';

interface Props {
  orderDetails?: ShopOrderDetails;
  handleClose: () => void;
}

function ShopCourierBookingModal({ orderDetails, handleClose }: Props) {
  // const { warehouseId } = useAppSelector((state) => state.shopDetails);
  const [afterRegistrationOption, setAfterRegistrationOption] =
    useState('print-invoice');
  const [selectedCourier, setSelectedCourier] = useState<string>('');
  const { data, isLoading } = useGetCouriersQuery({
    organizationId: Cookies.get('organizationId'),
    shopId: orderDetails?.shopId,
  });

  useEffect(() => {
    if (data?.data?.length) {
      setSelectedCourier(data.data[0].type);
    }
  }, [data]);

  useEffect(() => {
    const afterBooking = localStorage.getItem('afterBooking');
    if (afterBooking) {
      setAfterRegistrationOption(afterBooking);
    } else {
      setAfterRegistrationOption('print-invoice');
      localStorage.setItem('afterBooking', 'print-invoice');
    }
  }, []);

  return (
    <div className="flex w-[600px] flex-col gap-4 rounded-xl bg-white p-4">
      <ModalTitle text="Book Order On Courier" handleClose={handleClose} />
      {!isLoading ? (
        <div>
          {/* <div className="mb-2">
            {data?.data?.map((single: SingleCourierDetails) => (
              <button
                key={single.id}
                className={`rounded border px-4 py-2 ${selectedCourier === single.type ? 'bg-primary text-white' : ''}`}
                type="button"
                onClick={() => setSelectedCourier(single.type)}
              >
                {single.type}
              </button>
            ))}
          </div> */}
          {selectedCourier === 'STEADFAST' ? (
            <SteadFastBookingForm
              handleClose={handleClose}
              orderDetails={orderDetails}
              courierId={
                !isLoading
                  ? data?.data?.find(
                      (single: SingleCourierDetails) =>
                        single.type === 'STEADFAST',
                    )?.id
                  : ''
              }
            />
          ) : selectedCourier === 'PATHAO' ? (
            <PathaoBookingForm
              handleClose={handleClose}
              orderDetails={orderDetails}
              courierId={
                !isLoading
                  ? data?.data?.find(
                      (single: SingleCourierDetails) =>
                        single.type === 'PATHAO',
                    )?.id
                  : ''
              }
            />
          ) : (
            ''
          )}
          <div>
            <div className="ml-2 mt-2 flex items-center gap-4">
              <div className="text-center font-semibold">After Booking:</div>
              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  value="print-invoice"
                  checked={afterRegistrationOption === 'print-invoice'}
                  onClick={() => {
                    setAfterRegistrationOption('print-invoice');
                    localStorage.setItem('afterBooking', 'print-invoice');
                  }}
                  className="form-radio h-4 w-4 text-blue-600 transition duration-150 ease-in-out"
                />
                <button
                  type="button"
                  className="cursor-pointer select-none text-gray-700"
                  onClick={() => {
                    setAfterRegistrationOption('print-invoice');
                    localStorage.setItem('afterBooking', 'print-invoice');
                  }}
                >
                  Print Invoice
                </button>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  value="close-modal"
                  checked={afterRegistrationOption === 'close-modal'}
                  onClick={() => {
                    setAfterRegistrationOption('close-modal');
                    localStorage.setItem('afterBooking', 'close-modal');
                  }}
                  className="form-radio h-4 w-4 text-blue-600 transition duration-150 ease-in-out"
                />
                <button
                  type="button"
                  className="cursor-pointer select-none text-gray-700"
                  onClick={() => {
                    setAfterRegistrationOption('close-modal');
                    localStorage.setItem('afterBooking', 'close-modal');
                  }}
                >
                  Close Modal
                </button>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <SpinnerLoader />
      )}
    </div>
  );
}

export default ShopCourierBookingModal;
