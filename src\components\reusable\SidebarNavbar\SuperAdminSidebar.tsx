import Cookies from 'js-cookie';
import {
  Brain,
  Captions,
  DollarSign,
  LayoutDashboard,
  ShieldCheck,
  ShoppingBag,
  Store,
  X,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';

import { ROUTES } from '@/Routes';

interface Props {
  isSidebarOpen: boolean;
}

function SuperAdminSidebar({ isSidebarOpen }: Props) {
  const navigate = useNavigate();
  const [selectedPath, setSelectedPath] = useState('');
  const { pathname } = window.location;
  const shopId = pathname.split('/')[2];
  console.log(shopId);

  useEffect(() => {
    setSelectedPath(pathname);
  }, [pathname]);

  const link =
    'flex items-center px-4 py-2 text-[16px] hover:bg-gray-700 rounded-r-2xl';
  const selectedLink =
    'flex items-center px-4 py-2 text-[16px] hover:bg-gray-700 bg-gray-400 rounded-r-2xl';

  // const subLink = `dropdown-link flex items-center py-2 pr-4 text-[16px] before:mr-2 before:inline-block before:h-[1px] before:w-5 before:bg-gray-400 before:pl-[-10px] before:content-[''] hover:bg-gray-700 rounded-r-2xl`;
  // const selectedSubLink = `dropdown-link flex items-center py-2 pr-4 text-[16px] before:mr-2 before:inline-block before:h-[1px] before:w-5 before:bg-gray-400 before:pl-[-10px] before:content-[''] hover:bg-gray-700 bg-gray-700 before:bg-white rounded-r-2xl`;

  return (
    <div className="flex h-full bg-gray-100">
      <div
        className={`fixed inset-y-0 top-[60px] z-30 w-[260px] transform bg-[#28243D] text-white transition-transform delay-200 duration-200 md:relative md:top-0 md:translate-x-0 ${isSidebarOpen ? 'md:translate-x-0' : '-translate-x-full'}`}
      >
        <div className="flex h-full flex-col overflow-y-auto">
          <Link
            to={ROUTES.SUPER_ADMIN.DASHBOARD}
            className="flex items-center px-4 py-2 text-[16px] hover:bg-gray-700"
          >
            <span className="mr-4">
              <Store className="w-4" />
            </span>{' '}
            Home
          </Link>
          <Link
            to={ROUTES.SUPER_ADMIN.DASHBOARD}
            className={selectedPath.includes('dashboard') ? selectedLink : link}
          >
            <span className="mr-4">
              <LayoutDashboard className="w-4" />
            </span>{' '}
            Dashboard
          </Link>
          <Link
            to={ROUTES.SUPER_ADMIN.SHOP}
            className={
              selectedPath.includes('organizations') ? selectedLink : link
            }
          >
            <span className="mr-4">
              <ShoppingBag className="w-4" />
            </span>{' '}
            Organizations
          </Link>
          <Link
            to={ROUTES.SUPER_ADMIN.PLANS}
            className={selectedPath.includes('plans') ? selectedLink : link}
          >
            <span className="mr-4">
              <Brain className="w-4" />
            </span>{' '}
            Plans
          </Link>
          <Link
            to={ROUTES.SUPER_ADMIN.SUBSCRIPTIONS}
            className={
              selectedPath.includes('subscriptions') ? selectedLink : link
            }
          >
            <span className="mr-4">
              <Captions className="w-4" />
            </span>{' '}
            Subscriptions
          </Link>
          <Link
            to={ROUTES.SUPER_ADMIN.TRANSACTIONS}
            className={
              selectedPath.includes('transactions') ? selectedLink : link
            }
          >
            <span className="mr-4">
              <DollarSign className="w-4" />
            </span>{' '}
            Transaction History
          </Link>
          <Link
            to={ROUTES.SUPER_ADMIN.ADMINS}
            className={selectedPath.includes('admins') ? selectedLink : link}
          >
            <span className="mr-4">
              <ShieldCheck className="w-4" />
            </span>{' '}
            Admins
          </Link>
          <button
            type="button"
            className="flex items-center px-4 py-2 text-[16px] hover:bg-gray-700"
            onClick={() => {
              Cookies.remove('accessToken');
              Cookies.remove('refreshToken');
              navigate(ROUTES.LOG_IN);
            }}
          >
            <span className="mr-4">
              <X className="w-4" />
            </span>{' '}
            Log Out
          </button>
        </div>
      </div>
    </div>
  );
}

export default SuperAdminSidebar;
