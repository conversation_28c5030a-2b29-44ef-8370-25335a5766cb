import { useFormik } from 'formik';
import Cookies from 'js-cookie';
import { toast } from 'react-toastify';
import * as Yup from 'yup';

import FilledSubmitButton from '../../reusable/Buttons/FilledSubmitButton';
import CustomInputField from '../../reusable/CustomInputField/CustomInputField';
import ModalTitle from '../../reusable/Modal/ModalTitle';

import { useCreateSmsPanelMutation } from '@/redux/api/warehouseApis/smsApis';

interface Props {
  type: string;
  handleClose: () => void;
  updateRefreshCounter: () => void;
}

const formikInitialValues = {
  apiKey: null,
  senderId: null,
};

const steadFastValidation = Yup.object({
  apiKey: Yup.string().required('API Key is required'),
  senderId: Yup.string().required('Sender ID is required'),
});

function ElitbuzzSmsConfigModal({
  type,
  handleClose,
  updateRefreshCounter,
}: Props) {
  const [createSmsPanel, { isLoading }] = useCreateSmsPanelMutation();

  const formik = useFormik({
    initialValues: formikInitialValues,
    validationSchema: steadFastValidation,

    onSubmit: async (values) => {
      if (type === 'new') {
        toast.promise(
          createSmsPanel({
            ...values,
            organizationId: Cookies.get('organizationId'),
          }).unwrap(),
          {
            pending: 'Adding New SMS Panel...',
            success: {
              render({ data: res }) {
                if (
                  res.data?.statusCode === 200 ||
                  res.data?.statusCode === 201
                ) {
                  updateRefreshCounter();
                  handleClose();
                }
                return 'SMS Panel Added Successfully';
              },
            },
            error: {
              render({ data: error }) {
                console.log(error);
                return 'Error on creating SMS Panel';
              },
            },
          },
        );
      }
    },
  });

  return (
    <div className="flex w-[400px] flex-col gap-4 rounded-xl bg-white p-4">
      <ModalTitle
        text={type === 'new' ? 'Configure SMS panel' : 'Update SMS Panel'}
        handleClose={handleClose}
      />
      <form
        onSubmit={formik.handleSubmit}
        className="flex w-full flex-col gap-4"
      >
        <CustomInputField
          type="text"
          placeholder="Enter Api Key"
          name="apiKey"
          label="Api Key"
          formik={formik}
        />
        <CustomInputField
          type="text"
          placeholder="Enter Sender Id"
          name="senderId"
          label="Sender ID"
          formik={formik}
        />
        <div className="mt-[10px] flex w-full items-center justify-center">
          <FilledSubmitButton
            text={type === 'new' ? 'Add SMS Panel' : 'Update SMS Panel'}
            isLoading={isLoading}
          />
        </div>
      </form>
    </div>
  );
}

export default ElitbuzzSmsConfigModal;
