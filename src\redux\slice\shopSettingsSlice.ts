import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface Settings {
  id: string;
  shopId: string;
  maximumOrderReturnPolicy: number;
  maximumPaymentMethodEditPolicy: number;
  maximumOrderEditPolicy: number;
  profitMarginPercentage: number;
  returnPolicyText: string;
}

const initialSettingsStates: Settings = {
  id: '',
  shopId: '',
  maximumOrderReturnPolicy: 0,
  maximumPaymentMethodEditPolicy: 0,
  maximumOrderEditPolicy: 0,
  profitMarginPercentage: 0,
  returnPolicyText: '',
};

const shopSettingsSlice = createSlice({
  name: 'shopSettings',
  initialState: initialSettingsStates,
  reducers: {
    setShopSettings: (state, action: PayloadAction<Settings>) => {
      state.id = action.payload.id;
      state.shopId = action.payload.shopId;
      state.maximumOrderReturnPolicy = action.payload.maximumOrderReturnPolicy;
      state.maximumPaymentMethodEditPolicy =
        action.payload.maximumPaymentMethodEditPolicy;
      state.maximumOrderEditPolicy = action.payload.maximumOrderEditPolicy;
      state.profitMarginPercentage = action.payload.profitMarginPercentage;
      state.returnPolicyText = action.payload.returnPolicyText;
    },

    reset: () => initialSettingsStates,
  },
});

// Action creators are generated for each case reducer function
export const { reset, setShopSettings } = shopSettingsSlice.actions;

export default shopSettingsSlice.reducer;
