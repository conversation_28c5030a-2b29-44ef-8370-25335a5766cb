import { TransparentPrintButtonTop } from '@/components/reusable/Buttons/CommonButtons';
import {
  GetShopAccountsSummeryResponse,
  GetShopProductWiseSummeryResponse,
  SingleProductSale,
  SingleReceivedMethod,
} from '@/types/shopTypes/shopStockTransferReportTypes';
import { formatNumberWithComma } from '@/utils/formatNumberWithComma';
import { handleGenerateProductWiseSalesSummeryPdf } from '@/utils/GenerateReportPdf';

interface Props {
  productSummery?: GetShopProductWiseSummeryResponse;
  data?: GetShopAccountsSummeryResponse;
}

function ProductWiseSalesSummery({ productSummery, data }: Props) {
  return (
    <div>
      <div className="tableTop w-full">
        <p>Product Wise Sales Summery</p>
        <TransparentPrintButtonTop
          handleClick={() => {
            handleGenerateProductWiseSalesSummeryPdf(productSummery, data);
          }}
        />
      </div>
      <div className="w-full">
        <div className="full-table-box h-customExc">
          <table className="full-table">
            <thead className="bg-gray-100">
              <tr>
                <th className="tableHeadLeftAlign">Product Name</th>
                <th className="tableHead">Quantity</th>
                <th className="tableHeadRightAlign">Total</th>
              </tr>
            </thead>
            <tbody className="divide-y bg-slate-200">
              {productSummery?.data?.result?.length
                ? productSummery?.data?.result?.map(
                    (singleCategory: SingleProductSale) => (
                      <tr key={singleCategory?.productName}>
                        <td className="tableDataLeftAlign">
                          {singleCategory?.productName}
                        </td>
                        <td className="tableData">
                          {formatNumberWithComma(singleCategory?.quantity)}
                        </td>
                        <td className="tableDataRightAlign">
                          {formatNumberWithComma(
                            singleCategory?.totalRetailPrice,
                          )}
                        </td>
                      </tr>
                    ),
                  )
                : ''}
              <tr>
                <td className="tableDataLeftAlign">Total</td>
                <td className="tableData">
                  {formatNumberWithComma(data?.data?.totalStockSold)}
                </td>
                <td className="tableDataRightAlign" />
              </tr>
              <tr>
                <td className="tableDataRightAlign" />
                <td className="tableDataRightAlign">MRP</td>
                <td className="tableDataRightAlign">
                  {formatNumberWithComma(data?.data?.totalSubtotal)}
                </td>
              </tr>
              <tr>
                <td className="tableDataRightAlign" />
                <td className="tableDataRightAlign">Delivery Charge</td>
                <td className="tableDataRightAlign">
                  {formatNumberWithComma(data?.data?.totalDeliveryCharge)}
                </td>
              </tr>
              <tr>
                <td className="tableDataRightAlign" />
                <td className="tableDataRightAlign">Discount</td>
                <td className="tableDataRightAlign">
                  {formatNumberWithComma(data?.data?.totalDiscount)}
                </td>
              </tr>
              <tr>
                <td className="tableDataRightAlign" />
                <td className="tableDataRightAlign">Vat</td>
                <td className="tableDataRightAlign">
                  {formatNumberWithComma(data?.data?.totalVat)}
                </td>
              </tr>
              <tr>
                <td className="tableDataRightAlign" />
                <td className="tableDataRightAlign">Payable</td>
                <td className="tableDataRightAlign">
                  {' '}
                  {formatNumberWithComma(
                    Number(data?.data?.totalPayable) +
                      Number(data?.data?.totalDeliveryCharge),
                  )}
                </td>
              </tr>
              <tr>
                <td className="tableDataRightAlign">
                  <div className="flex items-center justify-end gap-2">
                    {data?.data?.methodBasedPaymentList?.map(
                      (singleMethod: SingleReceivedMethod) => (
                        <div>
                          <span>{singleMethod?.paymentMethod} - </span>
                          <span>
                            {formatNumberWithComma(singleMethod?.totalAmount)}
                          </span>
                        </div>
                      ),
                    )}
                  </div>
                </td>
                <td className="tableDataRightAlign">Received</td>
                <td className="tableDataRightAlign">
                  {formatNumberWithComma(data?.data?.cashReceive)}
                </td>
              </tr>
              <tr>
                <td className="tableDataRightAlign" />
                <td className="tableDataRightAlign">Due</td>
                <td className="tableDataRightAlign">
                  {formatNumberWithComma(data?.data?.totalDue)}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

export default ProductWiseSalesSummery;
