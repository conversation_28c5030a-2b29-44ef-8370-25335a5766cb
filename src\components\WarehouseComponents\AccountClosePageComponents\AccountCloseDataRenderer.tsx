import {
  GetShopAccountsSummeryResponse,
  SingleReceivedMethod,
} from '@/types/shopTypes/shopStockTransferReportTypes';
import { formatNumberWithComma } from '@/utils/formatNumberWithComma';

interface Props {
  data?: GetShopAccountsSummeryResponse;
}

function AccountCloseDataRenderer({ data }: Props) {
  const totalCost =
    Number(data?.data?.totalPurchasePrice) +
    Number(data?.data?.totalDiscount) +
    Number(data?.data?.totalDeliveryCharge) +
    Number(data?.data?.totalExpense);

  const subtotal = Number(data?.data?.totalSubtotal) || 0;
  const grossProfit =
    subtotal -
    Number(data?.data?.totalPurchasePrice || 0) -
    Number(data?.data?.totalDiscount || 0);
  const netProfit = subtotal - Number(totalCost);

  const grossProfitPercent =
    subtotal > 0 ? ((grossProfit / subtotal) * 100).toFixed(2) : '0.00';
  const netProfitPercent =
    subtotal > 0 ? ((netProfit / subtotal) * 100).toFixed(2) : '0.00';

  return (
    <div>
      <div className="grid grid-cols-12 gap-4">
        <div className="col col-span-12 w-full lg:col-span-6">
          <div className="tableTop w-full">
            <p>Cash Received / Receipts Type</p>
            <button className="text-white" type="button">
              {/* <Printer /> */}
            </button>
          </div>
          <div className="full-table-box">
            <table className="full-table">
              <thead className="bg-gray-100">
                <tr>
                  <th className="tableHead table-col-width">Received Method</th>
                  <th className="tableHead table-col-width">Amount</th>
                </tr>
              </thead>
              <tbody className="divide-y bg-slate-200">
                {data?.data?.methodBasedPaymentList?.map(
                  (singleMethod: SingleReceivedMethod) => (
                    <tr key={singleMethod?.paymentMethod}>
                      <td className="tableData table-col-width">
                        {singleMethod?.paymentMethod}
                      </td>
                      <td className="tableData table-col-width">
                        {singleMethod?.totalAmount}
                      </td>
                    </tr>
                  ),
                )}
                <tr>
                  <td className="tableData table-col-width">
                    Total Cash Received
                  </td>
                  <td className="tableData table-col-width">
                    {data?.data?.cashReceive}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div className="col col-span-12 w-full lg:col-span-6">
          <div className="tableTop w-full">
            <p>Profit Calculation</p>
            <button className="text-white" type="button">
              {/* <Printer /> */}
            </button>
          </div>
          <div className="full-table-box">
            <table className="full-table">
              <thead className="bg-gray-100">
                <tr>
                  <th className="tableHead table-col-width">Field Name</th>
                  <th className="tableHead">Cal</th>
                  <th className="tableHead table-col-width">Amount</th>
                </tr>
              </thead>
              <tbody className="divide-y bg-slate-200">
                {/* <tr>
                        <td className="tableData table-col-width">Shop</td>
                      </tr> */}
                {/* <tr>
                  <td className="tableData table-col-width">Invoice Qty</td>
                  <td className="tableData table-col-width">
                    ={data?.data?.totalOrderCount}
                  </td>
                </tr> */}
                <tr>
                  <td className="tableData table-col-width">MRP Total</td>
                  <td className="tableData"> = </td>
                  <td className="tableData table-col-width">
                    {formatNumberWithComma(Number(data?.data?.totalSubtotal))}
                  </td>
                </tr>
                <tr>
                  <td className="tableData table-col-width">Discount</td>
                  <td className="tableData"> - </td>
                  <td className="tableData table-col-width">
                    {formatNumberWithComma(data?.data?.totalDiscount)}
                  </td>
                </tr>
                <tr>
                  <td className="tableData table-col-width">Net Sale</td>
                  <td className="tableData"> = </td>
                  <td className="tableData table-col-width">
                    {formatNumberWithComma(
                      Number(data?.data?.totalSubtotal) -
                        Number(data?.data?.totalDiscount),
                    )}
                  </td>
                </tr>
                <tr>
                  <td className="tableData table-col-width">Delivery Charge</td>
                  <td className="tableData"> + </td>
                  <td className="tableData table-col-width">
                    {formatNumberWithComma(data?.data?.totalDeliveryCharge)}
                  </td>
                </tr>
                <tr>
                  <td className="tableData table-col-width">VAT</td>
                  <td className="tableData"> + </td>
                  <td className="tableData table-col-width">
                    {formatNumberWithComma(data?.data?.totalVat)}
                  </td>
                </tr>
                <tr>
                  <td className="tableData table-col-width">Payable</td>
                  <td className="tableData"> = </td>
                  <td className="tableData table-col-width">
                    {formatNumberWithComma(
                      Number(data?.data?.totalPayable) +
                        Number(data?.data?.totalDeliveryCharge),
                    )}
                  </td>
                </tr>
                <tr>
                  <td className="tableData table-col-width">Received</td>
                  <td className="tableData"> - </td>
                  <td className="tableData table-col-width">
                    {formatNumberWithComma(data?.data?.cashReceive)}
                  </td>
                </tr>
                <tr>
                  <td className="tableData table-col-width">Due</td>
                  <td className="tableData"> = </td>
                  <td className="tableData table-col-width">
                    {/* {data?.data?.totalDue} */}
                    {formatNumberWithComma(
                      Number(data?.data?.totalPayable) -
                        Number(data?.data?.cashReceive) +
                        Number(data?.data?.totalDeliveryCharge),
                    )}
                  </td>
                </tr>

                <tr>
                  <td className="tableData table-col-width">
                    Total Purchase Price
                  </td>
                  <td className="tableData"> - </td>
                  <td className="tableData table-col-width">
                    {formatNumberWithComma(data?.data?.totalPurchasePrice)}
                  </td>
                </tr>
                <tr>
                  <td className="tableData table-col-width">Gross Profit</td>
                  <td className="tableData"> = </td>
                  <td className="tableData table-col-width">
                    {formatNumberWithComma(grossProfit)} ({grossProfitPercent}%)
                  </td>
                </tr>
                <tr>
                  <td className="tableData table-col-width">Expenses</td>
                  <td className="tableData"> - </td>
                  <td className="tableData table-col-width">
                    {formatNumberWithComma(data?.data?.totalExpense)}
                  </td>
                </tr>
                <tr>
                  <td className="tableData table-col-width">Net Profit</td>
                  <td className="tableData"> = </td>
                  <td className="tableData table-col-width">
                    {formatNumberWithComma(netProfit)} ({netProfitPercent}%)
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <div className="tableTop mt-4 w-full">
        <p>Calculation Formula</p>
        <button className="text-white" type="button">
          {/* <Printer /> */}
        </button>
      </div>
      <div className="full-table-box mb-4">
        <table className="full-table">
          <thead className="bg-gray-100">
            <tr>
              <th className="tableHead table-col-width">Name</th>
              <th className="tableHead table-col-width">Formula</th>
            </tr>
          </thead>
          <tbody className="divide-y bg-slate-200">
            <tr>
              <td className="tableData table-col-width">Net Sale</td>
              <td className="tableData table-col-width">
                MRP Total - Discount
              </td>
            </tr>
            <tr>
              <td className="tableData table-col-width">Payable</td>
              <td className="tableData table-col-width">
                Net Sale + Delivery Charge
              </td>
            </tr>
            <tr>
              <td className="tableData table-col-width">Gross Profit</td>
              <td className="tableData table-col-width">
                Net Sale - Total Purchase Price
              </td>
            </tr>
            <tr>
              <td className="tableData table-col-width">Net Profit</td>
              <td className="tableData table-col-width">
                Gross Profit - Total Expenses
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
}

export default AccountCloseDataRenderer;
