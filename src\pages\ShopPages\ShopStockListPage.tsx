import { useParams } from 'react-router-dom';

import ShopStockListPageOverview from '@/components/ShopComponents/ShopStockListPageComponents/ShopStockListPageOverview';
import { useAppSelector } from '@/redux/hooks';

function ShopStockListPage() {
  const { shopId } = useParams();
  const { warehouseId } = useAppSelector((state) => state.shopDetails);
  return (
    <div>
      <ShopStockListPageOverview
        shopId={shopId ?? ''}
        warehouseId={warehouseId}
      />
    </div>
  );
}

export default ShopStockListPage;
