import BaseApi from '../baseApi';

import { TagTypes } from '@/redux/tag-types';
import {
  GetLocationAnalysisResponse,
  GetShopAccountsSummeryResponse,
  GetShopCategoryWiseSummeryResponse,
  GetShopOrderStatusWiseSummeryResponse,
  GetShopProductWiseSummeryResponse,
  GetShopSalesReportResponse,
  GetShopStockTransferReportResponse,
  ShopExpensesReportResponse,
  ShopStockReportResponse,
} from '@/types/shopTypes/shopStockTransferReportTypes';

interface GetStockLocationParams {
  shopId?: string;
  warehouseId?: string;
  organizationId?: string;
  type?: string;
  startDate?: string;
  endDate?: string;
  sortBy?: string;
  orderStatus?: string;
  createdById?: string;
  status?: string;
}

interface GetLocationAnalysisParams {
  shopId?: string;
}

const ShopReportsApi = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getShopStockTransferReport: builder.query<
      GetShopStockTransferReportResponse,
      GetStockLocationParams
    >({
      query: (params) => ({
        url: '/report/stock-transfer',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP_STOCK],
    }),
    getShopExpensesReport: builder.query<
      ShopExpensesReportResponse,
      GetStockLocationParams
    >({
      query: (params) => ({
        url: '/report/expense',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP_STOCK],
    }),
    getShopStockReport: builder.query<
      ShopStockReportResponse,
      GetStockLocationParams
    >({
      query: (params) => ({
        url: '/report/current-stock',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP_STOCK],
    }),
    getShopAccountsSummeryReport: builder.query<
      GetShopAccountsSummeryResponse,
      GetStockLocationParams
    >({
      query: (params) => ({
        url: '/report/profit-loss',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP_STOCK],
    }),
    getShopSalesReport: builder.query<
      GetShopSalesReportResponse,
      GetStockLocationParams
    >({
      query: (params) => ({
        url: '/report/sales',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP_STOCK],
    }),
    getShopCategoryWiseSaleSummery: builder.query<
      GetShopCategoryWiseSummeryResponse,
      GetStockLocationParams
    >({
      query: (params) => ({
        url: '/report/category',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP_STOCK],
    }),
    getShopProductWiseSaleSummery: builder.query<
      GetShopProductWiseSummeryResponse,
      GetStockLocationParams
    >({
      query: (params) => ({
        url: '/report/product-summary',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP_STOCK],
    }),
    getShopOrderStatusWiseSaleSummery: builder.query<
      GetShopOrderStatusWiseSummeryResponse,
      GetStockLocationParams
    >({
      query: (params) => ({
        url: '/report/order-status',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.STATUS_WISE_REPORT],
    }),
    getShopOrderLocationAnalysis: builder.query<
      GetLocationAnalysisResponse,
      GetLocationAnalysisParams
    >({
      query: (params) => ({
        url: '/report/sales-locations',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP_STOCK],
    }),
  }),
});

export const {
  useGetShopStockTransferReportQuery,
  useGetShopExpensesReportQuery,
  useGetShopStockReportQuery,
  useGetShopAccountsSummeryReportQuery,
  useGetShopSalesReportQuery,
  useGetShopCategoryWiseSaleSummeryQuery,
  useGetShopProductWiseSaleSummeryQuery,
  useGetShopOrderStatusWiseSaleSummeryQuery,
  useGetShopOrderLocationAnalysisQuery,
} = ShopReportsApi;
