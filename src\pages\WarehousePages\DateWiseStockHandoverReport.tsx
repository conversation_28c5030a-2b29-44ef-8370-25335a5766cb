import Cookies from 'js-cookie';
import { useLocation, useNavigate } from 'react-router-dom';

import FilterButton from '@/components/reusable/Buttons/FilterButton';
import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import CustomDateFilterInput from '@/components/reusable/Inputs/CustomDateFilterInput';
import CustomSelectForFilter from '@/components/reusable/Inputs/CustomSelectForFilter';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import OrderStatusViewer from '@/components/reusable/OrdersPagesReusableComponents/OrderStatusViewer';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import { useGetShopsQuery } from '@/redux/api/shopApi';
import { useGetShopSellersQuery } from '@/redux/api/shopApis/shopEmployeesApis';
import { useGetStockHandoverReportQuery } from '@/redux/api/warehouseApis/reportsApis';
import { ROUTES } from '@/Routes';
import { SingleShopDetails } from '@/types/shopTypes';
import { SingleSellerDetails } from '@/types/warehouseTypes/sellersTypes';
import { generateFilterParams } from '@/utils/generateFilterParams';

interface DateWiseStockReportProps {
  warehouseId?: string;
}

const stockStatusFilter = [
  { label: 'AVAILABLE', value: 'ASSIGNED' },
  { label: 'SOLD', value: 'ORDERED' },
];

function DateWiseStockHandoverReport({
  warehouseId,
}: DateWiseStockReportProps) {
  const navigate = useNavigate();
  const router = new URLSearchParams(useLocation().search);
  const shopId = router.get('shopId');
  const userId = router.get('userId');
  const startDate = router.get('date');
  const status = router.get('status');
  const { data: employees, isLoading: isEmployeesDataLoading } =
    useGetShopSellersQuery(
      {
        shopId,
        role: 'SHOP_BILLER',
        organizationId: Cookies.get('organizationId'),
      },
      { skip: !(shopId && warehouseId) },
    );
  const { data, isLoading, isFetching } = useGetStockHandoverReportQuery(
    {
      warehouseId: warehouseId ?? undefined,
      shopId: shopId ?? undefined,
      userId: userId ?? undefined,
      type: 'custom',
      startDate: startDate ?? '',
      endDate: startDate ?? '',
      status: status ?? undefined,
    },
    { skip: !warehouseId },
  );
  const { data: shopData, isLoading: isShopListLoading } = useGetShopsQuery({
    warehouseId,
    isFromStockTransfer: false,
  });
  const handleFilter = (fieldName: string, value: string) => {
    const query = generateFilterParams(fieldName, value);

    if (warehouseId) {
      navigate(
        ROUTES.WAREHOUSE.DATE_WISE_STOCK_HANDOVER_REPORT(
          warehouseId ?? '',
          query,
        ),
      );
    }
  };
  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-1">
        <div className="flex items-center gap-x-2">
          <div className="search-title-and-btn flex items-center gap-x-3">
            <div className="relative">
              <div className="block xl:hidden">
                <FilterButton handleClick={() => console.log('higbig')} />
              </div>
              <div className="block xl:hidden" />
            </div>
          </div>
          <div className="hidden xl:block">
            <div className="flex items-center gap-x-2">
              <CustomDateFilterInput
                value={startDate ?? ''}
                placeholder="Select Date"
                label="Date"
                handleChange={(value: string) => handleFilter('date', value)}
              />
              <CustomSelectForFilter
                options={
                  !isShopListLoading && shopData?.data?.length
                    ? shopData?.data?.map((single: SingleShopDetails) => {
                        return {
                          value: single.id,
                          label: `${single.name} (${single.nickName})`,
                        };
                      })
                    : []
                }
                selectedValue={shopId ?? ''}
                handleSelect={(e) => handleFilter('shopId', e)}
                placeHolder="Select Shop"
              />
              <CustomSelectForFilter
                options={
                  !isEmployeesDataLoading && employees?.data?.length
                    ? employees?.data?.map((sin: SingleSellerDetails) => {
                        return {
                          label: sin.User?.name,
                          value: sin.User?.id,
                        };
                      })
                    : []
                }
                selectedValue={userId ?? ''}
                handleSelect={(e) => handleFilter('userId', e)}
                placeHolder="Receiver"
              />
              <CustomSelectForFilter
                options={stockStatusFilter}
                selectedValue={status ?? ''}
                handleSelect={(e) => handleFilter('status', e)}
                placeHolder="Status"
              />
            </div>
          </div>
        </div>
      </div>
      {!isLoading && !isFetching ? (
        <div>
          <div className="tableTop w-full">
            <p>Stock Handover Report</p>
            <div className="flex items-center">
              <p>Total : {data?.data?.length}</p>
            </div>
          </div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {data?.data?.length ? (
              <div className="full-table-box h-customExc">
                <table className="full-table">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="tableHead">No</th>
                      <th className="tableHead table-col-width">Name</th>
                      <th className="tableHead table-col-width">Status</th>
                      <th className="tableHead">Barcode</th>
                      <th className="tableHead">Receiver</th>
                      <th className="tableHead">Shop</th>
                      <th className="tableHead">Received At</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y bg-slate-200">
                    {data?.data?.map((product: any, index: number) => (
                      <tr key={product}>
                        <td className="tableData">{index + 1}</td>
                        <td className="tableData table-col-width">
                          {product?.name
                            ? product?.name
                            : product?.Product?.name}
                        </td>
                        <td className="tableData">
                          <OrderStatusViewer
                            status={
                              product?.status === 'ASSIGNED'
                                ? 'AVAILABLE'
                                : product?.status === 'ORDERED'
                                  ? 'SOLD'
                                  : ''
                            }
                          />
                        </td>
                        <td className="tableData">{product?.barcode}</td>
                        <td className="tableData">
                          {product?.ReceivedBy?.name}
                        </td>
                        <td className="tableData">
                          {product?.AssignedShop?.name}
                        </td>
                        <td className="tableData">
                          {product?.updatedAt ? (
                            <DateAndTimeViewer date={product?.updatedAt} />
                          ) : (
                            ''
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <NoResultFound pageType="product" />
            )}
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={5} tableRow={6} />
      )}
    </div>
  );
}

export default DateWiseStockHandoverReport;
