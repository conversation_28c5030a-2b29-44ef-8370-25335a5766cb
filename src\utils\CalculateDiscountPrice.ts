import {
  ShopOrderDetails,
  ShopSingleOrderItem,
  SingleWholesaleOrderItem,
} from '@/types/shopTypes/shopOrderTypes';

interface Params {
  retailPrice: number;
  discountType: string;
  discount: number;
  vat?: number;
}
export const CalculateDiscountPrice = ({
  retailPrice,
  discount,
  discountType,
}: Params) => {
  let discountPrice = 0;
  if (discountType === 'FIXED') {
    discountPrice = retailPrice - discount;
  } else if (discountType === 'PERCENTAGE') {
    discountPrice = retailPrice - (retailPrice * discount) / 100;
  }
  return discountPrice;
};

export const CalculateVat = ({
  retailPrice,
  discount,
  discountType,
  vat,
}: Params) => {
  let discountPrice = 0;
  if (discountType === 'FIXED') {
    discountPrice = retailPrice - discount;
  } else if (discountType === 'PERCENTAGE') {
    discountPrice = retailPrice - (retailPrice * discount) / 100;
  }
  const totalVat = Math.round((discountPrice * Number(vat)) / 100);
  return totalVat;
};

export const ProductTpCalculator = (products: ShopSingleOrderItem[]) => {
  let total = 0;
  products?.forEach((product) => {
    total += product?.Stock?.purchasePrice;
  });
  return total;
};

export const ProfitCalculator = (orderDetails: ShopOrderDetails) => {
  const purchasePrice = ProductTpCalculator(orderDetails?.OrderItem);
  const profit = Number(orderDetails?.grandTotal) - Number(purchasePrice);
  const profitPercentage = (
    (profit * 100) /
    Number(orderDetails?.grandTotal)
  ).toFixed(2);
  return {
    profit,
    profitPercentage,
  };
};

export const ProductWholesaleTpCalculator = (
  products: SingleWholesaleOrderItem[],
) => {
  let total = 0;
  products.forEach((product) => {
    total += product?.purchasePrice * product?.orderedQuantity;
  });
  return total;
};

export const WholesaleProfitCalculator = (orderDetails: ShopOrderDetails) => {
  const purchasePrice = ProductWholesaleTpCalculator(
    orderDetails?.wholesaleOrderItem,
  );
  const profit = Number(orderDetails?.grandTotal) - Number(purchasePrice);
  const profitPercentage = (
    (profit * 100) /
    Number(orderDetails?.grandTotal)
  ).toFixed(2);
  return {
    profit,
    profitPercentage,
  };
};
