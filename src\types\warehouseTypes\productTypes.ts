import { Pagination } from '@/redux/commonTypes';

export interface GetProductsResponse {
  success: boolean;
  statusCode: number;
  message: string;
  data: SingleProductDetails[];
  pagination: Pagination;
}

export interface SingleProductDetails {
  id: string;
  createdAt: string;
  updatedAt: string;
  warehouseId: string;
  brandId: string;
  name: string;
  description: string;
  imgUrl: any;
  currentPurchasePrice: number;
  currentWholesaleSellingPrice: number;
  currentSellingPrice: number;
  discountType: string;
  discount: number;
  vat: number;
  unitId: string;
  subUnitId: string;
  unitWithSubUnitMultiplier: number;
  productCategoryId: string;
  productSubCategoryId?: string;
  quantity: number;
  totalSold: number;
  totalSoldCount: number;
  ProductCategory: ProductCategory;
  ProductSubCategory?: ProductSubCategory;
  Brand: Brand;
  Unit: Unit;
  currentStockQuantity: number;
  totalAvailable: number;
  serialNo: number;
  Options: ProductVariantOrSubVariant[];
  Variants: ProductVariant[];
  barcodes: string[];
}

export interface ProductCategory {
  id: string;
  createdAt: string;
  updatedAt: string;
  name: string;
  imgUrl: any;
  warehouseId: string;
}

export interface ProductSubCategory {
  id: string;
  createdAt: string;
  updatedAt: string;
  productCategoryId: string;
  name: string;
  imgUrl: any;
  warehouseId: string;
}

export interface Brand {
  id: string;
  createdAt: string;
  updatedAt: string;
  warehouseId: string;
  name: string;
  imgUrl: any;
}

export interface Unit {
  id: string;
  createdAt: string;
  updatedAt: string;
  warehouseId: string;
  name: string;
}

export interface SingleProductDetailsResponse {
  success: boolean;
  message: string;
  statusCode: number;
  data: SingleProductDataOnDetails;
}

export interface ProductVariantOrSubVariant {
  id: string;
  createdAt: string;
  updatedAt: string;
  name: string;
  values: string[];
  productId: string;
  createdById: string;
}

export interface ProductVariant {
  id: string;
  createdAt: string;
  updatedAt: string;
  name: string;
  retailPrice: number;
  wholesalePrice: number;
  option1: string;
  option2: string;
  option3: any;
  productId: string;
  createdById: string;
}

export interface SingleProductDataOnDetails {
  totalStock: number;
  totalAvailableStock: number;
  currentMonthStockEntryCount: number;
  thisMonthStockSoldCount: number;
  previousStockCount: number;
  previousUnsoldStockCount: number;
  id: string;
  createdAt: string;
  updatedAt: string;
  warehouseId: string;
  brandId: string;
  name: string;
  description: string;
  imgUrl: string;
  currentPurchasePrice: number;
  currentSellingPrice: number;
  discountType: string;
  discount: number;
  vat: number;
  unitId: any;
  subUnitId: any;
  unitWithSubUnitMultiplier: number;
  productCategoryId: string;
  productSubCategoryId: any;
  quantity: number;
  totalSold: number;
  serialNo: number;
  createdById: string;
  Stock: SingleProductDetails[];
  Options: ProductVariantOrSubVariant[];
}

export interface ProductStockLevelResponse {
  success: boolean;
  message: string;
  statusCode: number;
  data: ProductStockLevelData[];
}

export interface ProductStockLevelData {
  label: string;
  value: number;
}
