/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/label-has-associated-control */
import { Barcode, ChevronDown, ChevronRight, Package } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'react-toastify';

import FilledButton from '@/components/reusable/Buttons/FilledButton';
import {
  SingleProductOfAudit,
  useUpdateDiscrepancyReasonMutation,
} from '@/redux/api/shopApis/shopStockAuditApis';

function StockAuditAccordion({
  data,
  productDiscrepancyReasons,
  variantDiscrepancyReasons,
  onProductDiscrepancyReasonChange,
  onVariantDiscrepancyReasonChange,
}: {
  data: SingleProductOfAudit[];
  productDiscrepancyReasons: Record<string, string>;
  variantDiscrepancyReasons: Record<string, string>;
  onProductDiscrepancyReasonChange: (productId: string, reason: string) => void;
  onVariantDiscrepancyReasonChange: (variantId: string, reason: string) => void;
}) {
  const [openProducts, setOpenProducts] = useState<Set<string>>(new Set());
  const [openVariants, setOpenVariants] = useState<Set<string>>(new Set());

  const toggleProduct = (productId: string) => {
    const newOpenProducts = new Set(openProducts);
    if (newOpenProducts.has(productId)) {
      newOpenProducts.delete(productId);
    } else {
      newOpenProducts.add(productId);
    }
    setOpenProducts(newOpenProducts);
  };

  const toggleVariant = (variantId: string) => {
    const newOpenVariants = new Set(openVariants);
    if (newOpenVariants.has(variantId)) {
      newOpenVariants.delete(variantId);
    } else {
      newOpenVariants.add(variantId);
    }
    setOpenVariants(newOpenVariants);
  };

  const getVariantActualCount = (variant: any) => variant.Stocks.length;
  const getVariantExpectedCount = (variant: any) => variant.expectedCount ?? 0;
  const getVariantDiscrepancy = (variant: any) =>
    variant.discrepancy ??
    getVariantActualCount(variant) - getVariantExpectedCount(variant);

  const getDiscrepancyStatus = (discrepancy: number) => {
    if (discrepancy > 0)
      return { color: 'text-green-600', bg: 'bg-green-50', label: 'Surplus' };
    if (discrepancy < 0)
      return { color: 'text-red-600', bg: 'bg-red-50', label: 'Shortage' };
    return { color: 'text-gray-600', bg: 'bg-gray-50', label: 'Match' };
  };

  // Format single product data for update
  const getSingleProductFormattedData = (item: SingleProductOfAudit) => ({
    stockAuditReportProductId: item.id,
    productId: item.Product?.id,
    count: item.count,
    expectedCount: item.expectedCount,
    discrepancy: item.discrepancy,
    discrepancyReason:
      productDiscrepancyReasons[item.id] ?? item.discrepancyReason ?? '',
    variants: item.Variants?.map((variant: any) => ({
      stockAuditReportProductVariantId: variant.id,
      variantId: variant.Variant?.id,
      count: variant.Stocks?.length ?? 0,
      expectedCount: variant.expectedCount ?? 0,
      discrepancy:
        variant.discrepancy ??
        (variant.Stocks?.length ?? 0) - (variant.expectedCount ?? 0),
      discrepancyReason:
        variantDiscrepancyReasons[variant.id] ??
        variant.discrepancyReason ??
        '',
      stocks: variant.Stocks?.map((stockItem: any) => ({
        stockAuditReportProductVariantStockId: stockItem.id,
        stockId: stockItem.Stock?.id,
      })),
    })),
  });

  const [updateDiscrepancyReason] = useUpdateDiscrepancyReasonMutation();
  const handleSubmitReason = (item: SingleProductOfAudit) => {
    toast.promise(
      updateDiscrepancyReason(getSingleProductFormattedData(item)).unwrap(),
      {
        pending: 'Updating Product Discrepancy reason...',
        success: {
          render({ data: res }) {
            if (res.statusCode === 200 || res.statusCode === 201) {
              // refetch();
            }
            return 'Product Discrepancy reason updated Successfully';
          },
        },
        error: {
          render({ data: error }) {
            console.log(error);
            return 'Error updating Product Discrepancy reason';
          },
        },
      },
    );
  };

  return (
    <div className="mx-auto w-full space-y-4">
      <h2 className="mb-6 text-2xl font-bold text-gray-800">
        Stock Audit Report
      </h2>

      {data?.map((item: SingleProductOfAudit) => {
        const isProductOpen = openProducts.has(item.id);
        const discrepancyStatus = getDiscrepancyStatus(item.discrepancy);

        return (
          <div
            key={item.id}
            className="overflow-hidden rounded-lg border border-gray-200 shadow-sm"
          >
            <div
              className="flex cursor-pointer items-center justify-between bg-white p-4 transition-colors hover:bg-gray-50"
              onClick={() => toggleProduct(item.id)}
            >
              <div className="flex items-center space-x-3">
                {isProductOpen ? (
                  <ChevronDown className="h-5 w-5 text-gray-500" />
                ) : (
                  <ChevronRight className="h-5 w-5 text-gray-500" />
                )}
                <Package className="h-5 w-5 text-blue-600" />
                <div>
                  <h3 className="font-semibold text-gray-900">
                    {item.Product.name}
                  </h3>
                  {/* <p className="text-sm text-gray-500">
                    Product ID: {item.Product.id.slice(0, 8)}...
                  </p> */}
                </div>
              </div>

              <div className="flex items-center space-x-6 text-sm">
                <div className="text-center">
                  <p className="text-gray-500">Scanned</p>
                  <p className="font-semibold text-blue-600">{item.count}</p>
                </div>
                <div className="text-center">
                  <p className="text-gray-500">Expected</p>
                  <p className="font-semibold">{item.expectedCount}</p>
                </div>
                <div className="text-center">
                  <p className="text-gray-500">Discrepancy</p>
                  <div
                    className={`rounded-full px-2 py-1 ${discrepancyStatus.bg}`}
                  >
                    <p className={`font-semibold ${discrepancyStatus.color}`}>
                      {item.discrepancy === 0
                        ? '0'
                        : item.discrepancy > 0
                          ? `+${item.discrepancy}`
                          : item.discrepancy}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {isProductOpen && (
              <div className="border-t border-gray-200 bg-gray-50">
                <div className="p-4">
                  <div className="mb-4">
                    <label className="mb-2 block text-sm font-medium text-gray-700">
                      Discrepancy Reason
                    </label>
                    <textarea
                      className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500"
                      rows={2}
                      placeholder="Enter reason for discrepancy..."
                      value={
                        productDiscrepancyReasons[item.id] ||
                        item.discrepancyReason ||
                        ''
                      }
                      onChange={(e) =>
                        onProductDiscrepancyReasonChange(
                          item.id,
                          e.target.value,
                        )
                      }
                    />
                    <div className="mt-2">
                      <FilledButton
                        text="Update Reason"
                        isLoading={false}
                        isDisabled={
                          !(
                            productDiscrepancyReasons[item.id] &&
                            productDiscrepancyReasons[item.id] !==
                              item.discrepancyReason
                          )
                        }
                        handleClick={() => handleSubmitReason(item)}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-medium text-gray-800">
                      Variants ({item.Variants.length})
                    </h4>

                    {item?.Variants.map((variant: any) => {
                      const isVariantOpen = openVariants.has(variant.id);
                      const variantActualCount = getVariantActualCount(variant);
                      const variantExpectedCount =
                        getVariantExpectedCount(variant);
                      const variantDiscrepancy = getVariantDiscrepancy(variant);
                      const variantDiscrepancyStatus =
                        getDiscrepancyStatus(variantDiscrepancy);

                      return (
                        <div
                          key={variant.id}
                          className="rounded-md border border-gray-200 bg-white"
                        >
                          <div
                            className="flex cursor-pointer items-center justify-between p-3 transition-colors hover:bg-gray-50"
                            onClick={() => toggleVariant(variant.id)}
                          >
                            <div className="flex items-center space-x-2">
                              {isVariantOpen ? (
                                <ChevronDown className="h-4 w-4 text-gray-500" />
                              ) : (
                                <ChevronRight className="h-4 w-4 text-gray-500" />
                              )}
                              <span className="font-medium text-gray-700">
                                {variant?.Variant?.name}
                              </span>
                              <span className="text-sm text-gray-500">
                                ({variantActualCount} stocks)
                              </span>
                            </div>

                            <div className="flex items-center space-x-4 text-sm">
                              <div className="text-center">
                                <p className="text-gray-500">Actual</p>
                                <p className="font-semibold text-blue-600">
                                  {variantActualCount}
                                </p>
                              </div>
                              <div className="text-center">
                                <p className="text-gray-500">Expected</p>
                                <p className="font-semibold">
                                  {variantExpectedCount}
                                </p>
                              </div>
                              <div className="text-center">
                                <p className="text-gray-500">Discrepancy</p>
                                <div
                                  className={`rounded-full px-2 py-1 ${variantDiscrepancyStatus.bg}`}
                                >
                                  <p
                                    className={`font-semibold ${variantDiscrepancyStatus.color}`}
                                  >
                                    {variantDiscrepancy === 0
                                      ? '0'
                                      : variantDiscrepancy > 0
                                        ? `+${variantDiscrepancy}`
                                        : variantDiscrepancy}
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>

                          {isVariantOpen && (
                            <div className="border-t border-gray-200 bg-gray-50">
                              <div className="space-y-4 p-3">
                                <div className="grid grid-cols-1 gap-4">
                                  <div>
                                    <label className="mb-1 block text-sm font-medium text-gray-700">
                                      Variant Discrepancy Reason
                                    </label>
                                    <textarea
                                      className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500"
                                      rows={2}
                                      placeholder="Enter reason for variant discrepancy..."
                                      value={
                                        variantDiscrepancyReasons[variant.id] ||
                                        variant.discrepancyReason ||
                                        ''
                                      }
                                      onChange={(e) =>
                                        onVariantDiscrepancyReasonChange(
                                          variant.id,
                                          e.target.value,
                                        )
                                      }
                                      onClick={(e) => e.stopPropagation()}
                                    />
                                  </div>
                                </div>

                                <div>
                                  <h5 className="mb-2 font-medium text-gray-700">
                                    Stock Items
                                  </h5>
                                  <div className="grid grid-cols-1 gap-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6">
                                    {variant.Stocks.map((stockItem: any) => (
                                      <div
                                        key={stockItem.Stock.id}
                                        className="flex items-center space-x-2 rounded border border-gray-200 bg-white p-2"
                                      >
                                        <Barcode className="h-4 w-4 text-gray-500" />
                                        <div className="flex w-full items-center justify-between text-sm">
                                          <p className="font-medium">
                                            {stockItem.Stock.barcode}
                                          </p>
                                          <p
                                            className={
                                              stockItem.status === 'OK'
                                                ? 'rounded bg-green-400 p-1 font-semibold text-white'
                                                : stockItem.status === 'MISSING'
                                                  ? 'rounded bg-red-400 p-1 font-semibold text-white'
                                                  : 'rounded bg-yellow-400 p-1 font-semibold text-white'
                                            }
                                          >
                                            {stockItem.status}
                                          </p>
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
}

export default StockAuditAccordion;
