import { TagTypes } from '@/redux/tag-types';
import BaseApi from '../baseApi';

const OrgEmployeeApis = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getOrgEmployeePermission: builder.query<any, any>({
      query: (params) => ({
        url: `/orgs/permissions`,
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP],
    }),
    addNewPermissionToEmployee: builder.mutation({
      query: (data) => ({
        url: '/shop/permission/new',
        method: 'POST',
        data,
      }),
      invalidatesTags: [TagTypes.SELLERS],
    }),
    updateEmployeePermission: builder.mutation({
      query: ({ data, id }) => ({
        url: `/shop/permission/${id}`,
        method: 'PATCH',
        data,
      }),
      invalidatesTags: [TagTypes.SELLERS],
    }),
    deleteEmployeePermission: builder.mutation({
      query: (id) => ({
        url: `/shop/permission/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: [TagTypes.SELLERS],
    }),
  }),
});

export const {
  useGetOrgEmployeePermissionQuery,
  useAddNewPermissionToEmployeeMutation,
  useUpdateEmployeePermissionMutation,
  useDeleteEmployeePermissionMutation,
} = OrgEmployeeApis;
