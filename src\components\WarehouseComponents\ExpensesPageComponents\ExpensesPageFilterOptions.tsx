import { useLocation } from 'react-router-dom';

import CustomSelectForFilter from '@/components/reusable/Inputs/CustomSelectForFilter';
import SearchInput from '@/components/reusable/Inputs/SearchInput';

interface Props {
  handleFilter: (fieldName: string, value: string) => void;
  categories: any[];
  shopList: any[];
}

function ExpensesPageFilterOptions({
  handleFilter,
  categories,
  shopList,
}: Props) {
  const router = new URLSearchParams(useLocation().search);
  const name = router.get('name');
  const shopName = router.get('shopName');
  const expenseCategoryId = router.get('expenseCategoryId');
  const shopId = router.get('shopId');

  return (
    <div className="flex flex-col gap-3 md:flex-row md:gap-4">
      <CustomSelectForFilter
        options={categories}
        selectedValue={expenseCategoryId ?? ''}
        handleSelect={(e) => handleFilter('expenseCategoryId', e)}
        placeHolder="Select Category"
      />
      <CustomSelectForFilter
        options={shopList}
        selectedValue={shopId ?? ''}
        handleSelect={(e) => handleFilter('shopId', e)}
        placeHolder="Select Shop"
      />
      <SearchInput
        placeholder="Search by Name"
        handleSubmit={(value: string) => handleFilter('name', value)}
        value={name ?? ''}
      />
      <SearchInput
        placeholder="Search shop Name"
        handleSubmit={(value: string) => handleFilter('shopName', value)}
        value={shopName ?? ''}
      />
    </div>
  );
}

export default ExpensesPageFilterOptions;
