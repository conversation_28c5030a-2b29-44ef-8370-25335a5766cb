import { Printer, Sheet, Upload } from 'lucide-react';

import {
  <PERSON>ubar,
  MenubarContent,
  MenubarItem,
  MenubarMenu,
  MenubarSeparator,
  MenubarTrigger,
} from '@/components/ui/menubar';

interface ExportButtonProps {
  totalCount: number;
  handleExportPdf?: () => void;
  handleExportCsv?: () => void;
}

function ExportButton({
  totalCount,
  handleExportCsv,
  handleExportPdf,
}: ExportButtonProps) {
  return (
    <Menubar
      style={{
        border: 'none',
        backgroundColor: 'transparent',
      }}
    >
      <MenubarMenu>
        <MenubarTrigger className="cursor-pointer" disabled={totalCount === 0}>
          <button
            className="flex items-center gap-2 rounded bg-white px-4 py-1 text-black"
            type="button"
          >
            <Upload size={20} />
            <span>Export ({totalCount})</span>
          </button>
        </MenubarTrigger>
        <MenubarContent
          style={{
            marginRight: '25px',
            borderColor: 'black',
          }}
        >
          {handleExportCsv ? (
            <MenubarItem
              onClick={handleExportCsv}
              className="flex cursor-pointer items-center gap-2 bg-primary font-semibold text-white"
            >
              <Sheet size={20} />
              <span>Download CSV</span>
            </MenubarItem>
          ) : (
            ''
          )}
          {handleExportPdf ? (
            <>
              <MenubarSeparator />
              <MenubarItem
                onClick={handleExportPdf}
                className="flex cursor-pointer items-center gap-2 bg-primary font-semibold text-white"
              >
                <Printer size={20} />
                <span>Download PDF</span>
              </MenubarItem>
            </>
          ) : (
            ''
          )}
        </MenubarContent>
      </MenubarMenu>
    </Menubar>
  );
}

export default ExportButton;
