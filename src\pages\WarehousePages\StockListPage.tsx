import { useParams } from 'react-router-dom';

import StockListPageOverview from '@/components/WarehouseComponents/StockListPageComponents/StockListPageOverview';
import { ProtectedRoute } from '@/utils/ProtectedRoutes';

function StockListPage() {
  const { warehouseId } = useParams();
  return (
    <ProtectedRoute>
      <StockListPageOverview warehouseId={warehouseId ?? ''} />
    </ProtectedRoute>
  );
}

export default StockListPage;
