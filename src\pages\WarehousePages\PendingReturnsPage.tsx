import { useParams } from 'react-router-dom';

import OrderListViewer from '@/components/reusable/OrdersPagesReusableComponents/OrderListViewer';
import { useGetReturnOrdersListQuery } from '@/redux/api/warehouseApis/warehouseOrdersApis';

function PendingReturnsPage() {
  const { warehouseId } = useParams();
  const { data } = useGetReturnOrdersListQuery({ warehouseId });

  return (
    <div>
      <OrderListViewer
        orders={data?.data}
        total={data?.pagination.total}
        shopType="WAREHOUSE"
        warehouseId={warehouseId}
      />
    </div>
  );
}

export default PendingReturnsPage;
