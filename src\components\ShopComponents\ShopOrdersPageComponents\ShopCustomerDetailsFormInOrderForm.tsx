// import CustomDropdown from '@/components/reusable/CustomInputField/CustomDropdown';
import CustomInputField from '@/components/reusable/CustomInputField/CustomInputField';
import CustomTextArea from '@/components/reusable/CustomInputField/CustomTextArea';
import { useAppSelector } from '@/redux/hooks';

function ShopCustomerDetailsFormInOrderForm({
  formik,
  isLoading,
}: {
  formik: any;
  isLoading: boolean;
}) {
  const userDetailsFromState = useAppSelector((state) => state.userDetails);
  return (
    <div className="relative flex flex-col gap-2">
      {isLoading && (
        <div className="absolute inset-0 z-10 flex items-center justify-center rounded bg-white bg-opacity-75">
          <div className="text-xl font-bold">Loading...</div>
        </div>
      )}
      {/* <span className="absolute top-[-16px] bg-white text-center text-xl font-bold">
        Customer Info
      </span> */}
      <div className="flex items-center gap-2">
        <CustomInputField
          type="text"
          placeholder="Enter Customer Phone"
          name="mobileNumber"
          label="Phone Number"
          formik={formik}
        />
        <CustomInputField
          type="text"
          placeholder="Enter Customer Name"
          name="name"
          label="Name"
          formik={formik}
        />
      </div>
      {userDetailsFromState?.shopType !== 'PHYSICAL' ? (
        <div className="flex items-start gap-2">
          <CustomTextArea
            placeholder="Enter Customer Address"
            name="address"
            label="Address"
            formik={formik}
          />
          {/* <div className="flex w-full flex-col gap-1">
            <CustomInputField
              type="email"
              placeholder="Enter Customer Email"
              name="email"
              label="E-mail"
              formik={formik}
            />
            <CustomDropdown
              placeholder="Select Gender"
              name="gender"
              label="Gender"
              formik={formik}
              options={[
                { value: 'MALE', label: 'Male' },
                { value: 'FEMALE', label: 'Female' },
                { value: 'OTHERS', label: 'Others' },
              ]}
            />
          </div> */}
        </div>
      ) : (
        ''
      )}
    </div>
  );
}

export default ShopCustomerDetailsFormInOrderForm;
