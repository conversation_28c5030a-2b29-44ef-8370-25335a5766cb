import { useParams } from 'react-router-dom';

import SubCategoriesPageOverview from '@/components/WarehouseComponents/SubCategoriesPageComponents/SubCategoriesPageOverview';
import { ProtectedRoute } from '@/utils/ProtectedRoutes';

function SubCategoriesPage() {
  const { warehouseId } = useParams();
  return (
    <ProtectedRoute>
      <SubCategoriesPageOverview warehouseId={warehouseId ?? ''} />
    </ProtectedRoute>
  );
}

export default SubCategoriesPage;
