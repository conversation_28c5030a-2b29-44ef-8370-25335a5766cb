import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import Swal from 'sweetalert2';

import ShopAuditTimer from './ShopAuditTimer';
import StockAuditAccordion from './StockAuditAccordion';

import FilledButton from '@/components/reusable/Buttons/FilledButton';
import OrderStatusViewer from '@/components/reusable/OrdersPagesReusableComponents/OrderStatusViewer';
import {
  SingleAuditData,
  useAddItemOnAuditMutation,
  useGetShopStockAuditDetailsListQuery,
  useSubmitAuditReportMutation,
  useUpdateSingleAuditMutation,
} from '@/redux/api/shopApis/shopStockAuditApis';
import { handleGenerateStockAuditReportPdf } from '@/utils/GenerateReportPdf';

interface Props {
  shopId?: string;
  auditId?: string;
}

function ShopStockAuditDetailsAndPerformancePageOverview({
  shopId,
  auditId,
}: Props) {
  // State for discrepancy reasons
  const [productDiscrepancyReasons, setProductDiscrepancyReasons] = useState<
    Record<string, string>
  >({});
  const [variantDiscrepancyReasons, setVariantDiscrepancyReasons] = useState<
    Record<string, string>
  >({});
  const [comment, setComment] = useState<string>('');

  const { data, isLoading, isFetching, refetch } =
    useGetShopStockAuditDetailsListQuery(auditId);

  const audit: SingleAuditData | undefined = data?.data;

  // Load discrepancy reasons from API data
  useEffect(() => {
    if (audit?.Products) {
      const prodReasons: Record<string, string> = {};
      const variantReasons: Record<string, string> = {};
      audit.Products.forEach((prod: any) => {
        if (prod.discrepancyReason) {
          prodReasons[prod.id] = prod.discrepancyReason;
        }
        if (prod.Variants) {
          prod.Variants.forEach((variant: any) => {
            if (variant.discrepancyReason) {
              variantReasons[variant.id] = variant.discrepancyReason;
            }
          });
        }
      });
      setProductDiscrepancyReasons(prodReasons);
      setVariantDiscrepancyReasons(variantReasons);
    }
  }, [audit?.Products]);

  const [addItemOnAudit] = useAddItemOnAuditMutation();
  const handleAddItemOnAudit = (barcode: string) => {
    const itemData = {
      barcode: Number(barcode),
      stockAuditReportId: auditId,
      shopId,
      status: 'OK',
    };
    toast.promise(addItemOnAudit(itemData).unwrap(), {
      pending: 'Barcode adding...',
      success: {
        render({ data: res }) {
          if (res.statusCode === 200 || res.statusCode === 201) {
            refetch();
          }
          return 'Barcode added Successfully';
        },
      },
      error: {
        render() {
          Swal.fire({
            title: `Is ${barcode} is extra in your shop?`,
            text: `Add this item to the audit with status "Extra" or remove it from the shop.`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: `Yes, add as Extra`,
          }).then((result) => {
            if (result.isConfirmed) {
              const itemDataForExtra = {
                barcode: Number(barcode),
                stockAuditReportId: auditId,
                shopId,
                status: 'EXTRA',
              };
              toast.promise(addItemOnAudit(itemDataForExtra).unwrap(), {
                pending: 'Updating Audit Item...',
                success: {
                  render({ data: res }) {
                    if (res.statusCode === 200 || res.statusCode === 201) {
                      refetch();
                    }
                    return 'Audit Item updated Successfully';
                  },
                },
                error: {
                  render({ data: error }) {
                    console.log(error);
                    return 'Error updating Audit Item';
                  },
                },
              });
            }
          });
          return 'Barcode adding failed';
        },
      },
    });
  };

  // Handlers to update discrepancy reasons from child
  const handleProductDiscrepancyReasonChange = (
    productId: string,
    reason: string,
  ) => {
    setProductDiscrepancyReasons((prev) => ({
      ...prev,
      [productId]: reason,
    }));
  };
  const handleVariantDiscrepancyReasonChange = (
    variantId: string,
    reason: string,
  ) => {
    setVariantDiscrepancyReasons((prev) => ({
      ...prev,
      [variantId]: reason,
    }));
  };

  // Format data for submission
  const getFormattedAuditData = () => {
    return {
      stockAuditReportId: auditId,
      products: audit?.Products?.map((item: any) => ({
        stockAuditReportProductId: item.id,
        productId: item.Product?.id,
        count: item.count,
        expectedCount: item.expectedCount,
        discrepancy: item.discrepancy,
        discrepancyReason:
          productDiscrepancyReasons[item.id] ?? item.discrepancyReason ?? '',
        variants: item.Variants?.map((variant: any) => ({
          stockAuditReportProductVariantId: variant.id,
          variantId: variant.Variant?.id,
          count: variant.Stocks?.length ?? 0,
          expectedCount: variant.expectedCount ?? 0,
          discrepancy:
            variant.discrepancy ??
            (variant.Stocks?.length ?? 0) - (variant.expectedCount ?? 0),
          discrepancyReason:
            variantDiscrepancyReasons[variant.id] ??
            variant.discrepancyReason ??
            '',
          stocks: variant.Stocks?.map((stockItem: any) => ({
            stockAuditReportProductVariantStockId: stockItem.id,
            stockId: stockItem.Stock?.id,
          })),
        })),
      })),
      comment,
    };
  };

  const [updateSingleAudit] = useUpdateSingleAuditMutation();

  const handleUpdateAuditStatus = (status: string) => {
    const itemData = {
      status,
    };
    Swal.fire({
      title: `Are you sure? you want to ${status === 'IN_PROGRESS' ? 'Start' : status === 'IN_PROGRESS' ? 'PAUSE' : 'Stop'} the Audit`,
      text: `${status === 'IN_PROGRESS' ? "You won't be able to revert this! and billing for this shop will be stopped" : "You won't be able to revert this! and billing for this shop will be resumed"}`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: `Yes, ${status === 'IN_PROGRESS' ? 'Start' : 'Stop'} audit`,
    }).then((result) => {
      if (result.isConfirmed) {
        toast.promise(
          updateSingleAudit({
            id: auditId,
            data: itemData,
          }).unwrap(),
          {
            pending: 'Updating Audit Status...',
            success: {
              render({ data: res }) {
                if (res.statusCode === 200 || res.statusCode === 201) {
                  refetch();
                }
                return 'Audit Status updated Successfully';
              },
            },
            error: {
              render({ data: error }) {
                console.log(error);
                return 'Error updating Audit Status';
              },
            },
          },
        );
      }
    });
  };

  const [submitAuditReport] = useSubmitAuditReportMutation();
  const handleSubmitAuditReport = () => {
    const formattedData = getFormattedAuditData();
    Swal.fire({
      title: `Are you sure? you want to stop and submit the Audit`,
      text: "You won't be able to revert this! and billing for this shop will be resumed",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: `Yes, Stop and submit audit`,
    }).then((result) => {
      if (result.isConfirmed) {
        toast.promise(submitAuditReport(formattedData).unwrap(), {
          pending: 'Updating Audit Status...',
          success: {
            render({ data: res }) {
              if (res.statusCode === 200 || res.statusCode === 201) {
                refetch();
              }
              return 'Audit Submitted Successfully';
            },
          },
          error: {
            render({ data: error }) {
              console.log(error);
              return 'Error submitting Audit';
            },
          },
        });
      }
    });
  };

  useEffect(() => {
    let scannedBarcode = '';
    let timeout: NodeJS.Timeout;
    const handleBarcodeInput = (e: KeyboardEvent) => {
      if (timeout) clearTimeout(timeout);

      if (e.key === 'Enter') {
        if (scannedBarcode) {
          handleAddItemOnAudit(scannedBarcode);
        } else {
          toast.error('Barcode not found');
        }
        /* if (audit?.status === 'IN_PROGRESS') {
          if (scannedBarcode) {
            handleAddItemOnAudit(scannedBarcode);
          } else {
            toast.error('Barcode not found');
          }
        } else {
          toast.error('Audit is not in progress');
        } */
        scannedBarcode = '';
      } else {
        scannedBarcode += e.key;
        timeout = setTimeout(() => {
          scannedBarcode = '';
        }, 200);
      }
    };

    window.addEventListener('keydown', handleBarcodeInput);
    return () => {
      window.removeEventListener('keydown', handleBarcodeInput);
    };
  }, []);

  // useEffect(() => {
  //   handleAddItemOnAudit('61');
  // }, []);

  return (
    <div>
      {isLoading || isFetching ? (
        <div className="py-8 text-center text-gray-500">Loading...</div>
      ) : !audit ? (
        <div className="py-8 text-center text-gray-500">
          No audit data found.
        </div>
      ) : (
        <>
          <div className="mb-6 flex items-center justify-between gap-2 rounded-lg bg-gray-50 px-4 py-2 shadow">
            <div>
              <span className="font-semibold text-gray-700">Created By:</span>{' '}
              <span className="text-gray-900">
                {audit.CreatedBy?.name || '-'}
              </span>
            </div>
            <ShopAuditTimer audit={audit} />

            <div className="flex items-center gap-2">
              {/* <span className="font-semibold text-gray-700">Status:</span>{' '} */}
              <span className="w-[150px] text-center text-gray-900">
                <OrderStatusViewer status={audit.status} />
              </span>
            </div>
            {audit.status === 'COMPLETED' ? (
              <FilledButton
                isLoading={false}
                text="Download Report"
                handleClick={() => handleGenerateStockAuditReportPdf(audit)}
                isDisabled={false}
              />
            ) : audit.status === 'DRAFT' ? (
              <FilledButton
                isLoading={false}
                text="Start"
                handleClick={() => handleUpdateAuditStatus('IN_PROGRESS')}
                isDisabled={audit.status !== 'DRAFT'}
              />
            ) : (
              <div className="space-x-2">
                {audit.status === 'IN_PROGRESS' ? (
                  <FilledButton
                    isLoading={false}
                    text="Pause"
                    handleClick={() => handleUpdateAuditStatus('PAUSE')}
                    isDisabled={false}
                  />
                ) : (
                  ''
                )}
                {audit.status === 'PAUSE' ? (
                  <FilledButton
                    isLoading={false}
                    text="Resume"
                    handleClick={() => handleUpdateAuditStatus('IN_PROGRESS')}
                    isDisabled={false}
                  />
                ) : (
                  ''
                )}
                <FilledButton
                  isLoading={false}
                  text="Submit"
                  handleClick={() => handleSubmitAuditReport()}
                  isDisabled={audit.status !== 'IN_PROGRESS'}
                />
              </div>
            )}
          </div>

          <div className="overflow-x-auto">
            {audit.status !== 'DRAFT' ? (
              <div>
                {audit.Products?.length ? (
                  <StockAuditAccordion
                    data={audit?.Products}
                    productDiscrepancyReasons={productDiscrepancyReasons}
                    variantDiscrepancyReasons={variantDiscrepancyReasons}
                    onProductDiscrepancyReasonChange={
                      handleProductDiscrepancyReasonChange
                    }
                    onVariantDiscrepancyReasonChange={
                      handleVariantDiscrepancyReasonChange
                    }
                  />
                ) : (
                  ''
                )}
                <div className="mt-4">
                  <span className="mb-1 block text-sm font-medium text-gray-700">
                    Comment
                  </span>
                  <textarea
                    className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={2}
                    placeholder="Enter overall comment for this audit..."
                    value={comment}
                    onChange={(e) => setComment(e.target.value)}
                  />
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-center">
                <h2 className="blink-animation text-xl font-bold text-red-600">
                  At first click on the start audit button to start the audit
                </h2>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
}

export default ShopStockAuditDetailsAndPerformancePageOverview;
