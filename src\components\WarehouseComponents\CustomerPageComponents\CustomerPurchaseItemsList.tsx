import { Link } from 'react-router-dom';

import ImageViewer from '@/components/reusable/ImageViewer/ImageViewer';
import Pagination from '@/components/reusable/Pagination/Pagination';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import { useGetProductsQuery } from '@/redux/api/warehouseApis/productsApi';
import { SingleProductDetails } from '@/types/warehouseTypes/productTypes';

interface Props {
  customerId: string;
  warehouseId: string;
}
function CustomerPurchaseItemsList({ customerId, warehouseId }: Props) {
  const { data, isLoading } = useGetProductsQuery({
    warehouseId,
    customerId: customerId ?? undefined,
  });

  return (
    <div className="mb-20">
      {!isLoading ? (
        <div>
          <div className="tableTop w-full">
            <p>Products List</p>
            <p>Total : {data?.pagination?.total}</p>
          </div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {data?.data?.length ? (
              <div className="full-table-box h-custom">
                <table className="full-table">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="tableHead">Image</th>
                      <th className="tableHead">ID</th>
                      <th className="tableHead table-col-width">Name</th>
                      <th className="tableHead">Brand</th>
                      <th className="tableHead">Category</th>
                      <th className="tableHead">Quantity</th>
                      <th className="tableHead">Barcodes</th>
                      {/* <th className="tableHead">Actions</th> */}
                    </tr>
                  </thead>
                  <tbody className="divide-y bg-slate-200">
                    {data?.data?.map((product: SingleProductDetails) => (
                      <tr key={product?.id}>
                        <td className="tableData">
                          <ImageViewer imageUrl={product?.imgUrl} />
                        </td>
                        <td className="tableData">{product?.serialNo ?? 0}</td>
                        <td className="tableData table-col-width">
                          {product?.name}
                        </td>
                        <td className="tableData">{product?.Brand?.name}</td>
                        <td className="tableData">
                          {product?.ProductCategory?.name}
                        </td>

                        <td className="tableData">
                          {product?.barcodes?.length ?? 0}
                        </td>
                        <td className="tableData">
                          <div className="flex flex-wrap items-center gap-2">
                            {product?.barcodes?.map((sin: any) => (
                              <Link
                                to={`/warehouse/${warehouseId}/stock-list/${sin.id}`}
                                className="hover:text-blue-500 hover:underline"
                              >
                                {sin.barcode}
                              </Link>
                            ))}
                          </div>
                        </td>
                        {/* <td className="tableData">
                          <div className="flex items-center justify-center gap-2">
                            ac
                          </div>
                        </td> */}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div>
                <h2>No product found</h2>
              </div>
            )}
          </div>
          <div className="pagination-box flex justify-end rounded bg-white p-3">
            <Pagination
              // currentPage={page ?? '1'}
              // limit={Number(limit ?? 10)}
              /* handleFilter={(fieldName: string, value: any) =>
                handleFilter(fieldName, value)
              } */
              totalCount={data?.pagination?.total}
              totalPages={Math.ceil(
                Number(data?.pagination?.total) /
                  Number(data?.pagination?.limit),
              )}
            />
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={14} tableRow={6} />
      )}
    </div>
  );
}

export default CustomerPurchaseItemsList;
