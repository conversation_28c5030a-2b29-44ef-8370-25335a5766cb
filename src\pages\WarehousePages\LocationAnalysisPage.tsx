import { useParams } from 'react-router-dom';

import LocationAnalysisPageOverview from '@/components/reusable/LocationAnalysisPageComponents/LocationAnalysisPageOverview';

function LocationAnalysisPage() {
  const { warehouseId } = useParams();

  return (
    <div>
      <LocationAnalysisPageOverview type="warehouse" id={warehouseId ?? ''} />
    </div>
  );
}

export default LocationAnalysisPage;
