import { useState } from 'react';

interface Props {
  handleClose: () => void;
  handleDelete: (reason: string) => void;
  length: number;
}
function StockDeleteModal({ handleClose, handleDelete, length }: Props) {
  const [reason, setReason] = useState<string>('');
  return (
    <div className="w-[350px] rounded-xl bg-white p-4 md:w-[500px]">
      <div className="flex flex-col gap-4">
        <span className="text-center text-2xl font-bold">Confirm Delete</span>
        <span className="text-xl font-bold text-red-600">
          Are you sure you want to delete {length} stocks?
        </span>
        <div className="group relative z-0 mt-[-20px] w-full">
          <span className="relative left-3 top-2.5 w-auto bg-white px-1 font-mono text-[12px] font-bold text-gray-900 group-focus-within:text-red-600 dark:text-gray-300">
            Delete Reason
          </span>
          <textarea
            aria-multiline
            className="text-10 py-55-rem block h-20 w-full rounded-lg border bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 disabled:cursor-not-allowed disabled:bg-gray-100 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            placeholder="Enter Delete Reason why you want to delete stocks"
          />
        </div>
        <div className="flex items-center justify-center gap-4">
          <button
            type="button"
            onClick={() => handleDelete && handleDelete(reason)}
            className="w-full rounded-lg bg-[#28243D] px-8 py-2 font-semibold text-white"
          >
            Yes, Delete
          </button>
          <button
            type="button"
            onClick={handleClose}
            className="w-full rounded-lg border-2 border-[#28243D] px-8 py-2 font-semibold text-[#28243D]"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
}

export default StockDeleteModal;
