import { useEffect, useRef, useState } from 'react';

interface Props {
  placeholder: string;
  handleSubmit: (text: string) => void;
  value?: string;
}

export default function SearchInput({
  placeholder,
  handleSubmit,
  value,
}: Props) {
  const [changes, setChanges] = useState<string>('');
  const debounceTimeout = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    setChanges(value ?? '');
  }, [value]);

  const handleInputChange = (text: string) => {
    setChanges(text);
    if (debounceTimeout.current) {
      clearTimeout(debounceTimeout.current);
    }
    debounceTimeout.current = setTimeout(() => {
      handleSubmit(text);
    }, 1000); // 1-second debounce
  };

  return (
    <div className="relative w-full">
      {/* <button
        onClick={() => handleSubmit(changes)}
        disabled={changes.length === 0}
        type="button"
        className="absolute inset-y-0 end-0 z-10 flex items-center rounded-br-md rounded-tr-md border-b border-r border-t border-gray-400 bg-white px-3"
      >
        <svg
          className="h-4 w-4 text-gray-500"
          aria-hidden="true"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 20 20"
        >
          <path
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"
          />
        </svg>
      </button> */}
      <input
        value={changes}
        onChange={(e) => handleInputChange(e.target.value)}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.keyCode === 13) {
            handleSubmit(changes); // Immediate submit on Enter key
          }
        }}
        type="text"
        id="default-search"
        className="text-md block w-full rounded-lg border border-gray-400 px-4 py-[5px] ps-[15px] text-gray-900"
        placeholder={placeholder}
        style={{ outline: 'none' }}
      />
    </div>
  );
}
