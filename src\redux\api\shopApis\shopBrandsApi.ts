import BaseApi from '../baseApi';

import { TagTypes } from '@/redux/tag-types';
import { GetBrandsResponse } from '@/types/warehouseTypes/brandsTypes';

interface GetShopBrandParams {
  warehouseId?: string;
  shopId?: string;
  name?: string;
  page?: string;
  limit?: string;
}

const ShopBrandsApi = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getShopBrands: builder.query<GetBrandsResponse, GetShopBrandParams>({
      query: (params) => ({
        url: '/brand',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.SHOP_BRANDS],
    }),
  }),
});

export const { useGetShopBrandsQuery } = ShopBrandsApi;
