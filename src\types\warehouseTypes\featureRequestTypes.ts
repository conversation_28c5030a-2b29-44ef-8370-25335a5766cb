/* eslint-disable prettier/prettier */
import { CreatedBy, Pagination } from '@/redux/commonTypes';

export type FeatureRequestStatus =
  | 'PENDING'
  | 'UNDER_REVIEW'
  | 'PLANNED'
  | 'IN_PROGRESS'
  | 'COMPLETED'
  | 'REJECTED';

export interface GetFeatureRequestsResponse {
  success: boolean;
  statusCode: number;
  message: string;
  data: SingleFeatureRequestDetails[];
  pagination: Pagination;
}

export interface SingleFeatureRequestDetails {
  id: string;
  createdAt: string;
  updatedAt: string;
  title: string;
  description: string;
  status: FeatureRequestStatus;
  priority?: string;
  createdById: string;
  CreatedBy: CreatedBy;
}

export interface CreateFeatureRequestRequest {
  title: string;
  description: string;
  priority?: string;
}

export interface UpdateFeatureRequestRequest {
  title?: string;
  description?: string;
  status?: FeatureRequestStatus;
  priority?: string;
}
