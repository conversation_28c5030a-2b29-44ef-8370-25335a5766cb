import BaseApi from '../baseApi';

import { TagTypes } from '@/redux/tag-types';
import {
  GetSingleStockDetailsResponse,
  GetStockListResponse,
} from '@/types/warehouseTypes/stockTypes';

interface GetStockListParams {
  organizationId: string;
  warehouseId?: string;
  shopId?: string;
  page?: string;
  productName?: string;
  categoryName?: string;
  barcode?: string;
  limit?: string;
  productSerialNo?: string;
  productId?: string;
  status?: string;
  type?: string;
  startDate?: string;
  endDate?: string;
}

const StocksApi = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getStockList: builder.query<GetStockListResponse, GetStockListParams>({
      query: (params) => ({
        url: '/stock',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.STOCK],
    }),
    getSingleStockDetails: builder.query<GetSingleStockDetailsResponse, string>(
      {
        query: (id) => ({
          url: `/stock/${id}`,
          method: 'GET',
        }),
        providesTags: [TagTypes.STOCK],
      },
    ),
    getSingleStockByBarcode: builder.query<
      GetSingleStockDetailsResponse,
      GetStockListParams
    >({
      query: (params) => ({
        url: `/stock/barcode/${params?.barcode}`,
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.STOCK],
    }),
    entryStock: builder.mutation({
      query: (data) => ({
        url: '/stock/new',
        method: 'POST',
        data,
      }),
      invalidatesTags: [TagTypes.STOCK],
    }),
    transferStock: builder.mutation({
      query: (data) => ({
        url: '/stock/assign-stock',
        method: 'PUT',
        data,
      }),
      invalidatesTags: [TagTypes.STOCK],
    }),
    pullStock: builder.mutation({
      query: (data) => ({
        url: '/stock/pull-stock',
        method: 'PUT',
        data,
      }),
      invalidatesTags: [TagTypes.STOCK],
    }),
    deleteStocks: builder.mutation({
      query: (data) => ({
        url: '/stock',
        method: 'DELETE',
        data,
      }),
      invalidatesTags: [TagTypes.STOCK],
    }),
  }),
});

export const {
  useGetStockListQuery,
  useGetSingleStockDetailsQuery,
  useGetSingleStockByBarcodeQuery,
  useEntryStockMutation,
  useTransferStockMutation,
  usePullStockMutation,
  useDeleteStocksMutation,
} = StocksApi;

export default StocksApi;
