import { useFormik } from 'formik';
import { toast } from 'react-toastify';
import * as Yup from 'yup';

import FilledSubmitButton from '@/components/reusable/Buttons/FilledSubmitButton';
import CustomInputField from '@/components/reusable/CustomInputField/CustomInputField';
import ModalTitle from '@/components/reusable/Modal/ModalTitle';
import { useUpdateShopLimitMutation } from '@/redux/api/superAdminOrganizationsApi';
import { SuperAdminSingleOrganization } from '@/types/superAdminOrganizationsTypes';

interface Props {
  organizationDetails?: SuperAdminSingleOrganization;
  handleClose: () => void;
}

const formikInitialValues = {
  shopLimit: '',
};

const validation = Yup.object({
  shopLimit: Yup.number().required('Shop Limit is required'),
});
function OrganizationShopLimitEditModal({
  organizationDetails,
  handleClose,
}: Props) {
  const [updateShopLimit, { isLoading }] = useUpdateShopLimitMutation();
  const formik = useFormik({
    initialValues: formikInitialValues,
    validationSchema: validation,

    onSubmit: async (values) => {
      toast.promise(
        updateShopLimit({
          data: {
            shopLimit: values.shopLimit,
          },
          id: organizationDetails?.id,
        }).unwrap(),
        {
          pending: 'Updating Shop Limit...',
          success: {
            render({ data: res }) {
              if (res?.statusCode === 200 || res?.statusCode === 201) {
                // updateRefreshCounter();
                handleClose();
              }
              return 'Shop Limit Updated Successfully';
            },
          },
          error: {
            render({ data: error }) {
              console.log(error);
              return 'Error on updating shop limit';
            },
          },
        },
      );
    },
  });
  return (
    <div className="flex w-[400px] flex-col gap-4 rounded-xl bg-white p-4">
      <ModalTitle text="Create Organization" handleClose={handleClose} />
      <form
        onSubmit={formik.handleSubmit}
        className="flex w-full flex-col gap-4"
      >
        <CustomInputField
          type="number"
          placeholder="Enter Shop Limit"
          name="shopLimit"
          label="Shop Limit"
          formik={formik}
        />
        <div className="mt-[10px] flex w-full items-center justify-center">
          <FilledSubmitButton text="Update Limit" isLoading={isLoading} />
        </div>
      </form>
    </div>
  );
}

export default OrganizationShopLimitEditModal;
