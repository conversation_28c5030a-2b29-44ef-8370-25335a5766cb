import { SingleProductDetails } from './productTypes';

import { Pagination } from '@/redux/commonTypes';

export interface GetSupplierBillListResponse {
  success: boolean;
  statusCode: number;
  message: string;
  pagination: Pagination;
  data: SingleSupplierBillDetails[];
}

export interface SingleSupplierBillDetails {
  id: string;
  createdAt: string;
  updatedAt: string;
  supplierId: string;
  supplierInvoiceNo: string;
  note: string;
  proofUrl: any;
  totalPrice: number;
  totalPaid: number;
  advancePayment: number;
  totalDue: number;
  purchaseDate: string;
  warehouseId: string;
  serialNo: any;
  totalProduct: number;
  totalStock: number;
  Supplier: {
    User: { name: string };
  };
}

export interface SingleStockEntryDetailsResponse {
  success: boolean;
  statusCode: number;
  message: string;
  data: SingleStockEntryDetails;
}

export interface SingleStockEntryDetails {
  id: string;
  createdAt: string;
  updatedAt: string;
  supplierId: string;
  supplierInvoiceNo: string;
  note: string;
  proofUrl: any;
  totalPrice: number;
  totalPaid: number;
  advancePayment: number;
  totalDue: number;
  purchaseDate: string;
  warehouseId: string;
  serialNo: any;
  Purchase: SingleProductOnEntryDetails[];
  Supplier: {
    User: { name: string };
  };
}

export interface SingleProductOnEntryDetails {
  id: string;
  createdAt: string;
  updatedAt: string;
  supplierId: string;
  productId: string;
  quantity: number;
  supplierBillId: string;
  warehouseId: string;
  purchasePrice: number;
  retailPrice: number;
  serialNo: any;
  Product: SingleProductDetails;
}

export interface GetBarcodeListResponse {
  success: boolean;
  statusCode: number;
  message: string;
  data: {
    data: SingleBarcodeDetails[];
    pagination: Pagination;
  };
}
export interface GetBarcodeListOfSingleProductResponse {
  success: boolean;
  statusCode: number;
  message: string;
  data: SingleBarcodeDetails[];
}

export interface SingleBarcodeDetails {
  barcode: number;
  productName: string;
  name: string;
  retailPrice: number;
  isSold: boolean;
  isHold: boolean;
  Product?: {
    name: string;
  };
  Variant?: {
    name: string;
  };
}
