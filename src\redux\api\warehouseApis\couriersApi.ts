import BaseApi from '../baseApi';

import { TagTypes } from '@/redux/tag-types';
import {
  GetCourierSettingsListResponse,
  GetPathaoAreasResponse,
  GetPathaoCitiesResponse,
  GetPathaoZonesResponse,
  PathaoAddressParseResponse,
} from '@/types/warehouseTypes/settingsTypes';

interface GetBrandParams {
  organizationId?: string;
  warehouseId?: string;
  isAssigned?: boolean;
  shopId?: string;
}
interface GetCourierForShopParams {
  warehouseId?: string;
  shopId?: string;
}
interface GetCitiesParams {
  pathaoToken?: string;
  courierId?: string;
}
interface GetZonesParams {
  pathaoToken?: string;
  city?: number | null;
  courierId?: string;
}

interface GetAreasParams {
  pathaoToken?: string;
  zone?: number | null;
  courierId?: string;
}

export interface GetPathaoTokenResponse {
  success: boolean;
  message: string;
  statusCode: number;
  data: {
    token: string;
  };
}

const CouriersApi = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getCouriers: builder.query<GetCourierSettingsListResponse, GetBrandParams>({
      query: (params) => ({
        url: '/courier',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.BRAND],
    }),
    getCouriersForShop: builder.query<
      GetCourierSettingsListResponse,
      GetCourierForShopParams
    >({
      query: (params) => ({
        url: '/courier/shop',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.BRAND],
    }),
    createCourier: builder.mutation({
      query: (data) => ({
        url: '/courier/new',
        method: 'POST',
        data,
      }),
      invalidatesTags: [TagTypes.BRAND],
    }),
    updateCourier: builder.mutation({
      query: ({ data, id }) => ({
        url: `/courier/${id}`,
        method: 'PATCH',
        data,
      }),
      invalidatesTags: [TagTypes.BRAND],
    }),
    generatePathaoToken: builder.query<GetPathaoTokenResponse, any>({
      query: (params) => ({
        url: '/courier/pathao-token',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.PATHAO],
    }),
    pathaoAddressParser: builder.mutation<PathaoAddressParseResponse, any>({
      query: (data) => ({
        url: '/courier/pathao-address-parser',
        method: 'POST',
        data,
      }),
      // invalidatesTags: [TagTypes.BRAND],
    }),
    getPathaoCities: builder.query<GetPathaoCitiesResponse, GetCitiesParams>({
      query: (params) => ({
        url: '/courier/pathao-cities',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.PATHAO],
    }),
    getPathaoZones: builder.query<GetPathaoZonesResponse, GetZonesParams>({
      query: (params) => ({
        url: '/courier/pathao-zones',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.PATHAO],
    }),
    getPathaoAreas: builder.query<GetPathaoAreasResponse, GetAreasParams>({
      query: (params) => ({
        url: '/courier/pathao-area',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.PATHAO],
    }),
  }),
});

export const {
  useGetCouriersQuery,
  useGetCouriersForShopQuery,
  useCreateCourierMutation,
  useUpdateCourierMutation,
  usePathaoAddressParserMutation,
  useGetPathaoCitiesQuery,
  useGetPathaoZonesQuery,
  useGetPathaoAreasQuery,
  useGeneratePathaoTokenQuery,
} = CouriersApi;
