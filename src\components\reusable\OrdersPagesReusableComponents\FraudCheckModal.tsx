import axios from 'axios';
import { useEffect, useState } from 'react';

import SpinnerLoader from '../SpinnerLoader/SpinnerLoader';

interface Props {
  phoneNumber: string;
  handleClose: () => void;
}
function FraudCheckModal({ phoneNumber, handleClose }: Props) {
  const [isDataLoading, setIsDataLoading] = useState<boolean>(false);
  const [details, setDetails] = useState<CourierData>();

  useEffect(() => {
    if (phoneNumber) {
      const getFraudResult = async () => {
        setIsDataLoading(true);
        try {
          const res: AllCourierCheckResponse = await axios.post(
            'https://bdcourier.com/api/courier-check',
            { phone: phoneNumber },
            {
              headers: {
                Authorization: `Bearer 8okGF3TgrHbBPHSOmSL9F1YW3gKStbUc5TrIlR9ZZo1LxAB5FPohBu1ZLBHI`,
                'Content-Type': 'application/json',
              },
            },
          );
          setDetails(res?.data.courierData);
          setIsDataLoading(false);
        } catch (error) {
          console.log(error);
          setIsDataLoading(false);
        }
      };
      getFraudResult();
    }
    // @ts-ignore
  }, [phoneNumber]);

  return (
    <div className="w-[700px] bg-white p-4">
      <h2 className="mb-4 text-lg font-bold">Success Rate Checker</h2>
      <h2 className="mb-4 text-sm font-bold">Phone Number: {phoneNumber}</h2>

      {isDataLoading ? (
        <SpinnerLoader />
      ) : (
        <>
          {details ? (
            <div className="overflow-x-auto">
              <table className="w-full border-collapse border border-gray-300">
                <thead>
                  <tr className="bg-gray-200">
                    <th className="border border-gray-300 px-4 py-2">
                      Courier
                    </th>
                    <th className="border border-gray-300 px-4 py-2">Total</th>
                    <th className="border border-gray-300 px-4 py-2">
                      Success
                    </th>
                    <th className="border border-gray-300 px-4 py-2">
                      Cancelled
                    </th>
                    <th className="border border-gray-300 px-4 py-2">
                      Success Ratio (%)
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {Object.entries(details).map(
                    ([courier, data]: [string, SingleCourier]) => {
                      // Default to white if total_parcel is 0
                      let bgColor = 'bg-white text-black';

                      if (data.total_parcel > 0) {
                        if (data.success_ratio > 80) {
                          bgColor = 'bg-green-400 text-white'; // Green for >80%
                        } else if (data.success_ratio >= 50) {
                          bgColor = 'bg-orange-400 text-white'; // Orange for 50% - 80%
                        } else {
                          bgColor = 'bg-red-400 text-white'; // Red for <50%
                        }
                      }

                      return (
                        <tr key={courier} className={`text-center ${bgColor}`}>
                          <td className="border border-gray-300 px-4 py-2 font-semibold">
                            {courier}
                          </td>
                          <td className="border border-gray-300 px-4 py-2">
                            {data.total_parcel}
                          </td>
                          <td className="border border-gray-300 px-4 py-2">
                            {data.success_parcel}
                          </td>
                          <td className="border border-gray-300 px-4 py-2">
                            {data.cancelled_parcel}
                          </td>
                          <td className="border border-gray-300 px-4 py-2">
                            {`${data.success_ratio.toFixed(2)}%`}
                          </td>
                        </tr>
                      );
                    },
                  )}
                </tbody>
              </table>
            </div>
          ) : (
            <p className="font-medium text-red-500">
              No Data Found for {phoneNumber}
            </p>
          )}
        </>
      )}

      <div className="mt-4 flex w-full items-center justify-center gap-4 px-2">
        <button
          className="w-full rounded-lg bg-primary px-4 py-2 font-normal text-white disabled:bg-gray-400"
          onClick={() => handleClose()}
          type="button"
        >
          Close
        </button>
      </div>
    </div>
  );
}

export default FraudCheckModal;

export interface AllCourierCheckResponse {
  message: string;
  data: AllCourierResult;
  success: boolean;
}

export interface AllCourierResult {
  status: string;
  courierData: CourierData;
  reports: any[];
}

export interface CourierData {
  pathao: SingleCourier;
  steadfast: SingleCourier;
  redx: SingleCourier;
  paperfly: SingleCourier;
  summary: SingleCourier;
}

export interface SingleCourier {
  total_parcel: number;
  success_parcel: number;
  cancelled_parcel: number;
  success_ratio: number;
}
