import Cookies from 'js-cookie';
import { useLocation } from 'react-router-dom';

import OrderListForPathaoBulkEntry from './OrderListForPathaoBulkEntry';
import OrderListForSteadfastBulkEntry from './OrderListForSteadfastBulkEntry';

import Pagination from '@/components/reusable/Pagination/Pagination';
import StartDateEndDateWithSearch from '@/components/reusable/ReusableFilters/StartDateEndDateWithSearch';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import { useGetShopOrdersQuery } from '@/redux/api/shopApis/shopOrdersApis';
import { useGetCouriersQuery } from '@/redux/api/warehouseApis/couriersApi';
import { useAppSelector } from '@/redux/hooks';

function ShopCourierBulkEntryOverview({ shopId }: { shopId: string }) {
  const { warehouseId } = useAppSelector((state) => state.shopDetails);
  const router = new URLSearchParams(useLocation().search);
  const customerId = router.get('customerId');
  const customerName = router.get('customerName');
  const mobileNumber = router.get('mobileNumber');
  const serialNo = router.get('serialNo');
  const page = router.get('page');
  const limit = router.get('limit');
  const startDate = router.get('startDate');
  const endDate = router.get('endDate');

  const { data: courierData, isLoading: isCourierDataLoading } =
    useGetCouriersQuery({
      organizationId: Cookies.get('organizationId'),
      shopId,
    });

  const { data, isLoading, isFetching } = useGetShopOrdersQuery(
    {
      warehouseId,
      shopId,
      page: page ?? '1',
      serialNo: serialNo ?? undefined,
      customerName: customerName ?? undefined,
      mobileNumber: mobileNumber ?? undefined,
      customerId: customerId ?? undefined,
      limit: limit ?? '10',
      orderStatus: 'PENDING',
      startDate: startDate ?? undefined,
      endDate: endDate ?? undefined,
      bulkBookingStatus: 'NOT_SENT',
      type: startDate && endDate ? 'custom' : undefined,
    },
    { skip: !(shopId && warehouseId) },
  );

  return (
    <div>
      <div className="search-filters mb-1 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-2">
        <div className="flex items-center">
          <StartDateEndDateWithSearch />
        </div>
      </div>
      {!isCourierDataLoading && !isLoading && !isFetching ? (
        <div className="my-2">
          {courierData?.data[0]?.type === 'PATHAO' ? (
            <OrderListForPathaoBulkEntry
              orders={data?.data}
              total={data?.pagination.total}
              courierDetails={courierData?.data[0]}
              shopId={shopId}
            />
          ) : courierData?.data[0]?.type === 'STEADFAST' ? (
            <OrderListForSteadfastBulkEntry
              orders={data?.data}
              total={data?.pagination.total}
              shopId={shopId}
              courierDetails={courierData?.data[0]}
            />
          ) : (
            'Please setup courier first'
          )}
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={13} tableRow={10} />
      )}
      <div className="pagination-box flex justify-end rounded bg-white p-3">
        <Pagination
          totalCount={data?.pagination.total}
          totalPages={Math.ceil(
            Number(data?.pagination?.total) / Number(data?.pagination?.limit),
          )}
          limits={[10, 20, 50]}
        />
      </div>
    </div>
  );
}

export default ShopCourierBulkEntryOverview;
