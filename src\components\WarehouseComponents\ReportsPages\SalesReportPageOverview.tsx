import { useLocation, useNavigate } from 'react-router-dom';

import ExportButton from '@/components/reusable/Buttons/ExportButton';
import FilterButton from '@/components/reusable/Buttons/FilterButton';
import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import CustomDateFilterInput from '@/components/reusable/Inputs/CustomDateFilterInput';
import CustomSelectForFilter from '@/components/reusable/Inputs/CustomSelectForFilter';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import OrderStatusViewer from '@/components/reusable/OrdersPagesReusableComponents/OrderStatusViewer';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import { useGetShopsQuery } from '@/redux/api/shopApi';
import { useGetWarehouseSalesReportQuery } from '@/redux/api/warehouseApis/warehouseReportsApis';
import { ROUTES } from '@/Routes';
import { SingleShopDetails } from '@/types/shopTypes';
import {
  OrderSinglePaymentDetails,
  ShopOrderDetails,
} from '@/types/shopTypes/shopOrderTypes';
import { formatNumberWithComma } from '@/utils/formatNumberWithComma';
import { handleGenerateSalesSummaryCsv } from '@/utils/GenerateCsv';
import { generateFilterParams } from '@/utils/generateFilterParams';
import { handleGenerateSalesReportPdf } from '@/utils/GenerateReportPdf';

interface Props {
  warehouseId: string;
}

function SalesReportPageOverview({ warehouseId }: Props) {
  const navigate = useNavigate();
  const router = new URLSearchParams(useLocation().search);
  const shopId = router.get('shopId');
  const startDate = router.get('startDate') || `${new Date().toISOString()}`;
  const endDate = router.get('endDate') || `${new Date().toISOString()}`;
  const { data, isLoading, isFetching } = useGetWarehouseSalesReportQuery({
    warehouseId,
    type: 'custom',
    startDate,
    endDate,
    shopId: shopId ?? undefined,
  });

  const { data: shopData, isLoading: isShopListLoading } = useGetShopsQuery({
    warehouseId,
    isFromStockTransfer: false,
  });

  const handleFilter = (fieldName: string, value: string) => {
    const query = generateFilterParams(fieldName, value);
    navigate(ROUTES.WAREHOUSE.SALES_REPORT(warehouseId, query));
  };

  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-1">
        <div className="flex items-center gap-x-2">
          <div className="search-title-and-btn flex items-center gap-x-3">
            {/* <p className="whitespace-nowrap">Search Filters</p> */}
            <div className="relative">
              <div className="block xl:hidden">
                <FilterButton handleClick={() => console.log('higbig')} />
              </div>
              <div className="block xl:hidden">
                {/* <ProductPageFilterModal /> */}
              </div>
            </div>
          </div>
          <div className="hidden xl:block">
            <div className="flex items-center gap-x-2">
              <CustomDateFilterInput
                value={startDate}
                placeholder="Select Start Date"
                label="Start Date"
                handleChange={(value: string) =>
                  handleFilter('startDate', value)
                }
              />
              <CustomDateFilterInput
                value={endDate}
                placeholder="Select Start Date"
                label="End Date"
                handleChange={(value: string) => handleFilter('endDate', value)}
                minimumDate={startDate}
              />
              <CustomSelectForFilter
                options={
                  !isShopListLoading && shopData?.data?.length
                    ? shopData?.data?.map((single: SingleShopDetails) => {
                        return {
                          value: single.id,
                          label: `${single.name} (${single.nickName})`,
                        };
                      })
                    : []
                }
                selectedValue={shopId ?? ''}
                handleSelect={(e) => handleFilter('shopId', e)}
                placeHolder="Select Shop"
              />
            </div>
          </div>
        </div>
      </div>
      {!isLoading && !isFetching ? (
        <div>
          <div className="tableTop w-full">
            <p>Sales Report</p>
            <div className="ml-4">
              <ExportButton
                totalCount={data?.data?.orderList.length ?? 0}
                handleExportCsv={() =>
                  handleGenerateSalesSummaryCsv({
                    orderList: data?.data?.orderList,
                    startDate,
                    endDate,
                  })
                }
                handleExportPdf={() => handleGenerateSalesReportPdf(data?.data)}
              />
            </div>
          </div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {data?.data?.orderList?.length ? (
              <div className="full-table-box h-customExc">
                <table className="full-table">
                  <thead className="bg-gray-100">
                    <tr>
                      {/* <th className="tableHead">No</th> */}
                      <th className="tableHead">Order No</th>
                      <th className="tableHeadLeftAlign">Shop Name</th>

                      <th className="tableHead table-col-width">Name</th>
                      <th className="tableHead">Phone</th>
                      <th className="tableHead">Status</th>
                      <th className="tableHead">Total</th>
                      <th className="tableHead">Paid</th>
                      <th className="tableHead">Due</th>
                      <th className="tableHead">Seller</th>
                      <th className="tableHeadLeftAlign">Payment Method</th>
                      <th className="tableHead">Created At</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y bg-slate-200">
                    {data?.data?.orderList?.map((order: ShopOrderDetails) => (
                      <tr key={order?.id}>
                        {/* <td className="tableData">{index + 1}</td> */}
                        <td className="tableData">{order?.serialNo}</td>
                        <td className="tableDataLeftAlign">
                          {order?.Shop?.name}({order?.Shop?.nickName})
                        </td>
                        <td className="tableData table-col-width">
                          {order?.Customer?.name}
                        </td>
                        <td className="tableData">
                          {order?.Customer?.mobileNumber}
                        </td>
                        <td className="tableData">
                          <OrderStatusViewer status={order?.orderStatus} />
                        </td>
                        <td className="tableData">
                          {formatNumberWithComma(order?.grandTotal)}
                        </td>
                        <td className="tableData">
                          {formatNumberWithComma(order?.totalPaid)}
                        </td>
                        <td className="tableData">
                          {formatNumberWithComma(order?.totalDue)}
                        </td>
                        <td className="tableData">
                          {order?.Employee?.User?.name}
                        </td>
                        <td className="tableDataLeftAlign">
                          {order?.Payment?.map(
                            (singlePayment: OrderSinglePaymentDetails) => (
                              <div key={singlePayment?.id}>
                                {singlePayment?.paymentMethod} -{' '}
                                {formatNumberWithComma(singlePayment?.amount)}
                              </div>
                            ),
                          )}
                        </td>
                        <td className="tableData">
                          <DateAndTimeViewer date={order?.createdAt} />
                        </td>
                      </tr>
                    ))}
                    <tr>
                      <td className="tableDataRightAlign" colSpan={5}>
                        Total:
                      </td>
                      <td className="tableData">
                        {formatNumberWithComma(data?.data?.totalOrderAmount)}
                      </td>
                      <td className="tableData">
                        {formatNumberWithComma(data?.data?.cashReceive)}
                      </td>
                      <td className="tableData">
                        {formatNumberWithComma(data?.data?.totalDue)}
                      </td>
                      <td className="tableData" colSpan={3} />
                    </tr>
                    <tr>
                      <td className="tableData" colSpan={4} />
                      <td className="tableData">Total Order Amount</td>
                      <td className="tableData">
                        {formatNumberWithComma(data?.data?.totalOrderAmount)}
                      </td>
                      <td className="tableData" colSpan={5} />
                    </tr>
                    <tr>
                      <td className="tableData" colSpan={4} />
                      <td className="tableData">Cash Received</td>
                      <td className="tableData">
                        {formatNumberWithComma(data?.data?.cashReceive)}
                      </td>
                      <td className="tableData" colSpan={5} />
                    </tr>
                    <tr>
                      <td className="tableData" colSpan={4} />
                      <td className="tableData">Due Amount</td>
                      <td className="tableData">
                        {formatNumberWithComma(data?.data?.totalDue)}
                      </td>
                      <td className="tableData" colSpan={5} />
                    </tr>
                  </tbody>
                </table>
              </div>
            ) : (
              <NoResultFound pageType="order" />
            )}
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={10} tableRow={6} />
      )}
    </div>
  );
}

export default SalesReportPageOverview;
