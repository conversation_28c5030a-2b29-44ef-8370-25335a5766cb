import FilledButton from '@/components/reusable/Buttons/FilledButton';
import ModalTitle from '@/components/reusable/Modal/ModalTitle';
import { SingleStockDetails } from '@/types/warehouseTypes/stockTypes';
import { CalculateDiscountPrice } from '@/utils/CalculateDiscountPrice';
import { numberToWords } from '@/utils/NumberToText';
import StockName from '@/utils/StockName';

interface Props {
  selectedProducts?: SingleStockDetails[];
  formik?: any;
  netTotal?: number;
  totalPaidAmount?: number;
  selectedPayments: { method: string; amount: number }[];
  shopType: string;
  handleClose: () => void;
  handleSubmit: () => void;
  isOrderCreating: boolean;
}
function ShopOrderConfirmModal({
  selectedProducts,
  formik,
  netTotal,
  totalPaidAmount,
  selectedPayments,
  shopType,
  handleClose,
  handleSubmit,
  isOrderCreating,
}: Props) {
  return (
    <div className="flex w-[80vw] flex-col gap-4 rounded-xl bg-white p-4">
      <ModalTitle text="Confirm Submit" handleClose={handleClose} />
      <div className="grid grid-cols-12 gap-4">
        <div className="col col-span-9">
          <div className="full-table-box max-h-[300px]">
            <table className="full-table bg-white">
              <thead className="bg-gray-100">
                <tr>
                  <th className="tableHead" colSpan={6}>
                    Selected Products
                  </th>
                </tr>
                <tr>
                  <th className="tableHead">No</th>
                  <th className="tableHead">Barcode</th>
                  <th className="tableHeadLeftAlign">Name</th>
                  <th className="tableHeadRightAlign">Regular Price</th>
                  <th className="tableHeadRightAlign">Discount</th>
                  <th className="tableHeadRightAlign">Total</th>
                </tr>
              </thead>
              <tbody className="divide-y">
                {selectedProducts?.length
                  ? selectedProducts?.map(
                      (stock: SingleStockDetails, index: number) => (
                        <tr key={stock?.id}>
                          <td className="tableData">{index + 1}</td>
                          <td className="tableData">{stock?.barcode}</td>
                          <td className="tableDataLeftAlign">
                            <StockName stock={stock} name={stock.name} />
                          </td>
                          <td className="tableDataRightAlign">
                            {stock?.retailPrice.toFixed(2)}
                          </td>
                          <td className="tableDataRightAlign">
                            {(
                              Number(stock.retailPrice) -
                              CalculateDiscountPrice({
                                retailPrice: stock?.retailPrice,
                                discountType: stock.discountType,
                                discount: stock.discount,
                              })
                            ).toFixed(2)}
                          </td>
                          <td className="tableDataRightAlign">
                            {CalculateDiscountPrice({
                              retailPrice: stock?.retailPrice,
                              discountType: stock.discountType,
                              discount: stock.discount,
                            }).toFixed(2)}
                          </td>
                        </tr>
                      ),
                    )
                  : ''}
              </tbody>
            </table>
          </div>
          <div className="mt-2 flex justify-between">
            <div className="font-semibold">
              <p className="font-bold">Customer Details</p>
              <p>Name : {formik.values.name}</p>
              <p>Phone Number : {formik.values.mobileNumber}</p>
              {shopType === 'ONLINE' ? (
                <p>Address : {formik.values.address}</p>
              ) : (
                ''
              )}
            </div>
            <div className="flex flex-col gap-1 text-right font-semibold">
              <p className="font-bold">Payment Details</p>
              <p>
                Payable Amount: {numberToWords(Number(netTotal))?.toUpperCase()}
              </p>
              <p className="text-green-600">
                Paid Amount:{' '}
                {numberToWords(Number(totalPaidAmount))?.toUpperCase()}
                -(
                {selectedPayments?.map((sin) => (
                  <span
                    key={sin.method}
                  >{`${sin.method}-${sin.amount}/ `}</span>
                ))}
                )
              </p>
              <p
                className={`${Number(netTotal) - Number(totalPaidAmount) > 0 ? 'blink-animation text-red-600' : ''}`}
              >
                Due Amount:{' '}
                {numberToWords(
                  Number(netTotal) - Number(totalPaidAmount),
                )?.toUpperCase()}
              </p>
            </div>
          </div>
          <div className="mt-2 text-center">
            <p>Confirm everything before submitting the order</p>
            {Number(netTotal) - Number(totalPaidAmount) > 0 ? (
              <p className="blink-animation font-bold text-red-800">
                You Are submitting order with due amount
              </p>
            ) : (
              ''
            )}
          </div>
        </div>
        <div className="col col-span-3">
          <table className="full-table bg-white">
            <thead className="bg-gray-100">
              <tr>
                <th className="tableHead" colSpan={2}>
                  Cart Summary
                </th>
              </tr>
              <tr>
                <th className="tableHeadLeftAlign">Label</th>
                <th className="tableHeadRightAlign">Details</th>
              </tr>
            </thead>
            <tbody className="divide-y">
              <tr>
                <td className="tableDataLeftAlign">Cart Total</td>
                <td className="tableDataRightAlign">
                  <span className="text-lg">
                    (=) {formik.values.totalProductPrice.toFixed(2)}
                  </span>
                </td>
              </tr>
              <tr>
                <td className="tableDataLeftAlign">Discount</td>
                <td className="tableDataRightAlign">
                  <span className="text-lg">
                    (-) {formik.values.totalDiscount.toFixed(2)}
                  </span>
                </td>
              </tr>
              {shopType === 'ONLINE' && (
                <tr>
                  <td className="tableDataLeftAlign">Delivery Charge</td>
                  <td className="tableDataRightAlign">
                    <span className="text-lg">
                      (+) {Number(formik?.values?.deliveryCharge).toFixed(2)}
                    </span>
                  </td>
                </tr>
              )}
              <tr>
                <td className="tableDataLeftAlign">Vat</td>
                <td className="tableDataRightAlign">
                  <span className="text-lg">
                    (+) {formik.values.vat.toFixed(2)}
                  </span>
                </td>
              </tr>
              <tr>
                <td className="tableDataLeftAlign">Net Total</td>
                <td className="tableDataRightAlign">
                  <span className="text-lg">(=) {netTotal?.toFixed(2)}</span>
                </td>
              </tr>
              <tr>
                <td className="tableDataLeftAlign">Paid Amount</td>
                <td className="tableDataRightAlign text-green-600">
                  <span className="text-lg text-green-600">
                    (-) {totalPaidAmount?.toFixed(2)}
                  </span>
                </td>
              </tr>
              <tr>
                <td className="tableDataLeftAlign">Due/Return</td>
                <td className="tableDataRightAlign">
                  <span
                    className={`${
                      Number(netTotal) - Number(totalPaidAmount) > 0
                        ? 'blink-animation text-lg text-red-600'
                        : ''
                    }`}
                  >
                    (=){' '}
                    {(Number(netTotal) - Number(totalPaidAmount)).toFixed(2)}
                  </span>
                </td>
              </tr>
            </tbody>
          </table>

          {/* <div className="grid grid-cols-12 gap-4 rounded border border-black p-4 text-lg font-semibold">
            <p className="col col-span-5">Cart Total</p>
            <p className="col col-span-2">:</p>
            <p className="col col-span-5">
              {formik.values.totalProductPrice.toFixed(2)}
            </p>
            <p className="col col-span-5">Discount</p>
            <p className="col col-span-2">:</p>
            <p className="col col-span-5">
              {formik.values.totalDiscount.toFixed(2)}
            </p>
            {shopType === 'ONLINE' ? (
              <>
                <p className="col col-span-5">Delivery Charge</p>
                <p className="col col-span-2">:</p>
                <p className="col col-span-5">
                  {Number(formik?.values?.deliveryCharge).toFixed(2)}
                </p>
              </>
            ) : (
              ''
            )}
            <p className="col col-span-5">Vat</p>
            <p className="col col-span-2">:</p>
            <p className="col col-span-5">{formik.values.vat.toFixed(2)}</p>
            <p className="col col-span-5">Net Total</p>
            <p className="col col-span-2">:</p>
            <p className="col col-span-5">{netTotal?.toFixed(2)}</p>
            <p className="col col-span-5">Paid Amount</p>
            <p className="col col-span-2">:</p>
            <p className="col col-span-5 text-green-600">
              {totalPaidAmount?.toFixed(2)}
            </p>
            <p className="col col-span-5">Due/Return</p>
            <p className="col col-span-2">:</p>
            <p
              className={`${Number(netTotal) - Number(totalPaidAmount) > 0 ? 'blink-animation col col-span-5 text-red-600' : 'col col-span-5'}`}
            >
              {(Number(netTotal) - Number(totalPaidAmount)).toFixed(2)}
            </p>
          </div> */}
        </div>
      </div>
      <div className="flex items-center justify-center gap-8">
        <button
          className="cursor-pointer rounded-lg border-2 border-[#28243D] px-8 py-2 font-semibold hover:bg-[#28243dd4] disabled:bg-slate-300"
          type="button"
          onClick={handleClose}
          disabled={isOrderCreating}
        >
          Modify
        </button>
        <FilledButton
          text="Submit Order"
          handleClick={() => handleSubmit()}
          isDisabled={isOrderCreating}
          isLoading={isOrderCreating}
        />
      </div>
    </div>
  );
}

export default ShopOrderConfirmModal;
