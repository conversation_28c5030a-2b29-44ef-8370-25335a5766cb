import { Document, pdf } from '@react-pdf/renderer';
import { FileText } from 'lucide-react';
import { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';

import OrderPageFilterModal from '../OrdersPageComponents/OrderPageFilterModal';
import OrderPageFilterOptions from '../OrdersPageComponents/OrderPageFilterOptions';

import FilterButton from '@/components/reusable/Buttons/FilterButton';
import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import Pagination from '@/components/reusable/Pagination/Pagination';
import SingleOrderInvoicePdf from '@/components/reusable/SingeOrderInvoicePdf/SingleOrderInvoicePdf';
import SpinnerLoader from '@/components/reusable/SpinnerLoader/SpinnerLoader';
import { useGetWarehouseOrdersQuery } from '@/redux/api/warehouseApis/warehouseOrdersApis';
import { ROUTES } from '@/Routes';
import { ShopOrderDetails } from '@/types/shopTypes/shopOrderTypes';
import { generateFilterParams } from '@/utils/generateFilterParams';

interface Props {
  warehouseId: string;
}

function BookingsPageOverview({ warehouseId }: Props) {
  const navigate = useNavigate();
  const router = new URLSearchParams(useLocation().search);
  const customerId = router.get('customerId');
  const customerName = router.get('customerName');
  const serialNo = router.get('serialNo');
  const page = router.get('page');
  const limit = router.get('limit');
  const [isFilterModalOpen, setIsFilterModalOpen] = useState<boolean>(false);
  const { data, isLoading, isFetching } = useGetWarehouseOrdersQuery({
    warehouseId,
    isBooked: true,
    page: page ?? '1',
    serialNo: serialNo ?? undefined,
    customerName: customerName ?? undefined,
    customerId: customerId ?? undefined,
    limit: limit ?? '10',
  });

  const handleGeneratePdf = async (order: ShopOrderDetails) => {
    const blob = await pdf(
      <Document>
        <SingleOrderInvoicePdf orderDetails={order} />
      </Document>,
    ).toBlob();

    // Create a URL for the blob and open it in a new window
    const url = URL.createObjectURL(blob);
    window.open(url);
  };

  const handleFilter = (fieldName: string, value: string) => {
    const query = generateFilterParams(fieldName, value);
    navigate(ROUTES.WAREHOUSE.ORDERS(warehouseId, query));
    console.log(query);
  };

  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-2">
        <div className="flex items-center">
          <div className="search-title-and-btn flex items-center">
            <div className="relative">
              <div className="block xl:hidden">
                <FilterButton
                  handleClick={() => setIsFilterModalOpen(!isFilterModalOpen)}
                />
              </div>
              <div
                className={`${isFilterModalOpen ? 'block' : 'hidden'} xl:hidden`}
              >
                <OrderPageFilterModal
                  handleClearAndClose={() => setIsFilterModalOpen(false)}
                  handleFilter={handleFilter}
                />
              </div>
            </div>
          </div>
          <div className="hidden xl:block">
            <div className="flex items-center gap-x-2">
              <OrderPageFilterOptions handleFilter={handleFilter} />
            </div>
          </div>
        </div>
      </div>
      {!isLoading && isFetching ? (
        <div>
          <div className="tableTop w-full">
            <p>Booking List</p>
            <p>Total : {data?.pagination?.total}</p>
          </div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {data?.data?.length ? (
              <div className="full-table-box h-custom">
                <table className="full-table">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="tableHead">No</th>
                      <th className="tableHead">Order No</th>

                      <th className="tableHead table-col-width">Name</th>
                      <th className="tableHead">Phone</th>
                      {/* <th className="tableHead">Address</th> */}
                      <th className="tableHead">Total</th>
                      <th className="tableHead">Paid</th>
                      <th className="tableHead">COD</th>
                      <th className="tableHead">Booked By</th>
                      <th className="tableHead">Courier</th>
                      <th className="tableHead">Tracking</th>
                      <th className="tableHead">Status</th>
                      <th className="tableHead">Created At</th>
                      <th className="tableHead">Courier Entry Date</th>
                      <th className="tableHead">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y bg-slate-200">
                    {data?.data?.map(
                      (order: ShopOrderDetails, index: number) => (
                        <tr key={order?.id}>
                          <td className="tableData">{index + 1}</td>
                          <td className="tableData">{order?.serialNo}</td>
                          <td className="tableData table-col-width">
                            {order?.Customer.name}
                          </td>
                          <td className="tableData">
                            {order?.Customer.mobileNumber}
                          </td>
                          {/* <td className="tableData">{order?.address}</td> */}
                          <td className="tableData">{order?.grandTotal}</td>
                          <td className="tableData">{order?.totalPaid}</td>
                          <td className="tableData">
                            {order?.CourierBooking[0]?.codAmount}
                          </td>
                          <td className="tableData">
                            {order?.CreatedBy?.name ?? '--'}
                          </td>
                          <td className="tableData">
                            {order?.deliveryPartner ?? '--'}
                          </td>
                          <td className="tableData">
                            <Link
                              to={
                                order?.deliveryPartner === 'PATHAO' &&
                                order.trackingNumber
                                  ? `https://merchant.pathao.com/tracking?consignment_id=${order?.trackingNumber}&phone=${order?.Customer?.mobileNumber}`
                                  : `https://steadfast.com.bd/user/consignment/${order?.trackingNumber}`
                              }
                              target="_blank"
                              className="hover:text-blue-500 hover:underline"
                            >
                              {order?.trackingNumber}
                            </Link>
                          </td>
                          <td className="tableData">{order?.orderStatus}</td>
                          <td className="tableData">
                            <DateAndTimeViewer date={order?.createdAt} />
                          </td>
                          <td className="tableData">
                            <DateAndTimeViewer
                              date={order?.CourierBooking[0]?.createdAt}
                            />
                          </td>
                          <td className="tableData">
                            <div className="flex items-center justify-center gap-2">
                              <button
                                type="button"
                                onClick={() => handleGeneratePdf(order)}
                              >
                                <FileText />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ),
                    )}
                  </tbody>
                </table>
              </div>
            ) : (
              <NoResultFound pageType="order" />
            )}
          </div>
          <div className="pagination-box flex justify-end rounded bg-white p-3">
            <Pagination
              currentPage="1"
              limit={Number(10)}
              handleFilter={(fieldName: string, value: any) =>
                console.log(fieldName, value)
              }
              totalCount={data?.pagination?.total}
              totalPages={Math.ceil(
                Number(data?.pagination?.total) /
                  Number(data?.pagination?.limit),
              )}
            />
          </div>
        </div>
      ) : (
        <SpinnerLoader />
      )}
    </div>
  );
}

export default BookingsPageOverview;
