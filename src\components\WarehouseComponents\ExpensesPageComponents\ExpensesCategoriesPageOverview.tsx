import { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import AddOrEditExpenseCategoryModal from './AddOrEditExpenseCategoryModal';
import ExpensesCategoriesPageFilterModal from './ExpensesCategoriesPageFilterModal';
import ExpensesCategoryFilterOptions from './ExpensesCategoryFilterOptions';

import { EditButton } from '@/components/reusable/Buttons/CommonButtons';
import FilledButton from '@/components/reusable/Buttons/FilledButton';
import FilterButton from '@/components/reusable/Buttons/FilterButton';
import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import Modal from '@/components/reusable/Modal/Modal';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import Pagination from '@/components/reusable/Pagination/Pagination';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import { useGetExpenseCategoriesQuery } from '@/redux/api/warehouseApis/expensesApis';
import { ROUTES } from '@/Routes';
import { generateFilterParams } from '@/utils/generateFilterParams';

interface Props {
  warehouseId: string;
}
function ExpensesCategoriesPageOverview({ warehouseId }: Props) {
  const navigate = useNavigate();
  const router = new URLSearchParams(useLocation().search);
  const name = router.get('name');
  const page = router.get('page');
  const limit = router.get('limit');

  const { data, isLoading, refetch } = useGetExpenseCategoriesQuery({
    warehouseId,
    page: page ?? '1',
    name: name ?? undefined,
    limit: limit ?? '10',
  });
  // const [deleteBrand] = useDeleteBrandMutation();
  const [isCreateBrandModalOpen, setIsCreateBrandModalOpen] =
    useState<boolean>(false);
  const [isEditBrandModalOpen, setIsEditBrandModalOpen] = useState(false);
  const [selectedBrand, setSelectedBrand] = useState<any>();
  const [isFilterModalOpen, setIsFilterModalOpen] = useState<boolean>(false);
  /* const [isDeleteBrandModalOpen, setIsDeleteBrandModalOpen] = useState(false);
  const [deleteBrandData, setDeleteBrandData] = useState<any>(); */

  /* const handleDeleteBrand = async () => {
    toast.promise(deleteBrand(deleteBrandData?.id), {
      pending: 'Deleting Brand...',
      success: {
        render({ data: res }) {
          if (res.data?.statusCode === 200 || res.data?.statusCode === 201) {
            refetch();
            setIsDeleteBrandModalOpen(false);
          }
          return 'Brand Deleted Successfully';
        },
      },
      error: {
        render({ data: error }) {
          console.log(error);
          return 'Error on delete brand';
        },
      },
    });
  }; */

  const handleFilter = (fieldName: string, value: string) => {
    const query = generateFilterParams(fieldName, value);
    navigate(ROUTES.WAREHOUSE.EXPENSES_CATEGORIES(warehouseId, query));
  };

  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 lg:py-1">
        <div className="flex items-center">
          <div className="search-title-and-btn flex items-center">
            <div className="relative">
              <div className="block lg:hidden">
                <FilterButton
                  handleClick={() => setIsFilterModalOpen(!isFilterModalOpen)}
                />
              </div>
              <div
                className={`${isFilterModalOpen ? 'block' : 'hidden'} xl:hidden`}
              >
                <ExpensesCategoriesPageFilterModal
                  handleClearAndClose={() => setIsFilterModalOpen(false)}
                  handleFilter={handleFilter}
                />
              </div>
            </div>
          </div>
          <div className="hidden lg:block">
            <div className="flex items-center gap-x-2">
              <ExpensesCategoryFilterOptions handleFilter={handleFilter} />
            </div>
          </div>
        </div>
        <div>
          <FilledButton
            isLoading={false}
            text="Add New Expense Category"
            handleClick={() => setIsCreateBrandModalOpen(true)}
            isDisabled={false}
          />
        </div>
      </div>
      {!isLoading ? (
        <div>
          <div className="tableTop w-full">
            <p>Expense Categories</p>
            <p>Total : {data?.pagination?.total}</p>
          </div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {data?.data?.length ? (
              <div className="full-table-box h-custom">
                <table className="full-table">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="tableHead">No</th>
                      {/* <th className="tableHead">Image</th> */}
                      <th className="tableHead">Name</th>
                      <th className="tableHead">Created At</th>
                      <th className="tableHead">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y bg-slate-200">
                    {data?.data?.map((brand: any, index: number) => (
                      <tr key={brand?.id}>
                        <td className="tableData">{index + 1}</td>
                        {/* <td className="tableData">
                            <ImageViewer imageUrl={brand?.imgUrl} />
                          </td> */}
                        <td className="tableData">{brand?.name}</td>
                        <td className="tableData">
                          <DateAndTimeViewer date={brand?.createdAt} />
                        </td>
                        <td className="tableData">
                          <div className="flex items-center justify-center gap-2">
                            <EditButton
                              handleClick={() => {
                                setSelectedBrand(brand);
                                setIsEditBrandModalOpen(true);
                              }}
                            />
                            {/* <DeleteButton
                              handleClick={() => {
                                setIsDeleteBrandModalOpen(true);
                                setDeleteBrandData(brand);
                              }}
                            /> */}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <NoResultFound pageType="category" />
            )}
          </div>
          <div className="pagination-box flex justify-end rounded bg-white p-3">
            <Pagination
              currentPage={page ?? '1'}
              limit={Number(limit ?? 10)}
              handleFilter={(fieldName: string, value: any) =>
                handleFilter(fieldName, value)
              }
              totalCount={data?.pagination?.total}
              totalPages={Math.ceil(
                Number(data?.pagination?.total) /
                  Number(data?.pagination?.limit),
              )}
            />
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={4} tableRow={6} />
      )}
      <Modal
        setShowModal={setIsCreateBrandModalOpen}
        showModal={isCreateBrandModalOpen}
      >
        <AddOrEditExpenseCategoryModal
          type="new"
          warehouseId={warehouseId}
          handleClose={() => setIsCreateBrandModalOpen(false)}
          updateRefreshCounter={refetch}
        />
      </Modal>
      <Modal
        setShowModal={setIsEditBrandModalOpen}
        showModal={isEditBrandModalOpen}
      >
        <AddOrEditExpenseCategoryModal
          type="edit"
          warehouseId={warehouseId}
          handleClose={() => setIsEditBrandModalOpen(false)}
          brandData={selectedBrand}
          updateRefreshCounter={refetch}
        />
      </Modal>
      {/* <Modal
        setShowModal={setIsDeleteBrandModalOpen}
        showModal={isDeleteBrandModalOpen}
      >
        <DeleteModal
          type="Brand"
          name={deleteBrandData?.name ?? ''}
          handleClose={() => setIsDeleteBrandModalOpen(false)}
          handleDelete={handleDeleteBrand}
        />
      </Modal> */}
    </div>
  );
}

export default ExpensesCategoriesPageOverview;
