import { GetSuperAdminOrganizationsResponse } from '@/types/superAdminOrganizationsTypes';
import { TagTypes } from '../tag-types';
import BaseApi from './baseApi';

const SuperAdminOrganizationsApi = BaseApi.injectEndpoints({
  endpoints: (builder) => ({
    getOrganizations: builder.query<GetSuperAdminOrganizationsResponse, any>({
      query: (params) => ({
        // url: '/super-admin/customer/list',
        url: '/orgs',
        method: 'GET',
        params,
      }),
      providesTags: [TagTypes.ORGANIZATIONS],
    }),
    getOrganizationDetails: builder.query({
      query: (id) => ({
        url: `/orgs/${id}`,
        method: 'GET',
      }),
      providesTags: [TagTypes.ORGANIZATIONS],
    }),
    updateShopLimit: builder.mutation({
      query: ({ data, id }) => ({
        url: `/orgs/${id}`,
        method: 'PATCH',
        data,
      }),
      invalidatesTags: [TagTypes.BRAND],
    }),
    createBrand: builder.mutation({
      query: (data) => ({
        url: '/brand/new',
        method: 'POST',
        data,
      }),
      invalidatesTags: [TagTypes.BRAND],
    }),
    updateBrand: builder.mutation({
      query: ({ data, id }) => ({
        url: `/brand/${id}`,
        method: 'PATCH',
        data,
      }),
      invalidatesTags: [TagTypes.BRAND],
    }),
    deleteBrand: builder.mutation({
      query: (id) => ({
        url: `/brand/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: [TagTypes.BRAND],
    }),
  }),
});

export const {
  useGetOrganizationsQuery,
  useGetOrganizationDetailsQuery,
  useUpdateShopLimitMutation,
  useCreateBrandMutation,
  useDeleteBrandMutation,
  useUpdateBrandMutation,
} = SuperAdminOrganizationsApi;
