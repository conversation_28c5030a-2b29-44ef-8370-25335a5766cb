import { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

import AddOrEditSubCategoryModal from './AddOrEditSubCategoryModal';
import SubCategoryFilterOption from './SubCategoryFilterOption';
import SubCategoryPageFilterModal from './SubCategoryPageFilterModal';

import {
  DeleteButton,
  EditButton,
} from '@/components/reusable/Buttons/CommonButtons';
import FilledButton from '@/components/reusable/Buttons/FilledButton';
import FilterButton from '@/components/reusable/Buttons/FilterButton';
import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import DeleteModal from '@/components/reusable/DeleteModal/DeleteModal';
import ImageViewer from '@/components/reusable/ImageViewer/ImageViewer';
import Modal from '@/components/reusable/Modal/Modal';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import Pagination from '@/components/reusable/Pagination/Pagination';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import { useGetCategoriesQuery } from '@/redux/api/warehouseApis/categoriesApi';
import {
  useDeleteSubCategoryMutation,
  useGetSubCategoriesQuery,
} from '@/redux/api/warehouseApis/subCategoriesApi';
import { ROUTES } from '@/Routes';
import { SingleCategoryDetails } from '@/types/warehouseTypes/categoriesTypes';
import { SingleSubCategory } from '@/types/warehouseTypes/subCategoriesTypes';
import { generateFilterParams } from '@/utils/generateFilterParams';

interface Props {
  warehouseId: string;
}
function SubCategoriesPageOverview({ warehouseId }: Props) {
  const navigate = useNavigate();
  const router = new URLSearchParams(useLocation().search);
  const name = router.get('name');
  const page = router.get('page');
  const limit = router.get('limit');

  const { data, isLoading, refetch } = useGetSubCategoriesQuery({
    warehouseId,
    page: page ?? '1',
    name: name ?? undefined,
    limit: limit ?? '10',
  });
  const { data: categories } = useGetCategoriesQuery({
    warehouseId,
  });
  const [isCreateSubCategoryModalOpen, setIsCreateSubCategoryModalOpen] =
    useState<boolean>(false);
  const [deleteSubCategory] = useDeleteSubCategoryMutation();
  const [isEditSubCategoryModalOpen, setIsEditSubCategoryModalOpen] =
    useState(false);
  const [selectedSubCategory, setSelectedSubCategory] =
    useState<SingleSubCategory>();
  const [isDeleteSubCategoryModalOpen, setIsDeleteSubCategoryModalOpen] =
    useState(false);
  const [deleteSubCategoryData, setDeleteSubCategoryData] =
    useState<SingleSubCategory>();
  const [isFilterModalOpen, setIsFilterModalOpen] = useState<boolean>(false);

  const handleDeleteSubCategory = async () => {
    toast.promise(deleteSubCategory(deleteSubCategoryData?.id).unwrap(), {
      pending: 'Deleting Category...',
      success: {
        render({ data: res }) {
          if (res?.statusCode === 200 || res?.statusCode === 201) {
            refetch();
            setIsDeleteSubCategoryModalOpen(false);
          }
          return 'Category Deleted Successfully';
        },
      },
      error: {
        render({ data: error }) {
          console.log(error);
          return 'Error on delete category';
        },
      },
    });
  };

  const handleFilter = (fieldName: string, value: string) => {
    const query = generateFilterParams(fieldName, value);
    navigate(ROUTES.WAREHOUSE.SUB_CATEGORIES(warehouseId, query));
  };

  return (
    <div>
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 lg:py-2">
        <div className="flex items-center">
          <div className="search-title-and-btn flex items-center">
            <div className="relative">
              <div className="block lg:hidden">
                <FilterButton
                  handleClick={() => setIsFilterModalOpen(!isFilterModalOpen)}
                />
              </div>
              <div
                className={`${isFilterModalOpen ? 'block' : 'hidden'} xl:hidden`}
              >
                <SubCategoryPageFilterModal
                  handleClearAndClose={() => setIsFilterModalOpen(false)}
                  handleFilter={handleFilter}
                />
              </div>
            </div>
          </div>
          <div className="hidden lg:block">
            <div className="flex items-center gap-x-2">
              <SubCategoryFilterOption handleFilter={handleFilter} />
            </div>
          </div>
        </div>
        <div>
          <FilledButton
            isLoading={false}
            text="Add New Sub Category"
            handleClick={() => setIsCreateSubCategoryModalOpen(true)}
            isDisabled={false}
          />
        </div>
      </div>
      {!isLoading ? (
        <div>
          <div className="tableTop w-full">
            <p>Sub Category List</p>
            <p>Total : {data?.pagination?.total}</p>
          </div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {data?.data?.length ? (
              <div className="full-table-box h-custom">
                <table className="full-table">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="tableHead">No</th>
                      <th className="tableHead">Image</th>
                      <th className="tableHead">Name</th>
                      <th className="tableHead">Parent Category</th>
                      <th className="tableHead">Created By</th>
                      <th className="tableHead">Created At</th>
                      <th className="tableHead">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y bg-slate-200">
                    {data?.data?.map(
                      (subCategory: SingleSubCategory, index: number) => (
                        <tr key={subCategory?.id}>
                          <td className="tableData">{index + 1}</td>
                          <td className="tableData">
                            <ImageViewer imageUrl={subCategory?.imgUrl ?? ''} />
                          </td>
                          <td className="tableData">{subCategory?.name}</td>
                          <td className="tableData">
                            {subCategory?.ProductCategory?.name}
                          </td>
                          <td className="tableData">
                            {subCategory?.CreatedBy?.name}
                          </td>
                          <td className="tableData">
                            <DateAndTimeViewer date={subCategory?.createdAt} />
                          </td>
                          <td className="tableData">
                            <div className="flex items-center justify-center gap-2">
                              <EditButton
                                handleClick={() => {
                                  setSelectedSubCategory(subCategory);
                                  setIsEditSubCategoryModalOpen(true);
                                }}
                              />
                              <DeleteButton
                                handleClick={() => {
                                  setIsDeleteSubCategoryModalOpen(true);
                                  setDeleteSubCategoryData(subCategory);
                                }}
                              />
                            </div>
                          </td>
                        </tr>
                      ),
                    )}
                  </tbody>
                </table>
              </div>
            ) : (
              <NoResultFound pageType="sub category" />
            )}
          </div>
          <div className="pagination-box flex justify-end rounded bg-white p-3">
            <Pagination
              currentPage={page ?? '1'}
              limit={Number(limit ?? 10)}
              handleFilter={(fieldName: string, value: any) =>
                handleFilter(fieldName, value)
              }
              totalCount={data?.pagination?.total}
              totalPages={Math.ceil(
                Number(data?.pagination?.total) /
                  Number(data?.pagination?.limit),
              )}
            />
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={6} tableRow={6} />
      )}
      <Modal
        setShowModal={setIsCreateSubCategoryModalOpen}
        showModal={isCreateSubCategoryModalOpen}
      >
        <AddOrEditSubCategoryModal
          type="new"
          warehouseId={warehouseId}
          handleClose={() => setIsCreateSubCategoryModalOpen(false)}
          updateRefreshCounter={refetch}
          categories={categories?.data.map(
            (category: SingleCategoryDetails) => {
              return {
                value: category.id,
                label: category.name,
              };
            },
          )}
        />
      </Modal>
      <Modal
        setShowModal={setIsEditSubCategoryModalOpen}
        showModal={isEditSubCategoryModalOpen}
      >
        <AddOrEditSubCategoryModal
          type="edit"
          warehouseId={warehouseId}
          handleClose={() => setIsEditSubCategoryModalOpen(false)}
          subCategoryData={selectedSubCategory}
          updateRefreshCounter={refetch}
          categories={categories?.data.map(
            (category: SingleCategoryDetails) => {
              return {
                value: category.id,
                label: category.name,
              };
            },
          )}
        />
      </Modal>
      <Modal
        setShowModal={setIsDeleteSubCategoryModalOpen}
        showModal={isDeleteSubCategoryModalOpen}
      >
        <DeleteModal
          type="Sub Category"
          name={deleteSubCategoryData?.name ?? ''}
          handleClose={() => setIsDeleteSubCategoryModalOpen(false)}
          handleDelete={handleDeleteSubCategory}
        />
      </Modal>
    </div>
  );
}

export default SubCategoriesPageOverview;
