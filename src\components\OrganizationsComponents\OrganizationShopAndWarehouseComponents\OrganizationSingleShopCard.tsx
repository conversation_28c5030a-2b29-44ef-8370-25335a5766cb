import { Ellipsis, Pencil } from 'lucide-react';
import { useState } from 'react';
import { Link } from 'react-router-dom';

import AddOrEditShopModal from '@/components/HomePageComponents/AddOrEditShopModal';
import EditWarehouseModal from '@/components/HomePageComponents/EditWarehouseModal';
import DeleteModal from '@/components/reusable/DeleteModal/DeleteModal';
import Modal from '@/components/reusable/Modal/Modal';
import {
  Menubar,
  MenubarContent,
  MenubarItem,
  MenubarMenu,
  MenubarTrigger,
} from '@/components/ui/menubar';
import { useDeleteShopMutation } from '@/redux/api/shopApi';
import { ROUTES } from '@/Routes';
import { SingleShopDetails } from '@/types/shopTypes';

interface Props {
  shop: SingleShopDetails;
  type: 'shop' | 'warehouse';
}

function OrganizationSingleShopCard({ shop, type }: Props) {
  const [deleteShop] = useDeleteShopMutation();
  const [isDeleteShopModalOpen, setIsDeleteShopModalOpen] =
    useState<boolean>(false);
  const [isEditShopModalOpen, setIsEditShopModalOpen] =
    useState<boolean>(false);
  const [isEditWarehouseModalOpen, setIsEditWarehouseModalOpen] =
    useState<boolean>(false);
  // const [isAssignUserModalOpen, setIsAssignUserModalOpen] =
  //   useState<boolean>(false);

  const handleDeleteShop = async () => {
    try {
      await deleteShop(shop.id);
      setIsDeleteShopModalOpen(false);
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <Link
      to={
        type === 'shop'
          ? ROUTES.SHOP.DASHBOARD(shop?.id as string)
          : ROUTES.WAREHOUSE.DASHBOARD(shop?.id as string)
      }
      className="relative rounded-xl border border-gray-400 bg-white p-4 shadow-lg"
    >
      <div className="absolute -right-[1px] -top-[1px] z-10">
        <button
          type="button"
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
          }}
        >
          <Menubar>
            <MenubarMenu>
              <MenubarTrigger className="cursor-pointer py-1">
                <Ellipsis size={20} />
              </MenubarTrigger>
              <MenubarContent>
                {type === 'warehouse' ? (
                  <MenubarItem
                    onClick={() => setIsEditWarehouseModalOpen(true)}
                  >
                    <button type="button" className="flex items-center gap-2">
                      <Pencil size={18} />
                      <span>Edit Warehouse</span>
                    </button>
                  </MenubarItem>
                ) : (
                  <MenubarItem onClick={() => setIsEditShopModalOpen(true)}>
                    <button type="button" className="flex items-center gap-2">
                      <Pencil size={18} />
                      <span>Edit Shop</span>
                    </button>
                  </MenubarItem>
                )}
                {/* <MenubarSeparator /> */}
                {/* <MenubarItem onClick={() => setIsAssignUserModalOpen(true)}>
                  <div className="flex items-center gap-2">
                    <UserCog size={18} />
                    <span>Manage User</span>
                  </div>
                </MenubarItem> */}
                {/* <MenubarSeparator /> */}
                {/* <MenubarItem onClick={() => setIsDeleteShopModalOpen(true)}>
                  <button type="button" className="flex items-center gap-2">
                    <Trash2 size={18} />
                    <span>Delete Shop</span>
                  </button>
                </MenubarItem> */}
              </MenubarContent>
            </MenubarMenu>
          </Menubar>
        </button>
      </div>
      <div className="grid grid-cols-12 items-center gap-4 xl:gap-7">
        <img
          src={
            shop?.imgUrl
              ? `https://retail-pluse-upload.s3.ap-southeast-1.amazonaws.com/${
                  shop?.imgUrl
                }`
              : type === 'shop'
                ? 'https://cdn.pixabay.com/photo/2013/07/12/15/49/shop-150362_640.png'
                : 'https://c8.alamy.com/comp/2J2WW0P/warehouse-text-written-on-black-wooden-frame-school-blackboard-2J2WW0P.jpg'
          }
          alt=""
          className="col col-span-3"
        />
        <div className="col col-span-9">
          <div className="w-[85%]">
            <span className="font-bold">
              {shop?.name} {type === 'shop' ? `(${shop?.nickName})` : ''}
            </span>
          </div>
          <div className="mt-2 flex flex-col xl:mt-3">
            {/* <p className="text-sm">{shop?.address}</p> */}
            {/* <p className="text-sm">
              <span className="font-bold">Created At:</span>{' '}
              {new Date(shop?.createdAt).toLocaleDateString()}
            </p> */}
            <p className="text-sm">
              <span className="font-bold capitalize">Type:</span> {type}
            </p>
          </div>
        </div>
      </div>
      <Modal
        setShowModal={setIsDeleteShopModalOpen}
        showModal={isDeleteShopModalOpen}
      >
        <DeleteModal
          type="Shop"
          name={shop?.name}
          handleClose={() => setIsDeleteShopModalOpen(false)}
          handleDelete={() => handleDeleteShop()}
        />
      </Modal>
      <Modal
        setShowModal={setIsEditShopModalOpen}
        showModal={isEditShopModalOpen}
      >
        <AddOrEditShopModal
          type="edit"
          handleClose={() => setIsEditShopModalOpen(false)}
          updateRefreshCounter={() => console.log('flflf')}
          shopData={shop}
        />
      </Modal>
      <Modal
        setShowModal={setIsEditWarehouseModalOpen}
        showModal={isEditWarehouseModalOpen}
      >
        <EditWarehouseModal
          handleClose={() => setIsEditWarehouseModalOpen(false)}
          updateRefreshCounter={() => console.log('flflf')}
          shopData={shop}
        />
      </Modal>
    </Link>
  );
}

export default OrganizationSingleShopCard;
