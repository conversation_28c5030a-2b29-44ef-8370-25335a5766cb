import { Document, pdf } from '@react-pdf/renderer';
import { toast } from 'react-toastify';
import * as XLSX from 'xlsx';

import StockEntryReportPdf from '@/components/WarehouseComponents/ReportsPdf/StockEntryReportPdf';
import { SingleStockEntryDetails } from '@/types/warehouseTypes/reportTypes';

export const handleGenerateStockEntryReportPdf = async (
  entryList: any,
  warehouse: any,
) => {
  const blob = await pdf(
    <Document>
      <StockEntryReportPdf entryList={entryList} warehouse={warehouse} />
    </Document>,
  ).toBlob();

  const url = URL.createObjectURL(blob);
  window.open(url);
};

export const handleGenerateStockEntryReportCsv = ({
  data,
}: {
  data?: SingleStockEntryDetails[];
}) => {
  if (!data || data.length === 0) {
    toast.error('No data available to generate Excel');
    return;
  }

  // Define Excel headers
  const headers = [
    'No',
    'Created At',
    'Supplier Invoice No',
    'Note',
    'Total Price',
    'Total Paid',
    'Total Due',
    'Purchase Date',
    'Supplier Name',
    // 'Purchase Details',
  ];

  // Map data rows
  const rows = data.map((entry: SingleStockEntryDetails, index: number) => {
    return [
      index + 1,
      entry.createdAt,
      entry.supplierInvoiceNo,
      entry.note,
      entry.totalPrice,
      entry.totalPaid,
      entry.totalDue,
      entry.purchaseDate,
      entry.Supplier?.User?.name || 'N/A',
      // JSON.stringify(entry.Purchase), // Convert Purchase array to string for Excel
    ];
  });

  // Combine headers and rows
  const sheetData = [headers, ...rows];

  // Create a worksheet
  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // Create a workbook and add the worksheet
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Stock Entry Report');

  // Generate Excel file and download
  const excelFileName = `stock_entry_report.xlsx`;
  XLSX.writeFile(workbook, excelFileName);
};
