import { useFormik } from 'formik';
import Cookies from 'js-cookie';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import * as Yup from 'yup';

import FilledSubmitButton from '../../reusable/Buttons/FilledSubmitButton';
import CustomInputField from '../../reusable/CustomInputField/CustomInputField';
import ModalTitle from '../../reusable/Modal/ModalTitle';

import ImageSelector from '@/components/reusable/ImageSelector/ImageSelector';
import {
  useCreateCategoryMutation,
  useUpdateCategoryMutation,
} from '@/redux/api/warehouseApis/categoriesApi';
import { SingleCategoryDetails } from '@/types/warehouseTypes/categoriesTypes';
import { UploadImageOnAws } from '@/utils/ImageUploadModule';

interface Props {
  type: string;
  warehouseId: string | undefined;
  handleClose: () => void;
  updateRefreshCounter: () => void;
  categoryData?: SingleCategoryDetails;
}

const formikInitialValues = {
  name: '',
};

const validation = Yup.object({
  name: Yup.string().required('Category name is required'),
});

function AddOrEditCategoryModal({
  type,
  handleClose,
  updateRefreshCounter,
  categoryData,
}: Props) {
  const [currentFile, setCurrentFile] = useState<any>();
  const [createCategory, { isLoading }] = useCreateCategoryMutation();

  const [updateCategory, { isLoading: isUpdatingCategory }] =
    useUpdateCategoryMutation();

  const formik = useFormik({
    initialValues: formikInitialValues,
    validationSchema: validation,

    onSubmit: async (values) => {
      const imgUrl = currentFile
        ? await UploadImageOnAws(currentFile)
        : categoryData?.imgUrl
          ? categoryData?.imgUrl
          : null;
      if (type === 'new') {
        toast.promise(
          createCategory({
            name: values.name,
            purpose: 'PRODUCT',
            imgUrl,
            organizationId: Cookies.get('organizationId'),
          }).unwrap(),
          {
            pending: 'Creating New Category...',
            success: {
              render({ data: res }) {
                if (res?.statusCode === 200 || res?.statusCode === 201) {
                  updateRefreshCounter();
                  handleClose();
                }
                return 'Category created Successfully';
              },
            },
            error: {
              render({ data: error }) {
                console.log(error);
                return 'Error on creating Category';
              },
            },
          },
        );
      } else {
        toast.promise(
          updateCategory({
            data: { name: values.name, imgUrl },
            id: categoryData?.id,
          }).unwrap(),
          {
            pending: 'Updating Category...',
            success: {
              render({ data: res }) {
                if (res?.statusCode === 200 || res?.statusCode === 201) {
                  updateRefreshCounter();
                  handleClose();
                }
                return 'Category updated Successfully';
              },
            },
            error: {
              render({ data: error }) {
                console.log(error);
                return 'Error on update Category';
              },
            },
          },
        );
      }
    },
  });

  useEffect(() => {
    if (type === 'edit' && categoryData) {
      formik.setFieldValue('name', categoryData?.name);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [type, categoryData]);
  return (
    <div className="flex w-[400px] flex-col gap-4 rounded-xl bg-white p-4">
      <ModalTitle
        text={type === 'new' ? 'Create Category' : 'Edit Category'}
        handleClose={handleClose}
      />
      <form
        onSubmit={formik.handleSubmit}
        className="flex w-full flex-col items-center justify-center gap-2"
      >
        <div className="w-[100px]">
          <ImageSelector
            previousImage={categoryData?.imgUrl ?? ''}
            setNewImage={(e) => setCurrentFile(e)}
          />
        </div>
        <CustomInputField
          type="text"
          placeholder="Enter Category Name"
          name="name"
          label="Name"
          formik={formik}
        />
        <div className="mt-[10px] flex w-full items-center justify-center">
          <FilledSubmitButton
            text={type === 'new' ? 'Create Category' : 'Done Editing'}
            isLoading={isLoading || isUpdatingCategory}
          />
        </div>
      </form>
    </div>
  );
}

export default AddOrEditCategoryModal;
