import { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { toast } from 'react-toastify';

import {
  MoveToLeftButton,
  MoveToRightButton,
} from '@/components/reusable/Buttons/CommonButtons';
import ImageViewer from '@/components/reusable/ImageViewer/ImageViewer';
import NoResultFound from '@/components/reusable/NoResultFound/NoResultFound';
import NoResultFoundForOthers from '@/components/reusable/NoResultFound/NoResultFoundForOthers';
import TableSkeletonLoader from '@/components/reusable/SkeletonLoader/TableSkeletonLoader';
import { useGetShopProductsQuery } from '@/redux/api/shopApis/shopProductsApi';
import { useGetShopStockListQuery } from '@/redux/api/shopApis/shopStockApis';
import { useAppSelector } from '@/redux/hooks';
import { SingleProductDetails } from '@/types/warehouseTypes/productTypes';
import { SingleStockDetails } from '@/types/warehouseTypes/stockTypes';
import { CalculateDiscountPrice } from '@/utils/CalculateDiscountPrice';
import ProtectedDataViewer from '@/utils/ProtectedDataViewer';

interface Props {
  shopId?: string;
  productId?: string;
}
function ShopStockAuditPageOverview({ shopId, productId }: Props) {
  const { shopDetails } = useAppSelector((state) => state);
  const router = new URLSearchParams(useLocation().search);
  const limit = router.get('limit');
  const productSerialNo = router.get('productSerialNo');
  const [stocksInShop, setStocksInShop] = useState<SingleStockDetails[]>();
  const [scannedStocks, setScannedStocks] = useState<SingleStockDetails[]>();
  const { data: productData, isLoading: isProductDataLoading } =
    useGetShopProductsQuery(
      {
        shopId,
        page: '1',
        serialNo: productSerialNo ?? undefined,
        limit: '10',
      },
      {
        skip: !shopId && !productSerialNo,
      },
    );

  const { data, isLoading, isFetching } = useGetShopStockListQuery(
    {
      warehouseId: shopDetails?.warehouseId,
      shopId,
      productId,
      page: '1',
      barcode: undefined,
      limit: limit ?? '10',
      productName: undefined,
      productSerialNo: productSerialNo ?? undefined,
      status: 'ASSIGNED',
      optimal: true,
    },
    { skip: !(shopDetails?.warehouseId && shopId) },
  );

  useEffect(() => {
    let scannedBarcode = '';
    let timeout: NodeJS.Timeout;
    const handleBarcodeInput = (e: KeyboardEvent) => {
      if (timeout) clearTimeout(timeout);

      if (e.key === 'Enter') {
        if (scannedBarcode) {
          const scannedStock = stocksInShop?.find(
            (stock) => stock?.barcode === Number(scannedBarcode),
          );
          if (scannedStock) {
            const filteredStocks = stocksInShop?.filter(
              (stock) => stock?.barcode !== Number(scannedBarcode),
            );
            if (scannedStocks?.length) {
              setScannedStocks((prev) => [...(prev || []), scannedStock]);
            } else {
              setScannedStocks([scannedStock]);
            }
            setStocksInShop(filteredStocks);
            toast.success(
              `Barcode ${scannedStock?.barcode} added to scanned list`,
            );
          } else {
            toast.error('Barcode not found or already scanned');
          }
        } else {
          toast.error('Barcode not found');
        }
        scannedBarcode = '';
      } else {
        console.log('Scanned', e.key);
        scannedBarcode += e.key;
        timeout = setTimeout(() => {
          scannedBarcode = '';
        }, 200);
      }
    };

    window.addEventListener('keydown', handleBarcodeInput);
    return () => {
      window.removeEventListener('keydown', handleBarcodeInput);
    };
  }, [scannedStocks?.length, stocksInShop]);

  const handleMarkAsFound = (barcode: number) => {
    if (barcode) {
      const scannedStock = stocksInShop?.find(
        (stock) => stock?.barcode === Number(barcode),
      );
      if (scannedStock) {
        const filteredStocks = stocksInShop?.filter(
          (stock) => stock?.barcode !== Number(barcode),
        );
        if (scannedStocks?.length) {
          setScannedStocks((prev) => [...(prev || []), scannedStock]);
        } else {
          setScannedStocks([scannedStock]);
        }
        setStocksInShop(filteredStocks);
        toast.success(`Barcode ${scannedStock?.barcode} added to scanned list`);
      }
    } else {
      toast.error('Barcode not found');
    }
  };

  const handleMarkAsNotFound = (barcode: number) => {
    if (barcode) {
      const scannedStock = scannedStocks?.find(
        (stock) => stock?.barcode === Number(barcode),
      );
      if (scannedStock) {
        const filteredStocks = scannedStocks?.filter(
          (stock) => stock?.barcode !== Number(barcode),
        );
        if (stocksInShop?.length) {
          setStocksInShop((prev) => [...(prev || []), scannedStock]);
        } else {
          setStocksInShop([scannedStock]);
        }
        setScannedStocks(filteredStocks);
        toast.success(`Barcode ${scannedStock?.barcode} added to shop list`);
      }
    } else {
      toast.error('Barcode not found');
    }
  };

  useEffect(() => {
    if (data?.data?.length) {
      setStocksInShop(data?.data);
    }
  }, [data]);

  return (
    <div>
      {!isProductDataLoading ? (
        <div>
          <div className="full-table-container w-full md:w-custommd lg:w-customlg xl:w-custom">
            {data?.data?.length ? (
              <div className="full-table-box">
                <table className="full-table">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="tableHead">ID</th>
                      <th className="tableHead">Image</th>
                      <th className="tableHead table-col-width">Name</th>
                      <th className="tableHead">Brand</th>
                      <th className="tableHead">Category</th>
                      <ProtectedDataViewer>
                        <th className="tableHead">TP</th>
                      </ProtectedDataViewer>
                      <th className="tableHead">Regular</th>
                      <th className="tableHead">Discount Price</th>
                      <th className="tableHead">Available</th>
                      <th className="tableHead">Total Sold</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y bg-slate-200">
                    {productData?.data?.map((product: SingleProductDetails) => (
                      <tr key={product?.id}>
                        <td className="tableData">{product?.serialNo ?? 0}</td>
                        <td className="tableData">
                          <ImageViewer imageUrl={product?.imgUrl} />
                        </td>
                        <td className="tableData table-col-width">
                          {product?.name}
                        </td>
                        <td className="tableData">{product?.Brand?.name}</td>
                        <td className="tableData">
                          {product?.ProductCategory?.name}
                        </td>
                        <ProtectedDataViewer>
                          <td className="tableData">
                            {product?.currentPurchasePrice}
                          </td>
                        </ProtectedDataViewer>

                        <td className="tableData">
                          {product?.currentSellingPrice}
                        </td>
                        <td className="tableData">
                          {CalculateDiscountPrice({
                            retailPrice: product?.currentSellingPrice,
                            discount: product?.discount,
                            discountType: product?.discountType,
                          })}
                        </td>
                        <td className="tableData">{product?.totalAvailable}</td>
                        <td className="tableData">{product?.totalSold ?? 0}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <NoResultFound pageType="products" />
            )}
          </div>
        </div>
      ) : (
        <TableSkeletonLoader tableColumn={13} tableRow={6} />
      )}
      <div className="mt-2 grid grid-cols-2 gap-2">
        <div>
          {!isLoading && !isFetching ? (
            <div>
              <div className="tableTop w-full">
                <p>Available In Software</p>
                <p>Total : {data?.pagination?.total}</p>
              </div>
              <div className="w-full">
                {stocksInShop?.length ? (
                  <div className="full-table-box h-custom">
                    <table className="relative w-full border border-black">
                      <thead className="bg-gray-100">
                        <tr>
                          <th className="tableHead">#</th>
                          <th className="tableHead">Barcode</th>
                          <th className="tableHead">Regular Price</th>
                          <th className="tableHead">Discount Price</th>
                          <th className="tableHead">Action</th>
                        </tr>
                      </thead>
                      <tbody>
                        {stocksInShop?.map(
                          (stock: SingleStockDetails, index) => (
                            <tr key={stock?.id} className="tableRowRed">
                              <td className="tableData">{index + 1}</td>
                              <td className="tableData">{stock?.barcode}</td>
                              <td className="tableData">
                                {stock?.retailPrice}
                              </td>
                              <td className="tableData">
                                {CalculateDiscountPrice({
                                  retailPrice: stock?.retailPrice,
                                  discountType: stock.discountType,
                                  discount: stock.discount,
                                })}
                              </td>
                              <td className="tableData">
                                <MoveToRightButton
                                  handleClick={() =>
                                    handleMarkAsFound(stock.barcode)
                                  }
                                />
                              </td>
                            </tr>
                          ),
                        )}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <NoResultFoundForOthers pageType="stock" />
                )}
              </div>
            </div>
          ) : (
            <TableSkeletonLoader tableColumn={10} tableRow={6} />
          )}
        </div>
        <div>
          {!isLoading && !isFetching ? (
            <div>
              <div className="tableTop w-full">
                <p>Available In Shop</p>
                <p>Total : {scannedStocks?.length ?? 0}</p>
              </div>
              <div className="w-full">
                {scannedStocks?.length ? (
                  <div className="full-table-box h-custom">
                    <table className="relative w-full border border-black">
                      <thead className="bg-gray-100">
                        <tr>
                          <th className="tableHead">#</th>
                          <th className="tableHead">Barcode</th>
                          <th className="tableHead">Regular Price</th>
                          <th className="tableHead">Discount Price</th>
                          <th className="tableHead">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {scannedStocks?.map(
                          (stock: SingleStockDetails, index) => (
                            <tr key={stock?.id} className="tableRowRed">
                              <td className="tableData">{index + 1}</td>
                              <td className="tableData">{stock?.barcode}</td>
                              <td className="tableData">
                                {stock?.retailPrice}
                              </td>
                              <td className="tableData">
                                {CalculateDiscountPrice({
                                  retailPrice: stock?.retailPrice,
                                  discountType: stock.discountType,
                                  discount: stock.discount,
                                })}
                              </td>
                              <td className="tableData">
                                <MoveToLeftButton
                                  handleClick={() =>
                                    handleMarkAsNotFound(stock.barcode)
                                  }
                                />
                              </td>
                            </tr>
                          ),
                        )}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <NoResultFoundForOthers pageType="stock" />
                )}
              </div>
            </div>
          ) : (
            <TableSkeletonLoader tableColumn={10} tableRow={6} />
          )}
        </div>
      </div>
    </div>
  );
}

export default ShopStockAuditPageOverview;
