import { Page, Text, View } from '@react-pdf/renderer';

import PrintedTime from './PrintedTime';
import { reportPdfStyles } from './ReportPdfStyles';
import SoftwareMarketingOnPdf from './SoftwareMarketingOnPdf';
import WarehousePdfHeader from './WarehousePdfHeader';

import { WarehouseDetailsInRedux } from '@/redux/slice/warehouseSlice';
import {
  GetShopAccountsSummeryResponse,
  SingleShopSalesSummary,
} from '@/types/shopTypes/shopStockTransferReportTypes';
import { truncateText } from '@/utils/stringTruncate';

interface Props {
  data?: GetShopAccountsSummeryResponse;
  warehouseDetails: WarehouseDetailsInRedux;
}

// need to fix
function SalesSummeryReportPdf({ data, warehouseDetails }: Props) {
  return (
    <Page size="A4" style={reportPdfStyles.page}>
      <View>
        <WarehousePdfHeader warehouseDetails={warehouseDetails} />
        <PrintedTime />

        <View style={reportPdfStyles.table}>
          <View style={reportPdfStyles.tableRow}>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.salesCol,
                reportPdfStyles.salesName,
              ]}
            >
              Shop
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.salesCol,
                reportPdfStyles.salesQty,
              ]}
            >
              Qty
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.salesCol,
              ]}
            >
              MRP
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.salesCol,
              ]}
            >
              Charge
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.salesCol,
                reportPdfStyles.salesDiscount,
              ]}
            >
              Discount
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.salesCol,
              ]}
            >
              Vat
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.salesCol,
              ]}
            >
              Payable
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.salesCol,
              ]}
            >
              Received
            </Text>
            <Text
              style={[
                reportPdfStyles.tableCol,
                reportPdfStyles.headerCol,
                reportPdfStyles.tableColLast,
                reportPdfStyles.salesCol,
              ]}
            >
              Due
            </Text>
          </View>
          {data?.data?.shops?.map((product: SingleShopSalesSummary, index) => (
            <View
              key={product.shop.id}
              style={
                index === Number(data?.data?.shops?.length) - 1
                  ? [reportPdfStyles.tableRowLast]
                  : [reportPdfStyles.tableRow]
              }
            >
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.salesCol,
                  reportPdfStyles.salesName,
                ]}
              >
                {truncateText(product?.shop.name, 25)}
              </Text>
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.salesCol,
                  reportPdfStyles.salesQty,
                ]}
              >
                {product.totalOrderCount}
              </Text>
              <Text
                style={[reportPdfStyles.tableCol, reportPdfStyles.salesCol]}
              >
                {product.totalSubtotal}
              </Text>
              <Text
                style={[reportPdfStyles.tableCol, reportPdfStyles.salesCol]}
              >
                {product.totalDeliveryCharge}
              </Text>
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.salesCol,
                  reportPdfStyles.salesDiscount,
                ]}
              >
                {product.totalDiscount}
              </Text>
              <Text
                style={[reportPdfStyles.tableCol, reportPdfStyles.salesCol]}
              >
                {product.totalVat}
              </Text>
              <Text
                style={[reportPdfStyles.tableCol, reportPdfStyles.salesCol]}
              >
                {product.totalPayable}
              </Text>
              <Text
                style={[reportPdfStyles.tableCol, reportPdfStyles.salesCol]}
              >
                {product.cashReceive}
              </Text>
              <Text
                style={[
                  reportPdfStyles.tableCol,
                  reportPdfStyles.salesCol,
                  reportPdfStyles.tableColLast,
                ]}
              >
                {product.totalDue}
              </Text>
            </View>
          ))}
        </View>

        <SoftwareMarketingOnPdf />
      </View>
    </Page>
  );
}

export default SalesSummeryReportPdf;
