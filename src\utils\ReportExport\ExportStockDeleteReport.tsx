import { Document, pdf } from '@react-pdf/renderer';
import { toast } from 'react-toastify';
import * as XLSX from 'xlsx';

import StockDeleteReportPdf from '@/components/WarehouseComponents/ReportsPdf/StockDeleteReportPdf';
import { SingleStockReturnReport } from '@/types/reportTypes/stockReturnReportType';
import { SingleStockDeleteDetails } from '@/types/shopTypes/shopStockTransferReportTypes';
import { Warehouse } from '@/types/shopTypes/shopTransferAmountToOwnerTypes';

export const handleGenerateStockDeleteReportPdf = async (
  deleteList?: SingleStockDeleteDetails[],
  warehouse?: Warehouse,
) => {
  const blob = await pdf(
    <Document>
      <StockDeleteReportPdf deleteList={deleteList} warehouse={warehouse} />
    </Document>,
  ).toBlob();

  const url = URL.createObjectURL(blob);
  window.open(url);
};

export const handleGenerateStockDeleteReportCsv = ({
  data,
}: {
  data?: SingleStockDeleteDetails[];
}) => {
  if (!data || data.length === 0) {
    toast.error('No data available to generate Excel');
    return;
  }

  // Define Excel headers
  const headers = ['No', 'Deleted By', 'Note', 'Quantity', 'Delete Date'];

  // Map data rows
  const rows = data.map(
    (deleteData: SingleStockDeleteDetails, index: number) => {
      return [
        index + 1,
        deleteData?.CreatedBy?.name || 'N/A',
        deleteData.reason,
        deleteData.stockIds?.length || 0,
        deleteData.createdAt,
      ];
    },
  );

  // Combine headers and rows
  const sheetData = [headers, ...rows];

  // Create a worksheet
  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // Create a workbook and add the worksheet
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Stock delete Report');

  // Generate Excel file and download
  const excelFileName = `stock_delete_report.xlsx`;
  XLSX.writeFile(workbook, excelFileName);
};

export const handleGenerateStockReturnReportCsv = ({
  data,
}: {
  data?: SingleStockReturnReport[];
}) => {
  if (!data || data.length === 0) {
    toast.error('No data available to generate Excel');
    return;
  }

  // Define Excel headers
  const headers = [
    'No',
    'Barcode',
    'OrderId',
    'Status',
    'Regular Price',
    'Return Date',
  ];

  // Map data rows
  const rows = data.map((item, index) => [
    index + 1,
    item.barcode || '-',
    item.OrderItem[0].Order.serialNo || '-',
    item.status || '-',
    item.retailPrice != null ? item.retailPrice : '-',
    item.updatedAt ? String(item.updatedAt) : '-',
  ]);

  // Combine headers and rows
  const sheetData = [headers, ...rows];

  // Create a worksheet
  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // Create a workbook and add the worksheet
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Stock Return Report');

  // Generate Excel file and download
  const excelFileName = `stock_return_report.xlsx`;
  XLSX.writeFile(workbook, excelFileName);
};
