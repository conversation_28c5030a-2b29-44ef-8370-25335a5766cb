import { useParams } from 'react-router-dom';

import ShopExpensesReportOverview from '@/components/ShopComponents/ShopExpensesReportComponents/ShopExpensesReportOverview';
import { useAppSelector } from '@/redux/hooks';

function ShopExpensesReportPage() {
  const { shopId } = useParams();
  const { warehouseId } = useAppSelector((state) => state.shopDetails);
  return (
    <div>
      <ShopExpensesReportOverview
        shopId={shopId ?? ''}
        warehouseId={warehouseId}
      />
    </div>
  );
}

export default ShopExpensesReportPage;
