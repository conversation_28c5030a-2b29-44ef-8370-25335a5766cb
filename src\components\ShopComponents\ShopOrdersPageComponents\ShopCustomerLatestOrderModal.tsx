import DateAndTimeViewer from '@/components/reusable/DateAndTimeViewer/DateAndTimeViewer';
import ModalTitle from '@/components/reusable/Modal/ModalTitle';
import { ShopCustomerDetailsWithOrderList } from '@/types/shopTypes/shopCustomersTypes';
import { ShopOrderDetails } from '@/types/shopTypes/shopOrderTypes';

interface Props {
  customerDetails?: ShopCustomerDetailsWithOrderList;
  handleClose: () => void;
}

function ShopCustomerLatestOrderModal({ customerDetails, handleClose }: Props) {
  return (
    <div className="flex w-[400px] flex-col gap-4 rounded-xl bg-white p-4 md:w-[600px] lg:w-[800px] xl:w-[1000px]">
      <ModalTitle
        text="Customer Details With Latest Order"
        handleClose={handleClose}
      />
      <div>
        <h2>Name : {customerDetails?.name}</h2>
        <h2>Phone : {customerDetails?.mobileNumber}</h2>
        <h2>Address : {customerDetails?.address}</h2>
        <h2>Email : {customerDetails?.email}</h2>
        <h2>Gender : {customerDetails?.gender}</h2>
      </div>
      <div>
        <div className="tableTop w-full">
          <p>Latest Order List</p>
          {/* <p>Total : {data?.pagination?.total}</p> */}
        </div>
        <div className="full-table-container">
          {customerDetails?.Order?.length ? (
            <div className="full-table-box h-[100px]">
              <table className="full-table">
                <thead className="bg-gray-100">
                  <tr>
                    <th className="tableHead">Order No</th>
                    <th className="tableHead">Total</th>
                    <th className="tableHead">Paid</th>
                    <th className="tableHead">Due</th>
                    <th className="tableHead">Status</th>
                    <th className="tableHead">Created At</th>
                    <th className="tableHead">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y bg-slate-200">
                  {customerDetails?.Order?.map((order: ShopOrderDetails) => (
                    <tr key={order?.id}>
                      <td className="tableData">{order?.serialNo}</td>
                      <td className="tableData">
                        {Number(order?.grandTotal) +
                          Number(order?.deliveryCharge)}
                      </td>
                      <td className="tableData">{order?.totalPaid}</td>
                      <td className="tableData">{order?.totalDue}</td>
                      <td className="tableData">{order?.orderStatus}</td>
                      <td className="tableData">
                        <DateAndTimeViewer date={order?.createdAt} />
                      </td>
                      <td className="tableData">
                        <div className="flex items-center justify-end gap-2">
                          {/* <PrintA4Button
                                handleClick={() => {
                                  handleGenerateSingleOrderPdf(order);
                                }}
                              />
                              <PrintButton
                                handleClick={() => {
                                  handleGenerateSingleOrderPosPdf(order);
                                }}
                              /> */}
                          {/* <EyeButton
                                handleClick={() =>
                                  navigate(
                                    ROUTES.SHOP.ORDER_DETAILS(
                                      shopId,
                                      order?.id,
                                    ),
                                  )
                                }
                              /> */}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div>
              <h2>No Orders found</h2>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default ShopCustomerLatestOrderModal;
