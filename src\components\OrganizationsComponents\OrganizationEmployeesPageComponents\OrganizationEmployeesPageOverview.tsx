import Cookies from 'js-cookie';
import { Plus, Shield } from 'lucide-react';
import { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import Switch from 'react-switch';
import { toast } from 'react-toastify';
import Swal from 'sweetalert2';

import AddOrEditEmployeeModal from './AddOrEditEmployeeModal';
import AddOrEditEmployeePermissionModal from './AddOrEditEmployeePermissionModal';
import EmployeeRoleGuideModal from './EmployeeRoleGuideModal';

import {
  DeleteButton,
  EditButton,
  EyeButton,
} from '@/components/reusable/Buttons/CommonButtons';
import FilledButton from '@/components/reusable/Buttons/FilledButton';
import FilterButton from '@/components/reusable/Buttons/FilterButton';
import DeleteModal from '@/components/reusable/DeleteModal/DeleteModal';
import ImageViewer from '@/components/reusable/ImageViewer/ImageViewer';
import SearchInput from '@/components/reusable/Inputs/SearchInput';
import Modal from '@/components/reusable/Modal/Modal';
import Pagination from '@/components/reusable/Pagination/Pagination';
import SpinnerLoader from '@/components/reusable/SpinnerLoader/SpinnerLoader';
import { useDeleteEmployeePermissionMutation } from '@/redux/api/organizationApis/orgEmployeeApis';
import {
  useBlockOrUnblockEmployeeMutation,
  useGetSellersQuery,
} from '@/redux/api/warehouseApis/sellersApis';
import { ROUTES } from '@/Routes';
import {
  EmployeePermissionDetails,
  SingleSellerDetails,
} from '@/types/warehouseTypes/sellersTypes';
import { generateFilterParams } from '@/utils/generateFilterParams';

function OrganizationEmployeesPageOverview() {
  const navigate = useNavigate();
  const router = new URLSearchParams(useLocation().search);
  const page = router.get('page');
  const limit = router.get('limit');
  const name = router.get('name');
  const mobileNumber = router.get('mobileNumber');
  // apis
  // const warehouseId = Cookies.get('warehouseId');
  const { data, isLoading, refetch } = useGetSellersQuery({
    organizationId: Cookies.get('organizationId'),
    page: page ?? '1',
    limit: limit ?? '10',
    name: name ?? undefined,
    mobileNumber: mobileNumber ?? undefined,
  });

  // states
  const [isCreateSellerModalOpen, setIsCreateSellerModalOpen] =
    useState<boolean>(false);
  const [isEditSellerModalOpen, setIsEditSellerModalOpen] =
    useState<boolean>(false);
  const [selectedSeller] = useState<SingleSellerDetails>();
  const [isAddNewPermissionModalOpen, setIsAddNewPermissionModalOpen] =
    useState(false);
  const [selectedEmployee, setSelectedEmployee] =
    useState<SingleSellerDetails>();
  const [selectedPermission, setSelectedPermission] =
    useState<EmployeePermissionDetails>();
  const [isEditPermissionModalOpen, setIsEditPermissionModalOpen] =
    useState<boolean>(false);
  const [isDeletePermissionModalOpen, setIsDeletePermissionModalOpen] =
    useState<boolean>(false);
  const [isRoleGuideModalOpen, setIsRoleGuideModalOpen] = useState(false);

  const handleFilter = (fieldName: string, value: string) => {
    const query = generateFilterParams(fieldName, value);
    navigate(ROUTES.ORGANIZATION.EMPLOYEES(query));
  };

  const [blockOrUnblockEmployee, { isLoading: isSellerUpdating }] =
    useBlockOrUnblockEmployeeMutation();

  const [deleteEmployeePermission] = useDeleteEmployeePermissionMutation();

  const handleBlockOrUnblockUser = (
    userDetails: SingleSellerDetails,
    status: boolean,
  ) => {
    Swal.fire({
      title: `Are you sure? you want to ${status ? 'Unblock' : 'Block'} ${userDetails?.User?.name}`,
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: `Yes, ${status ? 'Unblock' : 'Block'} ${userDetails?.User?.name}!!`,
    }).then((result) => {
      if (result.isConfirmed) {
        toast.promise(
          blockOrUnblockEmployee({
            data: { isActive: status },
            id: userDetails.id,
          }).unwrap(),
          {
            pending: 'Assigning Employee To Shop...',
            success: {
              render() {
                return 'Employee Assigned Successfully';
              },
            },
            error: {
              render({ data: error }) {
                console.log(error);
                return 'Error on assign employee';
              },
            },
          },
        );
      }
    });
  };

  const handleDeletePermission = async () => {
    toast.promise(deleteEmployeePermission(selectedPermission?.id), {
      pending: 'Deleting Permission...',
      success: {
        render({ data: res }) {
          if (res.data?.statusCode === 200 || res.data?.statusCode === 201) {
            refetch();
            setIsDeletePermissionModalOpen(false);
          }
          return 'Permission Deleted Successfully';
        },
      },
      error: {
        render({ data: error }) {
          console.log(error);
          return 'Error on delete permission';
        },
      },
    });
  };
  return (
    <div className="w-full">
      <div className="search-filters mb-4 flex items-center justify-between rounded bg-white px-3 py-3 xl:py-1">
        <div className="flex items-center gap-x-2">
          <div className="search-title-and-btn flex items-center gap-x-3">
            {/* <p className="whitespace-nowrap">Search Filters</p> */}
            <div className="relative">
              <div className="block xl:hidden">
                <FilterButton handleClick={() => console.log('higbig')} />
              </div>
              <div className="block xl:hidden">
                {/* <ProductPageFilterModal /> */}
              </div>
            </div>
          </div>
          <div className="hidden xl:block">
            <div className="flex items-center gap-x-2">
              <SearchInput
                placeholder="Search by Name"
                handleSubmit={(value: string) => handleFilter('name', value)}
              />
              <SearchInput
                placeholder="Search by Phone"
                handleSubmit={(value: string) =>
                  handleFilter('mobileNumber', value)
                }
              />
            </div>
          </div>
        </div>
        <div className="flex items-center gap-4">
          <button
            type="button"
            className="flex cursor-pointer items-center gap-2 whitespace-nowrap rounded-lg border border-[#28243D] px-4 py-[6px] font-semibold text-primary hover:bg-[#28243dd4] hover:text-white disabled:cursor-not-allowed disabled:bg-slate-300"
            onClick={() => setIsRoleGuideModalOpen(true)}
          >
            <Shield size={18} />
            <span>Permissions Guideline</span>
          </button>
          <FilledButton
            isLoading={false}
            text="Add New"
            handleClick={() => setIsCreateSellerModalOpen(true)}
            isDisabled={false}
          />
        </div>
      </div>
      {!isLoading ? (
        <div>
          <div className="tableTop w-full">
            <p>Employees List</p>
            <p>Total : {data?.pagination.total}</p>
          </div>
          <div className="full-table-container Exc w-full">
            {data?.data?.length ? (
              <div className="full-table-box h-custom">
                <table className="full-table">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="tableHead">No</th>
                      <th className="tableHead table-col-width">Name</th>
                      <th className="tableHead">Image</th>
                      <th className="tableHead">Phone Number</th>
                      <th className="tableHead">Username</th>
                      {/* <th className="tableHead">Default Password</th> */}
                      {/* <th className="tableHead">Shop</th> */}
                      <th className="tableHead">Permissions</th>
                      <th className="tableHead">Status</th>
                      {/* <th className="tableHead">Order</th> */}
                      {/* <th className="tableHead">Amount</th> */}
                      {/* <th className="tableHead">Salary</th> */}
                      {/* <th className="tableHead">Created At</th> */}
                      <th className="tableHeadRightAlign">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y bg-slate-200">
                    {data?.data?.map((seller: SingleSellerDetails) => {
                      const permissions = seller?.EmployeePermission || [];
                      const hasPermissions = permissions.length > 0;

                      // If no permissions or is admin, show single row
                      if (
                        !hasPermissions ||
                        seller?.User?.type === 'CUSTOMER'
                      ) {
                        return (
                          <tr key={seller?.id}>
                            <td className="tableData">1</td>
                            <td className="tableData table-col-width">
                              {seller?.User?.name}
                            </td>
                            <td className="tableData">
                              <ImageViewer imageUrl={seller?.imgUrl} />
                            </td>
                            <td className="tableData">
                              {seller?.User?.mobileNumber}
                            </td>
                            <td className="tableData">
                              <span>{seller?.User?.username}</span> <br />
                              <span>123456</span>
                            </td>
                            <td className="tableData">
                              <span className="text-sm font-semibold text-gray-800">
                                {seller?.User?.type === 'CUSTOMER'
                                  ? 'OWNER'
                                  : 'No Permissions'}
                              </span>
                            </td>
                            <td className="tableData">
                              <div className="flex items-center justify-center border-none">
                                <Switch
                                  onChange={() =>
                                    handleBlockOrUnblockUser(
                                      seller,
                                      !seller?.isActive,
                                    )
                                  }
                                  checked={seller.isActive}
                                  disabled={
                                    isSellerUpdating ||
                                    seller?.User?.type === 'CUSTOMER'
                                  }
                                />
                              </div>
                            </td>
                            <td className="tableData">
                              <div className="flex items-center justify-end gap-2">
                                {seller?.User?.type !== 'CUSTOMER' ? (
                                  <button
                                    onClick={() => {
                                      setSelectedEmployee(seller);
                                      setIsAddNewPermissionModalOpen(true);
                                    }}
                                    type="button"
                                  >
                                    <Plus />
                                  </button>
                                ) : (
                                  ''
                                )}
                                <EyeButton
                                  handleClick={() =>
                                    navigate(
                                      ROUTES.ORGANIZATION.EMPLOYEE_DETAILS(
                                        seller.id,
                                      ),
                                    )
                                  }
                                />
                              </div>
                            </td>
                          </tr>
                        );
                      }

                      // If has permissions, show multiple rows
                      return permissions.map(
                        (
                          permission: EmployeePermissionDetails,
                          index: number,
                        ) => {
                          const { AssignedShop, role } = permission;
                          const shopName = AssignedShop?.name || 'Warehouse';
                          const nickname = AssignedShop?.nickName
                            ? `(${AssignedShop.nickName})`
                            : '';

                          return (
                            <tr key={`${seller?.id}-${permission.id}`}>
                              {index === 0 ? (
                                <>
                                  <td
                                    className="tableData"
                                    rowSpan={permissions.length}
                                  >
                                    {index + 1}
                                  </td>
                                  <td
                                    className="tableData table-col-width"
                                    rowSpan={permissions.length}
                                  >
                                    {seller?.User?.name}
                                  </td>
                                  <td
                                    className="tableData"
                                    rowSpan={permissions.length}
                                  >
                                    <ImageViewer imageUrl={seller?.imgUrl} />
                                  </td>
                                  <td
                                    className="tableData"
                                    rowSpan={permissions.length}
                                  >
                                    {seller?.User?.mobileNumber}
                                  </td>
                                  <td
                                    className="tableData"
                                    rowSpan={permissions.length}
                                  >
                                    <span>{seller?.User?.username}</span> <br />
                                    <span>123456</span>
                                  </td>
                                </>
                              ) : null}
                              <td className="tableData">
                                <div className="flex items-center justify-between">
                                  <span className="text-sm text-gray-700">
                                    {shopName} {nickname} - {role}
                                  </span>
                                  <div className="flex items-center gap-2">
                                    <EditButton
                                      handleClick={() => {
                                        setSelectedEmployee(seller);
                                        setSelectedPermission(permission);
                                        setIsEditPermissionModalOpen(true);
                                      }}
                                    />
                                    <DeleteButton
                                      handleClick={() => {
                                        setSelectedPermission(permission);
                                        setSelectedEmployee(seller);
                                        setIsDeletePermissionModalOpen(true);
                                      }}
                                    />
                                  </div>
                                </div>
                              </td>
                              {index === 0 ? (
                                <>
                                  <td
                                    className="tableData"
                                    rowSpan={permissions.length}
                                  >
                                    <div className="flex items-center justify-center border-none">
                                      <Switch
                                        onChange={() =>
                                          handleBlockOrUnblockUser(
                                            seller,
                                            !seller?.isActive,
                                          )
                                        }
                                        checked={seller.isActive}
                                        disabled={
                                          isSellerUpdating ||
                                          seller?.User?.type === 'CUSTOMER'
                                        }
                                      />
                                    </div>
                                  </td>
                                  <td
                                    className="tableData"
                                    rowSpan={permissions.length}
                                  >
                                    <div className="flex items-center justify-end gap-2">
                                      <button
                                        onClick={() => {
                                          setSelectedEmployee(seller);
                                          setIsAddNewPermissionModalOpen(true);
                                        }}
                                        type="button"
                                      >
                                        <Plus />
                                      </button>
                                      <EyeButton
                                        handleClick={() =>
                                          navigate(
                                            ROUTES.ORGANIZATION.EMPLOYEE_DETAILS(
                                              seller.id,
                                            ),
                                          )
                                        }
                                      />
                                    </div>
                                  </td>
                                </>
                              ) : null}
                            </tr>
                          );
                        },
                      );
                    })}
                  </tbody>
                </table>
              </div>
            ) : (
              <div>
                <h2>No seller found</h2>
              </div>
            )}
          </div>
          <div className="pagination-box flex justify-end rounded bg-white p-3">
            <Pagination
              currentPage={page ?? '1'}
              limit={Number(limit ?? 10)}
              handleFilter={(fieldName: string, value: any) =>
                handleFilter(fieldName, value)
              }
              totalCount={data?.pagination?.total}
              totalPages={Math.ceil(
                Number(data?.pagination?.total) /
                  Number(data?.pagination?.limit),
              )}
            />
          </div>
        </div>
      ) : (
        <SpinnerLoader />
      )}
      <Modal
        setShowModal={setIsCreateSellerModalOpen}
        showModal={isCreateSellerModalOpen}
      >
        <AddOrEditEmployeeModal
          type="new"
          // warehouseId={warehouseId ?? ''}
          handleClose={() => setIsCreateSellerModalOpen(false)}
        />
      </Modal>
      <Modal
        setShowModal={setIsEditSellerModalOpen}
        showModal={isEditSellerModalOpen}
      >
        <AddOrEditEmployeeModal
          type="edit"
          // warehouseId={warehouseId ?? ''}
          handleClose={() => setIsEditSellerModalOpen(false)}
          sellerData={selectedSeller}
        />
      </Modal>
      <Modal
        setShowModal={setIsAddNewPermissionModalOpen}
        showModal={isAddNewPermissionModalOpen}
      >
        <AddOrEditEmployeePermissionModal
          type="new"
          handleClose={() => setIsAddNewPermissionModalOpen(false)}
          updateRefreshCounter={refetch}
          employee={selectedEmployee}
        />
      </Modal>
      <Modal
        setShowModal={setIsEditPermissionModalOpen}
        showModal={isEditPermissionModalOpen}
      >
        <AddOrEditEmployeePermissionModal
          type="edit"
          handleClose={() => setIsEditPermissionModalOpen(false)}
          updateRefreshCounter={refetch}
          employee={selectedEmployee}
          permission={selectedPermission}
        />
      </Modal>
      <Modal
        setShowModal={setIsDeletePermissionModalOpen}
        showModal={isDeletePermissionModalOpen}
      >
        <DeleteModal
          type={`permission from ${selectedPermission?.Warehouse?.name || selectedPermission?.AssignedShop?.name} user`}
          name={selectedEmployee?.User.name ?? ''}
          handleClose={() => setIsDeletePermissionModalOpen(false)}
          handleDelete={handleDeletePermission}
        />
      </Modal>
      <Modal
        setShowModal={setIsRoleGuideModalOpen}
        showModal={isRoleGuideModalOpen}
      >
        <EmployeeRoleGuideModal
          handleClose={() => setIsRoleGuideModalOpen(false)}
        />
      </Modal>
    </div>
  );
}

export default OrganizationEmployeesPageOverview;
